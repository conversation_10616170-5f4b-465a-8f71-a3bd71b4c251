<script lang="ts">
  import { createEventDispatcher } from 'svelte'

  export let title: string = 'No analyses yet'
  export let description: string = 'Start your first brand analysis to see how AI models represent your company.'
  export let actionText: string = 'Start Analysis'
  export let showAction: boolean = true

  const dispatch = createEventDispatcher()

  function handleAction() {
    dispatch('action')
  }
</script>

<div class="card-brutal p-8 text-center">
  <div class="space-y-6">
    <!-- Icon -->
    <div class="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
      <svg class="w-8 h-8 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
        />
      </svg>
    </div>

    <!-- Content -->
    <div class="space-y-2">
      <h3 class="text-lg font-medium text-foreground">{title}</h3>
      <p class="text-muted-foreground max-w-md mx-auto">{description}</p>
    </div>

    <!-- Action -->
    {#if showAction}
      <button onclick={handleAction} class="btn-primary px-6 py-3 font-bold">
        {actionText}
      </button>
    {/if}

    <!-- Features List -->
    <div class="bg-muted/50 p-4 rounded-lg border border-border max-w-md mx-auto">
      <h4 class="text-sm font-medium text-foreground mb-3">What you'll get:</h4>
      <ul class="text-sm text-muted-foreground space-y-2 text-left">
        <li class="flex items-center gap-2">
          <svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          AI model brand visibility analysis
        </li>
        <li class="flex items-center gap-2">
          <svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          Competitor positioning insights
        </li>
        <li class="flex items-center gap-2">
          <svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          Actionable improvement recommendations
        </li>
        <li class="flex items-center gap-2">
          <svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          Multi-provider comparison matrix
        </li>
      </ul>
    </div>
  </div>
</div>

<style>
  .btn-primary {
    @apply bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
