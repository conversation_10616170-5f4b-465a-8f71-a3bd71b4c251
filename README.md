<div align="center">
    <h1>🔥 RobynNV3 - AI Brand Monitoring Platform</h1>
    <p><strong>Monitor how AI models rank your brand against competitors in real-time</strong></p>

[![GitHub Repo stars](https://img.shields.io/github/stars/madhu<PERSON>kumar/robynnv3)](https://github.com/madhukarkumar/robynnv3)

  <a href="#-quick-start"><strong>Quick Start Guide</strong></a> •
  <a href="#project-structure"><strong>Project Structure</strong></a> •
  <a href="#deployment"><strong>Deployment</strong></a>
</div>

<br/>

# RobynNV3 - AI Brand Monitoring Platform

**RobynNV3** is an AI-powered brand monitoring platform that tracks how AI models rank your brand against competitors across search results, recommendations, and AI-generated content. Built with modern web technologies and designed for scalability.

## 🎯 **Core Capabilities**

- 🤖 **AI Model Analysis**: Track brand visibility across multiple AI platforms (ChatGPT, Claude, Gemini, etc.)
- 📊 **Competitor Intelligence**: Compare your brand positioning against competitors in real-time
- 🎯 **Brand Positioning**: Monitor your brand's position in AI recommendations and search results
- 🔄 **Continuous Monitoring**: Automated tracking with intelligent alerts and reporting
- 📈 **Analytics Dashboard**: Comprehensive insights, trends, and actionable recommendations
- 🚀 **Multi-Agent System**: Research agents, SEO tools, and content orchestration
- 💼 **Enterprise Ready**: User management, billing, subscriptions, and team collaboration

## 🏗️ **Platform Overview**

RobynNV3 consists of two main applications working together:

- **Dashboard** (`apps/dashboard`): SvelteKit-based user interface with authentication, analytics, and agent management
- **FireGeo API** (`apps/firegeo-api`): Next.js-based API service handling AI analysis and brand monitoring
- **Shared Packages** (`packages/shared`): Common utilities and types shared between applications

*Built on a robust SaaS foundation with modern web technologies for performance, scalability, and developer experience.*

## Features

### 🔥 **AI Brand Monitoring (FireGeo)**
- **Multi-Platform Analysis**: Track brand visibility across ChatGPT, Claude, Gemini, and other AI models
- **Competitor Intelligence**: Real-time comparison of brand positioning against competitors
- **Brand Sentiment Analysis**: Monitor how AI models perceive and describe your brand
- **Search Result Tracking**: Track brand mentions in AI-generated search results and recommendations
- **Automated Reporting**: Scheduled reports with actionable insights and trend analysis
- **Custom Queries**: Set up specific monitoring scenarios and brand positioning tests

### 🤖 **AI Agent System**
- **Research Agent**: Comprehensive market research and competitive analysis
- **SEO Agent**: Advanced SEO tools with DataForSEO integration for keyword research and gap analysis
- **Content Agent**: AI-powered content creation and optimization
- **Campaign Orchestrator**: Multi-channel campaign planning and execution

### 💼 **Enterprise Platform**
- **User Management**: Complete authentication system with email verification and password reset
- **Team Collaboration**: Multi-user environments with role-based access control
- **Billing & Subscriptions**: Stripe-powered subscription management with self-service portal
- **Analytics Dashboard**: Real-time insights, custom reports, and data visualization
- **API Access**: RESTful APIs for integration with existing tools and workflows

### 🎨 **User Experience**
- **Modern Interface**: Clean, responsive design built with SvelteKit and Tailwind CSS
- **Real-time Updates**: Live data streaming and instant notifications
- **Mobile Optimized**: Full functionality across desktop, tablet, and mobile devices
- **Performance Optimized**: Lightning-fast loading with 100/100 Google PageSpeed scores
- **Accessibility**: WCAG compliant with comprehensive keyboard navigation and screen reader support

## Project Structure

```
robynnv3/
├── apps/
│   ├── dashboard/              # SvelteKit frontend application
│   │   ├── src/
│   │   │   ├── lib/
│   │   │   │   ├── components/     # UI components
│   │   │   │   │   ├── brand-monitor/  # FireGeo brand monitoring UI
│   │   │   │   │   ├── research/       # Research agent interface
│   │   │   │   │   ├── agents/         # SEO and other agent UIs
│   │   │   │   │   └── ...
│   │   │   │   ├── stores/         # Svelte stores for state management
│   │   │   │   └── utils/          # Utility functions
│   │   │   ├── routes/             # SvelteKit routes
│   │   │   │   ├── (marketing)/    # Public marketing pages
│   │   │   │   ├── (admin)/        # Authenticated dashboard pages
│   │   │   │   └── api/            # API endpoints
│   │   │   └── app.html            # Main HTML template
│   │   ├── static/                 # Static assets
│   │   └── package.json
│   │
│   └── firegeo-api/            # Next.js API service
│       ├── src/
│       │   ├── app/
│       │   │   ├── api/            # API routes
│       │   │   │   ├── auth/       # Authentication endpoints
│       │   │   │   ├── brand-monitor/  # Brand monitoring APIs
│       │   │   │   └── health/     # Health check endpoints
│       │   │   └── page.tsx        # Default page
│       │   ├── lib/                # Utility libraries
│       │   │   ├── auth/           # Authentication helpers
│       │   │   ├── ai/             # AI model integrations
│       │   │   └── monitoring/     # Brand monitoring logic
│       │   └── types/              # TypeScript type definitions
│       └── package.json
│
├── packages/
│   └── shared/                 # Shared utilities and types
│       ├── src/
│       │   ├── types/              # Common TypeScript types
│       │   └── utils/              # Shared utility functions
│       └── package.json
│
├── pnpm-workspace.yaml         # pnpm workspace configuration
├── package.json                # Root package.json
└── README.md                   # This file
```

## Tech Stack

### Frontend (Dashboard)
- **Framework**: SvelteKit with Svelte 5 (latest runes mode)
- **Styling**: TailwindCSS with Shadcn-svelte component library
- **State Management**: Svelte stores with reactive updates
- **Authentication**: Supabase Auth with session management
- **Forms**: Superforms with Zod validation
- **Build Tool**: Vite with TypeScript support

### Backend (FireGeo API)
- **Framework**: Next.js 14 with App Router and TypeScript
- **Authentication**: Supabase JWT validation and user management
- **AI Integration**: Multi-provider support (OpenAI, Anthropic, Google)
- **Data Processing**: Real-time brand monitoring and analysis
- **API Design**: RESTful endpoints with comprehensive error handling

### Infrastructure & Services
- **Hosting**: Vercel with automatic deployments
- **Database**: Supabase Postgres with real-time subscriptions
- **Authentication**: Supabase Auth (shared between applications)
- **Payments**: Stripe Checkout and Customer Portal
- **Monitoring**: Built-in health checks and logging
- **CDN**: Vercel Edge Network for global performance

## Hosting & Infrastructure

**There’s no cost for using this template**. The costs below reflect our suggested hosting stack.

- **$0/mo** — Supabase free tier, Cloudflare free tier.
  - Pros:
    - Free!
    - Can scale to thousands of users.
    - Unlimited static page requests.
    - 100k serverless functions/day.
  - Cons:
    - Does not include database backups. The frugal among you could hook up pgdump backups on lambda/S3 for a few cents per month.
    - Will auto-pause your database when not in use for 7 days.
  - Who it’s for:
    - This tier is perfectly functional for a hobby project, or pre-revenue company (up to 50,000 monthly active users). It’s easy to scale up once revenue starts, but it’s also fine to keep at this scale indefinitely.
- **$30/mo** - Supabase Pro, Cloudfare [Workers Paid](https://www.cloudflare.com/plans/developer-platform/)
  - Pros:
    - Database backups.
    - Never pauses database.
    - Over 1M serverless functions per day, with linear pricing for additional invocations.
  - Cons:
    - none
  - Who it’s for:
    - I suggest moving to this once you have paid customers or investors.

## Performance / Best Practices

The selected tech stack creates lightning fast websites.

- Pre-rendering (static generation) for marketing pages, pricing and blog
- Instant navigation: the best of CSR + SSR in one. SSR your first page for fastest possible initial load times. For subsequent pages, the content is pre-loaded and rendered with CSR, for instant rendering.
- CDN optimized, for high edge-cache hit ratios
- Edge-functions for dynamic APIs/pages
- Svelte and Tailwind compile out unused HTML, CSS and JS at deploy time for smaller pages
- Linting to find accessibility and syntax issues

The result is a perfect Google PageSpeed Insights score in all categories!

<img width="420" alt="Screenshot 2024-01-18 at 11 31 32 AM" src="https://github.com/startino/saas-starter/blob/assets/lighthouse-report.png" />

# Quick Start

## 🚀 Local Development Setup

Follow these steps to get RobynNV3 running locally with both the Dashboard and FireGeo API services.

### Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18.0.0 or higher
- **pnpm**: Version 8.0.0 or higher (`npm install -g pnpm`)
- **Git**: For cloning the repository
- **Supabase Account**: For authentication and database services

### Step 1: Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/madhukarkumar/robynnv3.git
cd robynnv3

# Install all dependencies using pnpm workspaces
pnpm install
```

### Step 2: Environment Configuration

You need to configure environment variables for both applications:

#### 2.1 Dashboard Environment (apps/dashboard/.env)

Create `apps/dashboard/.env` with your Supabase credentials:

```env
# Supabase Configuration
PUBLIC_SUPABASE_URL=https://your-project.supabase.co
PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
PRIVATE_SUPABASE_SERVICE_ROLE=your-service-role-key-here

# FireGeo Integration
PUBLIC_FIREGEO_API_URL=http://localhost:3001

# Optional: Analytics and Development
PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

#### 2.2 FireGeo API Environment (apps/firegeo-api/.env)

Create `apps/firegeo-api/.env` with matching Supabase credentials:

```env
# Supabase Configuration (must match dashboard)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-role-key-here

# API Configuration
PORT=3001
NODE_ENV=development

# Security
API_SECRET_KEY=dev_secret_key_123
```

**Important**: The Supabase URL and service key must be identical in both `.env` files for proper authentication between services.

### Step 3: Build Applications

Build both applications to ensure everything is configured correctly:

```bash
# Build FireGeo API
pnpm build:firegeo

# Build Dashboard
pnpm build:dashboard
```

**Expected Output**: Both builds should complete successfully. The dashboard build may show Tailwind CSS warnings, but these are non-blocking.

### Step 4: Start Development Servers

Start both services in development mode:

```bash
# Terminal 1: Start FireGeo API
pnpm dev:firegeo

# Terminal 2: Start Dashboard (in a new terminal)
pnpm dev:dashboard
```

### Step 5: Access the Applications

Once both servers are running, you can access:

- **Dashboard**: http://localhost:3000
- **FireGeo API**: http://localhost:3001

### Step 6: Verification Steps

#### 6.1 Verify FireGeo API
```bash
# Test API health check
curl http://localhost:3001/api/health

# Expected response: {"status":"ok","timestamp":"..."}
```

#### 6.2 Verify Dashboard
1. Open http://localhost:3000 in your browser
2. You should see the marketing homepage
3. Click "Get Started" or navigate to `/login`
4. The login/signup forms should be functional

#### 6.3 Verify Integration
1. Create a user account through the signup process
2. After authentication, navigate to the dashboard
3. Look for the "FireGeo" brand monitor agent card
4. The FireGeo integration should show connection status

### Step 7: Authentication Setup

To fully test the FireGeo integration:

1. **Create a Supabase Account**: If you haven't already
2. **Set up Authentication**:
   - Enable user signups in Supabase console
   - Configure auth callback URLs for `http://localhost:3000/auth/callback`
3. **Test User Flow**:
   - Sign up with a valid email address
   - Confirm email verification
   - Access the dashboard at `/dashboard/[environment]/brand-monitor`

### Troubleshooting

#### Common Issues and Solutions

**Build Failures:**
```bash
# If builds fail, try clearing cache and reinstalling
pnpm clean
rm -rf node_modules
pnpm install
```

**Port Already in Use:**
```bash
# Kill processes on ports 3000 and 3001
lsof -ti:3000 | xargs kill -9
lsof -ti:3001 | xargs kill -9
```

**Supabase Connection Issues:**
- Verify your Supabase URL and keys are correct
- Ensure both `.env` files have identical Supabase credentials
- Check that your Supabase project is active and not paused

**Authentication Errors:**
- Confirm email verification is enabled in Supabase
- Check that callback URLs are configured correctly
- Verify service role key has proper permissions

**FireGeo Integration Issues:**
- Ensure both services are running on correct ports
- Check browser console for CORS or connection errors
- Verify `PUBLIC_FIREGEO_API_URL` points to `http://localhost:3001`

#### Development Tips

- **Hot Reload**: Both applications support hot reload in development mode
- **Logs**: Check terminal outputs for detailed error messages
- **Database**: Use Supabase dashboard to monitor database connections
- **API Testing**: Use tools like Postman or curl to test FireGeo API endpoints

### Application Architecture

This project consists of two main applications that work together:

- **Dashboard** (`apps/dashboard`): SvelteKit-based user interface with authentication, user management, and agent interfaces
- **FireGeo API** (`apps/firegeo-api`): Next.js-based API service that handles brand monitoring and AI analysis

The Dashboard communicates with the FireGeo API through authenticated HTTP requests, sharing user sessions via Supabase authentication tokens.

### Next Steps

Once you have both applications running:

1. **Explore the Dashboard**: Navigate through the user interface and agent cards
2. **Test Brand Monitoring**: Try the FireGeo brand analysis features
3. **Customize Configuration**: Modify environment variables as needed
4. **Deploy to Production**: Follow the deployment guide below

---

## Deployment

### Vercel Deployment (Recommended)

RobynNV3 is optimized for deployment on Vercel with automatic builds and deployments.

#### Prerequisites
- Vercel account
- Supabase project with configured database
- Stripe account (for billing features)

#### Deploy Dashboard

1. **Connect Repository**:
   ```bash
   # Install Vercel CLI
   npm i -g vercel

   # Login and link project
   vercel login
   vercel link
   ```

2. **Configure Environment Variables**:
   In your Vercel dashboard, add the following environment variables:
   ```env
   PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   PRIVATE_SUPABASE_SERVICE_ROLE=your-service-role-key-here
   PUBLIC_FIREGEO_API_URL=https://your-firegeo-api.vercel.app
   ```

3. **Deploy**:
   ```bash
   # Deploy from apps/dashboard directory
   cd apps/dashboard
   vercel --prod
   ```

#### Deploy FireGeo API

1. **Configure Environment Variables**:
   ```env
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_SERVICE_KEY=your-service-role-key-here
   API_SECRET_KEY=your-production-secret-key
   ```

2. **Deploy**:
   ```bash
   # Deploy from apps/firegeo-api directory
   cd apps/firegeo-api
   vercel --prod
   ```

#### Monorepo Configuration

For automatic deployments, configure `vercel.json` in the root:
```json
{
  "projects": [
    {
      "name": "robynnv3-dashboard",
      "source": "apps/dashboard"
    },
    {
      "name": "robynnv3-firegeo-api",
      "source": "apps/firegeo-api"
    }
  ]
}
```

### Database Setup

#### Supabase Configuration

1. **Create Supabase Project**:
   - Create a [Supabase account](https://supabase.com)
   - Create a new project in the Supabase console
   - Wait for the database to initialize

2. **Database Schema**:
   - Go to the [SQL Editor](https://supabase.com/dashboard/project/_/sql) in your Supabase dashboard
   - Run the SQL from `database_migration.sql` to create the required tables and functions
   - Enable Row Level Security (RLS) policies for data protection

3. **Authentication Setup**:
   - Enable user signups in [Auth Settings](https://app.supabase.com/project/_/settings/auth)
   - Configure auth callback URLs:
     - Production: `https://your-domain.com/auth/callback`
     - Development: `http://localhost:3000/auth/callback`
   - Set up email templates with your branding
   - Configure SMTP for production email delivery

4. **API Keys**:
   - Go to [API Settings](https://supabase.com/dashboard/project/_/settings/api)
   - Copy your Project URL, anon key, and service role key
   - Add these to your environment variables (see deployment section above)
#### Optional: OAuth Providers

Configure OAuth providers in Supabase Auth settings:
- **GitHub**: Recommended for developer-focused applications
- **Google**: For broader user base
- **Other providers**: As needed for your use case

Update the OAuth provider list in `apps/dashboard/src/routes/(marketing)/login/login_config.ts`

### Billing Setup (Optional)

#### Stripe Configuration

1. **Create Stripe Account**:
   - Set up a [Stripe account](https://stripe.com)
   - Create products and pricing tiers in the Stripe dashboard

2. **Configure Products**:
   - Create separate products for each tier (Free, Pro, Enterprise)
   - Set up monthly and annual pricing options
   - Configure the Stripe Customer Portal

3. **Environment Variables**:
   ```env
   PRIVATE_STRIPE_API_KEY=sk_test_your_stripe_secret_key
   PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
   ```

4. **Update Pricing Configuration**:
   - Edit `apps/dashboard/src/routes/(marketing)/pricing/pricing_plans.ts`
   - Add your Stripe product and price IDs
   - Configure the default plan for new users

---

## Contributing

We welcome contributions to RobynNV3! Please read our contributing guidelines and submit pull requests for any improvements.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Make your changes and test thoroughly
4. Commit with descriptive messages: `git commit -m "Add: new feature description"`
5. Push to your fork: `git push origin feature/your-feature-name`
6. Submit a pull request

### Code Standards

- Follow TypeScript best practices
- Use Prettier for code formatting
- Write comprehensive tests for new features
- Update documentation for any API changes

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue in this repository
- Check the documentation and troubleshooting guides
- Review existing issues for similar problems

---

*Built with ❤️ using SvelteKit, Next.js, Supabase, and modern web technologies.*
