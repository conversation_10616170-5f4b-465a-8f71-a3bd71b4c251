import { FireGeoApiClient } from "shared"
import { PUBLIC_FIREGEO_API_URL } from "$env/static/public"

// Create a singleton instance of the FireGeo API client
let clientInstance: FireGeoApiClient | null = null

export function getFireGeoClient(): FireGeoApiClient {
  if (!clientInstance) {
    const apiUrl = PUBLIC_FIREGEO_API_URL || "http://localhost:3001"
    const apiSecret = "dev_secret_key_123" // This will be replaced with proper env var

    clientInstance = new FireGeoApiClient(apiUrl, apiSecret)
  }

  return clientInstance
}

// Export the client instance for convenience
export const fireGeoClient = getFireGeoClient()

// Helper functions for common operations
export async function authenticateWithFireGeo(supabaseToken: string) {
  try {
    const client = getFireGeoClient()
    const result = await client.authenticateUser(supabaseToken)

    if (!result.success) {
      throw new Error(result.error || "Authentication failed")
    }

    return result
  } catch (error) {
    console.error("FireGeo authentication failed:", error)
    throw error
  }
}

export async function startBrandAnalysis(
  url: string,
  supabaseToken: string,
  options: {
    companyName?: string
    competitors?: string[]
    prompts?: string[]
  } = {},
) {
  try {
    const client = getFireGeoClient()

    // First authenticate
    await authenticateWithFireGeo(supabaseToken)

    // Then start analysis
    const result = await client.startBrandAnalysis(
      {
        url,
        companyName: options.companyName,
        competitors: options.competitors,
        prompts: options.prompts,
      },
      supabaseToken,
    )

    return result
  } catch (error) {
    console.error("Failed to start brand analysis:", error)
    throw error
  }
}

export async function getBrandAnalyses(supabaseToken: string) {
  try {
    const client = getFireGeoClient()

    // Authenticate first
    await authenticateWithFireGeo(supabaseToken)

    // Get analyses
    return await client.getBrandAnalyses(supabaseToken)
  } catch (error) {
    console.error("Failed to get brand analyses:", error)
    throw error
  }
}

export async function getBrandAnalysis(
  analysisId: string,
  supabaseToken: string,
) {
  try {
    const client = getFireGeoClient()

    // Authenticate first
    await authenticateWithFireGeo(supabaseToken)

    // Get specific analysis
    return await client.getBrandAnalysis(analysisId, supabaseToken)
  } catch (error) {
    console.error("Failed to get brand analysis:", error)
    throw error
  }
}

// Svelte store for managing FireGeo connection state
import { writable } from "svelte/store"

export const fireGeoConnectionState = writable<{
  connected: boolean
  authenticated: boolean
  error: string | null
  latency?: number
}>({
  connected: false,
  authenticated: false,
  error: null,
  latency: undefined,
})

export async function testFireGeoConnection() {
  try {
    const client = getFireGeoClient()
    const startTime = Date.now()
    await client.healthCheck()
    const latency = Date.now() - startTime

    fireGeoConnectionState.update((state) => ({
      ...state,
      connected: true,
      error: null,
      latency,
    }))

    return true
  } catch (error) {
    fireGeoConnectionState.update((state) => ({
      ...state,
      connected: false,
      error: error instanceof Error ? error.message : "Connection failed",
      latency: undefined,
    }))

    return false
  }
}
