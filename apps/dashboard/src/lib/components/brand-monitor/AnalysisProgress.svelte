<script lang="ts">
  import type { AnalysisProgress } from 'shared'

  export let progress: AnalysisProgress
  export let analysisId: string

  function getProgressPercentage(): number {
    return Math.min(Math.max(progress.progress || 0, 0), 100)
  }

  function getStepIcon(completed: boolean) {
    if (completed) {
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />`
    }
    return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />`
  }

  $: progressPercentage = getProgressPercentage()
</script>

<div class="card-brutal p-6">
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-foreground">Analysis Progress</h3>
      <span class="text-sm text-muted-foreground">ID: {analysisId.slice(0, 8)}...</span>
    </div>

    <!-- Progress Bar -->
    <div class="space-y-2">
      <div class="flex items-center justify-between text-sm">
        <span class="text-foreground font-medium">{progress.step}</span>
        <span class="text-muted-foreground">{progressPercentage}%</span>
      </div>
      
      <div class="w-full bg-muted rounded-full h-3 border border-border">
        <div 
          class="bg-primary h-full rounded-full transition-all duration-500 ease-out"
          style="width: {progressPercentage}%"
        ></div>
      </div>
      
      <p class="text-sm text-muted-foreground">{progress.message}</p>
    </div>

    <!-- Current Step Details -->
    <div class="bg-muted/50 p-4 rounded-lg border border-border">
      <div class="flex items-start gap-3">
        <div class="w-5 h-5 text-primary mt-0.5">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            {@html getStepIcon(progress.completed)}
          </svg>
        </div>
        <div class="flex-1">
          <h4 class="font-medium text-foreground mb-1">Current Step: {progress.step}</h4>
          <p class="text-sm text-muted-foreground">{progress.message}</p>
        </div>
      </div>
    </div>

    <!-- Estimated Time -->
    <div class="text-center">
      <p class="text-xs text-muted-foreground">
        {#if progress.completed}
          Analysis completed successfully
        {:else if progressPercentage > 80}
          Almost done! Finalizing results...
        {:else if progressPercentage > 50}
          Estimated time remaining: 1-2 minutes
        {:else}
          Estimated time remaining: 2-4 minutes
        {/if}
      </p>
    </div>

    <!-- Cancel Button -->
    {#if !progress.completed}
      <div class="text-center">
        <button class="btn-secondary px-4 py-2 text-sm">
          Cancel Analysis
        </button>
      </div>
    {/if}
  </div>
</div>

<style>
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
