<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import type { BrandAnalysis } from "shared"

  export let analyses: BrandAnalysis[] = []
  export let selectedAnalysis: BrandAnalysis | null = null

  const dispatch = createEventDispatcher()

  function selectAnalysis(analysis: BrandAnalysis) {
    dispatch("select", analysis)
  }

  function clearSelection() {
    dispatch("clearSelection")
  }

  function getStatusColor(analysis: BrandAnalysis) {
    // This would be based on actual analysis status
    // For now, we'll use a simple heuristic
    if (analysis.analysisData) {
      return "bg-green-500"
    }
    return "bg-yellow-500"
  }

  function getStatusText(analysis: BrandAnalysis) {
    if (analysis.analysisData) {
      return "Completed"
    }
    return "In Progress"
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  function truncateUrl(url: string, maxLength: number = 30) {
    if (url.length <= maxLength) return url
    return url.substring(0, maxLength) + "..."
  }
</script>

<div class="card-brutal p-4">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-foreground">Analyses</h3>
    {#if selectedAnalysis}
      <button
        onclick={clearSelection}
        class="text-sm text-muted-foreground hover:text-foreground transition-colors"
      >
        ← Back to list
      </button>
    {/if}
  </div>

  {#if analyses.length === 0}
    <div class="text-center py-6">
      <div
        class="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-3"
      >
        <svg
          class="w-6 h-6 text-muted-foreground"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      </div>
      <p class="text-sm text-muted-foreground">No analyses yet</p>
      <p class="text-xs text-muted-foreground mt-1">
        Start your first analysis
      </p>
    </div>
  {:else}
    <div class="space-y-2">
      {#each analyses as analysis (analysis.id)}
        <button
          onclick={() => selectAnalysis(analysis)}
          class="w-full p-3 text-left border-2 border-border bg-background hover:bg-accent transition-colors {selectedAnalysis?.id ===
          analysis.id
            ? 'border-primary bg-primary/5'
            : ''}"
        >
          <div class="space-y-2">
            <!-- Header -->
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-foreground truncate">
                  {analysis.companyName || "Unknown Company"}
                </h4>
                <p class="text-xs text-muted-foreground truncate">
                  {truncateUrl(analysis.url)}
                </p>
              </div>
              <div class="flex items-center gap-1 ml-2">
                <div
                  class="w-2 h-2 rounded-full {getStatusColor(analysis)}"
                ></div>
              </div>
            </div>

            <!-- Status and Date -->
            <div class="flex items-center justify-between text-xs">
              <span class="text-muted-foreground">
                {getStatusText(analysis)}
              </span>
              <span class="text-muted-foreground">
                {formatDate(analysis.createdAt)}
              </span>
            </div>

            <!-- Competitors count (if any) -->
            {#if analysis.competitors && analysis.competitors.length > 0}
              <div class="text-xs text-muted-foreground">
                {analysis.competitors.length} competitor{analysis.competitors
                  .length !== 1
                  ? "s"
                  : ""}
              </div>
            {/if}
          </div>
        </button>
      {/each}
    </div>
  {/if}

  <!-- New Analysis Button -->
  <div class="mt-4 pt-4 border-t border-border">
    <button
      onclick={clearSelection}
      class="w-full px-4 py-2 text-sm border-2 border-dashed border-border text-muted-foreground hover:border-primary hover:text-foreground transition-colors"
    >
      + New Analysis
    </button>
  </div>
</div>

<style>
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
