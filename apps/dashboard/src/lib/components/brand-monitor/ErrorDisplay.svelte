<script lang="ts">
  import { createEventDispatcher } from 'svelte'

  export let error: string
  export let title: string = 'Error'
  export let retryable: boolean = true
  export let details: string = ''

  const dispatch = createEventDispatcher()

  function handleRetry() {
    dispatch('retry')
  }

  function handleDismiss() {
    dispatch('dismiss')
  }

  function getErrorIcon() {
    return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />`
  }
</script>

<div class="card-brutal p-6 bg-destructive/5 border-destructive/20">
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-start gap-3">
      <div class="w-6 h-6 text-destructive mt-0.5">
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          {@html getErrorIcon()}
        </svg>
      </div>
      <div class="flex-1">
        <h3 class="font-semibold text-destructive">{title}</h3>
        <p class="text-destructive/80 mt-1">{error}</p>
        {#if details}
          <p class="text-sm text-muted-foreground mt-2">{details}</p>
        {/if}
      </div>
      <button
        onclick={handleDismiss}
        class="text-muted-foreground hover:text-foreground transition-colors"
        title="Dismiss"
      >
        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Actions -->
    <div class="flex items-center gap-3">
      {#if retryable}
        <button onclick={handleRetry} class="btn-secondary px-4 py-2 text-sm">
          Try Again
        </button>
      {/if}
      <button onclick={handleDismiss} class="text-sm text-muted-foreground hover:text-foreground transition-colors">
        Dismiss
      </button>
    </div>
  </div>
</div>

<style>
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
