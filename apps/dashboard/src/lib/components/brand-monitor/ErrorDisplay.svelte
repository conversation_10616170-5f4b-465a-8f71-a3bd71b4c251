<script lang="ts">
  import { createEventDispatcher } from "svelte"

  export let error: string
  export let title: string = "Error"
  export let retryable: boolean = true
  export let details: string = ""

  const dispatch = createEventDispatcher()

  function handleRetry() {
    dispatch("retry")
  }

  function handleDismiss() {
    dispatch("dismiss")
  }

  function getErrorIcon() {
    return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />`
  }
</script>

<div class="card-brutal p-6 bg-destructive/5 border-destructive/20">
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-start gap-3">
      <div class="w-6 h-6 text-destructive mt-0.5">
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          {@html getErrorIcon()}
        </svg>
      </div>
      <div class="flex-1">
        <h3 class="font-semibold text-destructive">{title}</h3>
        <p class="text-destructive/80 mt-1">{error}</p>
        {#if details}
          <p class="text-sm text-muted-foreground mt-2">{details}</p>
        {/if}
      </div>
      <button
        onclick={handleDismiss}
        class="text-muted-foreground hover:text-foreground transition-colors"
        title="Dismiss"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Actions -->
    <div class="flex items-center gap-3">
      {#if retryable}
        <button onclick={handleRetry} class="btn-secondary px-4 py-2 text-sm">
          Try Again
        </button>
      {/if}
      <button
        onclick={handleDismiss}
        class="text-sm text-muted-foreground hover:text-foreground transition-colors"
      >
        Dismiss
      </button>
    </div>
  </div>
</div>

<style>
  .btn-secondary {
    background-color: #6b7280;
    color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
  }

  .btn-secondary:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translate(-2px, -2px);
  }

  .card-brutal {
    background-color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
</style>
