<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { useRealTimeAnalysis } from '$lib/hooks/useRealTimeAnalysis'
  import AnalysisProgress from './AnalysisProgress.svelte'
  import ErrorDisplay from './ErrorDisplay.svelte'
  import type { BrandAnalysisRequest } from 'shared'

  export let analysisData: BrandAnalysisRequest
  export let supabaseToken: string
  export let onComplete: (result: any) => void = () => {}
  export let onCancel: () => void = () => {}

  // Initialize real-time analysis hook
  const {
    analysisState,
    isAnalyzing,
    progressPercentage,
    currentStep,
    startAnalysis,
    cancelAnalysis,
    destroy
  } = useRealTimeAnalysis()

  // Start analysis on mount
  onMount(async () => {
    await startAnalysis(analysisData, supabaseToken)
  })

  // Cleanup on destroy
  onDestroy(() => {
    destroy()
  })

  // Handle analysis completion
  $: if ($analysisState.status === 'completed' && $analysisState.result) {
    onComplete($analysisState.result)
  }

  // Handle cancel
  function handleCancel() {
    cancelAnalysis()
    onCancel()
  }

  // Handle retry on error
  function handleRetry() {
    startAnalysis(analysisData, supabaseToken)
  }

  // Format time remaining
  function getTimeRemaining(progress: number): string {
    if (progress >= 100) return 'Completed'
    if (progress >= 80) return 'Almost done!'
    if (progress >= 50) return '1-2 minutes remaining'
    if (progress >= 20) return '2-4 minutes remaining'
    return '3-5 minutes remaining'
  }

  // Get step icon
  function getStepIcon(completed: boolean, inProgress: boolean) {
    if (completed) {
      return 'M5 13l4 4L19 7' // Check icon
    }
    if (inProgress) {
      return 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' // Clock icon
    }
    return 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' // Clock icon
  }
</script>

<div class="space-y-6">
  <!-- Error Display -->
  {#if $analysisState.status === 'failed' && $analysisState.error}
    <ErrorDisplay 
      error={$analysisState.error}
      title="Analysis Failed"
      on:retry={handleRetry}
      on:dismiss={handleCancel}
    />
  {/if}

  <!-- Progress Display -->
  {#if $isAnalyzing || $analysisState.status === 'completed'}
    <div class="card-brutal p-6">
      <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-foreground">
            {$analysisState.status === 'completed' ? 'Analysis Complete' : 'Analyzing Your Brand'}
          </h3>
          {#if $analysisState.id}
            <span class="text-sm text-muted-foreground">
              ID: {$analysisState.id.slice(0, 8)}...
            </span>
          {/if}
        </div>

        <!-- Progress Bar -->
        <div class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <span class="text-foreground font-medium">
              {$currentStep || 'Initializing...'}
            </span>
            <span class="text-muted-foreground">{Math.round($progressPercentage)}%</span>
          </div>
          
          <div class="w-full bg-muted rounded-full h-3 border border-border">
            <div 
              class="bg-primary h-full rounded-full transition-all duration-500 ease-out {$isAnalyzing ? 'animate-pulse' : ''}"
              style="width: {$progressPercentage}%"
            ></div>
          </div>
          
          <p class="text-sm text-muted-foreground">
            {$analysisState.progress?.message || 'Starting analysis...'}
          </p>
        </div>

        <!-- Current Step Details -->
        {#if $analysisState.progress}
          <div class="bg-muted/50 p-4 rounded-lg border border-border">
            <div class="flex items-start gap-3">
              <div class="w-5 h-5 text-primary mt-0.5">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="{$isAnalyzing ? 'animate-spin' : ''}">
                  <path 
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d={getStepIcon($analysisState.progress.completed, $isAnalyzing)}
                  />
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-medium text-foreground mb-1">
                  Current Step: {$analysisState.progress.step}
                </h4>
                <p class="text-sm text-muted-foreground">
                  {$analysisState.progress.message}
                </p>
              </div>
            </div>
          </div>
        {/if}

        <!-- Time Estimate -->
        <div class="text-center">
          <p class="text-xs text-muted-foreground">
            {getTimeRemaining($progressPercentage)}
          </p>
        </div>

        <!-- Analysis Details -->
        <div class="bg-muted/30 p-4 rounded-lg border border-border">
          <h4 class="font-medium text-foreground mb-2">Analyzing:</h4>
          <div class="space-y-1 text-sm text-muted-foreground">
            <p>• Website: {analysisData.url}</p>
            {#if analysisData.companyName}
              <p>• Company: {analysisData.companyName}</p>
            {/if}
            {#if analysisData.competitors && analysisData.competitors.length > 0}
              <p>• Competitors: {analysisData.competitors.join(', ')}</p>
            {/if}
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between">
          <div class="text-sm text-muted-foreground">
            {#if $analysisState.status === 'completed'}
              Analysis completed successfully
            {:else}
              Real-time updates active
            {/if}
          </div>
          
          {#if $isAnalyzing}
            <button 
              onclick={handleCancel}
              class="btn-secondary px-4 py-2 text-sm"
            >
              Cancel Analysis
            </button>
          {/if}
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
