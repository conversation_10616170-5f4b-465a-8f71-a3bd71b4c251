<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { BrandAnalysis } from 'shared'

  export let analysis: BrandAnalysis

  const dispatch = createEventDispatcher()

  function goBack() {
    dispatch('back')
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  function getAnalysisStatus() {
    if (analysis.analysisData) {
      return { status: 'completed', color: 'text-green-600', bgColor: 'bg-green-100' }
    }
    return { status: 'in_progress', color: 'text-yellow-600', bgColor: 'bg-yellow-100' }
  }

  $: statusInfo = getAnalysisStatus()
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="card-brutal p-6">
    <div class="flex items-start justify-between mb-4">
      <button
        onclick={goBack}
        class="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
      >
        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
        Back to analyses
      </button>
      
      <div class="flex items-center gap-2">
        <div class="px-3 py-1 rounded-full text-xs font-medium {statusInfo.bgColor} {statusInfo.color}">
          {statusInfo.status === 'completed' ? 'Completed' : 'In Progress'}
        </div>
      </div>
    </div>

    <div class="space-y-3">
      <h1 class="text-2xl font-semibold text-foreground">
        {analysis.companyName || 'Brand Analysis'}
      </h1>
      
      <div class="flex items-center gap-4 text-sm text-muted-foreground">
        <div class="flex items-center gap-1">
          <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
          <a href={analysis.url} target="_blank" rel="noopener noreferrer" class="hover:text-foreground transition-colors">
            {analysis.url}
          </a>
        </div>
        
        <div class="flex items-center gap-1">
          <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {formatDate(analysis.createdAt)}
        </div>
      </div>

      {#if analysis.competitors && analysis.competitors.length > 0}
        <div>
          <h3 class="text-sm font-medium text-foreground mb-2">Competitors Analyzed</h3>
          <div class="flex flex-wrap gap-2">
            {#each analysis.competitors as competitor}
              <span class="px-2 py-1 bg-muted text-muted-foreground text-xs rounded border">
                {competitor.name}
              </span>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Analysis Content -->
  {#if statusInfo.status === 'completed' && analysis.analysisData}
    <!-- Results Section -->
    <div class="card-brutal p-6">
      <h2 class="text-xl font-semibold text-foreground mb-4">Analysis Results</h2>
      
      <!-- This would be populated with actual analysis data -->
      <div class="space-y-4">
        <div class="bg-muted/50 p-4 rounded-lg border border-border">
          <h3 class="font-medium text-foreground mb-2">AI Model Responses</h3>
          <p class="text-sm text-muted-foreground">
            Analysis of how different AI models represent your brand in their responses.
          </p>
        </div>

        <div class="bg-muted/50 p-4 rounded-lg border border-border">
          <h3 class="font-medium text-foreground mb-2">Competitive Positioning</h3>
          <p class="text-sm text-muted-foreground">
            Comparison of your brand visibility against competitors in AI responses.
          </p>
        </div>

        <div class="bg-muted/50 p-4 rounded-lg border border-border">
          <h3 class="font-medium text-foreground mb-2">Recommendations</h3>
          <p class="text-sm text-muted-foreground">
            Actionable insights to improve your brand's representation in AI models.
          </p>
        </div>
      </div>
    </div>
  {:else}
    <!-- In Progress Section -->
    <div class="card-brutal p-6">
      <div class="text-center space-y-4">
        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        
        <div>
          <h2 class="text-xl font-semibold text-foreground mb-2">Analysis in Progress</h2>
          <p class="text-muted-foreground">
            We're analyzing your brand representation across multiple AI models. This typically takes 2-5 minutes.
          </p>
        </div>

        <div class="max-w-md mx-auto">
          <div class="bg-muted rounded-full h-2">
            <div class="bg-primary h-2 rounded-full animate-pulse" style="width: 60%"></div>
          </div>
          <p class="text-xs text-muted-foreground mt-2">Analyzing AI model responses...</p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Actions -->
  <div class="card-brutal p-4">
    <div class="flex items-center justify-between">
      <div class="text-sm text-muted-foreground">
        Analysis ID: {analysis.id}
      </div>
      
      <div class="flex gap-2">
        {#if statusInfo.status === 'completed'}
          <button class="btn-secondary px-4 py-2 text-sm">
            Export Results
          </button>
          <button class="btn-secondary px-4 py-2 text-sm">
            Share Analysis
          </button>
        {:else}
          <button class="btn-secondary px-4 py-2 text-sm">
            Refresh Status
          </button>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
