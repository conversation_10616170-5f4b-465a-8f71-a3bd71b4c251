<script lang="ts">
  import { createEventDispatcher } from "svelte"

  export let isAnalyzing = false

  const dispatch = createEventDispatcher()

  let url = ""
  let companyName = ""
  let competitors: string[] = [""]
  let urlValid = false

  $: urlValid = isValidUrl(url)

  function isValidUrl(str: string): boolean {
    if (!str.trim()) return false
    try {
      new URL(str.startsWith("http") ? str : `https://${str}`)
      return true
    } catch {
      return false
    }
  }

  function addCompetitor() {
    if (competitors.length < 5) {
      competitors = [...competitors, ""]
    }
  }

  function removeCompetitor(index: number) {
    if (competitors.length > 1) {
      competitors = competitors.filter((_, i) => i !== index)
    }
  }

  function handleSubmit() {
    if (urlValid && !isAnalyzing) {
      const analysisData = {
        url: url.startsWith("http") ? url : `https://${url}`,
        companyName: companyName.trim() || undefined,
        competitors: competitors.filter((c) => c.trim()).map((c) => c.trim()),
      }

      dispatch("startAnalysis", analysisData)
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
  }
</script>

<div class="card-brutal p-8">
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h2 class="text-2xl font-semibold text-foreground mb-2">
        Start Brand Analysis
      </h2>
      <p class="text-muted-foreground">
        Enter your company's website URL to analyze how AI models represent your
        brand in their responses.
      </p>
    </div>

    <!-- URL Input -->
    <div class="space-y-4">
      <div>
        <label for="url" class="block text-sm font-medium text-foreground mb-2">
          Company Website URL *
        </label>
        <input
          id="url"
          bind:value={url}
          placeholder="example.com or https://example.com"
          disabled={isAnalyzing}
          class="w-full px-4 py-3 border-2 border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors"
          onkeydown={handleKeydown}
        />
        {#if url && !urlValid}
          <p class="text-sm text-destructive mt-1">Please enter a valid URL</p>
        {/if}
      </div>

      <!-- Company Name (Optional) -->
      <div>
        <label
          for="company-name"
          class="block text-sm font-medium text-foreground mb-2"
        >
          Company Name (Optional)
        </label>
        <input
          id="company-name"
          bind:value={companyName}
          placeholder="Your Company Name"
          disabled={isAnalyzing}
          class="w-full px-4 py-3 border-2 border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors"
          onkeydown={handleKeydown}
        />
      </div>

      <!-- Competitors (Optional) -->
      <div>
        <label class="block text-sm font-medium text-foreground mb-2">
          Competitors (Optional)
        </label>
        <div class="space-y-2">
          {#each competitors as competitor, index}
            <div class="flex gap-2">
              <input
                bind:value={competitor}
                placeholder="competitor.com"
                disabled={isAnalyzing}
                class="flex-1 px-4 py-3 border-2 border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors"
                onkeydown={handleKeydown}
              />
              {#if competitors.length > 1}
                <button
                  type="button"
                  onclick={() => removeCompetitor(index)}
                  disabled={isAnalyzing}
                  class="px-3 py-3 border-2 border-border bg-destructive/10 text-destructive hover:bg-destructive/20 transition-colors"
                  title="Remove competitor"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              {/if}
            </div>
          {/each}

          {#if competitors.length < 5}
            <button
              type="button"
              onclick={addCompetitor}
              disabled={isAnalyzing}
              class="w-full px-4 py-3 border-2 border-dashed border-border text-muted-foreground hover:border-primary hover:text-foreground transition-colors"
            >
              + Add Competitor
            </button>
          {/if}
        </div>
      </div>

      <!-- Submit Button -->
      <button
        onclick={handleSubmit}
        disabled={!urlValid || isAnalyzing}
        class="btn-primary px-6 py-3 font-bold w-full flex items-center justify-center gap-2"
      >
        {#if isAnalyzing}
          <div
            class="animate-spin rounded-full h-4 w-4 border-b-2 border-current"
          ></div>
          Analyzing...
        {:else}
          <svg
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Start Analysis
        {/if}
      </button>
    </div>

    <!-- Info -->
    <div class="bg-muted/50 p-4 rounded-lg border border-border">
      <h3 class="text-sm font-medium text-foreground mb-2">
        What happens next?
      </h3>
      <ul class="text-sm text-muted-foreground space-y-1">
        <li>• We'll analyze your website content and brand positioning</li>
        <li>
          • Test how AI models like ChatGPT, Claude, and Perplexity represent
          your brand
        </li>
        <li>• Compare your brand visibility against competitors</li>
        <li>• Provide actionable insights to improve your AI presence</li>
      </ul>
    </div>
  </div>
</div>

<style>
  .btn-primary {
    background-color: #2563eb;
    color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
  }

  .btn-primary:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translate(-2px, -2px);
  }

  .btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .card-brutal {
    background-color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
</style>
