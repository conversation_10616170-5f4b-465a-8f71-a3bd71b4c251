<script lang="ts">
  export let title: string = 'Loading...'
  export let description: string = ''
  export let progress: number | null = null
  export let showSpinner: boolean = true
  export let size: 'sm' | 'md' | 'lg' = 'md'
  export let variant: 'default' | 'minimal' | 'detailed' = 'default'

  function getSizeClasses() {
    switch (size) {
      case 'sm':
        return {
          container: 'p-4',
          spinner: 'w-6 h-6',
          title: 'text-sm',
          description: 'text-xs'
        }
      case 'lg':
        return {
          container: 'p-8',
          spinner: 'w-12 h-12',
          title: 'text-xl',
          description: 'text-base'
        }
      default:
        return {
          container: 'p-6',
          spinner: 'w-8 h-8',
          title: 'text-lg',
          description: 'text-sm'
        }
    }
  }

  $: sizeClasses = getSizeClasses()
</script>

{#if variant === 'minimal'}
  <!-- Minimal Loading State -->
  <div class="flex items-center gap-3 {sizeClasses.container}">
    {#if showSpinner}
      <div class="animate-spin rounded-full {sizeClasses.spinner} border-b-2 border-primary"></div>
    {/if}
    <span class="text-muted-foreground {sizeClasses.title}">{title}</span>
  </div>
{:else if variant === 'detailed'}
  <!-- Detailed Loading State -->
  <div class="card-brutal {sizeClasses.container}">
    <div class="space-y-4">
      <!-- Header -->
      <div class="flex items-center gap-3">
        {#if showSpinner}
          <div class="animate-spin rounded-full {sizeClasses.spinner} border-b-2 border-primary"></div>
        {/if}
        <div>
          <h3 class="font-semibold text-foreground {sizeClasses.title}">{title}</h3>
          {#if description}
            <p class="text-muted-foreground {sizeClasses.description} mt-1">{description}</p>
          {/if}
        </div>
      </div>

      <!-- Progress Bar -->
      {#if progress !== null}
        <div class="space-y-2">
          <div class="flex items-center justify-between text-xs">
            <span class="text-muted-foreground">Progress</span>
            <span class="text-muted-foreground">{Math.round(progress)}%</span>
          </div>
          <div class="w-full bg-muted rounded-full h-2 border border-border">
            <div 
              class="bg-primary h-full rounded-full transition-all duration-300 ease-out"
              style="width: {progress}%"
            ></div>
          </div>
        </div>
      {/if}

      <!-- Loading Animation -->
      <div class="flex items-center justify-center py-4">
        <div class="flex space-x-1">
          <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0ms"></div>
          <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 150ms"></div>
          <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 300ms"></div>
        </div>
      </div>
    </div>
  </div>
{:else}
  <!-- Default Loading State -->
  <div class="card-brutal {sizeClasses.container} text-center">
    <div class="space-y-4">
      {#if showSpinner}
        <div class="flex justify-center">
          <div class="animate-spin rounded-full {sizeClasses.spinner} border-b-2 border-primary"></div>
        </div>
      {/if}
      
      <div>
        <h3 class="font-semibold text-foreground {sizeClasses.title}">{title}</h3>
        {#if description}
          <p class="text-muted-foreground {sizeClasses.description} mt-2">{description}</p>
        {/if}
      </div>

      {#if progress !== null}
        <div class="max-w-xs mx-auto space-y-2">
          <div class="w-full bg-muted rounded-full h-2 border border-border">
            <div 
              class="bg-primary h-full rounded-full transition-all duration-300 ease-out"
              style="width: {progress}%"
            ></div>
          </div>
          <p class="text-xs text-muted-foreground">{Math.round(progress)}% complete</p>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
