import { writable, derived } from "svelte/store"
import { fireGeoClient } from "$lib/firegeo-client"
import type { BrandAnalysisResponse, AnalysisProgress } from "shared"

export interface AnalysisState {
  id: string | null
  status:
    | "idle"
    | "starting"
    | "in_progress"
    | "completed"
    | "failed"
    | "retrying"
  progress: AnalysisProgress | null
  result: BrandAnalysisResponse | null
  error: string | null
  retryCount: number
  lastError: string | null
  connectionLost: boolean
}

export function useRealTimeAnalysis() {
  // Core state
  const analysisState = writable<AnalysisState>({
    id: null,
    status: "idle",
    progress: null,
    result: null,
    error: null,
    retryCount: 0,
    lastError: null,
    connectionLost: false,
  })

  // EventSource for SSE connection
  let eventSource: EventSource | null = null

  // Derived stores for convenience
  const isAnalyzing = derived(
    analysisState,
    ($state) =>
      $state.status === "starting" ||
      $state.status === "in_progress" ||
      $state.status === "retrying",
  )

  const progressPercentage = derived(
    analysisState,
    ($state) => $state.progress?.progress || 0,
  )

  const currentStep = derived(
    analysisState,
    ($state) => $state.progress?.step || "",
  )

  // Start a new analysis with real-time updates
  async function startAnalysis(
    analysisData: any,
    supabaseToken: string,
  ): Promise<void> {
    try {
      // Reset state
      analysisState.update((state) => ({
        ...state,
        status: "starting",
        progress: null,
        result: null,
        error: null,
      }))

      // Start the analysis (this will return immediately with an ID)
      const response = await fireGeoClient.startBrandAnalysis(
        analysisData,
        supabaseToken,
      )

      analysisState.update((state) => ({
        ...state,
        id: response.id,
        status: "in_progress",
      }))

      // Set up SSE connection for real-time updates
      setupSSEConnection(response.id, supabaseToken)
    } catch (error) {
      console.error("Failed to start analysis:", error)
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error:
          error instanceof Error ? error.message : "Failed to start analysis",
      }))
    }
  }

  // Set up Server-Sent Events connection
  function setupSSEConnection(analysisId: string, supabaseToken: string) {
    // Close existing connection if any
    if (eventSource) {
      eventSource.close()
    }

    try {
      // Create SSE connection to the analyze endpoint
      eventSource = fireGeoClient.createEventSource(
        `/api/brand-monitor/analyze?id=${analysisId}`,
        supabaseToken,
        handleSSEMessage,
        handleSSEError,
      )

      // Set up connection event handlers
      eventSource.onopen = () => {
        console.log("SSE connection opened for analysis:", analysisId)
      }
    } catch (error) {
      console.error("Failed to setup SSE connection:", error)
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error: "Failed to establish real-time connection",
      }))
    }
  }

  // Handle SSE messages
  function handleSSEMessage(data: any) {
    console.log("SSE message received:", data)

    if (data.type === "progress") {
      analysisState.update((state) => ({
        ...state,
        progress: data.progress,
      }))
    } else if (data.type === "completed") {
      analysisState.update((state) => ({
        ...state,
        status: "completed",
        result: data.result,
        progress: { ...state.progress!, completed: true, progress: 100 },
      }))
      closeConnection()
    } else if (data.type === "error") {
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error: data.error || "Analysis failed",
      }))
      closeConnection()
    }
  }

  // Handle SSE errors
  function handleSSEError(error: Event) {
    console.error("SSE connection error:", error)
    analysisState.update((state) => ({
      ...state,
      status: "failed",
      error: "Real-time connection lost",
    }))
    closeConnection()
  }

  // Close SSE connection
  function closeConnection() {
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
  }

  // Cancel ongoing analysis
  function cancelAnalysis() {
    closeConnection()
    analysisState.update((state) => ({
      ...state,
      status: "idle",
      id: null,
      progress: null,
      result: null,
      error: null,
    }))
  }

  // Retry failed analysis
  async function retryAnalysis(analysisData: any, supabaseToken: string) {
    const currentState = analysisState.get()

    if (currentState.retryCount >= 3) {
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error: "Maximum retry attempts reached. Please try again later.",
      }))
      return
    }

    analysisState.update((state) => ({
      ...state,
      status: "retrying",
      retryCount: state.retryCount + 1,
      lastError: state.error,
      error: null,
    }))

    // Wait before retrying (exponential backoff)
    const delay = Math.min(1000 * Math.pow(2, currentState.retryCount), 10000)
    await new Promise((resolve) => setTimeout(resolve, delay))

    await startAnalysis(analysisData, supabaseToken)
  }

  // Reset state
  function reset() {
    closeConnection()
    analysisState.set({
      id: null,
      status: "idle",
      progress: null,
      result: null,
      error: null,
      retryCount: 0,
      lastError: null,
      connectionLost: false,
    })
  }

  // Cleanup function
  function destroy() {
    closeConnection()
  }

  return {
    // Stores
    analysisState,
    isAnalyzing,
    progressPercentage,
    currentStep,

    // Actions
    startAnalysis,
    retryAnalysis,
    cancelAnalysis,
    reset,
    destroy,
  }
}
