import type { PageServerLoad } from "./$types"
import { redirect } from "@sveltejs/kit"

export const load: PageServerLoad = async ({
  locals: { supabase, safeGetSession },
  params,
}) => {
  const { session } = await safeGetSession()

  if (!session || session.user.is_anonymous) {
    throw redirect(303, "/login/sign_in")
  }

  // Validate envSlug parameter
  const { envSlug } = params
  if (!envSlug) {
    throw redirect(303, "/dashboard")
  }

  return {
    session,
    envSlug,
    user: session.user,
  }
}
