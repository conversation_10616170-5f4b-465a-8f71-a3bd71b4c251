<script lang="ts">
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import {
    fireGeoClient,
    fireGeoConnectionState,
    testFireGeoConnection,
  } from "$lib/firegeo-client"
  import BrandMonitorInterface from "$lib/components/brand-monitor/BrandMonitorInterface.svelte"
  import ConnectionStatus from "$lib/components/brand-monitor/ConnectionStatus.svelte"
  import LoadingState from "$lib/components/brand-monitor/LoadingState.svelte"
  import type { PageData } from "./$types"
  import type { BrandAnalysis } from "shared"

  export let data: PageData

  let analyses: BrandAnalysis[] = []
  let loading = true
  let error: string | null = null
  let connectionTested = false

  onMount(async () => {
    try {
      // Test FireGeo connection first
      const connected = await testFireGeoConnection()
      connectionTested = true

      if (!connected) {
        error = "Unable to connect to FireGeo service"
        loading = false
        return
      }

      const token = data.session?.access_token
      if (token) {
        // Authenticate with FireGeo API
        await fireGeoClient.authenticateUser(token)

        // Load existing analyses
        analyses = await fireGeoClient.getBrandAnalyses(token)
      }
    } catch (err) {
      console.error("Brand monitor initialization error:", err)
      error =
        err instanceof Error ? err.message : "Failed to load brand monitor"
    } finally {
      loading = false
    }
  })

  async function handleNewAnalysis(event: CustomEvent) {
    try {
      loading = true
      const token = data.session?.access_token
      if (token) {
        const result = await fireGeoClient.startBrandAnalysis(
          event.detail,
          token,
        )
        analyses = [result, ...analyses]
      }
    } catch (err) {
      console.error("Failed to start analysis:", err)
      error = err instanceof Error ? err.message : "Failed to start analysis"
    } finally {
      loading = false
    }
  }
</script>

<svelte:head>
  <title>Brand Monitor - FireGeo</title>
  <meta
    name="description"
    content="Track how AI models rank your brand against competitors in search results and recommendations."
  />
</svelte:head>

<div class="min-h-screen bg-background">
  <!-- Breadcrumb Navigation -->
  <nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
    <a
      href="/dashboard/{$page.params.envSlug}"
      class="hover:text-foreground transition-colors">Dashboard</a
    >
    <span>/</span>
    <span class="text-foreground font-medium">Brand Monitor</span>
  </nav>

  <!-- Hero Header -->
  <div class="mb-8">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </div>
      <div>
        <h1 class="text-4xl font-semibold text-foreground">
          FireGeo Brand Monitor
        </h1>
        <p class="text-lg text-muted-foreground mt-1">
          Track how AI models rank your brand against competitors in search
          results and recommendations.
        </p>
      </div>
    </div>

    <!-- Connection Status -->
    {#if connectionTested}
      <ConnectionStatus showDetails={false} />
    {/if}
  </div>

  <!-- Main Content -->
  {#if loading}
    <LoadingState
      title="Loading Brand Monitor"
      description="Connecting to FireGeo service and loading your analyses..."
      variant="detailed"
    />
  {:else if error}
    <div class="card-brutal p-6 bg-destructive/10 border-destructive">
      <div class="flex items-center gap-3">
        <svg
          class="w-5 h-5 text-destructive"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <div>
          <h3 class="font-medium text-destructive">Error</h3>
          <p class="text-destructive/80">{error}</p>
        </div>
      </div>
      <button
        class="mt-4 btn-secondary px-4 py-2 text-sm"
        onclick={() => window.location.reload()}
      >
        Retry
      </button>
    </div>
  {:else}
    <!-- Brand Monitor Interface -->
    <BrandMonitorInterface
      {analyses}
      {error}
      session={data.session}
      on:newAnalysis={handleNewAnalysis}
      on:retry={() => window.location.reload()}
      on:dismissError={() => (error = null)}
    />
  {/if}
</div>
