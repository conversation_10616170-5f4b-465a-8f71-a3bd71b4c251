{"framework": "svelte", "buildCommand": "pnpm run build", "outputDirectory": "build", "installCommand": "pnpm install", "devCommand": "pnpm run dev", "env": {"PUBLIC_SUPABASE_URL": "@supabase_url", "PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "PUBLIC_FIREGEO_API_URL": "@firegeo_api_url", "PUBLIC_APP_NAME": "<PERSON><PERSON>", "PUBLIC_APP_VERSION": "1.0.0", "PUBLIC_ENVIRONMENT": "production", "PUBLIC_ENABLE_FIREGEO": "true"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}]}