import * as client_hooks from '../../../src/hooks.client.ts';


export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28'),
	() => import('./nodes/29'),
	() => import('./nodes/30'),
	() => import('./nodes/31'),
	() => import('./nodes/32'),
	() => import('./nodes/33'),
	() => import('./nodes/34'),
	() => import('./nodes/35'),
	() => import('./nodes/36'),
	() => import('./nodes/37'),
	() => import('./nodes/38'),
	() => import('./nodes/39'),
	() => import('./nodes/40'),
	() => import('./nodes/41'),
	() => import('./nodes/42'),
	() => import('./nodes/43'),
	() => import('./nodes/44'),
	() => import('./nodes/45'),
	() => import('./nodes/46'),
	() => import('./nodes/47'),
	() => import('./nodes/48'),
	() => import('./nodes/49'),
	() => import('./nodes/50'),
	() => import('./nodes/51'),
	() => import('./nodes/52')
];

export const server_loads = [0,2,4,8];

export const dictionary = {
		"/": [~9],
		"/(marketing)/account": [~30,[5]],
		"/(marketing)/account/api": [~31,[5]],
		"/(marketing)/account/billing": [~32,[5]],
		"/(marketing)/account/billing/manage": [~33,[5]],
		"/(marketing)/account/select_plan": [~34,[5]],
		"/(marketing)/account/sign_out": [~35,[5]],
		"/(marketing)/account/subscribe/[slug]": [~36,[5]],
		"/(admin)/api": [10,[2]],
		"/(marketing)/blog": [37,[5,6]],
		"/(marketing)/blog/(posts)/awesome_post": [38,[5,6,7]],
		"/(marketing)/blog/(posts)/example_blog_post": [39,[5,6,7]],
		"/(marketing)/blog/(posts)/how_we_built_our_41kb_saas_website": [40,[5,6,7]],
		"/(marketing)/contact_us": [~41,[5]],
		"/(admin)/dashboard/[envSlug]/(menu)": [11,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/agent-seo": [21,[2,3]],
		"/(admin)/dashboard/[envSlug]/(menu)/billing": [~12,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/billing/manage": [~13,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/brand-monitor": [~22,[2,3]],
		"/(admin)/dashboard/[envSlug]/campaign-orchestrator": [23,[2,3]],
		"/(admin)/dashboard/[envSlug]/content-agent": [24,[2,3]],
		"/(admin)/dashboard/[envSlug]/create_profile": [~25,[2,3]],
		"/(admin)/dashboard/[envSlug]/researcher": [26,[2,3]],
		"/(admin)/dashboard/[envSlug]/select_plan": [27,[2,3]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings": [14,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings/change_email_subscription": [16,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings/change_email": [~15,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings/change_password": [~17,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings/delete_account": [~18,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings/edit_profile": [~19,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/(menu)/settings/reset_password": [~20,[2,3,4]],
		"/(admin)/dashboard/[envSlug]/subscribe/[slug]": [~28,[2,3]],
		"/(marketing)/find-env": [~42,[5]],
		"/(marketing)/login": [43,[5,8]],
		"/(marketing)/login/check_email": [~44,[5,8]],
		"/(marketing)/login/confirm": [~45,[5,8]],
		"/(marketing)/login/current_password_error": [46,[5,8]],
		"/(marketing)/login/forgot_password": [47,[5,8]],
		"/(marketing)/login/sign_in": [~48,[5,8]],
		"/(marketing)/login/sign_up": [~49,[5,8]],
		"/(marketing)/onboarding": [~50,[5]],
		"/(marketing)/pricing": [51,[5]],
		"/(marketing)/search": [52,[5]],
		"/(admin)/sign_out": [29,[2]]
	};

export const hooks = {
	handleError: client_hooks.handleError || (({ error }) => { console.error(error) }),
	init: client_hooks.init,
	reroute: (() => {}),
	transport: {}
};

export const decoders = Object.fromEntries(Object.entries(hooks.transport).map(([k, v]) => [k, v.decode]));

export const hash = false;

export const decode = (type, value) => decoders[type](value);

export { default as root } from '../root.js';