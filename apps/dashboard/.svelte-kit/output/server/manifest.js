export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["banner.png","favicon.png","images/example-home.png","images/rss.svg","images/startino_logo.svg","og-preview.jpg","robots.txt"]),
	mimeTypes: {".png":"image/png",".svg":"image/svg+xml",".jpg":"image/jpeg",".txt":"text/plain"},
	_: {
		client: {start:"_app/immutable/entry/start.CirxFZl5.js",app:"_app/immutable/entry/app.Cw27Ny1B.js",imports:["_app/immutable/entry/start.CirxFZl5.js","_app/immutable/chunks/Bm1TgOhB.js","_app/immutable/chunks/RnwjOPnl.js","_app/immutable/chunks/DDiqt3uM.js","_app/immutable/chunks/DWulv87v.js","_app/immutable/chunks/DtGADYZa.js","_app/immutable/chunks/rjRVMZXi.js","_app/immutable/entry/app.Cw27Ny1B.js","_app/immutable/chunks/BiqrECSP.js","_app/immutable/chunks/C4iS2aBk.js","_app/immutable/chunks/4KkXnDJG.js","_app/immutable/chunks/Bm1TgOhB.js","_app/immutable/chunks/RnwjOPnl.js","_app/immutable/chunks/DDiqt3uM.js","_app/immutable/chunks/DWulv87v.js","_app/immutable/chunks/DtGADYZa.js","_app/immutable/chunks/rjRVMZXi.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/2C89X9tI.js","_app/immutable/chunks/BCmD-YNt.js","_app/immutable/chunks/Dqu9JXqq.js","_app/immutable/chunks/C-ZVHnwW.js","_app/immutable/chunks/B82PTGnX.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:true},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js')),
			__memo(() => import('./nodes/8.js')),
			__memo(() => import('./nodes/9.js')),
			__memo(() => import('./nodes/10.js')),
			__memo(() => import('./nodes/11.js')),
			__memo(() => import('./nodes/12.js')),
			__memo(() => import('./nodes/13.js')),
			__memo(() => import('./nodes/14.js')),
			__memo(() => import('./nodes/15.js')),
			__memo(() => import('./nodes/16.js')),
			__memo(() => import('./nodes/17.js')),
			__memo(() => import('./nodes/18.js')),
			__memo(() => import('./nodes/19.js')),
			__memo(() => import('./nodes/20.js')),
			__memo(() => import('./nodes/21.js')),
			__memo(() => import('./nodes/22.js')),
			__memo(() => import('./nodes/23.js')),
			__memo(() => import('./nodes/24.js')),
			__memo(() => import('./nodes/25.js')),
			__memo(() => import('./nodes/26.js')),
			__memo(() => import('./nodes/27.js')),
			__memo(() => import('./nodes/28.js')),
			__memo(() => import('./nodes/29.js')),
			__memo(() => import('./nodes/30.js')),
			__memo(() => import('./nodes/31.js')),
			__memo(() => import('./nodes/32.js')),
			__memo(() => import('./nodes/33.js')),
			__memo(() => import('./nodes/34.js')),
			__memo(() => import('./nodes/35.js')),
			__memo(() => import('./nodes/36.js')),
			__memo(() => import('./nodes/41.js')),
			__memo(() => import('./nodes/42.js')),
			__memo(() => import('./nodes/43.js')),
			__memo(() => import('./nodes/44.js')),
			__memo(() => import('./nodes/45.js')),
			__memo(() => import('./nodes/46.js')),
			__memo(() => import('./nodes/47.js')),
			__memo(() => import('./nodes/48.js')),
			__memo(() => import('./nodes/49.js')),
			__memo(() => import('./nodes/50.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 7 },
				endpoint: null
			},
			{
				id: "/(marketing)/account",
				pattern: /^\/account\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 28 },
				endpoint: null
			},
			{
				id: "/(marketing)/account/api",
				pattern: /^\/account\/api\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 29 },
				endpoint: null
			},
			{
				id: "/(marketing)/account/billing",
				pattern: /^\/account\/billing\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 30 },
				endpoint: null
			},
			{
				id: "/(marketing)/account/billing/manage",
				pattern: /^\/account\/billing\/manage\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 31 },
				endpoint: null
			},
			{
				id: "/(marketing)/account/select_plan",
				pattern: /^\/account\/select_plan\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 32 },
				endpoint: null
			},
			{
				id: "/(marketing)/account/sign_out",
				pattern: /^\/account\/sign_out\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 33 },
				endpoint: null
			},
			{
				id: "/(marketing)/account/subscribe/[slug]",
				pattern: /^\/account\/subscribe\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,5,], errors: [1,,], leaf: 34 },
				endpoint: null
			},
			{
				id: "/(admin)/api",
				pattern: /^\/api\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 8 },
				endpoint: null
			},
			{
				id: "/api/contact",
				pattern: /^\/api\/contact\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/contact/_server.ts.js'))
			},
			{
				id: "/api/demo/researcher",
				pattern: /^\/api\/demo\/researcher\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/demo/researcher/_server.ts.js'))
			},
			{
				id: "/api/demo/seo",
				pattern: /^\/api\/demo\/seo\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/demo/seo/_server.ts.js'))
			},
			{
				id: "/(marketing)/auth/callback",
				pattern: /^\/auth\/callback\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/(marketing)/auth/callback/_server.js'))
			},
			{
				id: "/(marketing)/blog/rss.xml",
				pattern: /^\/blog\/rss\.xml\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/(marketing)/blog/rss.xml/_server.ts.js'))
			},
			{
				id: "/(marketing)/contact_us",
				pattern: /^\/contact_us\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 35 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)",
				pattern: /^\/dashboard\/([^/]+?)\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 9 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/agent-seo",
				pattern: /^\/dashboard\/([^/]+?)\/agent-seo\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 19 },
				endpoint: __memo(() => import('./entries/endpoints/(admin)/dashboard/_envSlug_/agent-seo/_server.ts.js'))
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/billing",
				pattern: /^\/dashboard\/([^/]+?)\/billing\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 10 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/billing/manage",
				pattern: /^\/dashboard\/([^/]+?)\/billing\/manage\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 11 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/brand-monitor",
				pattern: /^\/dashboard\/([^/]+?)\/brand-monitor\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 20 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/campaign-orchestrator",
				pattern: /^\/dashboard\/([^/]+?)\/campaign-orchestrator\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 21 },
				endpoint: __memo(() => import('./entries/endpoints/(admin)/dashboard/_envSlug_/campaign-orchestrator/_server.ts.js'))
			},
			{
				id: "/(admin)/dashboard/[envSlug]/content-agent",
				pattern: /^\/dashboard\/([^/]+?)\/content-agent\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 22 },
				endpoint: __memo(() => import('./entries/endpoints/(admin)/dashboard/_envSlug_/content-agent/_server.ts.js'))
			},
			{
				id: "/(admin)/dashboard/[envSlug]/content-agent/documents",
				pattern: /^\/dashboard\/([^/]+?)\/content-agent\/documents\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/(admin)/dashboard/_envSlug_/content-agent/documents/_server.ts.js'))
			},
			{
				id: "/(admin)/dashboard/[envSlug]/create_profile",
				pattern: /^\/dashboard\/([^/]+?)\/create_profile\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 23 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/researcher",
				pattern: /^\/dashboard\/([^/]+?)\/researcher\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 24 },
				endpoint: __memo(() => import('./entries/endpoints/(admin)/dashboard/_envSlug_/researcher/_server.ts.js'))
			},
			{
				id: "/(admin)/dashboard/[envSlug]/select_plan",
				pattern: /^\/dashboard\/([^/]+?)\/select_plan\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 25 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 12 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email_subscription",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/change_email_subscription\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 14 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/change_email\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 13 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings/change_password",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/change_password\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 15 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings/delete_account",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/delete_account\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 16 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings/edit_profile",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/edit_profile\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 17 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/(menu)/settings/reset_password",
				pattern: /^\/dashboard\/([^/]+?)\/settings\/reset_password\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,4,], errors: [1,,,,], leaf: 18 },
				endpoint: null
			},
			{
				id: "/(admin)/dashboard/[envSlug]/subscribe/[slug]",
				pattern: /^\/dashboard\/([^/]+?)\/subscribe\/([^/]+?)\/?$/,
				params: [{"name":"envSlug","optional":false,"rest":false,"chained":false},{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 26 },
				endpoint: null
			},
			{
				id: "/(marketing)/find-env",
				pattern: /^\/find-env\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 36 },
				endpoint: null
			},
			{
				id: "/(marketing)/login",
				pattern: /^\/login\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 37 },
				endpoint: null
			},
			{
				id: "/(marketing)/login/check_email",
				pattern: /^\/login\/check_email\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 38 },
				endpoint: null
			},
			{
				id: "/(marketing)/login/confirm",
				pattern: /^\/login\/confirm\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 39 },
				endpoint: null
			},
			{
				id: "/(marketing)/login/current_password_error",
				pattern: /^\/login\/current_password_error\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 40 },
				endpoint: null
			},
			{
				id: "/(marketing)/login/forgot_password",
				pattern: /^\/login\/forgot_password\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 41 },
				endpoint: null
			},
			{
				id: "/(marketing)/login/sign_in",
				pattern: /^\/login\/sign_in\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 42 },
				endpoint: null
			},
			{
				id: "/(marketing)/login/sign_up",
				pattern: /^\/login\/sign_up\/?$/,
				params: [],
				page: { layouts: [0,5,6,], errors: [1,,,], leaf: 43 },
				endpoint: null
			},
			{
				id: "/(marketing)/onboarding",
				pattern: /^\/onboarding\/?$/,
				params: [],
				page: { layouts: [0,5,], errors: [1,,], leaf: 44 },
				endpoint: null
			},
			{
				id: "/(marketing)/search/api.json",
				pattern: /^\/search\/api\.json\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/(marketing)/search/api.json/_server.ts.js'))
			},
			{
				id: "/(admin)/sign_out",
				pattern: /^\/sign_out\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 27 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
