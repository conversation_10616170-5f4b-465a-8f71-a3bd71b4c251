import { json } from "@sveltejs/kit";
import { createClient } from "@supabase/supabase-js";
import { P as PUBLIC_SUPABASE_URL, S as SUPABASE_SERVICE_ROLE_KEY } from "../../../../chunks/public.js";
import { h as handlePostSubmit } from "../../../../chunks/contact-notifier.js";
const supabaseUrl = PUBLIC_SUPABASE_URL;
const serviceRoleKey = SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, serviceRoleKey);
const POST = async ({ request }) => {
  try {
    const formData = await request.json();
    if (!formData.name || !formData.email || !formData.description) {
      return json(
        {
          success: false,
          error: "Missing required fields: name, email, and description are required"
        },
        { status: 400 }
      );
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      return json(
        {
          success: false,
          error: "Invalid email format"
        },
        { status: 400 }
      );
    }
    let companyName = "";
    if (formData.website && formData.website.trim()) {
      try {
        const url = new URL(formData.website.trim());
        companyName = url.hostname;
      } catch (error2) {
        companyName = formData.website.trim().substring(0, 100);
      }
    }
    const submissionData = {
      first_name: formData.name.trim().split(" ")[0] || formData.name.trim(),
      last_name: formData.name.trim().split(" ").slice(1).join(" ") || "",
      email: formData.email.toLowerCase().trim(),
      company_name: companyName,
      phone: "",
      // Optional field, not provided in modal
      message_body: formData.description.trim()
    };
    const { data, error } = await supabase.from("contact_requests").insert(submissionData).select("id, email, created_at").single();
    if (error) {
      console.error("Supabase error:", error);
      return json(
        {
          success: false,
          error: "Failed to save contact submission"
        },
        { status: 500 }
      );
    }
    await handlePostSubmit(submissionData);
    return json({
      success: true,
      message: "Contact submission saved successfully",
      data: {
        id: data.id,
        email: data.email,
        created_at: data.created_at
      }
    });
  } catch (error) {
    console.error("Contact form submission error:", error);
    return json(
      {
        success: false,
        error: "Internal server error"
      },
      { status: 500 }
    );
  }
};
export {
  POST
};
