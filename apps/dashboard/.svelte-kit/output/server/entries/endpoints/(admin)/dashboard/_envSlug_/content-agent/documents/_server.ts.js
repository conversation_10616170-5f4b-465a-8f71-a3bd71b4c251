import { json } from "@sveltejs/kit";
import { C as ContentService } from "../../../../../../../chunks/content-service.js";
const POST = async ({ request, locals, params }) => {
  const { session, supabase } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  try {
    const { title, content, contentType, metadata } = await request.json();
    if (!title || typeof title !== "string") {
      return json({ error: "Title is required" }, { status: 400 });
    }
    const { data: environment } = await supabase.from("environments").select("id").eq("slug", params.envSlug).single();
    if (!environment) {
      return json({ error: "Environment not found" }, { status: 404 });
    }
    const contentService = new ContentService(supabase);
    const document = await contentService.createDocument({
      title,
      content: content || {},
      content_type: contentType || "document",
      metadata: metadata || {},
      user_id: session.user.id,
      environment_id: environment.id
    });
    return json({ document }, { status: 201 });
  } catch (error) {
    console.error("Error creating document:", error);
    return json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
};
const PUT = async ({ request, locals, url }) => {
  const { session, supabase } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  const documentId = url.searchParams.get("id");
  if (!documentId) {
    return json({ error: "Document ID is required" }, { status: 400 });
  }
  try {
    const updates = await request.json();
    const { id, user_id, environment_id, created_at, ...allowedUpdates } = updates;
    const contentService = new ContentService(supabase);
    const document = await contentService.updateDocument(
      documentId,
      session.user.id,
      allowedUpdates
    );
    if (!document) {
      return json({ error: "Document not found or access denied" }, { status: 404 });
    }
    return json({ document });
  } catch (error) {
    console.error("Error updating document:", error);
    return json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
};
const DELETE = async ({ locals, url }) => {
  const { session, supabase } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  const documentId = url.searchParams.get("id");
  if (!documentId) {
    return json({ error: "Document ID is required" }, { status: 400 });
  }
  try {
    const contentService = new ContentService(supabase);
    const success = await contentService.deleteDocument(documentId, session.user.id);
    if (!success) {
      return json({ error: "Document not found or access denied" }, { status: 404 });
    }
    return json({ success: true });
  } catch (error) {
    console.error("Error deleting document:", error);
    return json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
};
export {
  DELETE,
  POST,
  PUT
};
