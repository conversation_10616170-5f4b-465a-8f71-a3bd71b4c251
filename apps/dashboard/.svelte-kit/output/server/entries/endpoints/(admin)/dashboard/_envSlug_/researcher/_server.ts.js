import { json } from "@sveltejs/kit";
import { a as companyResearcherAgent } from "../../../../../../chunks/orchestrator-agent.js";
import "../../../../../../chunks/seo-strategist.js";
const POST = async ({ request, locals, url }) => {
  const { session } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  const wantsStream = url.searchParams.get("stream") === "true";
  if (wantsStream) {
    return handleStreamingRequest(request);
  }
  try {
    const { message } = await request.json();
    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 });
    }
    console.log(
      "Researcher agent request received:",
      message.substring(0, 100) + "..."
    );
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(
          new Error("Agent timeout after 120 seconds - try a simpler query")
        ),
        12e4
      );
    });
    const agentPromise = companyResearcherAgent.generate([
      {
        role: "user",
        content: message
      }
    ]);
    const response = await Promise.race([agentPromise, timeoutPromise]);
    console.log(
      "Researcher agent response generated, length:",
      response.text?.length || 0
    );
    const citationMatches = response.text?.match(/\[\d+\]/g) || [];
    console.log("Citations found in raw response:", citationMatches);
    console.log(
      "First 500 chars of response:",
      response.text?.substring(0, 500)
    );
    return json({
      response: response.text
    });
  } catch (error) {
    console.error("Error in researcher agent:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};
async function handleStreamingRequest(request) {
  const { message } = await request.json();
  if (!message || typeof message !== "string") {
    return json({ error: "Message is required" }, { status: 400 });
  }
  console.log(
    "Researcher streaming request received:",
    message.substring(0, 100) + "..."
  );
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      const send = (data) => {
        const chunk = encoder.encode(`data: ${JSON.stringify(data)}

`);
        controller.enqueue(chunk);
      };
      performResearchWithProgress(message, send).then(() => {
        controller.close();
      }).catch((error) => {
        console.error("Streaming error:", error);
        send({ error: "Research failed", details: error.message });
        controller.close();
      });
    }
  });
  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive"
    }
  });
}
async function performResearchWithProgress(message, send) {
  try {
    send({
      step: 1,
      action: "Analyzing research request and extracting company information...",
      progress: 5,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 1500));
    send({
      step: 1,
      action: "Research scope identified",
      progress: 15,
      status: "completed"
    });
    send({
      step: 2,
      action: "Conducting comprehensive web search for company information...",
      progress: 20,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 3e3));
    send({
      step: 2,
      action: "Found relevant sources and company data",
      progress: 40,
      status: "completed"
    });
    send({
      step: 3,
      action: "Gathering financial performance and metrics data...",
      progress: 45,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 2500));
    send({
      step: 3,
      action: "Financial analysis completed",
      progress: 60,
      status: "completed"
    });
    send({
      step: 4,
      action: "Analyzing market position and competitive landscape...",
      progress: 65,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 2e3));
    send({
      step: 4,
      action: "Market analysis and competitive research completed",
      progress: 80,
      status: "completed"
    });
    send({
      step: 5,
      action: "Generating comprehensive research report...",
      progress: 85,
      status: "active"
    });
    const response = await companyResearcherAgent.generate([
      {
        role: "user",
        content: message
      }
    ]);
    send({
      step: 5,
      action: "Research report generated successfully!",
      progress: 100,
      status: "completed"
    });
    const citationMatches = response.text?.match(/\[\d+\]/g) || [];
    console.log("Citations found in streaming response:", citationMatches);
    console.log(
      "First 500 chars of streaming response:",
      response.text?.substring(0, 500)
    );
    send({
      type: "final_response",
      response: response.text
    });
  } catch (error) {
    console.error("Research process failed:", error);
    throw error;
  }
}
export {
  POST
};
