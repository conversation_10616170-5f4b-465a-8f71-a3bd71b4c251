import { json } from "@sveltejs/kit";
import { s as seoStrategistAgent } from "../../../../../../chunks/seo-strategist.js";
const POST = async ({ request, locals, url }) => {
  const { session } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  return handleStreamingRequest(request);
};
async function handleStreamingRequest(request) {
  const { message } = await request.json();
  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      const send = (data) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}

`));
      };
      try {
        const isGapAnalysis = message.toLowerCase().includes("analyze keyword gaps") || message.toLowerCase().includes("gap analysis") || message.toLowerCase().includes("competitors:");
        if (isGapAnalysis) {
          send({
            step: 1,
            action: "Validating domain formats and accessibility...",
            progress: 5,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 1500));
          send({
            step: 1,
            action: "Domains validated successfully",
            progress: 15,
            status: "completed"
          });
          send({
            step: 2,
            action: "Getting keywords for your domain...",
            progress: 20,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 2500));
          send({
            step: 2,
            action: "Found 150+ keywords for your site",
            progress: 35,
            status: "completed"
          });
          send({
            step: 3,
            action: "Analyzing competitor domains...",
            progress: 40,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 3e3));
          send({
            step: 3,
            action: "Competitor analysis complete",
            progress: 55,
            status: "completed"
          });
          send({
            step: 4,
            action: "Comparing keyword rankings across domains...",
            progress: 60,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          send({
            step: 4,
            action: "Found keyword overlaps and differences",
            progress: 75,
            status: "completed"
          });
          send({
            step: 5,
            action: "Identifying keyword opportunities and gaps...",
            progress: 80,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          send({
            step: 5,
            action: "Calculated opportunity scores",
            progress: 90,
            status: "completed"
          });
          send({
            step: 6,
            action: "Formatting your gap analysis report...",
            progress: 95,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 1e3));
          send({
            step: 6,
            action: "Report generation complete",
            progress: 100,
            status: "completed"
          });
        } else {
          send({
            step: 1,
            action: "Researching your industry and market trends...",
            progress: 10,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          send({
            step: 1,
            action: "Industry research completed",
            progress: 20,
            status: "completed"
          });
          send({
            step: 2,
            action: "Discovering relevant keywords for your business...",
            progress: 30,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 3e3));
          send({
            step: 2,
            action: "Found 30+ relevant keywords",
            progress: 40,
            status: "completed"
          });
          send({
            step: 3,
            action: "Analyzing search volumes and trends...",
            progress: 50,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          send({
            step: 3,
            action: "Search volume analysis complete",
            progress: 60,
            status: "completed"
          });
          send({
            step: 4,
            action: "Checking keyword difficulty and competition...",
            progress: 70,
            status: "active"
          });
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          send({
            step: 4,
            action: "Competition analysis complete",
            progress: 80,
            status: "completed"
          });
          send({
            step: 5,
            action: "Generating your SEO strategy report...",
            progress: 90,
            status: "active"
          });
        }
        console.log("Starting AI agent generation...");
        let response;
        try {
          const timeoutDuration = isGapAnalysis ? 9e4 : 12e4;
          response = await Promise.race([
            seoStrategistAgent.generate([
              {
                role: "user",
                content: message
              }
            ]),
            new Promise(
              (_, reject) => setTimeout(
                () => reject(
                  new Error(
                    `Agent generation timeout after ${timeoutDuration / 1e3} seconds`
                  )
                ),
                timeoutDuration
              )
            )
          ]);
          console.log("Agent generation completed");
        } catch (error) {
          console.error("Agent generation failed:", error);
          throw error;
        }
        if (!isGapAnalysis) {
          send({
            step: 5,
            action: "Report generated successfully!",
            progress: 100,
            status: "completed"
          });
        }
        send({
          type: "final",
          response: response.text
        });
        controller.close();
      } catch (error) {
        send({
          type: "error",
          error: error instanceof Error ? error.message : "Unknown error"
        });
        controller.close();
      }
    }
  });
  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive"
    }
  });
}
export {
  POST
};
