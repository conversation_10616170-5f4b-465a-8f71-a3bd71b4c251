import { n as noop, m as sanitize_props, o as spread_props, b as slot, a as push, A as fallback, c as attr, d as attr_class, e as escape_html, B as bind_props, p as pop, f as stringify, t as ensure_array_like, s as store_get, w as copy_payload, x as assign_payload, u as unsubscribe_stores, h as head } from "../../chunks/index2.js";
import { w as writable, d as derived, a as readable } from "../../chunks/index4.js";
import "clsx";
import { o as onDestroy } from "../../chunks/index-server.js";
import "../../chunks/schemas.js";
import { X } from "../../chunks/x.js";
import { B as Bot } from "../../chunks/bot.js";
import { S as Search } from "../../chunks/search.js";
import { C as Chevron_right } from "../../chunks/chevron-right.js";
import { T as Target, S as Sparkles, a as Trending_up } from "../../chunks/trending-up.js";
import { M as Menu } from "../../chunks/menu.js";
import { U as Users } from "../../chunks/users.js";
import { I as Icon } from "../../chunks/Icon.js";
import { Z as Zap } from "../../chunks/zap.js";
import { h as html } from "../../chunks/html.js";
const now = () => Date.now();
const raf = {
  // don't access requestAnimationFrame eagerly outside method
  // this allows basic testing of user code without JSDOM
  // bunder will eval and remove ternary when the user's app is built
  tick: (
    /** @param {any} _ */
    (_) => noop()
  ),
  now: () => now(),
  tasks: /* @__PURE__ */ new Set()
};
function loop(callback) {
  let task;
  if (raf.tasks.size === 0) ;
  return {
    promise: new Promise((fulfill) => {
      raf.tasks.add(task = { c: callback, f: fulfill });
    }),
    abort() {
      raf.tasks.delete(task);
    }
  };
}
function Brain($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      {
        "d": "M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"
      }
    ],
    [
      "path",
      {
        "d": "M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"
      }
    ],
    [
      "path",
      { "d": "M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4" }
    ],
    ["path", { "d": "M17.599 6.5a3 3 0 0 0 .399-1.375" }],
    ["path", { "d": "M6.003 5.125A3 3 0 0 0 6.401 6.5" }],
    ["path", { "d": "M3.477 10.896a4 4 0 0 1 .585-.396" }],
    ["path", { "d": "M19.938 10.5a4 4 0 0 1 .585.396" }],
    ["path", { "d": "M6 18a4 4 0 0 1-1.967-.516" }],
    ["path", { "d": "M19.967 17.484A4 4 0 0 1 18 18" }]
  ];
  Icon($$payload, spread_props([
    { name: "brain" },
    $$sanitized_props,
    {
      /**
       * @component @name Brain
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNWEzIDMgMCAxIDAtNS45OTcuMTI1IDQgNCAwIDAgMC0yLjUyNiA1Ljc3IDQgNCAwIDAgMCAuNTU2IDYuNTg4QTQgNCAwIDEgMCAxMiAxOFoiIC8+CiAgPHBhdGggZD0iTTEyIDVhMyAzIDAgMSAxIDUuOTk3LjEyNSA0IDQgMCAwIDEgMi41MjYgNS43NyA0IDQgMCAwIDEtLjU1NiA2LjU4OEE0IDQgMCAxIDEgMTIgMThaIiAvPgogIDxwYXRoIGQ9Ik0xNSAxM2E0LjUgNC41IDAgMCAxLTMtNCA0LjUgNC41IDAgMCAxLTMgNCIgLz4KICA8cGF0aCBkPSJNMTcuNTk5IDYuNWEzIDMgMCAwIDAgLjM5OS0xLjM3NSIgLz4KICA8cGF0aCBkPSJNNi4wMDMgNS4xMjVBMyAzIDAgMCAwIDYuNDAxIDYuNSIgLz4KICA8cGF0aCBkPSJNMy40NzcgMTAuODk2YTQgNCAwIDAgMSAuNTg1LS4zOTYiIC8+CiAgPHBhdGggZD0iTTE5LjkzOCAxMC41YTQgNCAwIDAgMSAuNTg1LjM5NiIgLz4KICA8cGF0aCBkPSJNNiAxOGE0IDQgMCAwIDEtMS45NjctLjUxNiIgLz4KICA8cGF0aCBkPSJNMTkuOTY3IDE3LjQ4NEE0IDQgMCAwIDEgMTggMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/brain
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Chart_column($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "M3 3v16a2 2 0 0 0 2 2h16" }],
    ["path", { "d": "M18 17V9" }],
    ["path", { "d": "M13 17V5" }],
    ["path", { "d": "M8 17v-3" }]
  ];
  Icon($$payload, spread_props([
    { name: "chart-column" },
    $$sanitized_props,
    {
      /**
       * @component @name ChartColumn
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTE4IDE3VjkiIC8+CiAgPHBhdGggZD0iTTEzIDE3VjUiIC8+CiAgPHBhdGggZD0iTTggMTd2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Circle_check_big($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "M21.801 10A10 10 0 1 1 17 3.335" }],
    ["path", { "d": "m9 11 3 3L22 4" }]
  ];
  Icon($$payload, spread_props([
    { name: "circle-check-big" },
    $$sanitized_props,
    {
      /**
       * @component @name CircleCheckBig
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Focus($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "3" }],
    ["path", { "d": "M3 7V5a2 2 0 0 1 2-2h2" }],
    ["path", { "d": "M17 3h2a2 2 0 0 1 2 2v2" }],
    ["path", { "d": "M21 17v2a2 2 0 0 1-2 2h-2" }],
    ["path", { "d": "M7 21H5a2 2 0 0 1-2-2v-2" }]
  ];
  Icon($$payload, spread_props([
    { name: "focus" },
    $$sanitized_props,
    {
      /**
       * @component @name Focus
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0zIDdWNWEyIDIgMCAwIDEgMi0yaDIiIC8+CiAgPHBhdGggZD0iTTE3IDNoMmEyIDIgMCAwIDEgMiAydjIiIC8+CiAgPHBhdGggZD0iTTIxIDE3djJhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNNyAyMUg1YTIgMiAwIDAgMS0yLTJ2LTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/focus
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function is_date(obj) {
  return Object.prototype.toString.call(obj) === "[object Date]";
}
function tick_spring(ctx, last_value, current_value, target_value) {
  if (typeof current_value === "number" || is_date(current_value)) {
    const delta = target_value - current_value;
    const velocity = (current_value - last_value) / (ctx.dt || 1 / 60);
    const spring2 = ctx.opts.stiffness * delta;
    const damper = ctx.opts.damping * velocity;
    const acceleration = (spring2 - damper) * ctx.inv_mass;
    const d = (velocity + acceleration) * ctx.dt;
    if (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {
      return target_value;
    } else {
      ctx.settled = false;
      return is_date(current_value) ? new Date(current_value.getTime() + d) : current_value + d;
    }
  } else if (Array.isArray(current_value)) {
    return current_value.map(
      (_, i) => (
        // @ts-ignore
        tick_spring(ctx, last_value[i], current_value[i], target_value[i])
      )
    );
  } else if (typeof current_value === "object") {
    const next_value = {};
    for (const k in current_value) {
      next_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);
    }
    return next_value;
  } else {
    throw new Error(`Cannot spring ${typeof current_value} values`);
  }
}
function spring(value, opts = {}) {
  const store = writable(value);
  const { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;
  let last_time;
  let task;
  let current_token;
  let last_value = (
    /** @type {T} */
    value
  );
  let target_value = (
    /** @type {T | undefined} */
    value
  );
  let inv_mass = 1;
  let inv_mass_recovery_rate = 0;
  let cancel_task = false;
  function set(new_value, opts2 = {}) {
    target_value = new_value;
    const token = current_token = {};
    if (value == null || opts2.hard || spring2.stiffness >= 1 && spring2.damping >= 1) {
      cancel_task = true;
      last_time = raf.now();
      last_value = new_value;
      store.set(value = target_value);
      return Promise.resolve();
    } else if (opts2.soft) {
      const rate = opts2.soft === true ? 0.5 : +opts2.soft;
      inv_mass_recovery_rate = 1 / (rate * 60);
      inv_mass = 0;
    }
    if (!task) {
      last_time = raf.now();
      cancel_task = false;
      task = loop((now2) => {
        if (cancel_task) {
          cancel_task = false;
          task = null;
          return false;
        }
        inv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);
        const elapsed = Math.min(now2 - last_time, 1e3 / 30);
        const ctx = {
          inv_mass,
          opts: spring2,
          settled: true,
          dt: elapsed * 60 / 1e3
        };
        const next_value = tick_spring(ctx, last_value, value, target_value);
        last_time = now2;
        last_value = /** @type {T} */
        value;
        store.set(value = /** @type {T} */
        next_value);
        if (ctx.settled) {
          task = null;
        }
        return !ctx.settled;
      });
    }
    return new Promise((fulfil) => {
      task.promise.then(() => {
        if (token === current_token) fulfil();
      });
    });
  }
  const spring2 = {
    set,
    update: (fn, opts2) => set(fn(
      /** @type {T} */
      target_value,
      /** @type {T} */
      value
    ), opts2),
    subscribe: store.subscribe,
    stiffness,
    damping,
    precision
  };
  return spring2;
}
const scrollState = readable(
  { y: 0, direction: "up", prevY: 0 },
  (set) => {
    return;
  }
);
const scrollY = derived(scrollState, ($scrollState) => $scrollState.y);
derived(
  scrollState,
  ($scrollState) => $scrollState.direction
);
derived(
  scrollState,
  ($scrollState) => $scrollState.y > 50
);
derived(
  scrollState,
  ($scrollState) => $scrollState.direction === "down" && $scrollState.y > 100
);
function ContactModal($$payload, $$props) {
  push();
  let isOpen = fallback($$props["isOpen"], false);
  let formData = { name: "", email: "", website: "", description: "" };
  let isSubmitting = false;
  let submitMessage = "";
  let submitError = "";
  let fieldErrors = {};
  function closeModal() {
    isOpen = false;
    submitMessage = "";
    submitError = "";
    fieldErrors = {};
    if (typeof document !== "undefined") {
      document.body.classList.remove("modal-open");
    }
  }
  function handleGlobalKeydown(event) {
    if (isOpen && event.key === "Escape") {
      closeModal();
    }
  }
  onDestroy(() => {
    if (typeof document !== "undefined") {
      document.removeEventListener("keydown", handleGlobalKeydown);
      document.body.classList.remove("modal-open");
    }
  });
  if (!isOpen && typeof document !== "undefined") {
    try {
      document.body.classList.remove("modal-open");
    } catch (error) {
      console.debug("Error during modal closing:", error);
    }
  }
  if (isOpen) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background: rgba(0, 0, 0, 0.8);" role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1"><div class="relative w-full max-w-lg border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-xl);" role="document"><div class="flex items-center justify-between p-6 border-b-2" style="border-color: var(--border);"><h2 id="modal-title" class="text-2xl font-black" style="color: var(--foreground);">Let's Start Your Growth Story</h2> <button class="p-2 transition-colors hover:opacity-70" style="color: var(--muted-foreground);" aria-label="Close modal">`);
    X($$payload, { class: "w-6 h-6" });
    $$payload.out.push(`<!----></button></div> <form class="p-6 space-y-6"><div><label for="name" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Full Name *</label> <input id="name" type="text"${attr("value", formData.name)} required${attr_class(`input-brutal w-full ${stringify(fieldErrors.name ? "border-red-500" : "")}`)} placeholder="Your full name"/> `);
    if (fieldErrors.name) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<p class="text-red-500 text-sm mt-1">${escape_html(fieldErrors.name)}</p>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div><label for="email" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Email Address *</label> <input id="email" type="email"${attr("value", formData.email)} required${attr_class(`input-brutal w-full ${stringify(fieldErrors.email ? "border-red-500" : "")}`)} placeholder="<EMAIL>"/> `);
    if (fieldErrors.email) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<p class="text-red-500 text-sm mt-1">${escape_html(fieldErrors.email)}</p>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div><label for="website" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Company Website</label> <input id="website" type="url"${attr("value", formData.website)}${attr_class(`input-brutal w-full ${stringify(fieldErrors.website ? "border-red-500" : "")}`)} placeholder="https://yourcompany.com"/> `);
    if (fieldErrors.website) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<p class="text-red-500 text-sm mt-1">${escape_html(fieldErrors.website)}</p>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div><label for="description" class="block text-sm font-bold mb-2" style="color: var(--foreground);">What would you like to learn or discuss? *</label> <textarea id="description" required rows="4"${attr_class(`textarea-brutal w-full ${stringify(fieldErrors.description ? "border-red-500" : "")}`)} placeholder="Tell us about your marketing challenges, goals, or what you'd like to explore with our team...">`);
    const $$body = escape_html(formData.description);
    if ($$body) {
      $$payload.out.push(`${$$body}`);
    }
    $$payload.out.push(`</textarea> `);
    if (fieldErrors.description) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<p class="text-red-500 text-sm mt-1">${escape_html(fieldErrors.description)}</p>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> `);
    if (submitMessage) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="modal-success-message">${escape_html(submitMessage)}</div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> `);
    if (submitError) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="modal-error-message">${escape_html(submitError)}</div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> <div class="flex gap-4 pt-4"><button type="submit"${attr("disabled", isSubmitting, true)}${attr_class(`btn-primary flex-1 px-6 py-3 font-bold ${stringify("")}`)}>${escape_html("Send Message")}</button> <button type="button" class="btn-secondary px-6 py-3 font-bold">Cancel</button></div></form></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { isOpen });
  pop();
}
function AgentDemo($$payload, $$props) {
  push();
  let input = "";
  let isLoading = false;
  const demoPrompts = [
    "Research Stripe's recent marketing strategy",
    "Analyze Notion's competitive positioning",
    "Tell me about Figma's go-to-market approach"
  ];
  $$payload.out.push(`<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center mb-6"><div class="flex items-center justify-center gap-2 mb-4"><div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded">`);
  Bot($$payload, { class: "w-4 h-4 text-primary-foreground" });
  $$payload.out.push(`<!----></div> <h3 class="text-lg font-bold">Try Athena Live</h3></div> <p class="text-sm text-muted-foreground mb-4">Get instant competitive intelligence on any company</p></div> `);
  {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(demoPrompts);
    $$payload.out.push(`<div class="grid gap-3 mb-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let prompt = each_array[$$index];
      $$payload.out.push(`<button class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"><div class="flex items-center gap-2 mb-1">`);
      Search($$payload, { class: "w-3 h-3 text-primary" });
      $$payload.out.push(`<!----> <span class="text-xs font-medium text-foreground">${escape_html(prompt)}</span></div> <div class="flex items-center justify-between"><span class="text-xs text-muted-foreground">Click to try →</span> `);
      Chevron_right($$payload, {
        class: "w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors"
      });
      $$payload.out.push(`<!----></div></button>`);
    }
    $$payload.out.push(`<!--]--></div> <div class="flex gap-2"><input${attr("value", input)} placeholder="Or ask about any company..." class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"${attr("disabled", isLoading, true)}/> <button${attr("disabled", !input.trim() || isLoading, true)} class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50">`);
    {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`Try`);
    }
    $$payload.out.push(`<!--]--></button></div>`);
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function SEOAgentDemo($$payload, $$props) {
  push();
  let input = "";
  let isLoading = false;
  const demoPrompts = [
    "Find long-tail keywords for organic skincare products",
    "Analyze local SEO keywords for coffee shops in Seattle",
    "Research B2B keywords for project management software"
  ];
  $$payload.out.push(`<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center mb-6"><div class="flex items-center justify-center gap-2 mb-4"><div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded">`);
  Target($$payload, { class: "w-4 h-4 text-primary-foreground" });
  $$payload.out.push(`<!----></div> <h3 class="text-lg font-bold">Try Lexi Live</h3></div> <p class="text-sm text-muted-foreground mb-4">Get instant SEO keyword analysis for any topic</p></div> `);
  {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(demoPrompts);
    $$payload.out.push(`<div class="grid gap-3 mb-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let prompt = each_array[$$index];
      $$payload.out.push(`<button class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"><div class="flex items-center gap-2 mb-1">`);
      Search($$payload, { class: "w-3 h-3 text-primary" });
      $$payload.out.push(`<!----> <span class="text-xs font-medium text-foreground">${escape_html(prompt)}</span></div> <div class="flex items-center justify-between"><span class="text-xs text-muted-foreground">Click to analyze →</span> `);
      Chevron_right($$payload, {
        class: "w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors"
      });
      $$payload.out.push(`<!----></div></button>`);
    }
    $$payload.out.push(`<!--]--></div> <div class="flex gap-2"><input${attr("value", input)} placeholder="e.g., vegan protein powder, sustainable fashion..." class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"${attr("disabled", isLoading, true)}/> <button${attr("disabled", !input.trim() || isLoading, true)} class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50">`);
    {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`Analyze`);
    }
    $$payload.out.push(`<!--]--></button></div>`);
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let data = $$props["data"];
  const { homeContent, meta } = data;
  const heroOpacity = spring(1);
  const heroScale = spring(1);
  let showContactModal = false;
  let showMobileMenu = false;
  function getAgentTagClass(index) {
    const classes = ["linear-tag-green", "linear-tag-blue", "linear-tag-purple"];
    return classes[index % classes.length];
  }
  function getAgentTagLabel(agentName) {
    if (agentName.toLowerCase().includes("content") || agentName.toLowerCase().includes("seo")) {
      return "Content Agent";
    }
    if (agentName.toLowerCase().includes("competitive") || agentName.toLowerCase().includes("research")) {
      return "Research Agent";
    }
    if (agentName.toLowerCase().includes("social") || agentName.toLowerCase().includes("campaign") || agentName.toLowerCase().includes("orchestrator")) {
      return "Social Agent";
    }
    return "Agent";
  }
  {
    heroOpacity.set(1 - Math.min(store_get($$store_subs ??= {}, "$scrollY", scrollY), 300) / 300);
    heroScale.set(1 - Math.min(store_get($$store_subs ??= {}, "$scrollY", scrollY), 300) / 300 * 0.2);
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>Your unfair advantage - Agentic marketing team</title>`;
      $$payload3.out.push(`<meta name="description" content="Fractional CMO team with Agents working for you 24/7"/> <meta property="og:title" content="Your unfair advantage - Agentic marketing team"/> <meta property="og:description" content="Fractional CMO team with Agents working for you 24/7"/> <meta property="og:image" content="/og-preview.jpg"/> <meta property="og:image:width" content="1200"/> <meta property="og:image:height" content="630"/> <meta property="og:image:alt" content="Robynn AI - Your unfair advantage - Agentic marketing team"/> <meta property="og:type" content="website"/> <meta property="og:url" content="https://robynn.ai"/> <meta property="og:site_name" content="Robynn AI"/> <meta name="twitter:card" content="summary_large_image"/> <meta name="twitter:title" content="Your unfair advantage - Agentic marketing team"/> <meta name="twitter:description" content="Fractional CMO team with Agents working for you 24/7"/> <meta name="twitter:image" content="/og-preview.jpg"/> <link rel="preconnect" href="https://fonts.googleapis.com"/> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/> <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=JetBrains+Mono:wght@400;500;600&amp;display=swap" rel="stylesheet"/>`);
    });
    $$payload2.out.push(`<div class="min-h-screen bg-background text-foreground"><nav class="linear-nav fixed top-0 left-0 right-0 z-50 px-6 py-4 transition-all duration-300 ease-in-out"><div class="max-w-7xl mx-auto flex items-center justify-between"><div class="flex items-center space-x-8"><button class="linear-heading text-xl font-bold text-foreground hover:text-primary transition-colors cursor-pointer" aria-label="Scroll to top">Robynn.ai</button> <div class="hidden md:flex items-center space-x-6"><button class="nav-link text-foreground font-medium transition-all">Approach</button> <button class="nav-link text-foreground font-medium transition-all">Services</button> <button class="nav-link text-foreground font-medium transition-all">Agents</button> <button class="nav-link text-foreground font-medium transition-all">Stories</button></div></div> <button class="hidden md:block linear-btn-primary px-6 py-2 rounded-lg">↗ Get Started</button> <button class="md:hidden p-2 text-foreground hover:text-primary transition-colors" aria-label="Toggle mobile menu">`);
    {
      $$payload2.out.push("<!--[!-->");
      Menu($$payload2, { class: "h-6 w-6" });
    }
    $$payload2.out.push(`<!--]--></button></div></nav> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> <div${attr_class("fixed top-0 right-0 h-full w-80 bg-background border-l border-border z-50 transform transition-transform duration-300 ease-in-out md:hidden", void 0, {
      "translate-x-0": showMobileMenu,
      "translate-x-full": !showMobileMenu
    })}><div class="p-6"><div class="flex items-center justify-between mb-8"><div class="linear-heading text-xl font-bold text-foreground">Robynn.ai</div> <button class="p-2 text-foreground hover:text-primary transition-colors" aria-label="Close menu">`);
    X($$payload2, { class: "h-6 w-6" });
    $$payload2.out.push(`<!----></button></div> <nav class="space-y-2 touch-spacing"><button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Approach</button> <button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Services</button> <button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Agents</button> <button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Stories</button> <div class="pt-4"><button class="w-full linear-btn-primary px-6 py-3 rounded-lg text-center">↗ Get Started</button></div></nav></div></div> <section class="linear-hero linear-grid min-h-screen flex items-center justify-center px-6 py-20" style="padding-top: calc(5rem + 40px);"><div class="max-w-7xl mx-auto text-center hero-content"><div class="mb-8" data-animated="">`);
    if (homeContent?.hero) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<h1 class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight">${escape_html(homeContent.hero.frontmatter.title)}<span class="text-primary">${escape_html(homeContent.hero.frontmatter.titleHighlight)}</span></h1> <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto">${html(homeContent.hero.content)}</p>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<h1 class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight">The <span class="text-primary">10X fractional CMO</span><br/> team you've been <span class="text-primary">looking for</span></h1> <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto">Strategic 10x marketing team that scales with your ambitions. We
            combine seasoned CMO expertise with cutting-edge AI to transform
            your go-to-market strategy build your GTM machine.</p>`);
    }
    $$payload2.out.push(`<!--]--></div> <div class="mb-12" data-animated=""><div class="flex flex-wrap justify-center gap-3 mb-8">`);
    if (homeContent?.hero?.frontmatter?.badge) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<span class="linear-tag linear-tag-green">${escape_html(homeContent.hero.frontmatter.badge)}</span>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<span class="linear-tag linear-tag-green">Strategic Partnership</span> <span class="linear-tag linear-tag-purple">AI-Powered</span>`);
    }
    $$payload2.out.push(`<!--]--></div> <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8 touch-spacing"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center">${escape_html(homeContent?.hero?.frontmatter?.ctaPrimary || "Talk to Us")} `);
    Chevron_right($$payload2, { class: "ml-2 h-4 w-4" });
    $$payload2.out.push(`<!----></button> <a href="#agents" class="linear-btn-secondary px-8 py-3 text-lg rounded-lg inline-flex items-center justify-center">See Agents In Action</a></div> <p class="linear-mono text-sm text-muted-foreground">${escape_html(homeContent?.hero?.frontmatter?.trustBadge || "Trusted by 50+ scaling startups")}</p></div> <div data-animated=""><div class="max-w-4xl mx-auto"><h3 class="linear-heading text-2xl text-center mb-8 flex items-center justify-center gap-3">`);
    Sparkles($$payload2, { class: "h-6 w-6 text-primary" });
    $$payload2.out.push(`<!----> Everything you need. Nothing you don't.</h3> <div class="grid md:grid-cols-3 gap-6 mb-6"><div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded text-sm font-medium mb-4">Fractional CMO</div> <p class="linear-body text-muted-foreground">Strategic clarity without full-time overhead</p></div></div> <div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-blue-500/10 text-blue-600 rounded text-sm font-medium mb-4">Expert Marketers</div> <p class="linear-body text-muted-foreground">Campaigns crafted by hands-on pros</p></div></div> <div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-purple-500/10 text-purple-600 rounded text-sm font-medium mb-4">AI Agents</div> <p class="linear-body text-muted-foreground">Automations that move fast—and scale faster</p></div></div></div> <div class="text-center"><p class="linear-mono text-sm text-muted-foreground/80">Custom-assembled for your company. No bloat. No fluff.</p></div></div></div></div></section> <div class="linear-section-separator"></div> <section class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">You've built something <span class="text-primary">remarkable</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">You're past the early stage hustle. Your product works. Your customers
          love it. You've hit that magical 5M+ revenue milestone. But now you're
          facing a new challenge.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg">`);
    Users($$payload2, { class: "h-8 w-8 text-primary mb-4" });
    $$payload2.out.push(`<!----> <h3 class="linear-heading text-xl mb-4">Expensive Team Building</h3> <p class="linear-body text-muted-foreground">VP of Marketing + team costs $500K+ annually</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg">`);
    Focus($$payload2, { class: "h-8 w-8 text-blue-400 mb-4" });
    $$payload2.out.push(`<!----> <h3 class="linear-heading text-xl mb-4">Scattered Marketing</h3> <p class="linear-body text-muted-foreground">Tactics without strategy, campaigns without cohesion</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg">`);
    Chart_column($$payload2, { class: "h-8 w-8 text-orange-400 mb-4" });
    $$payload2.out.push(`<!----> <h3 class="linear-heading text-xl mb-4">Unclear ROI</h3> <p class="linear-body text-muted-foreground">Spending money without knowing what's working</p></div></div></div></section> <div class="linear-dotted-line"></div> <section id="approach" class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Think different about marketing <br/> <span class="text-primary">in the world of AI</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">While others offer fractional hours, we offer strategic embedded team
          members and custom agents for your team. Our CMO-led team doesn't just
          provide strategy, we build your entire GTM machine from the tech stack
          to a GTM campaign system built for your needs.</p></div> <div class="grid md:grid-cols-3 gap-8 mb-16"><div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">01</div> <span class="linear-tag linear-tag-green mb-4 inline-block">Embedded Team</span> <h3 class="linear-heading text-xl mb-4">Your marketing co-founder and your agents when and where you need
            them</h3> <p class="linear-body text-muted-foreground">We think like an owner, act like a partner, and deliver like the CMO
            you've been looking for.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">02</div> <span class="linear-tag linear-tag-purple mb-4 inline-block">AI-Native Approach</span> <h3 class="linear-heading text-xl mb-4">Built for efficiency, powered by intelligence. Automation for
            scaling up.</h3> <p class="linear-body text-muted-foreground">Our methodology leverages AI at every step, making us faster and
            more efficient than traditional approaches.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">03</div> <span class="linear-tag linear-tag-green mb-4 inline-block">Continuous Learning</span> <h3 class="linear-heading text-xl mb-4">End-to-end campaigns tried and tested to generate pipelines and
            revenue</h3> <p class="linear-body text-muted-foreground">Deep product understanding drives differentiated messaging that
            actually resonates with your market.</p></div></div> <div class="linear-card linear-fade-in p-8 rounded-lg"><h3 class="linear-heading text-2xl text-center mb-8">Strategic Leadership, Not Just Services</h3> <p class="linear-body text-center text-muted-foreground mb-8">You get the strategic thinking of a seasoned CMO, the execution power
          of a full marketing team, and the efficiency of AI-native tools. All
          without the politics, overhead, or six-figure salaries.</p> <div class="grid md:grid-cols-4 gap-6"><div class="text-center">`);
    Circle_check_big($$payload2, { class: "h-6 w-6 text-primary mx-auto mb-2" });
    $$payload2.out.push(`<!----> <div class="linear-body font-semibold">Strategy</div> <div class="linear-body text-sm text-muted-foreground">GTM Strategy &amp; Execution</div></div> <div class="text-center">`);
    Circle_check_big($$payload2, { class: "h-6 w-6 text-primary mx-auto mb-2" });
    $$payload2.out.push(`<!----> <div class="linear-body font-semibold">Messaging</div> <div class="linear-body text-sm text-muted-foreground">Differentiated Positioning</div></div> <div class="text-center">`);
    Circle_check_big($$payload2, { class: "h-6 w-6 text-primary mx-auto mb-2" });
    $$payload2.out.push(`<!----> <div class="linear-body font-semibold">Campaigns</div> <div class="linear-body text-sm text-muted-foreground">AI-Powered 1-to-1 Marketing</div></div> <div class="text-center">`);
    Circle_check_big($$payload2, { class: "h-6 w-6 text-primary mx-auto mb-2" });
    $$payload2.out.push(`<!----> <div class="linear-body font-semibold">Growth</div> <div class="linear-body text-sm text-muted-foreground">Predictable Pipeline</div></div></div></div></div></section> <section id="services" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Our <span class="text-primary">Services</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">We don't just advise—we build, deploy, and run the complete marketing
          infrastructure your growing business needs to scale efficiently.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg">`);
    Brain($$payload2, { class: "h-8 w-8 text-primary mb-4" });
    $$payload2.out.push(`<!----> <h3 class="linear-heading text-xl mb-4">Custom Marketing Agents</h3> <p class="linear-body text-muted-foreground">Build and run your custom marketing agents centered around your
            product, your audience and your business requirements.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg">`);
    Zap($$payload2, { class: "h-8 w-8 text-blue-400 mb-4" });
    $$payload2.out.push(`<!----> <h3 class="linear-heading text-xl mb-4">AI Campaign Orchestration</h3> <p class="linear-body text-muted-foreground">Build and setup an entire AI based campaign orchestration machine
            for your business.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg">`);
    Trending_up($$payload2, { class: "h-8 w-8 text-orange-400 mb-4" });
    $$payload2.out.push(`<!----> <h3 class="linear-heading text-xl mb-4">Complete Marketing Tech Stack</h3> <p class="linear-body text-muted-foreground">Build, setup, and run your entire marketing tech stack from website
            to AI-powered automation, data pipelines and analytics dashboard.</p></div></div></div></section> <section id="agents" class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in animate-on-load"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Marketing <span class="text-primary">Agents</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Our AI Agents That Scale Your Marketing. Meet your new marketing team.
          Each agent is specifically designed to handle complex marketing tasks
          that typically require hours of manual work, delivering results in
          minutes with unprecedented accuracy and insight.</p></div> <div class="grid md:grid-cols-3 gap-8">`);
    if (homeContent?.marketingAgents && homeContent.marketingAgents.length > 0) {
      $$payload2.out.push("<!--[-->");
      const each_array = ensure_array_like(homeContent.marketingAgents);
      $$payload2.out.push(`<!--[-->`);
      for (let index = 0, $$length = each_array.length; index < $$length; index++) {
        let agent = each_array[index];
        $$payload2.out.push(`<div class="linear-card linear-fade-in p-6 rounded-lg"><span${attr_class(`linear-tag ${stringify(getAgentTagClass(index))} mb-4 inline-block`)}>${escape_html(getAgentTagLabel(agent.frontmatter.name))}</span> <h3 class="linear-heading text-xl mb-4">${escape_html(agent.frontmatter.name)}</h3> <p class="linear-body text-muted-foreground">${html(agent.content)}</p></div>`);
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-green mb-4 inline-block">Content Agent</span> <h3 class="linear-heading text-xl mb-4">Custom Content Agent</h3> <p class="linear-body text-muted-foreground">Generate SEO optimized content in your brand voice. This agent
              analyzes your existing content, understands your unique voice and
              tone, then creates high-performing content that ranks while
              staying true to your brand identity.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-blue mb-4 inline-block">Research Agent</span> <h3 class="linear-heading text-xl mb-4">Competitive Researcher</h3> <p class="linear-body text-muted-foreground">Custom agent to do deep competitive analysis on real-time data.
              Continuously monitors your competitors' strategies, pricing,
              content, and market positioning to give you actionable insights
              and strategic advantages.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-purple mb-4 inline-block">Social Agent</span> <h3 class="linear-heading text-xl mb-4">Social Media Agent</h3> <p class="linear-body text-muted-foreground">Listen to your brand and your competitors' signals, assess
              sentiment and act upon them. This agent monitors social
              conversations, tracks brand mentions, analyzes sentiment trends,
              and provides real-time insights to optimize your social media
              strategy.</p></div>`);
    }
    $$payload2.out.push(`<!--]--></div> <div class="mt-16 max-w-4xl mx-auto linear-fade-in"><div class="text-center mb-8"><h3 class="linear-heading text-2xl font-bold mb-4">See Our Agents in Action</h3> <p class="linear-body text-muted-foreground">Try Athena, our competitive researcher, and see real-time analysis
            in action.</p></div> `);
    AgentDemo($$payload2);
    $$payload2.out.push(`<!----></div> <div class="max-w-3xl mx-auto mt-16"><div class="text-center mb-8"><h3 class="linear-heading text-3xl font-bold mb-4">🎯 SEO Keyword Research Demo</h3> <p class="linear-body text-muted-foreground">Try Lexi, our SEO strategist, and discover high-value keywords
            instantly.</p></div> `);
    SEOAgentDemo($$payload2);
    $$payload2.out.push(`<!----></div></div></section> <section id="stories" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Real companies. <span class="text-primary">Real results</span>.</h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">See how we've helped AI and technology companies transform their
          go-to-market strategy and achieve remarkable growth.</p></div> <div class="grid md:grid-cols-2 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-green mb-4 inline-block">Protecto</span> <h3 class="linear-heading text-xl mb-4">Data Guardrails for Enterprise AI Agents</h3> <p class="linear-body text-muted-foreground mb-6">A brilliant AI privacy startup helping prevent data leaks, privacy
            violations, and compliance risks in AI automation couldn't break
            through in a crowded market. We repositioned them around "Data
            Guardrails" and built a comprehensive GTM strategy.</p> <div class="linear-testimonial mb-6"><p class="linear-body italic text-muted-foreground">"Working with Robynn was like having a strategic co-founder who
              understood both our technology and our market better than we did."</p> <div class="mt-4"><div class="linear-body font-semibold">Amar Kanagaraj</div> <div class="linear-mono text-sm text-muted-foreground">CEO, Protecto</div></div></div> <div class="grid grid-cols-3 gap-4 text-center"><div><div class="linear-mono text-xl font-bold text-primary">300%</div> <div class="linear-body text-sm text-muted-foreground">Pipeline Growth</div></div> <div><div class="linear-mono text-xl font-bold text-primary">60%</div> <div class="linear-body text-sm text-muted-foreground">Shorter Cycles</div></div> <div><div class="linear-mono text-xl font-bold text-primary">3x</div> <div class="linear-body text-sm text-muted-foreground">Valuation</div></div></div></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-pink mb-4 inline-block">Apptware</span> <h3 class="linear-heading text-xl mb-4">Design first AI Services</h3> <p class="linear-body text-muted-foreground mb-6">A successful AI services company was struggling to gain traction in
            the US market. We rebuilt their market entry strategy from the
            ground up, created campaigns and helped them achieve a 2X pipeline
            growth.</p> <div class="linear-testimonial mb-6"><p class="linear-body italic text-muted-foreground">"They transformed our US market entry from a costly experiment
              into our fastest-growing revenue stream."</p> <div class="mt-4"><div class="linear-body font-semibold">Harish Rohokale</div> <div class="linear-mono text-sm text-muted-foreground">CEO, Apptware</div></div></div> <div class="grid grid-cols-3 gap-4 text-center"><div><div class="linear-mono text-xl font-bold text-primary">$8M</div> <div class="linear-body text-sm text-muted-foreground">Pipeline Built</div></div> <div><div class="linear-mono text-xl font-bold text-primary">150%</div> <div class="linear-body text-sm text-muted-foreground">Goal Exceeded</div></div> <div><div class="linear-mono text-xl font-bold text-primary">4</div> <div class="linear-body text-sm text-muted-foreground">Months</div></div></div></div></div></div></section> <section class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">The Robynn <span class="text-primary">Team</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Meet the strategic minds behind your growth. We're not just
          marketers—we're growth architects with deep expertise in AI,
          technology, and scaling businesses.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-primary">MK</span></div> <h3 class="linear-heading text-xl mb-2">Madhukar Kumar</h3> <p class="linear-body text-muted-foreground mb-4">CEO, Co-Founder</p> <p class="linear-body text-muted-foreground mb-4">Former CMO at two unicorn startups. Two decades plus years scaling
            B2B SaaS companies from $5M to $100M+ ARR. Expert in product-led
            growth and AI-native marketing strategies including building
            compelling and memorable brands.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-green">Go-to-Market Strategy</span> <span class="linear-tag linear-tag-purple">Product-Led Growth</span></div></div> <div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-blue-400/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-blue-400">JH</span></div> <h3 class="linear-heading text-xl mb-2">Joel Horwitz</h3> <p class="linear-body text-muted-foreground mb-4">The AI Demand Gen Maestro</p> <p class="linear-body text-muted-foreground mb-4">Former Head of Growth at Series B AI company. Built marketing
            automation systems that scaled 10x without proportional team growth.
            Expert in AI Demand Gen Orchestration.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-blue">Marketing Automation</span> <span class="linear-tag linear-tag-orange">Demand Generation</span></div></div> <div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-orange-400/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-orange-400">MT</span></div> <h3 class="linear-heading text-xl mb-2">Matt Tanner</h3> <p class="linear-body text-muted-foreground mb-4">SEO and Content 10Xer</p> <p class="linear-body text-muted-foreground mb-4">Software engineer turned SEO expert. Analyze and optimize content
            for AI-powered search engines. Helped large and small companies
            scale their organic traffic by orders of magnitude.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-orange">AI Implementation</span> <span class="linear-tag linear-tag-purple">SEO</span></div></div></div></div></section> <section class="py-20 px-6 linear-grid"><div class="max-w-4xl mx-auto text-center linear-fade-in">`);
    if (homeContent?.sections?.cta) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">${escape_html(homeContent.sections.cta.frontmatter.title)} <span class="text-primary">${escape_html(homeContent.sections.cta.frontmatter.titleHighlight)}</span></h2> <p class="linear-body text-xl text-muted-foreground mb-12">${escape_html(homeContent.sections.cta.frontmatter.subtitle)}</p> <div class="flex justify-center"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center">${escape_html(homeContent.sections.cta.frontmatter.ctaPrimary)} `);
      Chevron_right($$payload2, { class: "ml-2 h-4 w-4" });
      $$payload2.out.push(`<!----></button></div> <p class="linear-mono text-sm text-muted-foreground mt-8">${escape_html(homeContent.sections.cta.frontmatter.footer)}</p>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Your next chapter starts with <span class="text-primary">us</span></h2> <p class="linear-body text-xl text-muted-foreground mb-12">We don't believe in hard sells or high-pressure tactics. We believe in
          finding the right fit. If you're ready to transform your marketing
          from a cost center into a growth engine, let's talk.</p> <div class="flex justify-center"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center">Start Your Growth Journey `);
      Chevron_right($$payload2, { class: "ml-2 h-4 w-4" });
      $$payload2.out.push(`<!----></button></div> <p class="linear-mono text-sm text-muted-foreground mt-8">Ready to grow smarter, not just faster?</p>`);
    }
    $$payload2.out.push(`<!--]--></div></section></div> `);
    ContactModal($$payload2, {
      get isOpen() {
        return showContactModal;
      },
      set isOpen($$value) {
        showContactModal = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
