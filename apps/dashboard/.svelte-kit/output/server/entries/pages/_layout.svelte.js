import { p as pop, a as push, s as store_get, d as attr_class, u as unsubscribe_stores } from "../../chunks/index2.js";
import { n as navigating } from "../../chunks/stores.js";
import { s as setEnvironmentState } from "../../chunks/environment.svelte.js";
import "clsx";
import { w as writable } from "../../chunks/index4.js";
function ThemeProvider($$payload, $$props) {
  push();
  pop();
}
function createPageTransitionStore() {
  const { subscribe, set, update } = writable({
    isTransitioning: false,
    transitionType: "fade",
    direction: void 0,
    fromRoute: void 0,
    toRoute: void 0
  });
  return {
    subscribe,
    // Start a page transition
    startTransition: (type = "fade", direction, fromRoute, toRoute) => {
      update((state) => ({
        ...state,
        isTransitioning: true,
        transitionType: type,
        direction,
        fromRoute,
        toRoute
      }));
    },
    // End the transition
    endTransition: () => {
      update((state) => ({
        ...state,
        isTransitioning: false
      }));
    },
    // Set transition type
    setTransitionType: (type) => {
      update((state) => ({
        ...state,
        transitionType: type
      }));
    },
    // Reset to default state
    reset: () => {
      set({
        isTransitioning: false,
        transitionType: "fade",
        direction: void 0,
        fromRoute: void 0,
        toRoute: void 0
      });
    }
  };
}
const pageTransitionStore = createPageTransitionStore();
function _layout($$payload, $$props) {
  push();
  var $$store_subs;
  let { children, data } = $$props;
  setEnvironmentState(data.environment);
  ThemeProvider($$payload, { themeCSS: data.themeCSS });
  $$payload.out.push(`<!----> `);
  if (store_get($$store_subs ??= {}, "$navigating", navigating)) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="fixed w-full top-0 right-0 left-0 h-1 z-50 bg-primary"></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (store_get($$store_subs ??= {}, "$pageTransitionStore", pageTransitionStore).isTransitioning) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm"></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <main${attr_class("page-content", void 0, {
    "transitioning": store_get($$store_subs ??= {}, "$pageTransitionStore", pageTransitionStore).isTransitioning
  })}>`);
  children($$payload);
  $$payload.out.push(`<!----></main>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _layout as default
};
