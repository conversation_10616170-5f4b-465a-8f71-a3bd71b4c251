import "clsx";
import { p as pop, a as push } from "../../../../../chunks/index2.js";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
function _layout($$payload, $$props) {
  push();
  let { data, children } = $$props;
  let { supabase, session } = data;
  children($$payload);
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _layout as default
};
