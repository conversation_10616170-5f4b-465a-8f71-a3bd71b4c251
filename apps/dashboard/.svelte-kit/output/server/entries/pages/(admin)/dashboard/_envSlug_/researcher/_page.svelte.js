import { m as sanitize_props, o as spread_props, b as slot, a as push, d as attr_class, c as attr, f as stringify, e as escape_html, B as bind_props, p as pop, A as fallback, t as ensure_array_like, F as attr_style, v as clsx, s as store_get, G as maybe_selected, u as unsubscribe_stores, w as copy_payload, x as assign_payload, h as head } from "../../../../../../chunks/index2.js";
import { w as writable } from "../../../../../../chunks/index4.js";
import "@sveltejs/kit/internal";
import "../../../../../../chunks/exports.js";
import "../../../../../../chunks/state.svelte.js";
import { M as Menu } from "../../../../../../chunks/menu.js";
import { X } from "../../../../../../chunks/x.js";
import { G as Grid_3x3, C as Circle } from "../../../../../../chunks/grid-3x3.js";
import { S as Search } from "../../../../../../chunks/search.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { U as User, C as Clock } from "../../../../../../chunks/user.js";
import { C as Calendar, D as Download } from "../../../../../../chunks/download.js";
import { C as Chevron_down } from "../../../../../../chunks/chevron-down.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { C as Circle_check } from "../../../../../../chunks/circle-check.js";
import { L as Loader_circle } from "../../../../../../chunks/loader-circle.js";
import { C as Chevron_right } from "../../../../../../chunks/chevron-right.js";
import { Z as Zap } from "../../../../../../chunks/zap.js";
import { B as Bot } from "../../../../../../chunks/bot.js";
import { E as Eye } from "../../../../../../chunks/eye.js";
import { h as html } from "../../../../../../chunks/html.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
function Building_2($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z" }],
    ["path", { "d": "M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2" }],
    ["path", { "d": "M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2" }],
    ["path", { "d": "M10 6h4" }],
    ["path", { "d": "M10 10h4" }],
    ["path", { "d": "M10 14h4" }],
    ["path", { "d": "M10 18h4" }]
  ];
  Icon($$payload, spread_props([
    { name: "building-2" },
    $$sanitized_props,
    {
      /**
       * @component @name Building2
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAyMlY0YTIgMiAwIDAgMSAyLTJoOGEyIDIgMCAwIDEgMiAydjE4WiIgLz4KICA8cGF0aCBkPSJNNiAxMkg0YTIgMiAwIDAgMC0yIDJ2NmEyIDIgMCAwIDAgMiAyaDIiIC8+CiAgPHBhdGggZD0iTTE4IDloMmEyIDIgMCAwIDEgMiAydjlhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNMTAgNmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxMGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxNGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOGg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/building-2
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Chart_bar($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "M3 3v16a2 2 0 0 0 2 2h16" }],
    ["path", { "d": "M7 16h8" }],
    ["path", { "d": "M7 11h12" }],
    ["path", { "d": "M7 6h3" }]
  ];
  Icon($$payload, spread_props([
    { name: "chart-bar" },
    $$sanitized_props,
    {
      /**
       * @component @name ChartBar
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTcgMTZoOCIgLz4KICA8cGF0aCBkPSJNNyAxMWgxMiIgLz4KICA8cGF0aCBkPSJNNyA2aDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-bar
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function History($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      { "d": "M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" }
    ],
    ["path", { "d": "M3 3v5h5" }],
    ["path", { "d": "M12 7v5l4 2" }]
  ];
  Icon($$payload, spread_props([
    { name: "history" },
    $$sanitized_props,
    {
      /**
       * @component @name History
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAxIDAgOS05IDkuNzUgOS43NSAwIDAgMC02Ljc0IDIuNzRMMyA4IiAvPgogIDxwYXRoIGQ9Ik0zIDN2NWg1IiAvPgogIDxwYXRoIGQ9Ik0xMiA3djVsNCAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/history
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function House($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      { "d": "M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" }
    ],
    [
      "path",
      {
        "d": "M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "house" },
    $$sanitized_props,
    {
      /**
       * @component @name House
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Keyboard($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "M10 8h.01" }],
    ["path", { "d": "M12 12h.01" }],
    ["path", { "d": "M14 8h.01" }],
    ["path", { "d": "M16 12h.01" }],
    ["path", { "d": "M18 8h.01" }],
    ["path", { "d": "M6 8h.01" }],
    ["path", { "d": "M7 16h10" }],
    ["path", { "d": "M8 12h.01" }],
    [
      "rect",
      { "width": "20", "height": "16", "x": "2", "y": "4", "rx": "2" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "keyboard" },
    $$sanitized_props,
    {
      /**
       * @component @name Keyboard
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgOGguMDEiIC8+CiAgPHBhdGggZD0iTTEyIDEyaC4wMSIgLz4KICA8cGF0aCBkPSJNMTQgOGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDEyaC4wMSIgLz4KICA8cGF0aCBkPSJNMTggOGguMDEiIC8+CiAgPHBhdGggZD0iTTYgOGguMDEiIC8+CiAgPHBhdGggZD0iTTcgMTZoMTAiIC8+CiAgPHBhdGggZD0iTTggMTJoLjAxIiAvPgogIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNiIgeD0iMiIgeT0iNCIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/keyboard
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Message_square($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      {
        "d": "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "message-square" },
    $$sanitized_props,
    {
      /**
       * @component @name MessageSquare
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Refresh_cw($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      { "d": "M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" }
    ],
    ["path", { "d": "M21 3v5h-5" }],
    [
      "path",
      { "d": "M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" }
    ],
    ["path", { "d": "M8 16H3v5" }]
  ];
  Icon($$payload, spread_props([
    { name: "refresh-cw" },
    $$sanitized_props,
    {
      /**
       * @component @name RefreshCw
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Triangle_alert($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      {
        "d": "m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"
      }
    ],
    ["path", { "d": "M12 9v4" }],
    ["path", { "d": "M12 17h.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "triangle-alert" },
    $$sanitized_props,
    {
      /**
       * @component @name TriangleAlert
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function ResearchSidebar($$payload, $$props) {
  push();
  let {
    collapsed = false,
    isMobile = false,
    session = null,
    profile = null
  } = $$props;
  let user = {
    name: profile?.full_name || session?.user?.user_metadata?.full_name || session?.user?.email?.split("@")[0] || "User",
    email: session?.user?.email || "<EMAIL>",
    initials: getInitials(profile?.full_name || session?.user?.user_metadata?.full_name || session?.user?.email?.split("@")[0] || "User")
  };
  function getInitials(name) {
    return name.split(" ").map((word) => word.charAt(0).toUpperCase()).slice(0, 2).join("");
  }
  $$payload.out.push(`<aside${attr_class("bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative", void 0, {
    "w-64": !collapsed,
    "w-16": collapsed && !isMobile,
    "w-0": collapsed && isMobile,
    "overflow-hidden": collapsed && isMobile
  })}><button${attr_class("absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors svelte-947kzy", void 0, { "hidden": isMobile && collapsed })}>`);
  if (collapsed) {
    $$payload.out.push("<!--[-->");
    Menu($$payload, { class: "w-3 h-3" });
  } else {
    $$payload.out.push("<!--[!-->");
    X($$payload, { class: "w-3 h-3" });
  }
  $$payload.out.push(`<!--]--></button> <header${attr_class("p-4 border-b border-sidebar-border", void 0, { "hidden": collapsed && isMobile })}><div class="flex items-center space-x-2 mb-4"><div class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center">`);
  Grid_3x3($$payload, { class: "w-4 h-4 text-sidebar-primary-foreground" });
  $$payload.out.push(`<!----></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="font-semibold text-lg text-sidebar-foreground">Robynn AI</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="relative">`);
    Search($$payload, {
      class: "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
    });
    $$payload.out.push(`<!----> <input type="text" placeholder="Search" class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"/> <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">⌘K</kbd></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="flex justify-center"><button class="p-2 hover:bg-sidebar-accent rounded-lg svelte-947kzy">`);
    Search($$payload, { class: "w-4 h-4 text-muted-foreground" });
    $$payload.out.push(`<!----></button></div>`);
  }
  $$payload.out.push(`<!--]--></header> <nav class="flex-1 p-4"><div class="space-y-1"><button${attr_class("flex items-center w-full px-3 py-2 text-sm text-sidebar-primary-foreground bg-sidebar-primary rounded-lg svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? "AI Chat" : "")}>`);
  Message_square($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
  $$payload.out.push(`<!----> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`AI Chat`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> <button${attr_class("flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? "Projects" : "")}>`);
  Grid_3x3($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
  $$payload.out.push(`<!----> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`Projects`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> <button${attr_class("flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? "Templates" : "")}>`);
  Grid_3x3($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
  $$payload.out.push(`<!----> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`Templates`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> <button${attr_class("flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? "Documents" : "")}>`);
  Grid_3x3($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
  $$payload.out.push(`<!----> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`Documents`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> <button${attr_class("flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? "Community" : "")}>`);
  User($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
  $$payload.out.push(`<!----> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`Community <span class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">NEW</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> <button${attr_class("flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? "History" : "")}>`);
  Calendar($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
  $$payload.out.push(`<!----> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`History`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button></div></nav> <footer${attr_class("p-4 border-t border-sidebar-border", void 0, { "hidden": collapsed && isMobile })}><div class="relative mt-4 pt-4 border-t border-sidebar-border"><button${attr_class("flex items-center w-full p-2 hover:bg-sidebar-accent rounded-lg transition-colors group svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? user.email : "Profile menu")}><div${attr_class("w-8 h-8 bg-primary rounded-full flex items-center justify-center svelte-947kzy", void 0, { "mr-3": !collapsed })}><span class="text-sm font-medium text-primary-foreground">${escape_html(user.initials)}</span></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="flex-1 min-w-0"><p class="text-sm font-medium text-sidebar-foreground truncate text-left">${escape_html(user.name)}</p> <p class="text-xs text-muted-foreground truncate text-left">${escape_html(user.email)}</p></div> `);
    Chevron_down($$payload, {
      class: `w-4 h-4 text-muted-foreground transition-transform duration-200 ${stringify("")}`
    });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></footer></aside>`);
  bind_props($$props, { collapsed });
  pop();
}
function SkeletonLoader($$payload, $$props) {
  let type = fallback($$props["type"], "message");
  let count = fallback($$props["count"], 1);
  if (type === "message") {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(Array(count));
    $$payload.out.push(`<!--[-->`);
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      each_array[i];
      $$payload.out.push(`<div class="flex items-start space-x-4 animate-pulse svelte-5pvuvw"><div class="w-10 h-10 bg-muted rounded-full flex-shrink-0 svelte-5pvuvw"></div> <div class="flex-1 space-y-3 svelte-5pvuvw"><div class="flex items-center gap-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-16 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-20 svelte-5pvuvw"></div></div> <div class="space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-4/5 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div></div></div></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (type === "sidebar") {
    $$payload.out.push("<!--[-->");
    const each_array_1 = ensure_array_like(Array(5));
    $$payload.out.push(`<div class="animate-pulse space-y-4 svelte-5pvuvw"><div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-6 h-6 bg-muted rounded svelte-5pvuvw"></div> <div class="h-5 bg-muted rounded w-24 svelte-5pvuvw"></div></div> <div class="h-10 bg-muted rounded-lg svelte-5pvuvw"></div> <!--[-->`);
    for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
      each_array_1[i];
      $$payload.out.push(`<div class="flex items-center space-x-3 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-20 svelte-5pvuvw"></div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (type === "progress") {
    $$payload.out.push("<!--[-->");
    const each_array_2 = ensure_array_like(Array(3));
    $$payload.out.push(`<div class="animate-pulse space-y-3 svelte-5pvuvw"><div class="flex items-center justify-between svelte-5pvuvw"><div class="h-4 bg-muted rounded w-32 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-12 svelte-5pvuvw"></div></div> <div class="h-2 bg-muted rounded-full w-full svelte-5pvuvw"></div> <div class="space-y-2 svelte-5pvuvw"><!--[-->`);
    for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
      each_array_2[i];
      $$payload.out.push(`<div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-40 svelte-5pvuvw"></div></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (type === "project") {
    $$payload.out.push("<!--[-->");
    const each_array_3 = ensure_array_like(Array(count));
    $$payload.out.push(`<!--[-->`);
    for (let i = 0, $$length = each_array_3.length; i < $$length; i++) {
      each_array_3[i];
      $$payload.out.push(`<div class="animate-pulse p-4 border border-border rounded-lg space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-2/3 svelte-5pvuvw"></div></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { type, count });
}
function ProgressTracker($$payload, $$props) {
  push();
  let steps = fallback($$props["steps"], () => [], true);
  let currentProgress = fallback($$props["currentProgress"], 0);
  let isVisible = fallback($$props["isVisible"], false);
  let animatedProgress = 0;
  function animateProgress(targetProgress) {
    const startProgress = animatedProgress;
    const duration = 800;
    const startTime = Date.now();
    function animate() {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      animatedProgress = startProgress + (targetProgress - startProgress) * easeOutCubic;
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        animatedProgress = targetProgress;
      }
    }
    requestAnimationFrame(animate);
  }
  function getStepIcon(step, index) {
    if (step.status === "completed") {
      return Circle_check;
    } else if (step.status === "pending" && index === steps.findIndex((s) => s.status === "pending")) {
      return Loader_circle;
    } else {
      return Circle;
    }
  }
  function getStepClass(step, index) {
    const baseClass = "flex items-center space-x-3 p-3 rounded-lg transition-all duration-500";
    if (step.status === "completed") {
      return `${baseClass} bg-green-50 border border-green-200 text-green-800`;
    } else if (step.status === "pending" && index === steps.findIndex((s) => s.status === "pending")) {
      return `${baseClass} bg-blue-50 border border-blue-200 text-blue-800 animate-pulse`;
    } else {
      return `${baseClass} bg-muted/30 border border-border text-muted-foreground`;
    }
  }
  if (isVisible && currentProgress !== animatedProgress) {
    animateProgress(currentProgress);
  }
  if (
    // ms
    // Easing function for smooth animation
    isVisible && steps.length > 0
  ) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(steps);
    $$payload.out.push(`<div class="fixed top-4 right-4 w-80 bg-background border-2 border-border rounded-lg shadow-lg z-50 p-4 animate-slide-in svelte-lmyhl8" style="box-shadow: var(--shadow-lg);"><div class="flex items-center justify-between mb-4"><h3 class="font-semibold text-foreground flex items-center gap-2">`);
    Clock($$payload, { class: "w-4 h-4" });
    $$payload.out.push(`<!----> Research Progress</h3> <span class="text-sm font-medium text-muted-foreground">${escape_html(Math.round(animatedProgress))}%</span></div> <div class="mb-4"><div class="w-full bg-muted rounded-full h-2 overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"${attr_style(`width: ${stringify(animatedProgress)}%`)}></div></div></div> <div class="space-y-2 max-h-64 overflow-y-auto"><!--[-->`);
    for (let index = 0, $$length = each_array.length; index < $$length; index++) {
      let step = each_array[index];
      $$payload.out.push(`<div${attr_class(clsx(getStepClass(step, index)), "svelte-lmyhl8")}><!---->`);
      getStepIcon(step, index)?.($$payload, {
        class: `w-4 h-4 flex-shrink-0 ${stringify(step.status === "pending" && index === steps.findIndex((s) => s.status === "pending") ? "animate-spin" : "")}`
      });
      $$payload.out.push(`<!----> <div class="flex-1 min-w-0"><p class="font-medium text-sm truncate">${escape_html(step.title)}</p> <p class="text-xs opacity-80 truncate">${escape_html(step.description)}</p></div></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { steps, currentProgress, isVisible });
  pop();
}
function ErrorBoundary($$payload, $$props) {
  push();
  let fallback$1 = fallback($$props["fallback"], "full");
  let onRetry = fallback($$props["onRetry"], null);
  let error = fallback($$props["error"], null);
  let hasError = false;
  let errorMessage = "";
  let errorStack = "";
  if (error) {
    hasError = true;
    errorMessage = error.message;
    errorStack = error.stack || "";
  }
  if (
    // Global error handler for unhandled promise rejections
    hasError
  ) {
    $$payload.out.push("<!--[-->");
    if (fallback$1 === "minimal") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">`);
      Triangle_alert($$payload, { class: "w-4 h-4 text-destructive flex-shrink-0" });
      $$payload.out.push(`<!----> <span class="text-sm text-destructive">Something went wrong</span> `);
      if (onRetry) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button class="ml-auto text-xs text-destructive hover:text-destructive/80 underline">Retry</button>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div class="flex flex-col items-center justify-center min-h-[400px] p-8 text-center"><div class="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">`);
      Triangle_alert($$payload, { class: "w-8 h-8 text-destructive" });
      $$payload.out.push(`<!----></div> <h2 class="text-xl font-semibold text-foreground mb-2">Oops! Something went wrong</h2> <p class="text-muted-foreground mb-6 max-w-md">We encountered an unexpected error. This has been logged and our team will investigate.</p> `);
      if (errorMessage) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<details class="mb-6 w-full max-w-md"><summary class="cursor-pointer text-sm text-muted-foreground hover:text-foreground">Error Details</summary> <div class="mt-2 p-3 bg-muted rounded-lg text-left"><p class="text-xs font-mono text-destructive break-all">${escape_html(errorMessage)}</p> `);
        if (errorStack) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<pre class="text-xs text-muted-foreground mt-2 overflow-auto max-h-32">
                ${escape_html(errorStack)}
              </pre>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--></div></details>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <div class="flex gap-3">`);
      if (onRetry) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button class="btn-primary px-4 py-2 flex items-center gap-2">`);
        Refresh_cw($$payload, { class: "w-4 h-4" });
        $$payload.out.push(`<!----> Try Again</button>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <button class="btn-secondary px-4 py-2 flex items-center gap-2">`);
      House($$payload, { class: "w-4 h-4" });
      $$payload.out.push(`<!----> Go Home</button></div></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<!---->`);
    slot($$payload, $$props, "default", {}, null);
    $$payload.out.push(`<!---->`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { fallback: fallback$1, onRetry, error });
  pop();
}
function ResearchChatArea($$payload, $$props) {
  push();
  var $$store_subs;
  let displayMessages, filteredMessages;
  let messages = fallback($$props["messages"], () => [], true);
  let isLoading = fallback($$props["isLoading"], false);
  let progressSteps = fallback($$props["progressSteps"], () => [], true);
  let currentProgress = fallback($$props["currentProgress"], 0);
  let leftSidebarCollapsed = fallback($$props["leftSidebarCollapsed"], false);
  let rightSidebarCollapsed = fallback($$props["rightSidebarCollapsed"], false);
  const messagesStore = writable(messages);
  let input = "";
  let outputFormat = "comprehensive";
  const outputFormats = [
    {
      value: "comprehensive",
      label: "Comprehensive",
      description: "Full detailed analysis"
    },
    {
      value: "executive",
      label: "Executive Summary",
      description: "High-level overview"
    },
    {
      value: "slide-ready",
      label: "Slide-ready",
      description: "Sections + headers for export"
    },
    {
      value: "battlecard",
      label: "Competitive Battlecard",
      description: "Strategic comparison format"
    }
  ];
  const placeholderExamples = [
    "Research Stripe's competitive positioning and market strategy...",
    "Analyze OpenAI's business model and recent developments...",
    "Compare Notion vs. Obsidian feature sets and pricing...",
    "Investigate Figma's growth strategy and market expansion...",
    "Study Shopify's competitive advantages in e-commerce..."
  ];
  let currentPlaceholder = placeholderExamples[0];
  function generateId() {
    return Math.random().toString(36).substring(2, 11);
  }
  async function sendMessage() {
    if (!input.trim() || isLoading) return;
    let formatPrefix = "";
    const userMessage = formatPrefix + input.trim();
    input = "";
    isLoading = true;
    progressSteps = [
      {
        id: 1,
        title: "Initial Analysis",
        description: "Analyzing research request...",
        status: "pending"
      },
      {
        id: 2,
        title: "Web Search",
        description: "Conducting comprehensive search...",
        status: "pending"
      },
      {
        id: 3,
        title: "Financial Analysis",
        description: "Gathering financial data...",
        status: "pending"
      },
      {
        id: 4,
        title: "Market Research",
        description: "Analyzing market position...",
        status: "pending"
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating research report...",
        status: "pending"
      }
    ];
    currentProgress = 0;
    const newUserMessage = {
      id: generateId(),
      role: "user",
      content: userMessage,
      timestamp: /* @__PURE__ */ new Date()
    };
    messagesStore.update((msgs) => [...msgs, newUserMessage]);
    messages = [...messages, newUserMessage];
    try {
      const response = await fetch(`/dashboard/${store_get($$store_subs ??= {}, "$page", page).params.envSlug}/researcher?stream=true`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: userMessage })
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");
          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.type === "final_response") {
                  const assistantMessage = {
                    id: generateId(),
                    role: "assistant",
                    content: data.response,
                    timestamp: /* @__PURE__ */ new Date(),
                    isReport: true
                  };
                  messagesStore.update((msgs) => [...msgs, assistantMessage]);
                  messages = [...messages, assistantMessage];
                } else if (data.step) {
                  currentProgress = data.progress;
                  progressSteps = progressSteps.map((step) => {
                    if (step.id === data.step) {
                      return { ...step, status: data.status, description: data.action };
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" };
                    }
                    return step;
                  });
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage = {
        id: generateId(),
        role: "assistant",
        content: "I apologize, but Compass encountered an error while processing your request. Please try again.",
        timestamp: /* @__PURE__ */ new Date()
      };
      messagesStore.update((msgs) => [...msgs, errorMessage]);
      messages = [...messages, errorMessage];
    } finally {
      isLoading = false;
      progressSteps = [];
      currentProgress = 0;
    }
  }
  function retryLastMessage() {
    sendMessage();
  }
  function extractInsights(content) {
    const lines = content.split("\n").filter((line) => line.trim());
    const summary = lines.slice(0, 2).join(" ").substring(0, 200) + "...";
    const insights = lines.filter((line) => line.includes("key") || line.includes("important") || line.includes("significant")).slice(0, 3);
    const badges = [];
    if (content.includes("growth") || content.includes("increase")) badges.push("↑ Trending");
    if (content.includes("insight") || content.includes("analysis")) badges.push("💡 Insight");
    if (content.includes("challenge") || content.includes("weakness")) badges.push("⚠ Weakness");
    return { summary, insights, badges };
  }
  function formatContent(content) {
    let formatted = content.replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>').replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>').replace(/^#### (.*$)/gim, '<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>').replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/\*(.*?)\*/g, '<em class="italic" style="color: var(--muted-foreground);">$1</em>').replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>').replace(/`([^`]+)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>').replace(/^[\s]*[-*+] (.+)$/gim, '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>').replace(/^[\s]*(\d+)\.\s+(.+)$/gim, '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$2</li>').replace(/^> (.+)$/gim, '<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>').replace(/\[(\d+)\]/g, '<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-bold ml-1">[$1]</sup>').replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>').replace(/\n\n/g, '</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">').replace(/\n/g, "<br>");
    if (!formatted.startsWith("<h") && !formatted.startsWith("<p") && !formatted.startsWith("<ul") && !formatted.startsWith("<ol") && !formatted.startsWith("<blockquote")) {
      formatted = '<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">' + formatted + "</p>";
    }
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>)/gs, (match) => {
      if (match.includes("list-style-type: disc")) {
        return '<ul class="mb-4">' + match + "</ul>";
      } else if (match.includes("list-style-type: decimal")) {
        return '<ol class="mb-4">' + match + "</ol>";
      }
      return match;
    });
    return formatted;
  }
  messagesStore.set(messages);
  displayMessages = store_get($$store_subs ??= {}, "$messagesStore", messagesStore).length > 0 ? store_get($$store_subs ??= {}, "$messagesStore", messagesStore) : [];
  filteredMessages = displayMessages;
  const each_array_3 = ensure_array_like(outputFormats);
  $$payload.out.push(`<main class="flex-1 flex flex-col bg-background svelte-m6peww"><header class="flex items-center justify-between p-6 border-b border-border bg-background svelte-m6peww"><div class="flex items-center svelte-m6peww"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-2 svelte-m6peww"><a${attr("href", `/dashboard/${stringify(store_get($$store_subs ??= {}, "$page", page).params.envSlug)}`)} class="hover:text-foreground transition-colors svelte-m6peww">Dashboard</a> `);
  Chevron_right($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----> <span class="text-foreground font-medium svelte-m6peww">Research Agent</span></nav></div> <div class="flex items-center space-x-2 svelte-m6peww"><button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww" title="Search messages (Ctrl+K)">`);
  Search($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----></button> <button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww" title="Keyboard shortcuts: Ctrl+K (search), / (focus input), Ctrl+Enter (send)">`);
  Keyboard($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----></button> <button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww" title="Chat history">`);
  History($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----></button> <span class="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full svelte-m6peww">Online</span></div></header> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="px-6 py-4 border-b border-border svelte-m6peww"><div class="flex items-center space-x-4 svelte-m6peww"><div class="w-12 h-12 flex items-center justify-center border-2 border-border bg-primary svelte-m6peww" style="box-shadow: var(--shadow-sm);">`);
  Building_2($$payload, { class: "w-6 h-6 text-primary-foreground" });
  $$payload.out.push(`<!----></div> <div class="svelte-m6peww"><h1 class="text-3xl font-black flex items-center gap-3 text-foreground svelte-m6peww">`);
  Zap($$payload, { class: "w-8 h-8 text-primary" });
  $$payload.out.push(`<!----> Compass</h1> <p class="text-muted-foreground svelte-m6peww">AI-powered research assistant for competitive analysis and market
          insights</p></div></div></div> <div class="flex-1 overflow-y-auto p-6 svelte-m6peww"><div class="max-w-4xl mx-auto space-y-6 svelte-m6peww">`);
  ErrorBoundary($$payload, {
    onRetry: retryLastMessage,
    children: ($$payload2) => {
      const each_array = ensure_array_like(filteredMessages);
      {
        $$payload2.out.push("<!--[-->");
        SkeletonLoader($$payload2, { type: "message", count: 2 });
      }
      $$payload2.out.push(`<!--]--> <!--[-->`);
      for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
        let message = each_array[$$index_1];
        $$payload2.out.push(`<div class="flex items-start space-x-4 svelte-m6peww"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-m6peww"${attr_style(`background: var(--${stringify(message.role === "user" ? "primary" : "secondary")}); border-color: var(--border); box-shadow: var(--shadow-sm);`)}>`);
        if (message.role === "user") {
          $$payload2.out.push("<!--[-->");
          User($$payload2, { class: "w-5 h-5", style: "color: var(--primary-foreground);" });
        } else {
          $$payload2.out.push("<!--[!-->");
          Bot($$payload2, {
            class: "w-5 h-5",
            style: "color: var(--secondary-foreground);"
          });
        }
        $$payload2.out.push(`<!--]--></div> <div class="flex-1 max-w-4xl svelte-m6peww"><div class="flex items-center gap-2 mb-2 svelte-m6peww"><span class="text-sm font-bold svelte-m6peww" style="color: var(--foreground);">${escape_html(message.role === "user" ? "You" : "Compass")}</span> <div class="flex items-center gap-1 svelte-m6peww">`);
        Clock($$payload2, { class: "w-3 h-3", style: "color: var(--muted-foreground);" });
        $$payload2.out.push(`<!----> <span class="text-xs svelte-m6peww" style="color: var(--muted-foreground);">${escape_html(message.timestamp.toLocaleTimeString())}</span></div> `);
        if (message.role === "assistant" && message.isReport) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1 svelte-m6peww" title="Download as Markdown">`);
          Download($$payload2, { class: "w-3 h-3" });
          $$payload2.out.push(`<!----> Download</button>`);
        } else {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></div> `);
        if (message.role === "user") {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<div class="p-4 border-2 svelte-m6peww" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium svelte-m6peww" style="color: var(--primary-foreground);">${escape_html(message.content)}</p></div>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          const insights = extractInsights(message.content);
          $$payload2.out.push(`<div class="border-2 svelte-m6peww" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;">`);
          if (insights.summary) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<div class="p-4 border-b-2 border-border bg-muted/50 svelte-m6peww"><div class="flex items-start gap-2 svelte-m6peww"><span class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded svelte-m6peww">TL;DR</span> <div class="text-sm font-medium formatted-summary svelte-m6peww" style="color: var(--foreground);">${html(formatContent(insights.summary))}</div></div></div>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (insights.badges.length > 0) {
            $$payload2.out.push("<!--[-->");
            const each_array_1 = ensure_array_like(insights.badges);
            $$payload2.out.push(`<div class="px-6 pt-4 pb-2 svelte-m6peww"><div class="flex flex-wrap gap-2 svelte-m6peww"><!--[-->`);
            for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
              let badge = each_array_1[$$index];
              $$payload2.out.push(`<span class="text-xs font-bold px-2 py-1 border border-border rounded svelte-m6peww" style="background: var(--accent); color: var(--accent-foreground);">${escape_html(badge)}</span>`);
            }
            $$payload2.out.push(`<!--]--></div></div>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> <div class="p-6 svelte-m6peww"><div class="formatted-content max-w-none svelte-m6peww">${html(formatContent(message.content))}</div></div> <div class="px-6 pb-4 border-t border-border svelte-m6peww"><div class="flex flex-wrap gap-2 mt-4 svelte-m6peww"><button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1 svelte-m6peww">`);
          Eye($$payload2, { class: "w-3 h-3" });
          $$payload2.out.push(`<!----> Compare with competitor</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1 svelte-m6peww">`);
          Chart_bar($$payload2, { class: "w-3 h-3" });
          $$payload2.out.push(`<!----> Add visuals</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1 svelte-m6peww">`);
          Message_square($$payload2, { class: "w-3 h-3" });
          $$payload2.out.push(`<!----> Turn into slides</button></div></div></div>`);
        }
        $$payload2.out.push(`<!--]--></div></div>`);
      }
      $$payload2.out.push(`<!--]--> `);
      if (isLoading && progressSteps.length > 0) {
        $$payload2.out.push("<!--[-->");
        const each_array_2 = ensure_array_like(progressSteps);
        $$payload2.out.push(`<div class="border-2 border-border bg-background p-4 rounded-lg svelte-m6peww"><h3 class="font-semibold text-foreground mb-3 svelte-m6peww">Research Progress</h3> <div class="space-y-2 svelte-m6peww"><!--[-->`);
        for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
          let step = each_array_2[$$index_2];
          $$payload2.out.push(`<div class="flex items-center space-x-3 svelte-m6peww"><div${attr_class(
            `w-4 h-4 rounded-full ${stringify(step.status === "completed" ? "bg-green-500" : step.status === "active" ? "bg-primary" : "bg-muted")}`,
            "svelte-m6peww"
          )}></div> <span${attr_class(`text-sm ${stringify(step.status === "completed" ? "text-green-600" : "text-foreground")}`, "svelte-m6peww")}>${escape_html(step.title)}</span></div>`);
        }
        $$payload2.out.push(`<!--]--></div> `);
        if (currentProgress > 0) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<div class="mt-3 svelte-m6peww"><div class="w-full bg-muted rounded-full h-2 svelte-m6peww"><div class="bg-primary h-2 rounded-full transition-all duration-300 svelte-m6peww"${attr_style(`width: ${stringify(currentProgress)}%`)}></div></div> <p class="text-xs text-muted-foreground mt-1 svelte-m6peww">${escape_html(currentProgress)}% complete</p></div>`);
        } else {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div> `);
  ProgressTracker($$payload, {
    steps: progressSteps,
    currentProgress,
    isVisible: isLoading && progressSteps.length > 0
  });
  $$payload.out.push(`<!----> <div class="p-6 border-t border-border bg-background svelte-m6peww"><div class="max-w-4xl mx-auto svelte-m6peww"><div class="flex items-center gap-2 mb-4 svelte-m6peww"><span class="text-sm font-bold svelte-m6peww" style="color: var(--muted-foreground);">Format:</span> <select class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded svelte-m6peww" style="border-radius: 0.375rem;">`);
  $$payload.select_value = outputFormat;
  $$payload.out.push(`<!--[-->`);
  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
    let format = each_array_3[$$index_3];
    $$payload.out.push(`<option${attr("value", format.value)}${maybe_selected($$payload, format.value)} class="svelte-m6peww">${escape_html(format.label)}</option>`);
  }
  $$payload.out.push(`<!--]-->`);
  $$payload.select_value = void 0;
  $$payload.out.push(`</select></div> <div class="relative svelte-m6peww"><textarea${attr("placeholder", isLoading ? "Researching..." : currentPlaceholder)}${attr_class("w-full px-4 py-3 pr-32 border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-foreground bg-background placeholder:text-muted-foreground transition-all duration-200 svelte-m6peww", void 0, { "opacity-50": isLoading })} rows="1" style="min-height: 48px; max-height: 120px;"${attr("disabled", isLoading, true)}>`);
  const $$body = escape_html(input);
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea> <div class="absolute right-2 bottom-2 flex items-center space-x-2 svelte-m6peww"><button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww"><svg class="w-4 h-4 text-muted-foreground svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" class="svelte-m6peww"></path></svg></button> <button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww"><svg class="w-4 h-4 text-muted-foreground svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" class="svelte-m6peww"></path></svg></button> <button${attr("disabled", !input.trim() || isLoading, true)} class="p-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 svelte-m6peww"${attr("title", isLoading ? "Researching..." : "Send message (Ctrl+Enter)")}>`);
  if (isLoading) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin svelte-m6peww"></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    Search($$payload, { class: "w-4 h-4" });
  }
  $$payload.out.push(`<!--]--></button></div></div> <div class="flex items-center space-x-4 mt-3 svelte-m6peww"><button class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors svelte-m6peww"><svg class="w-4 h-4 mr-1 svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" class="svelte-m6peww"></path></svg> Attach</button> <button class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors svelte-m6peww"><svg class="w-4 h-4 mr-1 svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" class="svelte-m6peww"></path></svg> Voice Message</button> <button class="flex items-center px-3 py-1.5 text-sm text-primary-foreground bg-primary hover:bg-primary/90 rounded-lg border border-primary transition-colors svelte-m6peww"><svg class="w-4 h-4 mr-1 svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" class="svelte-m6peww"></path></svg> Browse Prompts</button> <div class="ml-auto text-xs text-muted-foreground svelte-m6peww">0 / 3,000</div></div> <p class="text-xs text-muted-foreground text-center mt-4 svelte-m6peww">Research may generate inaccurate information about companies, markets,
        or facts. Model: Compass AI v1.3</p></div></div></main>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    messages,
    isLoading,
    progressSteps,
    currentProgress,
    leftSidebarCollapsed,
    rightSidebarCollapsed
  });
  pop();
}
function ProjectsSidebar($$payload, $$props) {
  push();
  let displayProjects;
  let collapsed = fallback($$props["collapsed"], false);
  let isMobile = fallback($$props["isMobile"], false);
  let projects = fallback($$props["projects"], () => [], true);
  const defaultProjects = [
    {
      id: "1",
      title: "Generate 5 attention-grab...",
      description: '"Revolutionize Customer Enga...'
    },
    {
      id: "2",
      title: "Learning From 100 Years o...",
      description: "For athletes, high altitude prod..."
    },
    {
      id: "3",
      title: "Research officiants",
      description: "Maxwell's equations—the foun..."
    },
    {
      id: "4",
      title: "What does a senior lead de...",
      description: "Physiological respiration involv..."
    },
    {
      id: "5",
      title: "Write a sweet note to your...",
      description: "In the eighteenth century the G..."
    },
    {
      id: "6",
      title: "Meet with cake bakers",
      description: "Physical space is often conceiv..."
    },
    {
      id: "7",
      title: "Meet with cake bakers",
      description: "Physical space is often conceiv..."
    }
  ];
  displayProjects = projects.length > 0 ? projects : defaultProjects;
  $$payload.out.push(`<aside${attr_class("bg-background border-l border-border overflow-y-auto transition-all duration-300 ease-in-out relative", void 0, {
    "w-80": !collapsed,
    "w-16": collapsed && !isMobile,
    "w-0": collapsed && isMobile,
    "overflow-hidden": collapsed && isMobile
  })}><button${attr_class("absolute -left-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors", void 0, { "hidden": isMobile && collapsed })}>`);
  if (collapsed) {
    $$payload.out.push("<!--[-->");
    Menu($$payload, { class: "w-3 h-3" });
  } else {
    $$payload.out.push("<!--[!-->");
    X($$payload, { class: "w-3 h-3" });
  }
  $$payload.out.push(`<!--]--></button> <div${attr_class("p-6", void 0, { "hidden": collapsed && isMobile })}><div class="flex items-center justify-between mb-6">`);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<h2 class="text-lg font-semibold text-foreground">Projects (${escape_html(displayProjects.length)})</h2> <button class="text-muted-foreground hover:text-foreground transition-colors">`);
    Grid_3x3($$payload, { class: "w-5 h-5" });
    $$payload.out.push(`<!----></button>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="flex flex-col items-center w-full"><button class="p-2 hover:bg-accent rounded-lg mb-2" title="Projects">`);
    Grid_3x3($$payload, { class: "w-5 h-5" });
    $$payload.out.push(`<!----></button> <span class="text-xs text-muted-foreground">${escape_html(displayProjects.length)}</span></div>`);
  }
  $$payload.out.push(`<!--]--></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(displayProjects);
    $$payload.out.push(`<div class="space-y-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let project = each_array[$$index];
      $$payload.out.push(`<div class="p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors group"><h3 class="font-medium text-sm mb-2 text-foreground group-hover:text-accent-foreground">${escape_html(project.title)}</h3> <p class="text-xs text-muted-foreground group-hover:text-accent-foreground/80">${escape_html(project.description)}</p></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array_1 = ensure_array_like(displayProjects.slice(0, 5));
    $$payload.out.push(`<div class="flex flex-col items-center space-y-2"><!--[-->`);
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let project = each_array_1[$$index_1];
      $$payload.out.push(`<button class="w-2 h-2 bg-muted-foreground rounded-full hover:bg-foreground transition-colors"${attr("title", project.title)}></button>`);
    }
    $$payload.out.push(`<!--]--> `);
    if (displayProjects.length > 5) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="text-xs text-muted-foreground">+${escape_html(displayProjects.length - 5)}</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--> <div class="mt-8">`);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<h3 class="text-md font-semibold text-foreground mb-4">Quick Start Templates</h3> <div class="space-y-3"><button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Competitive Analysis</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Analyze competitors' strategies and positioning</div></button> <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Market Research</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Research market trends and opportunities</div></button> <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Industry Analysis</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Deep dive into industry dynamics</div></button> <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Customer Research</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Understand customer needs and behavior</div></button></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="flex justify-center"><button class="p-2 hover:bg-accent rounded-lg" title="Quick Start Templates">`);
    Plus($$payload, { class: "w-4 h-4" });
    $$payload.out.push(`<!----></button></div>`);
  }
  $$payload.out.push(`<!--]--></div></div></aside>`);
  bind_props($$props, { collapsed, isMobile, projects });
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  let { session, profile } = data;
  const messages = writable([]);
  let isLoading = false;
  let progressSteps = [];
  let currentProgress = 0;
  let leftSidebarCollapsed = false;
  let rightSidebarCollapsed = false;
  let innerWidth = 0;
  let isMobile = innerWidth < 768;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>Compass - AI Market Researcher</title>`;
    });
    $$payload2.out.push(`<div class="flex h-screen bg-background text-foreground">`);
    ResearchSidebar($$payload2, {
      isMobile,
      session,
      profile,
      get collapsed() {
        return leftSidebarCollapsed;
      },
      set collapsed($$value) {
        leftSidebarCollapsed = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!----> `);
    ResearchChatArea($$payload2, {
      messages: store_get($$store_subs ??= {}, "$messages", messages),
      leftSidebarCollapsed,
      rightSidebarCollapsed,
      get isLoading() {
        return isLoading;
      },
      set isLoading($$value) {
        isLoading = $$value;
        $$settled = false;
      },
      get progressSteps() {
        return progressSteps;
      },
      set progressSteps($$value) {
        progressSteps = $$value;
        $$settled = false;
      },
      get currentProgress() {
        return currentProgress;
      },
      set currentProgress($$value) {
        currentProgress = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!----> `);
    ProjectsSidebar($$payload2, {
      isMobile,
      get collapsed() {
        return rightSidebarCollapsed;
      },
      set collapsed($$value) {
        rightSidebarCollapsed = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!----></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
