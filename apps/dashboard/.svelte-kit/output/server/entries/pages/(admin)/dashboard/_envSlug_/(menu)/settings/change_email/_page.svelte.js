import { h as head, p as pop, a as push } from "../../../../../../../../chunks/index2.js";
import { e as emailSchema } from "../../../../../../../../chunks/schemas.js";
import { S as Settings_module } from "../../../../../../../../chunks/settings_module.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let { session } = data;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Change Email</title>`;
  });
  $$payload.out.push(`<h1 class="text-2xl font-bold mb-6">Settings</h1> `);
  Settings_module($$payload, {
    data: data.form,
    schema: emailSchema,
    title: "Change Email",
    editable: true,
    successTitle: "Email change initiated",
    successBody: "You should receive an email at the new address to confirm the change. Please click the link in the email to finalized the change. Until finalized, you must sign in with your current email.",
    formTarget: "/api?/updateEmail",
    fields: [
      {
        id: "email",
        label: "Email",
        initialValue: session?.user?.email ?? "",
        placeholder: "Email address"
      }
    ]
  });
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _page as default
};
