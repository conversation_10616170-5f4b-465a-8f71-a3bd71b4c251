import { A as fallback, d as attr_class, f as stringify, e as escape_html, F as attr_style, B as bind_props, h as head, c as attr, s as store_get, u as unsubscribe_stores, p as pop, a as push } from "../../../../../../chunks/index2.js";
import { p as page } from "../../../../../../chunks/stores.js";
function LoadingState($$payload, $$props) {
  let sizeClasses;
  let title = fallback($$props["title"], "Loading...");
  let description = fallback($$props["description"], "");
  let progress = fallback($$props["progress"], null);
  let showSpinner = fallback($$props["showSpinner"], true);
  let size = fallback($$props["size"], "md");
  let variant = fallback($$props["variant"], "default");
  function getSizeClasses() {
    switch (size) {
      case "sm":
        return {
          container: "p-4",
          spinner: "w-6 h-6",
          title: "text-sm",
          description: "text-xs"
        };
      case "lg":
        return {
          container: "p-8",
          spinner: "w-12 h-12",
          title: "text-xl",
          description: "text-base"
        };
      default:
        return {
          container: "p-6",
          spinner: "w-8 h-8",
          title: "text-lg",
          description: "text-sm"
        };
    }
  }
  sizeClasses = getSizeClasses();
  if (variant === "minimal") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div${attr_class(`flex items-center gap-3 ${stringify(sizeClasses.container)}`)}>`);
    if (showSpinner) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div${attr_class(`animate-spin rounded-full ${stringify(sizeClasses.spinner)} border-b-2 border-primary`)}></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> <span${attr_class(`text-muted-foreground ${stringify(sizeClasses.title)}`)}>${escape_html(title)}</span></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    if (variant === "detailed") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div${attr_class(`card-brutal ${stringify(sizeClasses.container)}`)}><div class="space-y-4"><div class="flex items-center gap-3">`);
      if (showSpinner) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div${attr_class(`animate-spin rounded-full ${stringify(sizeClasses.spinner)} border-b-2 border-primary`)}></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <div><h3${attr_class(`font-semibold text-foreground ${stringify(sizeClasses.title)}`)}>${escape_html(title)}</h3> `);
      if (description) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<p${attr_class(`text-muted-foreground ${stringify(sizeClasses.description)} mt-1`)}>${escape_html(description)}</p>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div></div> `);
      if (progress !== null) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="space-y-2"><div class="flex items-center justify-between text-xs"><span class="text-muted-foreground">Progress</span> <span class="text-muted-foreground">${escape_html(Math.round(progress))}%</span></div> <div class="w-full bg-muted rounded-full h-2 border border-border"><div class="bg-primary h-full rounded-full transition-all duration-300 ease-out"${attr_style(`width: ${stringify(progress)}%`)}></div></div></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <div class="flex items-center justify-center py-4"><div class="flex space-x-1"><div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0ms"></div> <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 150ms"></div> <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 300ms"></div></div></div></div></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div${attr_class(`card-brutal ${stringify(sizeClasses.container)} text-center`)}><div class="space-y-4">`);
      if (showSpinner) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="flex justify-center"><div${attr_class(`animate-spin rounded-full ${stringify(sizeClasses.spinner)} border-b-2 border-primary`)}></div></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <div><h3${attr_class(`font-semibold text-foreground ${stringify(sizeClasses.title)}`)}>${escape_html(title)}</h3> `);
      if (description) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<p${attr_class(`text-muted-foreground ${stringify(sizeClasses.description)} mt-2`)}>${escape_html(description)}</p>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div> `);
      if (progress !== null) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="max-w-xs mx-auto space-y-2"><div class="w-full bg-muted rounded-full h-2 border border-border"><div class="bg-primary h-full rounded-full transition-all duration-300 ease-out"${attr_style(`width: ${stringify(progress)}%`)}></div></div> <p class="text-xs text-muted-foreground">${escape_html(Math.round(progress))}% complete</p></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { title, description, progress, showSpinner, size, variant });
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let data = $$props["data"];
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Brand Monitor - FireGeo</title>`;
    $$payload2.out.push(`<meta name="description" content="Track how AI models rank your brand against competitors in search results and recommendations."/>`);
  });
  $$payload.out.push(`<div class="min-h-screen bg-background"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-6"><a${attr("href", `/dashboard/${stringify(store_get($$store_subs ??= {}, "$page", page).params.envSlug)}`)} class="hover:text-foreground transition-colors">Dashboard</a> <span>/</span> <span class="text-foreground font-medium">Brand Monitor</span></nav> <div class="mb-8"><div class="flex items-center gap-4 mb-4"><div class="w-12 h-12 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div> <div><h1 class="text-4xl font-semibold text-foreground">FireGeo Brand Monitor</h1> <p class="text-lg text-muted-foreground mt-1">Track how AI models rank your brand against competitors in search
          results and recommendations.</p></div></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> `);
  {
    $$payload.out.push("<!--[-->");
    LoadingState($$payload, {
      title: "Loading Brand Monitor",
      description: "Connecting to FireGeo service and loading your analyses...",
      variant: "detailed"
    });
  }
  $$payload.out.push(`<!--]--></div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
