import { e as escape_html, p as pop, a as push } from "../../../../chunks/index2.js";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let { supabase } = data;
  let message = "Signing out....";
  $$payload.out.push(`<h1 class="text-2xl font-bold m-6">${escape_html(message)}</h1>`);
  pop();
}
export {
  _page as default
};
