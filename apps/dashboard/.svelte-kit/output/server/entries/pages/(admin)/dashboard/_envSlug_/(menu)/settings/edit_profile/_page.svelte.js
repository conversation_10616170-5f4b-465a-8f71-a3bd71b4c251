import { h as head, p as pop, a as push } from "../../../../../../../../chunks/index2.js";
import { S as Settings_module } from "../../../../../../../../chunks/settings_module.js";
import { p as profileSchema } from "../../../../../../../../chunks/schemas.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let { profile } = data;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Edit Profile</title>`;
  });
  $$payload.out.push(`<h1 class="text-2xl font-bold mb-6">Settings</h1> `);
  Settings_module($$payload, {
    data: data.form,
    schema: profileSchema,
    editable: true,
    title: "Edit Profile",
    successTitle: "Saved Profile",
    formTarget: "/api?/updateProfile",
    fields: [
      {
        id: "full_name",
        label: "Name",
        initialValue: profile?.full_name ?? "",
        placeholder: "Your full name",
        maxlength: 50
      },
      {
        id: "company_name",
        label: "Company Name",
        initialValue: profile?.company_name ?? "",
        maxlength: 50
      },
      {
        id: "website",
        label: "Company Website",
        initialValue: profile?.website ?? "",
        maxlength: 50
      }
    ]
  });
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _page as default
};
