import { p as public_env } from "../../../chunks/shared-server.js";
import { createBrowserClient } from "@supabase/ssr";
const load = async ({ data, depends }) => {
  depends("supabase:auth");
  const supabase = createBrowserClient(
    public_env.PUBLIC_SUPABASE_URL,
    public_env.PUBLIC_SUPABASE_ANON_KEY
  );
  const {
    data: { session }
  } = await supabase.auth.getSession();
  const profile = data.profile;
  return {
    ...data,
    supabase,
    session,
    profile
  };
};
const _hasFullProfile = (profile) => {
  if (!profile) {
    return false;
  }
  if (!profile.full_name) {
    return false;
  }
  if (!profile.company_name) {
    return false;
  }
  if (!profile.website) {
    return false;
  }
  return true;
};
export {
  _hasFullProfile,
  load
};
