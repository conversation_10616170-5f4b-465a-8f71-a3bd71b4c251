import { m as sanitize_props, o as spread_props, b as slot, a as push, t as ensure_array_like, d as attr_class, e as escape_html, c as attr, f as stringify, B as bind_props, p as pop, w as copy_payload, x as assign_payload, F as attr_style, v as clsx, s as store_get, u as unsubscribe_stores, h as head } from "../../../../../../chunks/index2.js";
import { w as writable } from "../../../../../../chunks/index4.js";
import "@sveltejs/kit/internal";
import "../../../../../../chunks/exports.js";
import "../../../../../../chunks/state.svelte.js";
import { M as Menu } from "../../../../../../chunks/menu.js";
import { X } from "../../../../../../chunks/x.js";
import { G as Grid_3x3, C as Circle } from "../../../../../../chunks/grid-3x3.js";
import { S as Search } from "../../../../../../chunks/search.js";
import { C as Chevron_down } from "../../../../../../chunks/chevron-down.js";
import { C as Clock, U as User } from "../../../../../../chunks/user.js";
import { C as Circle_check } from "../../../../../../chunks/circle-check.js";
import { L as Loader_circle } from "../../../../../../chunks/loader-circle.js";
import { C as Chevron_right } from "../../../../../../chunks/chevron-right.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { a as Trending_up, T as Target, S as Sparkles } from "../../../../../../chunks/trending-up.js";
import { B as Bot } from "../../../../../../chunks/bot.js";
import { C as Copy } from "../../../../../../chunks/copy.js";
function Book_open($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" }],
    [
      "path",
      { "d": "M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "book-open" },
    $$sanitized_props,
    {
      /**
       * @component @name BookOpen
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAzaDZhNCA0IDAgMCAxIDQgNHYxNGEzIDMgMCAwIDAtMy0zSDJ6IiAvPgogIDxwYXRoIGQ9Ik0yMiAzaC02YTQgNCAwIDAgMC00IDR2MTRhMyAzIDAgMCAxIDMtM2g3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book-open
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Chart_no_axes_column_increasing($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["line", { "x1": "12", "x2": "12", "y1": "20", "y2": "10" }],
    ["line", { "x1": "18", "x2": "18", "y1": "20", "y2": "4" }],
    ["line", { "x1": "6", "x2": "6", "y1": "20", "y2": "16" }]
  ];
  Icon($$payload, spread_props([
    { name: "chart-no-axes-column-increasing" },
    $$sanitized_props,
    {
      /**
       * @component @name ChartNoAxesColumnIncreasing
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIwIiB5Mj0iMTAiIC8+CiAgPGxpbmUgeDE9IjE4IiB4Mj0iMTgiIHkxPSIyMCIgeTI9IjQiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSI2IiB5MT0iMjAiIHkyPSIxNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chart-no-axes-column-increasing
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function File_down($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      {
        "d": "M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
      }
    ],
    ["path", { "d": "M14 2v4a2 2 0 0 0 2 2h4" }],
    ["path", { "d": "M12 18v-6" }],
    ["path", { "d": "m9 15 3 3 3-3" }]
  ];
  Icon($$payload, spread_props([
    { name: "file-down" },
    $$sanitized_props,
    {
      /**
       * @component @name FileDown
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMiAxOHYtNiIgLz4KICA8cGF0aCBkPSJtOSAxNSAzIDMgMy0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-down
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Filter($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "polygon",
      { "points": "22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "filter" },
    $$sanitized_props,
    {
      /**
       * @component @name Filter
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjIyIDMgMiAzIDEwIDEyLjQ2IDEwIDE5IDE0IDIxIDE0IDEyLjQ2IDIyIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/filter
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Settings($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      {
        "d": "M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"
      }
    ],
    ["circle", { "cx": "12", "cy": "12", "r": "3" }]
  ];
  Icon($$payload, spread_props([
    { name: "settings" },
    $$sanitized_props,
    {
      /**
       * @component @name Settings
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function AgentSidebar($$payload, $$props) {
  push();
  let {
    collapsed = false,
    isMobile = false,
    session = null,
    profile = null,
    agentType = "research",
    navigationItems = [],
    brandName = "Robynn AI",
    searchPlaceholder = "Search"
  } = $$props;
  let user = {
    name: profile?.full_name || session?.user?.user_metadata?.full_name || session?.user?.email?.split("@")[0] || "User",
    email: session?.user?.email || "<EMAIL>",
    initials: getInitials(profile?.full_name || session?.user?.user_metadata?.full_name || session?.user?.email?.split("@")[0] || "User")
  };
  function getInitials(name) {
    return name.split(" ").map((word) => word.charAt(0).toUpperCase()).slice(0, 2).join("");
  }
  const each_array = ensure_array_like(navigationItems);
  $$payload.out.push(`<aside${attr_class("bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative", void 0, {
    "w-64": !collapsed,
    "w-16": collapsed && !isMobile,
    "w-0": collapsed && isMobile,
    "overflow-hidden": collapsed && isMobile
  })}><button${attr_class("absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors svelte-947kzy", void 0, { "hidden": isMobile && collapsed })}>`);
  if (collapsed) {
    $$payload.out.push("<!--[-->");
    Menu($$payload, { class: "w-3 h-3" });
  } else {
    $$payload.out.push("<!--[!-->");
    X($$payload, { class: "w-3 h-3" });
  }
  $$payload.out.push(`<!--]--></button> <header${attr_class("p-4 border-b border-sidebar-border", void 0, { "hidden": collapsed && isMobile })}><div class="flex items-center space-x-2 mb-4"><div class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center">`);
  Grid_3x3($$payload, { class: "w-4 h-4 text-sidebar-primary-foreground" });
  $$payload.out.push(`<!----></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="font-semibold text-lg text-sidebar-foreground">${escape_html(brandName)}</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="relative">`);
    Search($$payload, {
      class: "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
    });
    $$payload.out.push(`<!----> <input type="text"${attr("placeholder", searchPlaceholder)} class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"/> <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">⌘K</kbd></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="flex justify-center"><button class="p-2 hover:bg-sidebar-accent rounded-lg svelte-947kzy">`);
    Search($$payload, { class: "w-4 h-4 text-muted-foreground" });
    $$payload.out.push(`<!----></button></div>`);
  }
  $$payload.out.push(`<!--]--></header> <nav class="flex-1 p-4"><div class="space-y-1"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let item = each_array[$$index];
    $$payload.out.push(`<button${attr_class("flex items-center w-full px-3 py-2 text-sm rounded-lg transition-colors svelte-947kzy", void 0, {
      "text-sidebar-primary-foreground": item.active,
      "bg-sidebar-primary": item.active,
      "text-sidebar-foreground": !item.active,
      "hover:bg-sidebar-accent": !item.active,
      "justify-center": collapsed
    })}${attr("title", collapsed ? item.label : "")}><!---->`);
    item.icon?.($$payload, { class: `w-4 h-4 ${stringify(!collapsed ? "mr-3" : "")}` });
    $$payload.out.push(`<!----> `);
    if (!collapsed) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`${escape_html(item.label)} `);
      if (item.badge) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<span class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">${escape_html(item.badge)}</span>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]-->`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></button>`);
  }
  $$payload.out.push(`<!--]--></div></nav> <footer${attr_class("p-4 border-t border-sidebar-border", void 0, { "hidden": collapsed && isMobile })}><div class="relative mt-4 pt-4 border-t border-sidebar-border"><button${attr_class("flex items-center w-full p-2 hover:bg-sidebar-accent rounded-lg transition-colors group svelte-947kzy", void 0, { "justify-center": collapsed })}${attr("title", collapsed ? user.email : "Profile menu")}><div${attr_class("w-8 h-8 bg-primary rounded-full flex items-center justify-center svelte-947kzy", void 0, { "mr-3": !collapsed })}><span class="text-sm font-medium text-primary-foreground">${escape_html(user.initials)}</span></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="flex-1 min-w-0"><p class="text-sm font-medium text-sidebar-foreground truncate text-left">${escape_html(user.name)}</p> <p class="text-xs text-muted-foreground truncate text-left">${escape_html(user.email)}</p></div> `);
    Chevron_down($$payload, {
      class: `w-4 h-4 text-muted-foreground transition-transform duration-200 ${stringify("")}`
    });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></button> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></footer></aside>`);
  bind_props($$props, { collapsed });
  pop();
}
function ErrorBoundary($$payload, $$props) {
  push();
  let { fallback = "full", onRetry = null, error = null } = $$props;
  {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<!---->`);
    slot($$payload, $$props, "default", {}, null);
    $$payload.out.push(`<!---->`);
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function AgentLayout($$payload, $$props) {
  push();
  let {
    session = null,
    profile = null,
    agentType = "research",
    navigationItems = [],
    brandName = "Robynn AI",
    searchPlaceholder = "Search",
    leftSidebarCollapsed = false,
    rightSidebarCollapsed = false,
    showRightSidebar = true,
    rightSidebarWidth = "w-80",
    children,
    rightSidebar
  } = $$props;
  let innerWidth = 0;
  let isMobile = innerWidth < 768;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    ErrorBoundary($$payload2, {
      children: ($$payload3) => {
        $$payload3.out.push(`<div class="flex h-screen bg-background text-foreground svelte-bm75gl">`);
        AgentSidebar($$payload3, {
          isMobile,
          session,
          profile,
          agentType,
          navigationItems,
          brandName,
          searchPlaceholder,
          get collapsed() {
            return leftSidebarCollapsed;
          },
          set collapsed($$value) {
            leftSidebarCollapsed = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----> <main class="flex-1 flex flex-col min-w-0 svelte-bm75gl"><div class="flex-1 overflow-hidden">`);
        children?.($$payload3);
        $$payload3.out.push(`<!----></div></main> `);
        if (showRightSidebar) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<aside${attr_class(`bg-sidebar border-l border-sidebar-border flex flex-col transition-all duration-300 ease-in-out ${stringify(rightSidebarWidth)}`, "svelte-bm75gl", {
            "w-0": rightSidebarCollapsed,
            "overflow-hidden": rightSidebarCollapsed
          })}>`);
          rightSidebar?.($$payload3);
          $$payload3.out.push(`<!----></aside>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--></div>`);
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { leftSidebarCollapsed, rightSidebarCollapsed });
  pop();
}
function SkeletonLoader($$payload, $$props) {
  let { type = "message", count = 1 } = $$props;
  if (type === "message") {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(Array(count));
    $$payload.out.push(`<!--[-->`);
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      each_array[i];
      $$payload.out.push(`<div class="flex items-start space-x-4 animate-pulse svelte-5pvuvw"><div class="w-10 h-10 bg-muted rounded-full flex-shrink-0 svelte-5pvuvw"></div> <div class="flex-1 space-y-3 svelte-5pvuvw"><div class="flex items-center gap-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-16 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-20 svelte-5pvuvw"></div></div> <div class="space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-4/5 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div></div></div></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (type === "sidebar") {
    $$payload.out.push("<!--[-->");
    const each_array_1 = ensure_array_like(Array(5));
    $$payload.out.push(`<div class="animate-pulse space-y-4 svelte-5pvuvw"><div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-6 h-6 bg-muted rounded svelte-5pvuvw"></div> <div class="h-5 bg-muted rounded w-24 svelte-5pvuvw"></div></div> <div class="h-10 bg-muted rounded-lg svelte-5pvuvw"></div> <!--[-->`);
    for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
      each_array_1[i];
      $$payload.out.push(`<div class="flex items-center space-x-3 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-20 svelte-5pvuvw"></div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (type === "progress") {
    $$payload.out.push("<!--[-->");
    const each_array_2 = ensure_array_like(Array(3));
    $$payload.out.push(`<div class="animate-pulse space-y-3 svelte-5pvuvw"><div class="flex items-center justify-between svelte-5pvuvw"><div class="h-4 bg-muted rounded w-32 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-12 svelte-5pvuvw"></div></div> <div class="h-2 bg-muted rounded-full w-full svelte-5pvuvw"></div> <div class="space-y-2 svelte-5pvuvw"><!--[-->`);
    for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
      each_array_2[i];
      $$payload.out.push(`<div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-40 svelte-5pvuvw"></div></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (type === "project") {
    $$payload.out.push("<!--[-->");
    const each_array_3 = ensure_array_like(Array(count));
    $$payload.out.push(`<!--[-->`);
    for (let i = 0, $$length = each_array_3.length; i < $$length; i++) {
      each_array_3[i];
      $$payload.out.push(`<div class="animate-pulse p-4 border border-border rounded-lg space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-2/3 svelte-5pvuvw"></div></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
}
function ProgressTracker($$payload, $$props) {
  push();
  let {
    steps = [],
    isVisible = false,
    title = "Progress"
  } = $$props;
  let animatedProgress = 0;
  function getStepIcon(step, index) {
    if (step.status === "completed") {
      return Circle_check;
    } else if (step.status === "pending" && index === steps.findIndex((s) => s.status === "pending")) {
      return Loader_circle;
    } else {
      return Circle;
    }
  }
  function getStepClass(step, index) {
    const baseClass = "flex items-center space-x-3 p-3 rounded-lg transition-all duration-500";
    if (step.status === "completed") {
      return `${baseClass} bg-green-50 border border-green-200 text-green-800`;
    } else if (step.status === "pending" && index === steps.findIndex((s) => s.status === "pending")) {
      return `${baseClass} bg-blue-50 border border-blue-200 text-blue-800 animate-pulse`;
    } else {
      return `${baseClass} bg-muted/30 border border-border text-muted-foreground`;
    }
  }
  if (isVisible && steps.length > 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(steps);
    $$payload.out.push(`<div class="fixed top-4 right-4 w-80 bg-background border-2 border-border rounded-lg shadow-lg z-50 p-4 animate-slide-in svelte-lmyhl8" style="box-shadow: var(--shadow-lg);"><div class="flex items-center justify-between mb-4"><h3 class="font-semibold text-foreground flex items-center gap-2">`);
    Clock($$payload, { class: "w-4 h-4" });
    $$payload.out.push(`<!----> ${escape_html(title)}</h3> <span class="text-sm font-medium text-muted-foreground">${escape_html(Math.round(animatedProgress))}%</span></div> <div class="mb-4"><div class="w-full bg-muted rounded-full h-2 overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"${attr_style(`width: ${stringify(animatedProgress)}%`)}></div></div></div> <div class="space-y-2 max-h-64 overflow-y-auto"><!--[-->`);
    for (let index = 0, $$length = each_array.length; index < $$length; index++) {
      let step = each_array[index];
      $$payload.out.push(`<div${attr_class(clsx(getStepClass(step, index)), "svelte-lmyhl8")}><!---->`);
      getStepIcon(step, index)?.($$payload, {
        class: `w-4 h-4 flex-shrink-0 ${stringify(step.status === "pending" && index === steps.findIndex((s) => s.status === "pending") ? "animate-spin" : "")}`
      });
      $$payload.out.push(`<!----> <div class="flex-1 min-w-0"><p class="font-medium text-sm truncate">${escape_html(step.title)}</p> <p class="text-xs opacity-80 truncate">${escape_html(step.description)}</p></div></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function SEOChatArea($$payload, $$props) {
  push();
  var $$store_subs;
  let {
    messages = writable([]),
    isLoading = false,
    progressSteps = [],
    envSlug = ""
  } = $$props;
  let input = "";
  let currentPlaceholder = "";
  const interactiveCards = [
    {
      icon: Search,
      title: "Niche Keyword Discovery",
      description: "Find untapped long-tail keywords in your specific niche",
      prompt: "Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"
    },
    {
      icon: Chart_no_axes_column_increasing,
      title: "Competitor Gap Analysis",
      description: "Identify keyword opportunities your competitors are missing",
      prompt: "Analyze keyword gaps between my website and [Competitor Domain] in the [Industry] space"
    },
    {
      icon: Trending_up,
      title: "Content Cluster Mapping",
      description: "Build topic clusters for better content organization",
      prompt: "Create a content cluster strategy for [Main Topic] with supporting subtopics and internal linking"
    },
    {
      icon: Target,
      title: "Local SEO Research",
      description: "Optimize for location-based search queries",
      prompt: "Find local SEO opportunities for a [Business Type] in [City, State]"
    }
  ];
  function formatTimestamp(date) {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  }
  if (progressSteps.length > 0) {
    $$payload.out.push("<!--[-->");
    ProgressTracker($$payload, {
      steps: progressSteps,
      isVisible: isLoading,
      title: "SEO Analysis Progress"
    });
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <main class="flex-1 flex flex-col bg-background"><header class="flex items-center justify-between p-6 border-b border-border bg-background"><div class="flex items-center"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-2"><a${attr("href", `/dashboard/${stringify(envSlug)}`)} class="hover:text-foreground transition-colors">Dashboard</a> `);
  Chevron_right($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----> <span class="text-foreground font-medium">SEO Agent</span></nav></div></header> <div class="flex-1 overflow-y-auto p-6">`);
  if (store_get($$store_subs ??= {}, "$messages", messages).length === 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(interactiveCards);
    $$payload.out.push(`<div class="max-w-4xl mx-auto"><div class="text-center mb-12"><div class="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">`);
    Target($$payload, { class: "w-8 h-8 text-primary" });
    $$payload.out.push(`<!----></div> <h1 class="text-3xl font-bold text-foreground mb-4">SEO Strategy Assistant</h1> <p class="text-lg text-muted-foreground max-w-2xl mx-auto">Discover high-value keywords, analyze competitor gaps, and build
            content clusters that drive organic traffic to your website.</p></div> <div class="grid md:grid-cols-2 gap-6 mb-12"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let card = each_array[$$index];
      $$payload.out.push(`<button class="group p-6 bg-card border border-border rounded-xl hover:border-primary/50 transition-all duration-300 text-left hover:shadow-lg"><div class="flex items-start gap-4"><div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><!---->`);
      card.icon?.($$payload, { class: "w-6 h-6 text-primary" });
      $$payload.out.push(`<!----></div> <div class="flex-1"><h3 class="font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">${escape_html(card.title)}</h3> <p class="text-sm text-muted-foreground mb-3">${escape_html(card.description)}</p> <div class="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 font-mono">${escape_html(card.prompt)}</div></div></div></button>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$messages", messages));
    $$payload.out.push(`<div class="max-w-4xl mx-auto space-y-6"><!--[-->`);
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let message = each_array_1[$$index_1];
      $$payload.out.push(`<div class="flex gap-4"><div${attr_class(`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${stringify(message.role === "user" ? "bg-primary" : "bg-muted")}`)}>`);
      if (message.role === "user") {
        $$payload.out.push("<!--[-->");
        User($$payload, { class: "w-4 h-4 text-primary-foreground" });
      } else {
        $$payload.out.push("<!--[!-->");
        Bot($$payload, { class: "w-4 h-4 text-muted-foreground" });
      }
      $$payload.out.push(`<!--]--></div> <div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-2"><span class="font-medium text-sm text-foreground">${escape_html(message.role === "user" ? "You" : "SEO Assistant")}</span> <span class="text-xs text-muted-foreground">${escape_html(formatTimestamp(message.timestamp))}</span></div> <div class="prose prose-sm max-w-none text-foreground"><div class="whitespace-pre-wrap">${escape_html(message.content)}</div></div> `);
      if (message.role === "assistant") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="flex items-center gap-2 mt-3"><button class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1">`);
        Copy($$payload, { class: "w-3 h-3" });
        $$payload.out.push(`<!----> Copy</button> `);
        if (message.isReport) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<button class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1">`);
          File_down($$payload, { class: "w-3 h-3" });
          $$payload.out.push(`<!----> Download</button>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div></div>`);
    }
    $$payload.out.push(`<!--]--> `);
    if (isLoading) {
      $$payload.out.push("<!--[-->");
      SkeletonLoader($$payload, { type: "message", count: 1 });
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--></div> <div class="border-t border-border p-6"><div class="max-w-4xl mx-auto space-y-4">`);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="flex gap-3"><button${attr_class(`px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors ${stringify("")}`)}>`);
  Filter($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----></button> <div class="flex-1 relative"><textarea${attr("placeholder", currentPlaceholder)} class="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-primary/50" rows="2"${attr("disabled", isLoading, true)}>`);
  const $$body = escape_html(input);
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea></div> <button${attr("disabled", !input.trim() || isLoading, true)} class="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2">`);
  if (isLoading) {
    $$payload.out.push("<!--[-->");
    Loader_circle($$payload, { class: "w-4 h-4 animate-spin" });
  } else {
    $$payload.out.push("<!--[!-->");
    Target($$payload, { class: "w-4 h-4" });
  }
  $$payload.out.push(`<!--]--> Analyze</button></div></div></div></main>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function SEOToolsSidebar($$payload, $$props) {
  push();
  let {
    collapsed = false,
    isMobile = false,
    activeTab = "niche",
    onNicheDiscovery = null,
    onGapAnalysis = null,
    onContentCluster = null,
    isLoading = false,
    nicheKeywords = [],
    gapKeywords = [],
    clusterData = []
  } = $$props;
  let expandedSections = /* @__PURE__ */ new Set(["tools"]);
  const seoTools = [
    {
      id: "niche",
      title: "Niche Discovery",
      description: "Find untapped long-tail keywords",
      icon: Sparkles,
      color: "text-blue-500",
      bgColor: "bg-blue-50"
    },
    {
      id: "gap",
      title: "Gap Analysis",
      description: "Identify competitor keyword gaps",
      icon: Chart_no_axes_column_increasing,
      color: "text-green-500",
      bgColor: "bg-green-50"
    },
    {
      id: "cluster",
      title: "Content Clusters",
      description: "Build topic cluster strategies",
      icon: Book_open,
      color: "text-purple-500",
      bgColor: "bg-purple-50"
    }
  ];
  $$payload.out.push(`<aside${attr_class("bg-background border-l border-border overflow-y-auto transition-all duration-300 ease-in-out relative", void 0, {
    "w-80": !collapsed,
    "w-16": collapsed && !isMobile,
    "w-0": collapsed && isMobile,
    "overflow-hidden": collapsed && isMobile
  })}><button${attr_class("absolute -left-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors", void 0, { "hidden": isMobile && collapsed })}>`);
  if (collapsed) {
    $$payload.out.push("<!--[-->");
    Menu($$payload, { class: "w-3 h-3" });
  } else {
    $$payload.out.push("<!--[!-->");
    X($$payload, { class: "w-3 h-3" });
  }
  $$payload.out.push(`<!--]--></button> <div${attr_class("p-6", void 0, { "hidden": collapsed && isMobile })}><div class="flex items-center justify-between mb-6">`);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<h2 class="text-lg font-semibold text-foreground">SEO Tools</h2> <button class="text-muted-foreground hover:text-foreground transition-colors">`);
    Settings($$payload, { class: "w-5 h-5" });
    $$payload.out.push(`<!----></button>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="flex flex-col items-center w-full"><button class="p-2 hover:bg-accent rounded-lg mb-2" title="SEO Tools">`);
    Settings($$payload, { class: "w-5 h-5" });
    $$payload.out.push(`<!----></button> <span class="text-xs text-muted-foreground">Tools</span></div>`);
  }
  $$payload.out.push(`<!--]--></div> `);
  if (!collapsed) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="space-y-4"><div><button class="flex items-center justify-between w-full text-left mb-3"><span class="font-medium text-foreground">Analysis Tools</span> `);
    if (expandedSections.has("tools")) {
      $$payload.out.push("<!--[-->");
      Chevron_down($$payload, { class: "w-4 h-4 text-muted-foreground" });
    } else {
      $$payload.out.push("<!--[!-->");
      Chevron_right($$payload, { class: "w-4 h-4 text-muted-foreground" });
    }
    $$payload.out.push(`<!--]--></button> `);
    if (expandedSections.has("tools")) {
      $$payload.out.push("<!--[-->");
      const each_array = ensure_array_like(seoTools);
      $$payload.out.push(`<div class="space-y-2"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let tool = each_array[$$index];
        $$payload.out.push(`<button${attr_class(`w-full p-3 rounded-lg border transition-all text-left ${stringify(activeTab === tool.id ? "border-primary bg-primary/5" : "border-border hover:border-primary/50 hover:bg-muted/50")}`)}><div class="flex items-start gap-3"><div${attr_class(`w-8 h-8 rounded-lg ${stringify(tool.bgColor)} flex items-center justify-center`)}><!---->`);
        tool.icon?.($$payload, { class: `w-4 h-4 ${stringify(tool.color)}` });
        $$payload.out.push(`<!----></div> <div class="flex-1 min-w-0"><h3 class="font-medium text-sm text-foreground mb-1">${escape_html(tool.title)}</h3> <p class="text-xs text-muted-foreground">${escape_html(tool.description)}</p></div></div></button>`);
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div class="border-t border-border pt-4">`);
    if (activeTab === "niche") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="space-y-4"><h3 class="font-medium text-foreground">Niche Discovery</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Seed Keywords</label> <textarea placeholder="organic skincare, natural beauty" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground resize-none" rows="2"></textarea></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Industry</label> <input type="text" placeholder="e.g., Beauty &amp; Cosmetics" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button${attr("disabled", isLoading, true)} class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50">`);
      if (isLoading) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`Analyzing...`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`Discover Keywords`);
      }
      $$payload.out.push(`<!--]--></button></div> `);
      if (nicheKeywords.length > 0) {
        $$payload.out.push("<!--[-->");
        const each_array_1 = ensure_array_like(nicheKeywords.slice(0, 5));
        $$payload.out.push(`<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2">Found ${escape_html(nicheKeywords.length)} Keywords</h4> <div class="space-y-2 max-h-40 overflow-y-auto"><!--[-->`);
        for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
          let keyword = each_array_1[$$index_1];
          $$payload.out.push(`<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground">${escape_html(keyword.keyword)}</div> <div class="text-muted-foreground">Vol: ${escape_html(keyword.search_volume)} | Diff: ${escape_html(keyword.difficulty)}</div></div>`);
        }
        $$payload.out.push(`<!--]--></div></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      if (activeTab === "gap") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="space-y-4"><h3 class="font-medium text-sidebar-foreground">Gap Analysis</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Your Domain</label> <input type="text" placeholder="yourdomain.com" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Competitor Domain</label> <input type="text" placeholder="competitor.com" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button${attr("disabled", isLoading, true)} class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50">`);
        if (isLoading) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`Analyzing...`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`Find Gaps`);
        }
        $$payload.out.push(`<!--]--></button></div> `);
        if (gapKeywords.length > 0) {
          $$payload.out.push("<!--[-->");
          const each_array_2 = ensure_array_like(gapKeywords.slice(0, 5));
          $$payload.out.push(`<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2">Found ${escape_html(gapKeywords.length)} Opportunities</h4> <div class="space-y-2 max-h-40 overflow-y-auto"><!--[-->`);
          for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
            let keyword = each_array_2[$$index_2];
            $$payload.out.push(`<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground">${escape_html(keyword.keyword)}</div> <div class="text-muted-foreground">Gap: ${escape_html(keyword.gap_type)} | Vol: ${escape_html(keyword.search_volume)}</div></div>`);
          }
          $$payload.out.push(`<!--]--></div></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        if (activeTab === "cluster") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<div class="space-y-4"><h3 class="font-medium text-sidebar-foreground">Content Clusters</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Main Topic</label> <input type="text" placeholder="e.g., Digital Marketing" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Target Audience</label> <input type="text" placeholder="e.g., Small business owners" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button${attr("disabled", isLoading, true)} class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50">`);
          if (isLoading) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`Creating...`);
          } else {
            $$payload.out.push("<!--[!-->");
            $$payload.out.push(`Build Cluster`);
          }
          $$payload.out.push(`<!--]--></button></div> `);
          if (clusterData.length > 0) {
            $$payload.out.push("<!--[-->");
            const each_array_3 = ensure_array_like(clusterData.slice(0, 3));
            $$payload.out.push(`<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2">Cluster Strategy</h4> <div class="space-y-2 max-h-40 overflow-y-auto"><!--[-->`);
            for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
              let cluster = each_array_3[$$index_3];
              $$payload.out.push(`<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground">${escape_html(cluster.topic)}</div> <div class="text-muted-foreground">${escape_html(cluster.subtopics?.length || 0)} subtopics</div></div>`);
            }
            $$payload.out.push(`<!--]--></div></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></div> <div class="border-t border-sidebar-border p-4"><h3 class="font-medium text-sidebar-foreground mb-3">Quick Actions</h3> <div class="space-y-2"><button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Export Keywords</button> <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Save Analysis</button> <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Share Report</button></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></aside>`);
  bind_props($$props, { collapsed, activeTab });
  pop();
}
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  const messages = writable([]);
  let isLoading = false;
  let progressSteps = [];
  let nicheKeywords = [];
  let gapKeywords = [];
  let clusterData = [];
  let rightSidebarCollapsed = false;
  let activeToolTab = "niche";
  let innerWidth = 0;
  let isMobile = innerWidth < 768;
  const navigationItems = [
    { id: "chat", label: "Chat", icon: Search, active: true },
    { id: "niche", label: "Niche Discovery", icon: Sparkles },
    { id: "gap", label: "Gap Analysis", icon: Chart_no_axes_column_increasing },
    { id: "cluster", label: "Content Clusters", icon: Book_open }
  ];
  function handleNicheDiscovery(data2) {
    console.log("Niche discovery:", data2);
  }
  function handleGapAnalysis(data2) {
    console.log("Gap analysis:", data2);
  }
  function handleContentCluster(data2) {
    console.log("Content cluster:", data2);
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>SEO Agent - Robynn AI</title>`;
      $$payload3.out.push(`<meta name="description" content="AI-powered SEO strategy and keyword research assistant"/>`);
    });
    {
      let rightSidebar = function($$payload3) {
        SEOToolsSidebar($$payload3, {
          isMobile,
          onNicheDiscovery: handleNicheDiscovery,
          onGapAnalysis: handleGapAnalysis,
          onContentCluster: handleContentCluster,
          isLoading,
          nicheKeywords,
          gapKeywords,
          clusterData,
          get collapsed() {
            return rightSidebarCollapsed;
          },
          set collapsed($$value) {
            rightSidebarCollapsed = $$value;
            $$settled = false;
          },
          get activeTab() {
            return activeToolTab;
          },
          set activeTab($$value) {
            activeToolTab = $$value;
            $$settled = false;
          }
        });
      };
      AgentLayout($$payload2, {
        session: data.session,
        profile: data.profile,
        agentType: "seo",
        navigationItems,
        brandName: "Robynn AI",
        searchPlaceholder: "Search SEO tools...",
        rightSidebarCollapsed,
        showRightSidebar: true,
        rightSidebarWidth: "320px",
        rightSidebar,
        children: ($$payload3) => {
          SEOChatArea($$payload3, {
            messages,
            isLoading,
            progressSteps,
            envSlug: data.environment?.slug
          });
        },
        $$slots: { rightSidebar: true, default: true }
      });
    }
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
