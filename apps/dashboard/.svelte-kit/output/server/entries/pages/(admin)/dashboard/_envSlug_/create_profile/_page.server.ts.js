import { redirect } from "@sveltejs/kit";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../../../../chunks/exports.js";
import "../../../../../../chunks/state.svelte.js";
import "../../../../../../chunks/formData.js";
import "../../../../../../chunks/superForm.js";
import { s as superValidate } from "../../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../../chunks/zod.js";
import { _hasFullProfile } from "../../../_layout.ts.js";
import { p as profileSchema } from "../../../../../../chunks/schemas.js";
const load = async ({ parent }) => {
  const data = await parent();
  if (_hasFullProfile(data?.profile)) {
    redirect(303, "/account/select_plan");
  }
  const form = await superValidate(
    {
      full_name: data.profile?.full_name ?? "",
      company_name: data.profile?.company_name ?? "",
      website: data.profile?.website ?? ""
    },
    zod(profileSchema),
    { errors: false }
  );
  return { form };
};
export {
  load
};
