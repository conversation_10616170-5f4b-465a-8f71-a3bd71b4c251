import { h as head, e as escape_html, p as pop, a as push } from "../../../../../../../chunks/index2.js";
import { S as Settings_module } from "../../../../../../../chunks/settings_module.js";
import { P as Pricing_module } from "../../../../../../../chunks/pricing_module.js";
import { d as defaultPlanId, p as pricingPlans } from "../../../../../../../chunks/pricing_plans.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let currentPlanId = data.currentPlanId ?? defaultPlanId;
  let currentPlanName = pricingPlans.find((x) => x.id === data.currentPlanId)?.name;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Billing</title>`;
  });
  $$payload.out.push(`<h1 class="text-2xl font-bold mb-2">${escape_html(data.isActiveCustomer ? "Billing" : "Select a Plan")}</h1> <div>View our <a href="/pricing" target="_blank" class="underline">pricing page</a> for details.</div> `);
  if (!data.isActiveCustomer) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="mt-8">`);
    Pricing_module($$payload, { currentPlanId, callToAction: "Select Plan", center: false });
    $$payload.out.push(`<!----></div> `);
    if (data.hasEverHadSubscription) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="mt-10"><a href="/account/billing/manage" class="underline">View past invoices</a></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
    Settings_module($$payload, {
      title: "Subscription",
      editable: false,
      fields: [
        {
          id: "plan",
          label: "Current Plan",
          initialValue: currentPlanName || ""
        }
      ],
      editButtonTitle: "Manage Subscription",
      editLink: "/account/billing/manage"
    });
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
export {
  _page as default
};
