import { h as head, p as pop, a as push } from "../../../../../../../../chunks/index2.js";
import { S as Settings_module } from "../../../../../../../../chunks/settings_module.js";
import { d as deleteAccountSchema } from "../../../../../../../../chunks/schemas.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Delete Account</title>`;
  });
  $$payload.out.push(`<h1 class="text-2xl font-bold mb-6">Settings</h1> `);
  Settings_module($$payload, {
    data: data.form,
    schema: deleteAccountSchema,
    title: "Delete Account",
    editable: true,
    dangerous: true,
    message: "Deleting your account can not be undone.",
    saveButtonTitle: "Delete Account",
    successTitle: "Account queued for deletion",
    successBody: "Your account will be deleted shortly.",
    formTarget: "/api?/deleteAccount",
    fields: [
      {
        id: "currentPassword",
        label: "Current Password",
        initialValue: "",
        inputType: "password"
      }
    ]
  });
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _page as default
};
