import "clsx";
import "@sveltejs/kit/internal";
import "../../../../../../../../chunks/exports.js";
import "../../../../../../../../chunks/state.svelte.js";
import "../../../../../../../../chunks/formData.js";
import "../../../../../../../../chunks/superForm.js";
import "@sveltejs/kit";
import { s as superValidate } from "../../../../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../../../../chunks/zod.js";
import { d as deleteAccountSchema } from "../../../../../../../../chunks/schemas.js";
const load = async () => {
  const form = await superValidate(zod(deleteAccountSchema));
  return { form };
};
export {
  load
};
