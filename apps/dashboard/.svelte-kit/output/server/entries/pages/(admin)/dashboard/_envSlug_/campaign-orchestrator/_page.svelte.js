import { m as sanitize_props, o as spread_props, b as slot, s as store_get, t as ensure_array_like, h as head, c as attr, f as stringify, e as escape_html, d as attr_class, F as attr_style, u as unsubscribe_stores, p as pop, a as push } from "../../../../../../chunks/index2.js";
import { w as writable } from "../../../../../../chunks/index4.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { C as Chevron_right } from "../../../../../../chunks/chevron-right.js";
import { U as User, C as Clock } from "../../../../../../chunks/user.js";
import { B as Bot } from "../../../../../../chunks/bot.js";
import { D as Download, C as Calendar } from "../../../../../../chunks/download.js";
import { U as Users } from "../../../../../../chunks/users.js";
import { C as Copy } from "../../../../../../chunks/copy.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { Z as Zap } from "../../../../../../chunks/zap.js";
import { h as html } from "../../../../../../chunks/html.js";
function Megaphone($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["path", { "d": "m3 11 18-5v12L3 14v-3z" }],
    ["path", { "d": "M11.6 16.8a3 3 0 1 1-5.8-1.6" }]
  ];
  Icon($$payload, spread_props([
    { name: "megaphone" },
    $$sanitized_props,
    {
      /**
       * @component @name Megaphone
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxMSAxOC01djEyTDMgMTR2LTN6IiAvPgogIDxwYXRoIGQ9Ik0xMS42IDE2LjhhMyAzIDAgMSAxLTUuOC0xLjYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/megaphone
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Share_2($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["circle", { "cx": "18", "cy": "5", "r": "3" }],
    ["circle", { "cx": "6", "cy": "12", "r": "3" }],
    ["circle", { "cx": "18", "cy": "19", "r": "3" }],
    [
      "line",
      { "x1": "8.59", "x2": "15.42", "y1": "13.51", "y2": "17.49" }
    ],
    [
      "line",
      { "x1": "15.41", "x2": "8.59", "y1": "6.51", "y2": "10.49" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "share-2" },
    $$sanitized_props,
    {
      /**
       * @component @name Share2
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjUiIHI9IjMiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjEyIiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTkiIHI9IjMiIC8+CiAgPGxpbmUgeDE9IjguNTkiIHgyPSIxNS40MiIgeTE9IjEzLjUxIiB5Mj0iMTcuNDkiIC8+CiAgPGxpbmUgeDE9IjE1LjQxIiB4Mj0iOC41OSIgeTE9IjYuNTEiIHkyPSIxMC40OSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/share-2
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const messages = writable([]);
  let input = "";
  let isLoading = false;
  let currentPlaceholder = "";
  const interactiveCards = [
    {
      icon: Zap,
      title: "Competitor Analysis",
      description: "Deep dive into competitor landscape and market positioning",
      prompt: "coreweave.com"
    },
    {
      icon: Calendar,
      title: "Market Research",
      description: "Comprehensive company intelligence with contact discovery",
      prompt: "SingleStore"
    },
    {
      icon: Users,
      title: "Lead Generation",
      description: "Find similar companies and key decision-maker contacts",
      prompt: "research stripe.com"
    }
  ];
  function formatContent(content) {
    return content.replace(/^### (.+)$/gm, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>').replace(/^## (.+)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>').replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>').replace(/^\* (.+)$/gm, '<li class="ml-4">• $1</li>').replace(/^- (.+)$/gm, '<li class="ml-4">• $1</li>').replace(/\*\*(.+?)\*\*/g, "<strong>$1</strong>").replace(/\n\n/g, '</p><p class="mb-4">').replace(/^/, '<p class="mb-4">').replace(/$/, "</p>");
  }
  if (store_get($$store_subs ??= {}, "$messages", messages).length > 0) {
    setTimeout(
      () => {
        window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
      },
      100
    );
  }
  const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$messages", messages));
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Catalyst - AI Company Research &amp; Intelligence</title>`;
    $$payload2.out.push(`<meta name="description" content="Comprehensive company research with competitor analysis and contact discovery powered by AI" class="svelte-i1g7n4"/>`);
  });
  $$payload.out.push(`<div class="modern-chat-container svelte-i1g7n4"><div class="border-b-2 flex-shrink-0 svelte-i1g7n4" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6 svelte-i1g7n4"><div class="flex items-center justify-between svelte-i1g7n4"><div class="flex items-center space-x-4 svelte-i1g7n4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer svelte-i1g7n4" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);">`);
  Megaphone($$payload, {
    class: "w-6 h-6 animate-pulse",
    style: "color: var(--primary-foreground);"
  });
  $$payload.out.push(`<!----></div> <div class="svelte-i1g7n4"><h1 class="text-3xl font-black svelte-i1g7n4" style="color: var(--foreground);">Catalyst</h1> <p class="text-lg font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">AI-powered company research with competitor analysis and contact
              discovery</p></div></div> <div class="flex items-center space-x-4 svelte-i1g7n4"><div class="flex items-center space-x-2 px-4 py-2 border-2 svelte-i1g7n4" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);">`);
  Share_2($$payload, { class: "w-4 h-4", style: "color: var(--accent-foreground);" });
  $$payload.out.push(`<!----> <span class="text-sm font-bold svelte-i1g7n4" style="color: var(--accent-foreground);">Multi-Channel</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4 svelte-i1g7n4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground svelte-i1g7n4"><a${attr("href", `/dashboard/${stringify(store_get($$store_subs ??= {}, "$page", page).params.envSlug)}`)} class="hover:text-foreground transition-colors svelte-i1g7n4">Dashboard</a> `);
  Chevron_right($$payload, { class: "w-4 h-4" });
  $$payload.out.push(`<!----> <span class="text-foreground font-medium svelte-i1g7n4">Catalyst</span></nav></div> <div class="modern-messages-area svelte-i1g7n4"><div class="modern-message-container svelte-i1g7n4"><div class="space-y-6 svelte-i1g7n4">`);
  if (store_get($$store_subs ??= {}, "$messages", messages).length === 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(interactiveCards);
    $$payload.out.push(`<div class="text-center py-12 svelte-i1g7n4"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2 svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);">`);
    Megaphone($$payload, { class: "w-8 h-8", style: "color: var(--muted-foreground);" });
    $$payload.out.push(`<!----></div> <h3 class="text-xl font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Start Your Company Research</h3> <p class="font-medium mb-6 svelte-i1g7n4" style="color: var(--muted-foreground);">Get comprehensive company intelligence with competitor analysis
              and contacts</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto svelte-i1g7n4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let card = each_array[$$index];
      $$payload.out.push(`<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group svelte-i1g7n4" style="background: var(--card); border-color: var(--border);"${attr("disabled", isLoading, true)}><div class="flex items-center gap-3 mb-3 svelte-i1g7n4"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform svelte-i1g7n4" style="background: var(--primary); border-color: var(--border);"><!---->`);
      card.icon?.($$payload, { class: "w-4 h-4", style: "color: var(--primary-foreground);" });
      $$payload.out.push(`<!----></div> <h4 class="font-bold text-sm svelte-i1g7n4" style="color: var(--foreground);">${escape_html(card.title)}</h4></div> <p class="text-xs mb-3 svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(card.description)}</p> <div class="text-xs font-mono p-2 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);">${escape_html(card.prompt)}</div></button>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <!--[-->`);
  for (let $$index_4 = 0, $$length = each_array_1.length; $$index_4 < $$length; $$index_4++) {
    let message = each_array_1[$$index_4];
    $$payload.out.push(`<div${attr_class(`modern-message-item flex gap-4 ${stringify(message.role === "user" ? "flex-row-reverse" : "")}`, "svelte-i1g7n4")}><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-i1g7n4"${attr_style(`background: var(--${stringify(message.role === "user" ? "primary" : "secondary")}); border-color: var(--border); box-shadow: var(--shadow-sm);`)}>`);
    if (message.role === "user") {
      $$payload.out.push("<!--[-->");
      User($$payload, { class: "w-5 h-5", style: "color: var(--primary-foreground);" });
    } else {
      $$payload.out.push("<!--[!-->");
      Bot($$payload, {
        class: "w-5 h-5",
        style: "color: var(--secondary-foreground);"
      });
    }
    $$payload.out.push(`<!--]--></div> <div class="flex-1 max-w-3xl svelte-i1g7n4"><div class="flex items-center gap-2 mb-2 svelte-i1g7n4"><span class="text-sm font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.role === "user" ? "You" : "Catalyst")}</span> <div class="flex items-center gap-1 svelte-i1g7n4">`);
    Clock($$payload, { class: "w-3 h-3", style: "color: var(--muted-foreground);" });
    $$payload.out.push(`<!----> <span class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(message.timestamp.toLocaleTimeString())}</span></div> `);
    if (message.role === "assistant" && message.isReport) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Download as Markdown">`);
      Download($$payload, { class: "w-3 h-3" });
      $$payload.out.push(`<!----> Download</button>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> `);
    if (message.role === "user") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="p-4 border-2 svelte-i1g7n4" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium svelte-i1g7n4" style="color: var(--primary-foreground);">${escape_html(message.content)}</p></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div class="p-6 border-2 mb-4 svelte-i1g7n4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);">`);
      if (message.data && (message.data.targetCompany || message.data.competitors)) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="space-y-6 svelte-i1g7n4"><div class="text-center border-b-2 pb-4 svelte-i1g7n4" style="border-color: var(--border);"><h2 class="text-2xl font-black mb-2 svelte-i1g7n4" style="color: var(--foreground);">Company Intelligence Report</h2> <p class="text-sm svelte-i1g7n4" style="color: var(--muted-foreground);">Comprehensive analysis with competitor landscape and
                          contact intelligence</p></div> `);
        if (message.data.targetCompany) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border); box-shadow: var(--shadow);"><div class="p-4 svelte-i1g7n4" style="background: var(--primary);"><h3 class="text-lg font-bold flex items-center svelte-i1g7n4" style="color: var(--primary-foreground);">`);
          Megaphone($$payload, { class: "w-5 h-5 mr-2" });
          $$payload.out.push(`<!----> Target Company Profile</h3></div> <div class="p-6 svelte-i1g7n4" style="background: var(--background);"><div class="flex items-start justify-between mb-4 svelte-i1g7n4"><div class="svelte-i1g7n4"><h4 class="text-xl font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.data.targetCompany.name || "Unknown Company")}</h4> <p class="text-sm font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(message.data.targetCompany.domain || "No domain available")}</p></div> `);
          if (message.data.targetCompany.founded_year) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`<div class="text-right svelte-i1g7n4"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Founded</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.data.targetCompany.founded_year)}</div></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 svelte-i1g7n4"><div class="p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs font-medium mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Industry</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.data.targetCompany.industry || "Not specified")}</div></div> <div class="p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs font-medium mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Employees</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.data.targetCompany.employees || "Unknown")}</div></div> <div class="p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs font-medium mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Revenue</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.data.targetCompany.revenue || "Not disclosed")}</div></div></div> `);
          if (message.data.targetCompany.description) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`<div class="p-4 border-2 rounded svelte-i1g7n4" style="background: var(--card); border-color: var(--border);"><h5 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Company Overview</h5> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(message.data.targetCompany.description)}</p></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--> `);
          if (message.data.targetCompany.contacts && message.data.targetCompany.contacts.length > 0) {
            $$payload.out.push("<!--[-->");
            const each_array_2 = ensure_array_like(message.data.targetCompany.contacts);
            $$payload.out.push(`<div class="mt-4 svelte-i1g7n4"><h5 class="font-bold mb-3 flex items-center svelte-i1g7n4" style="color: var(--foreground);">`);
            Users($$payload, { class: "w-4 h-4 mr-2" });
            $$payload.out.push(`<!----> Key Contacts (${escape_html(message.data.targetCompany.contacts.length)})</h5> <div class="grid gap-3 svelte-i1g7n4"><!--[-->`);
            for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
              let contact = each_array_2[$$index_1];
              $$payload.out.push(`<div class="p-3 border-2 rounded flex items-center justify-between svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="svelte-i1g7n4"><div class="font-medium svelte-i1g7n4" style="color: var(--foreground);">${escape_html(contact.name || `${contact.first_name || ""} ${contact.last_name || ""}`.trim() || "Unknown")}</div> <div class="text-sm svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(contact.title || "No title available")}</div></div> `);
              if (contact.email) {
                $$payload.out.push("<!--[-->");
                $$payload.out.push(`<div class="text-xs font-mono p-1 border rounded svelte-i1g7n4" style="background: var(--background); border-color: var(--border); color: var(--muted-foreground);">${escape_html(contact.email)}</div>`);
              } else {
                $$payload.out.push("<!--[!-->");
              }
              $$payload.out.push(`<!--]--></div>`);
            }
            $$payload.out.push(`<!--]--></div></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--></div></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--> `);
        if (message.data.competitors && message.data.competitors.length > 0) {
          $$payload.out.push("<!--[-->");
          const each_array_3 = ensure_array_like(message.data.competitors.slice(0, 8));
          $$payload.out.push(`<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border); box-shadow: var(--shadow);"><div class="p-4 svelte-i1g7n4" style="background: var(--secondary);"><h3 class="text-lg font-bold flex items-center svelte-i1g7n4" style="color: var(--secondary-foreground);">`);
          Users($$payload, { class: "w-5 h-5 mr-2" });
          $$payload.out.push(`<!----> Competitive Landscape (${escape_html(message.data.competitors.length)} companies)</h3></div> <div class="p-6 svelte-i1g7n4" style="background: var(--background);"><div class="grid gap-4 svelte-i1g7n4"><!--[-->`);
          for (let index = 0, $$length2 = each_array_3.length; index < $$length2; index++) {
            let competitor = each_array_3[index];
            $$payload.out.push(`<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border);"><div class="p-4 svelte-i1g7n4"><div class="flex items-start justify-between mb-3 svelte-i1g7n4"><div class="flex-1 svelte-i1g7n4"><div class="flex items-center gap-2 mb-1 svelte-i1g7n4"><span class="text-xs font-bold px-2 py-1 rounded svelte-i1g7n4" style="background: var(--accent); color: var(--accent-foreground);">#${escape_html(index + 1)}</span> <h4 class="text-lg font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(competitor.name || "Unknown Company")}</h4></div> <p class="text-sm font-medium mb-2 svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(competitor.domain || "No domain available")}</p> `);
            if (competitor.description) {
              $$payload.out.push("<!--[-->");
              $$payload.out.push(`<p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(competitor.description.length > 150 ? competitor.description.substring(0, 150) + "..." : competitor.description)}</p>`);
            } else {
              $$payload.out.push("<!--[!-->");
            }
            $$payload.out.push(`<!--]--></div> <div class="text-right ml-4 svelte-i1g7n4"><div class="text-xs mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Similarity</div> <div class="text-sm font-bold px-2 py-1 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border); color: var(--foreground);">${escape_html(competitor.similarity || "Similar")}</div></div></div> <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3 svelte-i1g7n4">`);
            if (competitor.industry) {
              $$payload.out.push("<!--[-->");
              $$payload.out.push(`<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Industry</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);">${escape_html(competitor.industry)}</div></div>`);
            } else {
              $$payload.out.push("<!--[!-->");
            }
            $$payload.out.push(`<!--]--> `);
            if (competitor.employees) {
              $$payload.out.push("<!--[-->");
              $$payload.out.push(`<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Employees</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);">${escape_html(competitor.employees)}</div></div>`);
            } else {
              $$payload.out.push("<!--[!-->");
            }
            $$payload.out.push(`<!--]--> `);
            if (competitor.location) {
              $$payload.out.push("<!--[-->");
              $$payload.out.push(`<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Location</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);">${escape_html(competitor.location)}</div></div>`);
            } else {
              $$payload.out.push("<!--[!-->");
            }
            $$payload.out.push(`<!--]--> `);
            if (competitor.market_position) {
              $$payload.out.push("<!--[-->");
              $$payload.out.push(`<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Position</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);">${escape_html(competitor.market_position)}</div></div>`);
            } else {
              $$payload.out.push("<!--[!-->");
            }
            $$payload.out.push(`<!--]--></div> `);
            if (competitor.contacts && competitor.contacts.length > 0) {
              $$payload.out.push("<!--[-->");
              const each_array_4 = ensure_array_like(competitor.contacts.slice(0, 3));
              $$payload.out.push(`<div class="border-t pt-3 svelte-i1g7n4" style="border-color: var(--border);"><h6 class="text-sm font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Key Contacts (${escape_html(competitor.contacts.length)})</h6> <div class="grid gap-2 svelte-i1g7n4"><!--[-->`);
              for (let $$index_2 = 0, $$length3 = each_array_4.length; $$index_2 < $$length3; $$index_2++) {
                let contact = each_array_4[$$index_2];
                $$payload.out.push(`<div class="flex items-center justify-between p-2 border rounded svelte-i1g7n4" style="background: var(--card); border-color: var(--border);"><div class="svelte-i1g7n4"><div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);">${escape_html(contact.name || `${contact.first_name || ""} ${contact.last_name || ""}`.trim() || "Unknown")}</div> <div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(contact.title || "No title")}</div></div> `);
                if (contact.email) {
                  $$payload.out.push("<!--[-->");
                  $$payload.out.push(`<div class="text-xs font-mono px-2 py-1 border rounded svelte-i1g7n4" style="background: var(--background); border-color: var(--border); color: var(--muted-foreground);">${escape_html(contact.email)}</div>`);
                } else {
                  $$payload.out.push("<!--[!-->");
                }
                $$payload.out.push(`<!--]--></div>`);
              }
              $$payload.out.push(`<!--]--></div></div>`);
            } else {
              $$payload.out.push("<!--[!-->");
            }
            $$payload.out.push(`<!--]--></div></div>`);
          }
          $$payload.out.push(`<!--]--></div></div></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--> `);
        if (message.data.intelligence) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border); box-shadow: var(--shadow);"><div class="p-4 svelte-i1g7n4" style="background: var(--accent);"><h3 class="text-lg font-bold flex items-center svelte-i1g7n4" style="color: var(--accent-foreground);">`);
          Megaphone($$payload, { class: "w-5 h-5 mr-2" });
          $$payload.out.push(`<!----> Market Intelligence Summary</h3></div> <div class="p-6 svelte-i1g7n4" style="background: var(--background);"><div class="grid md:grid-cols-3 gap-6 svelte-i1g7n4">`);
          if (message.data.intelligence.market_insights) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`<div class="svelte-i1g7n4"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Market Insights</h4> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(message.data.intelligence.market_insights)}</p></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--> `);
          if (message.data.intelligence.competitive_landscape) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`<div class="svelte-i1g7n4"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Competitive Landscape</h4> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(message.data.intelligence.competitive_landscape)}</p></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--> `);
          if (message.data.intelligence.key_differentiators) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`<div class="svelte-i1g7n4"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Key Differentiators</h4> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(message.data.intelligence.key_differentiators)}</p></div>`);
          } else {
            $$payload.out.push("<!--[!-->");
          }
          $$payload.out.push(`<!--]--></div></div></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--> `);
        if (message.data.metadata) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<div class="border-2 rounded-lg p-4 svelte-i1g7n4" style="border-color: var(--border); background: var(--muted);"><h4 class="font-bold mb-3 svelte-i1g7n4" style="color: var(--foreground);">Research Summary</h4> <div class="grid grid-cols-2 md:grid-cols-4 gap-4 svelte-i1g7n4"><div class="text-center svelte-i1g7n4"><div class="text-2xl font-black mb-1 svelte-i1g7n4" style="color: var(--primary);">${escape_html(message.data.metadata.totalCompanies || 0)}</div> <div class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Companies Analyzed</div></div> <div class="text-center svelte-i1g7n4"><div class="text-2xl font-black mb-1 svelte-i1g7n4" style="color: var(--primary);">${escape_html(message.data.metadata.totalContacts || 0)}</div> <div class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Contacts Found</div></div> <div class="text-center svelte-i1g7n4"><div class="text-sm font-bold px-2 py-1 border rounded svelte-i1g7n4" style="background: var(--background); border-color: var(--border); color: var(--foreground);">${escape_html(message.data.metadata.dataQuality || "N/A")}</div> <div class="text-xs font-medium mt-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Data Quality</div></div> <div class="text-center svelte-i1g7n4"><div class="text-sm font-bold svelte-i1g7n4" style="color: var(--foreground);">${escape_html(message.data.metadata.processingTime || "N/A")}</div> <div class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Processing Time</div></div></div></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--> <div class="border-t-2 pt-4 text-center svelte-i1g7n4" style="border-color: var(--border);"><div class="flex items-center justify-center gap-2 mb-2 svelte-i1g7n4">`);
        Megaphone($$payload, { class: "w-4 h-4", style: "color: var(--primary);" });
        $$payload.out.push(`<!----> <span class="font-bold svelte-i1g7n4" style="color: var(--foreground);">Catalyst Intelligence Report</span></div> <p class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Generated on ${escape_html(message.timestamp.toLocaleDateString())}
                          at ${escape_html(message.timestamp.toLocaleTimeString())}
                          • Powered by Apollo &amp; Exa APIs</p></div></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="prose prose-sm max-w-none svelte-i1g7n4">${html(formatContent(message.content))}</div> `);
        if (message.data && !message.data.targetCompany && !message.data.competitors) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<div class="mt-4 p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Debug: Raw Data</h4> <pre class="text-xs overflow-auto svelte-i1g7n4" style="color: var(--muted-foreground);">${escape_html(JSON.stringify(message.data, null, 2))}</pre></div>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]--></div> <div class="flex flex-wrap gap-2 mb-4 svelte-i1g7n4"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Copy research report to clipboard">`);
      Copy($$payload, { class: "w-3 h-3" });
      $$payload.out.push(`<!----> Copy Report</button> `);
      if (message.isReport) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Download as Markdown file">`);
        Download($$payload, { class: "w-3 h-3" });
        $$payload.out.push(`<!----> Download</button>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Export contact list">`);
      Users($$payload, { class: "w-3 h-3" });
      $$payload.out.push(`<!----> Export Contacts</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Share research findings">`);
      Mail($$payload, { class: "w-3 h-3" });
      $$payload.out.push(`<!----> Share Report</button></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div></div></div> <div${attr_class(`modern-input-area modern-input-area-seo ${stringify("")}`, "svelte-i1g7n4")}><div class="max-w-4xl mx-auto svelte-i1g7n4"><div class="flex svelte-i1g7n4" style="gap: 15px;"><div class="flex-1 relative svelte-i1g7n4"><textarea${attr("placeholder", currentPlaceholder)} class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full svelte-i1g7n4" style="min-height: 60px;"${attr("disabled", isLoading, true)}>`);
  const $$body = escape_html(input);
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea></div> <button${attr("disabled", !input.trim() || isLoading, true)} class="btn-primary px-6 font-bold flex items-center gap-2 svelte-i1g7n4" style="height: auto; align-self: stretch;">`);
  {
    $$payload.out.push("<!--[!-->");
    Megaphone($$payload, { class: "w-4 h-4 animate-pulse" });
  }
  $$payload.out.push(`<!--]--> Research</button></div></div></div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
