import { h as head, p as pop, a as push } from "../../../../../../../../chunks/index2.js";
import { c as changePasswordSchema } from "../../../../../../../../chunks/schemas.js";
import { S as Settings_module } from "../../../../../../../../chunks/settings_module.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  console.log({ ...data.session });
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Reset Password</title>`;
  });
  $$payload.out.push(`<h1 class="text-2xl font-bold mb-6">Settings</h1> `);
  Settings_module($$payload, {
    data: data.form,
    schema: changePasswordSchema,
    title: "Reset Password",
    editable: true,
    saveButtonTitle: "Reset Password",
    successTitle: "Password Changed",
    successBody: "On next sign in, use your new password.",
    formTarget: "/api?/updatePassword",
    fields: [
      {
        id: "newPassword1",
        label: "New Password",
        initialValue: "",
        inputType: "password"
      },
      {
        id: "newPassword2",
        label: "Confirm New Password",
        initialValue: "",
        inputType: "password"
      }
    ]
  });
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _page as default
};
