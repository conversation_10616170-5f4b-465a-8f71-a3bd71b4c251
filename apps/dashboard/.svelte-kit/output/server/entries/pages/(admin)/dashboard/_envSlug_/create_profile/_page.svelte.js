import { w as copy_payload, x as assign_payload, p as pop, a as push, h as head, y as invalid_default_snippet, o as spread_props, z as store_mutate, s as store_get, e as escape_html, u as unsubscribe_stores } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/formData.js";
import { a as zodClient } from "../../../../../../chunks/zod.js";
import "@sveltejs/kit/internal";
import "../../../../../../chunks/exports.js";
import "clsx";
import "../../../../../../chunks/state.svelte.js";
import { s as superForm } from "../../../../../../chunks/superForm.js";
import "@sveltejs/kit";
import { I as Input } from "../../../../../../chunks/input.js";
import "../../../../../../chunks/index6.js";
import { F as Form_field, C as Control, a as Form_label, b as Form_field_errors } from "../../../../../../chunks/index8.js";
import { p as profileSchema } from "../../../../../../chunks/schemas.js";
import { B as Button } from "../../../../../../chunks/button.js";
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  let { session } = data;
  const form = superForm(data.form, { validators: zodClient(profileSchema) });
  const { enhance, form: formData, delayed, errors } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>Create Profile</title>`;
    });
    $$payload2.out.push(`<div class="text-center content-center max-w-lg mx-auto min-h-[100vh] pb-12 flex items-center place-content-center"><div class="flex flex-col w-64 lg:w-80"><div><h1 class="text-2xl font-bold mb-6">Create Profile</h1> <form class="grid gap-4" method="POST" action="/account/api?/updateProfile"><!---->`);
    Form_field($$payload2, {
      form,
      name: "full_name",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Control($$payload3, {
          children: invalid_default_snippet,
          $$slots: {
            default: ($$payload4, { attrs }) => {
              $$payload4.out.push(`<!---->`);
              Form_label($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Your Name`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> `);
              Input($$payload4, spread_props([
                attrs,
                {
                  get value() {
                    return store_get($$store_subs ??= {}, "$formData", formData).full_name;
                  },
                  set value($$value) {
                    store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).full_name = $$value);
                    $$settled = false;
                  }
                }
              ]));
              $$payload4.out.push(`<!----> <!---->`);
              Form_field_errors($$payload4, {});
              $$payload4.out.push(`<!---->`);
            }
          }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----> <!---->`);
    Form_field($$payload2, {
      form,
      name: "company_name",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Control($$payload3, {
          children: invalid_default_snippet,
          $$slots: {
            default: ($$payload4, { attrs }) => {
              $$payload4.out.push(`<!---->`);
              Form_label($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Company Name`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> `);
              Input($$payload4, spread_props([
                attrs,
                {
                  get value() {
                    return store_get($$store_subs ??= {}, "$formData", formData).company_name;
                  },
                  set value($$value) {
                    store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).company_name = $$value);
                    $$settled = false;
                  }
                }
              ]));
              $$payload4.out.push(`<!----> <!---->`);
              Form_field_errors($$payload4, {});
              $$payload4.out.push(`<!---->`);
            }
          }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----> <!---->`);
    Form_field($$payload2, {
      form,
      name: "website",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Control($$payload3, {
          children: invalid_default_snippet,
          $$slots: {
            default: ($$payload4, { attrs }) => {
              $$payload4.out.push(`<!---->`);
              Form_label($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Company Website`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> `);
              Input($$payload4, spread_props([
                attrs,
                {
                  get value() {
                    return store_get($$store_subs ??= {}, "$formData", formData).website;
                  },
                  set value($$value) {
                    store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).website = $$value);
                    $$settled = false;
                  }
                }
              ]));
              $$payload4.out.push(`<!----> <!---->`);
              Form_field_errors($$payload4, {});
              $$payload4.out.push(`<!---->`);
            }
          }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----> `);
    if (store_get($$store_subs ??= {}, "$errors", errors)._errors) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<p class="text-destructive text-sm font-bold text-center mt-3">${escape_html(store_get($$store_subs ??= {}, "$errors", errors)._errors[0])}</p>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> <div class="mt-4">`);
    Button($$payload2, {
      type: "submit",
      class: "mt-3",
      disabled: store_get($$store_subs ??= {}, "$delayed", delayed),
      children: ($$payload3) => {
        if (store_get($$store_subs ??= {}, "$delayed", delayed)) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`...`);
        } else {
          $$payload3.out.push("<!--[!-->");
          $$payload3.out.push(`Create Profile`);
        }
        $$payload3.out.push(`<!--]-->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></div></form> <div class="text-sm mt-14">You are logged in as ${escape_html(session?.user?.email ?? "an anonymous user")}. <br/> <a class="underline" href="/account/sign_out">Sign out</a></div></div></div></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
