import { redirect, fail } from "@sveltejs/kit";
import { s as sendAdminEmail, a as sendUserEmail } from "../../../../chunks/mailer.js";
import "../../../../chunks/formData.js";
import { z as zod } from "../../../../chunks/zod.js";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/superForm.js";
import { s as superValidate, a as setError } from "../../../../chunks/superValidate.js";
import { p as profileSchema, d as deleteAccountSchema, c as changePasswordSchema, e as emailSchema } from "../../../../chunks/schemas.js";
const actions = {
  toggleEmailSubscription: async ({ locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
      redirect(303, "/login");
    }
    const { data: currentProfile } = await supabase.from("profiles").select("unsubscribed").eq("id", session.user.id).single();
    const newUnsubscribedStatus = !currentProfile?.unsubscribed;
    const { error } = await supabase.from("profiles").update({ unsubscribed: newUnsubscribedStatus }).eq("id", session.user.id);
    if (error) {
      return fail(500, { message: "Failed to update subscription status" });
    }
    return {
      unsubscribed: newUnsubscribedStatus
    };
  },
  updateEmail: async ({ request, locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
      redirect(303, "/login");
    }
    const form = await superValidate(request, zod(emailSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const { error } = await supabase.auth.updateUser({ email: form.data.email });
    if (error) {
      console.error(error);
      return setError(
        form,
        "Unknown error. If this persists please contact us.",
        {
          status: 500
        }
      );
    }
    redirect(
      303,
      `/login/check_email?email=${form.data.email}&type=email_change`
    );
  },
  updatePassword: async ({ request, locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
      redirect(303, "/login");
    }
    const form = await superValidate(request, zod(changePasswordSchema));
    const isRecoverySession = session.user.recovery_sent_at && !form.data.currentPassword;
    if (isRecoverySession) {
      const timeSinceLogin = Date.now() - Date.parse(session.user.recovery_sent_at) * 1e3;
      if (timeSinceLogin > 1e3 * 60 * 15) {
        return setError(
          form,
          'Recovery code expired. Please log out, then use "Forgot Password" on the sign in page to reset your password. Codes are valid for 15 minutes.',
          {
            status: 400
          }
        );
      }
    }
    if (!form.valid) {
      return fail(400, { form });
    }
    if (!isRecoverySession) {
      const { error: error2 } = await supabase.auth.signInWithPassword({
        email: session?.user.email || "",
        password: form.data.currentPassword
      });
      if (error2) {
        await supabase.auth.signOut();
        redirect(303, "/login/current_password_error");
      }
    }
    const { error } = await supabase.auth.updateUser({
      email: session.user.email,
      password: form.data.newPassword1,
      data: {
        hasPassword: true
      }
    });
    if (error) {
      console.log(error);
      return setError(
        form,
        "Unknown error. If this persists please contact us.",
        { status: 500 }
      );
    }
    return {
      form
    };
  },
  deleteAccount: async ({
    request,
    locals: { supabase, supabaseServiceRole, safeGetSession }
  }) => {
    const { session } = await safeGetSession();
    if (!session) {
      redirect(303, "/login");
    }
    const form = await superValidate(request, zod(deleteAccountSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const { error: pwError } = await supabase.auth.signInWithPassword({
      email: session?.user.email || "",
      password: form.data.currentPassword
    });
    if (pwError) {
      redirect(303, "/login/current_password_error");
    }
    const { error } = await supabaseServiceRole.auth.admin.deleteUser(
      session.user.id,
      true
    );
    if (error) {
      return setError(
        form,
        "Unknown error. If this persists please contact us.",
        {
          status: 500
        }
      );
    }
    await supabase.auth.signOut();
    redirect(303, "/");
  },
  updateProfile: async ({ request, locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
      redirect(303, "/login");
    }
    const form = await superValidate(request, zod(profileSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const { data: priorProfile, error: priorProfileError } = await supabase.from("profiles").select(`*`).eq("id", session?.user.id).single();
    const { error } = await supabase.from("profiles").upsert({
      ...form.data,
      id: session?.user.id,
      unsubscribed: priorProfile?.unsubscribed ?? false
    }).select();
    if (error) {
      return setError(
        form,
        "Unknown error. If this persists please contact us.",
        {
          status: 500
        }
      );
    }
    const newProfile = priorProfile?.updated_at === null && priorProfileError === null;
    if (newProfile) {
      await sendAdminEmail({
        subject: "Profile Created",
        body: `Profile created by ${session.user.email}
Full name: ${form.data.full_name}
Company name: ${form.data.company_name}
Website: ${form.data.website}`
      });
      await sendUserEmail({
        user: session.user,
        subject: "Welcome!",
        from_email: "<EMAIL>",
        template_name: "welcome_email",
        template_properties: {
          companyName: "Full Stack Starter Pack"
        }
      });
    }
    return {
      form
    };
  },
  signout: async ({ locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (session) {
      await supabase.auth.signOut();
      redirect(303, "/");
    } else {
      redirect(303, "/");
    }
  }
};
export {
  actions
};
