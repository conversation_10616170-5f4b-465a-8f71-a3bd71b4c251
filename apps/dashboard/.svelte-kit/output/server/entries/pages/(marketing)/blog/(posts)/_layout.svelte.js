import { s as store_get, h as head, e as escape_html, b as slot, u as unsubscribe_stores, p as pop, a as push, c as attr } from "../../../../../chunks/index2.js";
import { p as page } from "../../../../../chunks/stores.js";
import { error } from "@sveltejs/kit";
import { s as sortedBlogPosts } from "../../../../../chunks/posts.js";
import { W as WebsiteName } from "../../../../../chunks/config.js";
import { h as html } from "../../../../../chunks/html.js";
function _layout($$payload, $$props) {
  push();
  var $$store_subs;
  let currentPost, jsonldScript, pageUrl;
  function getCurrentPost(url) {
    let searchPost = null;
    for (const post of sortedBlogPosts) {
      if (url == post.link || url == post.link + "/") {
        searchPost = post;
        continue;
      }
    }
    if (!searchPost) {
      error(404, "Blog post not found");
    }
    return searchPost;
  }
  function buildLdJson(post) {
    return {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      headline: post.title,
      datePublished: post.parsedDate?.toISOString(),
      dateModified: post.parsedDate?.toISOString()
    };
  }
  currentPost = getCurrentPost(store_get($$store_subs ??= {}, "$page", page).url.pathname);
  jsonldScript = `<script type="application/ld+json">${JSON.stringify(buildLdJson(currentPost)) + "<"}/script>`;
  pageUrl = store_get($$store_subs ??= {}, "$page", page).url.origin + store_get($$store_subs ??= {}, "$page", page).url.pathname;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(currentPost.title)}</title>`;
    $$payload2.out.push(`<meta name="description"${attr("content", currentPost.description)}/> <meta property="og:title"${attr("content", currentPost.title)}/> <meta property="og:description"${attr("content", currentPost.description)}/> <meta property="og:site_name"${attr("content", WebsiteName)}/> <meta property="og:url"${attr("content", pageUrl)}/>  <meta name="twitter:card" content="summary"/> <meta name="twitter:title"${attr("content", currentPost.title)}/> <meta name="twitter:description"${attr("content", currentPost.description)}/>  ${html(jsonldScript)}`);
  });
  $$payload.out.push(`<article class="prose mx-auto py-12 px-6 font-sans"><div class="text-sm text-accent">${escape_html(currentPost.parsedDate?.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" }))}</div> <h1>${escape_html(currentPost.title)}</h1> <!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></article>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _layout as default
};
