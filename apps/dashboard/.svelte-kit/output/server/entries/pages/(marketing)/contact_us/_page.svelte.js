import { m as sanitize_props, C as rest_props, a as push, A as fallback, D as spread_attributes, v as clsx, e as escape_html, B as bind_props, p as pop, w as copy_payload, x as assign_payload, y as invalid_default_snippet, o as spread_props, z as store_mutate, s as store_get, u as unsubscribe_stores } from "../../../../chunks/index2.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "clsx";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/formData.js";
import { s as superForm } from "../../../../chunks/superForm.js";
import "@sveltejs/kit";
import { a as zodClient } from "../../../../chunks/zod.js";
import { I as Input } from "../../../../chunks/input.js";
import { c as cn } from "../../../../chunks/utils.js";
import "../../../../chunks/index6.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { F as Form_field, C as Control, a as Form_label, b as Form_field_errors } from "../../../../chunks/index8.js";
import { a as contactSchema } from "../../../../chunks/schemas.js";
import { B as Button } from "../../../../chunks/button.js";
function Textarea($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "value", "readonly"]);
  push();
  let className = fallback($$props["class"], void 0);
  let value = fallback($$props["value"], void 0);
  let readonly = fallback($$props["readonly"], void 0);
  $$payload.out.push(`<textarea${spread_attributes(
    {
      class: clsx(cn("border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[80px] w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className)),
      readonly,
      ...$$restProps
    },
    null
  )}>`);
  const $$body = escape_html(value);
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea>`);
  bind_props($$props, { class: className, value, readonly });
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  const form = superForm(data.form, {
    validators: zodClient(contactSchema),
    onUpdated: ({ form: f }) => {
      if (f.valid) {
        showSuccess = true;
      }
    }
  });
  const { form: formData, enhance, errors, delayed } = form;
  let showSuccess = false;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<div class="flex flex-col lg:flex-row mx-auto my-4 min-h-[70vh] place-items-center lg:place-items-start place-content-center"><div class="max-w-[400px] lg:max-w-[500px] flex flex-col place-content-center p-4 lg:mr-8 lg:mb-8 lg:min-h-[70vh]"><div class="px-6"><h1 class="text-2xl lg:text-4xl font-bold mb-4">Contact Us</h1> <p class="text-lg">Talk to one of augmented marketers:</p> <ul class="list-disc list-outside pl-6 py-4 space-y-1"><li>See a live marketer agent in action</li> <li>Discuss your specific needs</li> <li>Get a quote</li> <li>Get answers for your technical questions</li></ul> <p>Once you submit the form, we'll reach out to you!</p></div></div> <div class="flex flex-col flex-grow m-4 lg:ml-10 min-w-[300px] stdphone:min-w-[360px] max-w-[400px] place-content-center lg:min-h-[70vh]">`);
    if (showSuccess) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="flex flex-col place-content-center lg:min-h-[70vh]"><div class="card card-bordered shadow-lg py-6 px-6 mx-2 lg:mx-0 lg:p-6 mb-10"><div class="text-2xl font-bold mb-4">Thank you!</div> <p>We've received your message and will be in touch soon.</p></div></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<!---->`);
      Card($$payload2, {
        class: "shadow-lg pt-6 mx-2 lg:mx-0 lg:p-6",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<form class="flex flex-col" method="POST" action="?/submitContactUs"><!---->`);
              Form_field($$payload4, {
                form,
                name: "first_name",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Control($$payload5, {
                    children: invalid_default_snippet,
                    $$slots: {
                      default: ($$payload6, { attrs }) => {
                        $$payload6.out.push(`<!---->`);
                        Form_label($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->First Name *`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Input($$payload6, spread_props([
                          attrs,
                          {
                            autocomplete: "given-name",
                            get value() {
                              return store_get($$store_subs ??= {}, "$formData", formData).first_name;
                            },
                            set value($$value) {
                              store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).first_name = $$value);
                              $$settled = false;
                            }
                          }
                        ]));
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      }
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Form_field($$payload4, {
                form,
                name: "last_name",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Control($$payload5, {
                    children: invalid_default_snippet,
                    $$slots: {
                      default: ($$payload6, { attrs }) => {
                        $$payload6.out.push(`<!---->`);
                        Form_label($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->Last Name *`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Input($$payload6, spread_props([
                          attrs,
                          {
                            autocomplete: "family-name",
                            get value() {
                              return store_get($$store_subs ??= {}, "$formData", formData).last_name;
                            },
                            set value($$value) {
                              store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).last_name = $$value);
                              $$settled = false;
                            }
                          }
                        ]));
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      }
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Form_field($$payload4, {
                form,
                name: "email",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Control($$payload5, {
                    children: invalid_default_snippet,
                    $$slots: {
                      default: ($$payload6, { attrs }) => {
                        $$payload6.out.push(`<!---->`);
                        Form_label($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->Email *`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Input($$payload6, spread_props([
                          attrs,
                          {
                            autocomplete: "email",
                            get value() {
                              return store_get($$store_subs ??= {}, "$formData", formData).email;
                            },
                            set value($$value) {
                              store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).email = $$value);
                              $$settled = false;
                            }
                          }
                        ]));
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      }
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Form_field($$payload4, {
                form,
                name: "phone",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Control($$payload5, {
                    children: invalid_default_snippet,
                    $$slots: {
                      default: ($$payload6, { attrs }) => {
                        $$payload6.out.push(`<!---->`);
                        Form_label($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->Phone`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Input($$payload6, spread_props([
                          attrs,
                          {
                            inputmode: "tel",
                            autocomplete: "tel",
                            get value() {
                              return store_get($$store_subs ??= {}, "$formData", formData).phone;
                            },
                            set value($$value) {
                              store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).phone = $$value);
                              $$settled = false;
                            }
                          }
                        ]));
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      }
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Form_field($$payload4, {
                form,
                name: "company_name",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Control($$payload5, {
                    children: invalid_default_snippet,
                    $$slots: {
                      default: ($$payload6, { attrs }) => {
                        $$payload6.out.push(`<!---->`);
                        Form_label($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->Company`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Input($$payload6, spread_props([
                          attrs,
                          {
                            autocomplete: "organization",
                            get value() {
                              return store_get($$store_subs ??= {}, "$formData", formData).company_name;
                            },
                            set value($$value) {
                              store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).company_name = $$value);
                              $$settled = false;
                            }
                          }
                        ]));
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      }
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Form_field($$payload4, {
                form,
                name: "message_body",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Control($$payload5, {
                    children: invalid_default_snippet,
                    $$slots: {
                      default: ($$payload6, { attrs }) => {
                        $$payload6.out.push(`<!---->`);
                        Form_label($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->Message`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Textarea($$payload6, spread_props([
                          attrs,
                          {
                            get value() {
                              return store_get($$store_subs ??= {}, "$formData", formData).message_body;
                            },
                            set value($$value) {
                              store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).message_body = $$value);
                              $$settled = false;
                            }
                          }
                        ]));
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      }
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> `);
              if (store_get($$store_subs ??= {}, "$errors", errors)._errors) {
                $$payload4.out.push("<!--[-->");
                $$payload4.out.push(`<p class="text-destructive text-sm mb-2">${escape_html(store_get($$store_subs ??= {}, "$errors", errors)._errors[0])}</p>`);
              } else {
                $$payload4.out.push("<!--[!-->");
              }
              $$payload4.out.push(`<!--]--> `);
              Button($$payload4, {
                disabled: store_get($$store_subs ??= {}, "$delayed", delayed),
                type: "submit",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->${escape_html(store_get($$store_subs ??= {}, "$delayed", delayed) ? "Submitting" : "Submit")}`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----></form>`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    }
    $$payload2.out.push(`<!--]--></div></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
