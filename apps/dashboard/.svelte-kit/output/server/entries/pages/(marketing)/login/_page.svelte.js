import { h as head, d as attr_class, v as clsx, p as pop, a as push } from "../../../../chunks/index2.js";
import { b as buttonVariants } from "../../../../chunks/index6.js";
function _page($$payload, $$props) {
  push();
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Log In</title>`;
  });
  $$payload.out.push(`<div><h1 class="text-xl font-bold mb-2">Get Started</h1> <a href="/login/sign_up"${attr_class(clsx(buttonVariants({ size: "lg" })))}>Sign Up</a> <h1 class="text-xl mt-6 mb-2">Already have an account?</h1> <a href="/login/sign_in"${attr_class(clsx(buttonVariants({ size: "lg", variant: "outline" })))}>Sign In</a></div>`);
  pop();
}
export {
  _page as default
};
