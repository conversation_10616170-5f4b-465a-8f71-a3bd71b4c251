import { t as ensure_array_like, h as head, e as escape_html, c as attr, p as pop, a as push } from "../../../../chunks/index2.js";
import { s as sortedBlogPosts, b as blogInfo } from "../../../../chunks/posts.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import "clsx";
function _page($$payload, $$props) {
  push();
  const each_array = ensure_array_like(sortedBlogPosts);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(blogInfo.name)}</title>`;
    $$payload2.out.push(`<meta name="description" content="Our blog posts."/>`);
  });
  $$payload.out.push(`<div class="py-8 lg:py-12 px-6 max-w-lg mx-auto"><div class="text-3xl lg:text-5xl font-medium text-primary flex gap-3 items-baseline text-center place-content-center"><div class="text-center leading-relaxed font-bold text-primary">${escape_html(blogInfo.name)}</div> <a href="/blog/rss.xml" target="_blank" rel="noreferrer"><img class="flex-none w-5 h-5 object-contain" src="/images/rss.svg" alt="rss feed"/></a></div> <div class="text-lg text-center">A demo blog with sample content.</div> <!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let post = each_array[$$index];
    $$payload.out.push(`<a${attr("href", post.link)}>`);
    Card($$payload, {
      class: "my-6",
      children: ($$payload2) => {
        Card_content($$payload2, {
          class: "shadow-xl p-6 flex flex-row overflow-hidden",
          children: ($$payload3) => {
            $$payload3.out.push(`<div class="flex-none w-6 md:w-32 bg-secondary"></div> <div class="py-6 px-6"><div class="text-xl">${escape_html(post.title)}</div> <div class="text-sm text-accent">${escape_html(post.parsedDate?.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" }))}</div> <div class="text-muted-foreground">${escape_html(post.description)}</div></div>`);
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload.out.push(`<!----></a>`);
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
export {
  _page as default
};
