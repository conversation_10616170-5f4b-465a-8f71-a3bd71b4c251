import { m as sanitize_props, o as spread_props, b as slot, C as rest_props, a as push, A as fallback, D as spread_attributes, v as clsx, B as bind_props, p as pop, d as attr_class, e as escape_html, c as attr, t as ensure_array_like, F as attr_style, f as stringify, w as copy_payload, x as assign_payload, h as head, y as invalid_default_snippet, s as store_get, z as store_mutate, u as unsubscribe_stores } from "../../../../../chunks/index2.js";
import { c as Circle_alert, F as Form_field, C as Control, a as Form_label, b as Form_field_errors } from "../../../../../chunks/index8.js";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../../chunks/card-title.js";
import { c as cn } from "../../../../../chunks/utils.js";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "clsx";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/formData.js";
import { s as superForm } from "../../../../../chunks/superForm.js";
import "@sveltejs/kit";
import { a as zodClient } from "../../../../../chunks/zod.js";
import { s as signUpSchema } from "../../../../../chunks/schemas.js";
import { I as Input } from "../../../../../chunks/input.js";
import "../../../../../chunks/index6.js";
import { I as Icon } from "../../../../../chunks/Icon.js";
import { C as Circle_check } from "../../../../../chunks/circle-check.js";
import { M as Mail } from "../../../../../chunks/mail.js";
import { E as Eye } from "../../../../../chunks/eye.js";
import { B as Button } from "../../../../../chunks/button.js";
function Circle_x($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "10" }],
    ["path", { "d": "m15 9-6 6" }],
    ["path", { "d": "m9 9 6 6" }]
  ];
  Icon($$payload, spread_props([
    { name: "circle-x" },
    $$sanitized_props,
    {
      /**
       * @component @name CircleX
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Eye_off($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    [
      "path",
      {
        "d": "M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"
      }
    ],
    ["path", { "d": "M14.084 14.158a3 3 0 0 1-4.242-4.242" }],
    [
      "path",
      {
        "d": "M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"
      }
    ],
    ["path", { "d": "m2 2 20 20" }]
  ];
  Icon($$payload, spread_props([
    { name: "eye-off" },
    $$sanitized_props,
    {
      /**
       * @component @name EyeOff
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNzMzIDUuMDc2YTEwLjc0NCAxMC43NDQgMCAwIDEgMTEuMjA1IDYuNTc1IDEgMSAwIDAgMSAwIC42OTYgMTAuNzQ3IDEwLjc0NyAwIDAgMS0xLjQ0NCAyLjQ5IiAvPgogIDxwYXRoIGQ9Ik0xNC4wODQgMTQuMTU4YTMgMyAwIDAgMS00LjI0Mi00LjI0MiIgLz4KICA8cGF0aCBkPSJNMTcuNDc5IDE3LjQ5OWExMC43NSAxMC43NSAwIDAgMS0xNS40MTctNS4xNTEgMSAxIDAgMCAxIDAtLjY5NiAxMC43NSAxMC43NSAwIDAgMSA0LjQ0Ni01LjE0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-off
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Info($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "10" }],
    ["path", { "d": "M12 16v-4" }],
    ["path", { "d": "M12 8h.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "info" },
    $$sanitized_props,
    {
      /**
       * @component @name Info
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMTZ2LTQiIC8+CiAgPHBhdGggZD0iTTEyIDhoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/info
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
function Card_footer($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  let className = fallback($$props["class"], void 0);
  $$payload.out.push(`<div${spread_attributes(
    {
      class: clsx(cn("flex items-center p-6 pt-0", className)),
      ...$$restProps
    },
    null
  )}><!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { class: className });
  pop();
}
function PasswordStrengthIndicator($$payload, $$props) {
  push();
  let lengthCheck, hasLowercase, hasUppercase, hasDigit, checks, metRequirements, isValid, strengthLevel, strengthColor, progressColor, charProgress, charCountColor;
  let password = fallback($$props["password"], "");
  let showRequirements = fallback($$props["showRequirements"], true);
  function getIcon(met, pending = false) {
    if (pending) return Circle_alert;
    return met ? Circle_check : Circle_x;
  }
  function getIconColor(met, pending = false) {
    if (pending) return "text-base-content/30";
    return met ? "text-success" : "text-base-content/30";
  }
  lengthCheck = password.length >= 8 && password.length <= 16;
  hasLowercase = /[a-z]/.test(password);
  hasUppercase = /[A-Z]/.test(password);
  hasDigit = /\d/.test(password);
  checks = [lengthCheck, hasLowercase, hasUppercase, hasDigit];
  metRequirements = checks.filter(Boolean).length;
  isValid = metRequirements === 4;
  strengthLevel = metRequirements === 0 ? "empty" : metRequirements === 1 ? "weak" : metRequirements === 2 ? "fair" : metRequirements === 3 ? "good" : "strong";
  strengthColor = strengthLevel === "empty" ? "text-base-content/50" : strengthLevel === "weak" ? "text-error" : strengthLevel === "fair" ? "text-warning" : strengthLevel === "good" ? "text-info" : "text-success";
  progressColor = strengthLevel === "empty" ? "progress-base-200" : strengthLevel === "weak" ? "progress-error" : strengthLevel === "fair" ? "progress-warning" : strengthLevel === "good" ? "progress-info" : "progress-success";
  charProgress = Math.min(password.length / 16 * 100, 100);
  charCountColor = password.length === 0 ? "text-base-content/50" : password.length < 8 ? "text-error" : password.length <= 16 ? "text-success" : "text-error";
  $$payload.out.push(`<div class="space-y-3 svelte-1dxq2jv"><div class="space-y-1 svelte-1dxq2jv"><div class="flex items-center justify-between text-sm svelte-1dxq2jv"><span${attr_class(`font-medium ${stringify(charCountColor)} transition-colors duration-200`, "svelte-1dxq2jv")}>Character length</span> <span${attr_class(`${stringify(charCountColor)} tabular-nums transition-colors duration-200`, "svelte-1dxq2jv")}>${escape_html(password.length)}/16</span></div> <div class="relative svelte-1dxq2jv"><progress${attr_class(`progress ${stringify(progressColor)} h-2 transition-all duration-300`, "svelte-1dxq2jv")}${attr("value", charProgress)} max="100"></progress> <div class="absolute top-0 left-[50%] w-px h-2 bg-base-content/20 svelte-1dxq2jv"></div> <div class="absolute -bottom-4 left-[50%] -translate-x-1/2 text-xs text-base-content/50 svelte-1dxq2jv">8</div></div></div> `);
  if (password.length > 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(Array(4));
    $$payload.out.push(`<div class="flex items-center justify-between svelte-1dxq2jv"><span${attr_class(`text-sm font-medium ${stringify(strengthColor)} transition-colors duration-200`, "svelte-1dxq2jv")}>Password strength</span> <div class="flex items-center gap-1 svelte-1dxq2jv"><!--[-->`);
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      each_array[i];
      $$payload.out.push(`<div${attr_class(`h-1 w-8 rounded-full transition-all duration-300 ${stringify(i < metRequirements ? "bg-current" : "bg-base-300")}`, "svelte-1dxq2jv", {
        "opacity-100": i < metRequirements,
        "opacity-30": i >= metRequirements
      })}${attr_style(`color: var(--${stringify(strengthLevel === "weak" ? "er" : strengthLevel === "fair" ? "wa" : strengthLevel === "good" ? "in" : "su")})`)}></div>`);
    }
    $$payload.out.push(`<!--]--> <span${attr_class(`ml-2 text-sm ${stringify(strengthColor)} font-medium capitalize transition-colors duration-200`, "svelte-1dxq2jv")}>${escape_html(strengthLevel)}</span></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (showRequirements && (password.length > 0 || showRequirements === "always")) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="space-y-1.5 p-3 bg-base-200/50 rounded-lg svelte-1dxq2jv"><div class="flex items-center gap-2 text-sm font-medium text-base-content/70 mb-2 svelte-1dxq2jv">`);
    Info($$payload, { size: 14 });
    $$payload.out.push(`<!----> <span class="svelte-1dxq2jv">Password requirements</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!---->`);
    getIcon(lengthCheck, password.length === 0)?.($$payload, {
      size: 16,
      class: getIconColor(lengthCheck, password.length === 0)
    });
    $$payload.out.push(`<!----> <span${attr_class(`${stringify(lengthCheck ? "text-success" : "text-base-content/70")} transition-colors duration-200`, "svelte-1dxq2jv")}>8-16 characters</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!---->`);
    getIcon(hasLowercase, password.length === 0)?.($$payload, {
      size: 16,
      class: getIconColor(hasLowercase, password.length === 0)
    });
    $$payload.out.push(`<!----> <span${attr_class(`${stringify(hasLowercase ? "text-success" : "text-base-content/70")} transition-colors duration-200`, "svelte-1dxq2jv")}>One lowercase letter</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!---->`);
    getIcon(hasUppercase, password.length === 0)?.($$payload, {
      size: 16,
      class: getIconColor(hasUppercase, password.length === 0)
    });
    $$payload.out.push(`<!----> <span${attr_class(`${stringify(hasUppercase ? "text-success" : "text-base-content/70")} transition-colors duration-200`, "svelte-1dxq2jv")}>One uppercase letter</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!---->`);
    getIcon(hasDigit, password.length === 0)?.($$payload, {
      size: 16,
      class: getIconColor(hasDigit, password.length === 0)
    });
    $$payload.out.push(`<!----> <span${attr_class(`${stringify(hasDigit ? "text-success" : "text-base-content/70")} transition-colors duration-200`, "svelte-1dxq2jv")}>One number</span></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (isValid) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="flex items-center gap-2 text-sm text-success svelte-1dxq2jv">`);
    Circle_check($$payload, { size: 16 });
    $$payload.out.push(`<!----> <span class="font-medium svelte-1dxq2jv">Strong password!</span></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  bind_props($$props, { password, showRequirements });
  pop();
}
function EmailValidationFeedback($$payload, $$props) {
  push();
  let isValidEmail, hasAtSymbol, showValidation, emailDomain, hasValidDomain;
  let email = fallback($$props["email"], "");
  let showConfirmationNotice = fallback($$props["showConfirmationNotice"], true);
  let touched = fallback($$props["touched"], false);
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  isValidEmail = emailRegex.test(email);
  hasAtSymbol = email.includes("@");
  showValidation = touched && email.length > 0;
  emailDomain = email.includes("@") ? email.split("@")[1] : "";
  hasValidDomain = emailDomain && emailDomain.includes(".") && emailDomain.length > 3;
  $$payload.out.push(`<div class="space-y-2">`);
  if (showValidation) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="flex items-center gap-2">`);
    if (isValidEmail) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400">`);
      Circle_check($$payload, { size: 16, class: "flex-shrink-0" });
      $$payload.out.push(`<!----> <span>Valid email format</span></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      if (hasAtSymbol) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 text-sm font-medium dark:bg-yellow-950/20 dark:border-yellow-800/30 dark:text-yellow-400">`);
        Circle_alert($$payload, { size: 16, class: "flex-shrink-0" });
        $$payload.out.push(`<!----> <span>`);
        if (!hasValidDomain) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`Please complete the email address`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`Please check the email format`);
        }
        $$payload.out.push(`<!--]--></span></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="flex items-center gap-2 p-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-600 text-sm dark:bg-gray-950/20 dark:border-gray-800/30 dark:text-gray-400">`);
        Mail($$payload, { size: 16, class: "flex-shrink-0" });
        $$payload.out.push(`<!----> <span>Enter your email address</span></div>`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (showConfirmationNotice && (isValidEmail || !touched && email.length === 0)) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="flex items-start gap-2 p-3 bg-info/10 border border-info/20 rounded-lg text-sm">`);
    Info($$payload, { size: 16, class: "text-info mt-0.5 flex-shrink-0" });
    $$payload.out.push(`<!----> <div class="space-y-1"><p class="font-medium text-info">Email confirmation required</p> <p class="text-base-content/70">We'll send you a confirmation email to verify your account before you
          can sign in.</p></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (showValidation && hasAtSymbol && !isValidEmail) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-xs text-base-content/60 ml-6">`);
    if (emailDomain === "gmail" || emailDomain === "yahoo" || emailDomain === "hotmail") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span>Did you mean <span class="font-medium">${escape_html(emailDomain)}.com</span>?</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  bind_props($$props, { email, showConfirmationNotice, touched });
  pop();
}
function PasswordMatchIndicator($$payload, $$props) {
  push();
  let hasInput, isMatching, isPartialMatch, matchingChars, matchPercentage;
  let password = fallback($$props["password"], "");
  let confirmPassword = fallback($$props["confirmPassword"], "");
  let showPassword = fallback($$props["showPassword"], false);
  hasInput = confirmPassword.length > 0;
  isMatching = password === confirmPassword && hasInput;
  isPartialMatch = password.startsWith(confirmPassword) && hasInput && !isMatching;
  matchingChars = (() => {
    let count = 0;
    for (let i = 0; i < Math.min(password.length, confirmPassword.length); i++) {
      if (password[i] === confirmPassword[i]) {
        count++;
      } else {
        break;
      }
    }
    return count;
  })();
  matchPercentage = password.length > 0 ? matchingChars / password.length * 100 : 0;
  if (hasInput) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="space-y-3 p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-950/20 dark:border-gray-800/30"><div class="flex items-center justify-between"><div class="flex items-center gap-2 text-sm">`);
    if (isMatching) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg text-green-700 font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400">`);
      Circle_check($$payload, { size: 16, class: "flex-shrink-0" });
      $$payload.out.push(`<!----> <span>Passwords match!</span></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      if (isPartialMatch) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 font-medium dark:bg-yellow-950/20 dark:border-yellow-800/30 dark:text-yellow-400"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="flex-shrink-0"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v6l4 2"></path></svg> <span>Keep typing...</span></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400">`);
        Circle_x($$payload, { size: 16, class: "flex-shrink-0" });
        $$payload.out.push(`<!----> <span>Passwords don't match</span></div>`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></div> `);
    if (!isMatching && confirmPassword.length > 3) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button type="button" class="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-1 rounded">`);
      if (showPassword) {
        $$payload.out.push("<!--[-->");
        Eye_off($$payload, { size: 14 });
        $$payload.out.push(`<!----> <span>Hide</span>`);
      } else {
        $$payload.out.push("<!--[!-->");
        Eye($$payload, { size: 14 });
        $$payload.out.push(`<!----> <span>Show</span>`);
      }
      $$payload.out.push(`<!--]--></button>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> `);
    if (!isMatching && confirmPassword.length > 0) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="space-y-1"><div class="flex justify-between text-xs text-base-content/60"><span>Match progress</span> <span>${escape_html(matchingChars)}/${escape_html(password.length)} characters</span></div> <div class="relative h-1.5 bg-base-300 rounded-full overflow-hidden"><div class="absolute inset-y-0 left-0 bg-warning rounded-full transition-all duration-300"${attr_style(`width: ${stringify(matchPercentage)}%`)}></div></div></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> `);
    if (!isMatching && confirmPassword.length > password.length) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<p class="text-xs text-base-content/60">Confirmation is ${escape_html(confirmPassword.length - password.length)} character${escape_html(confirmPassword.length - password.length !== 1 ? "s" : "")} longer</p>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { password, confirmPassword, showPassword });
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  const form = superForm(data.form, { validators: zodClient(signUpSchema) });
  const { form: formData, enhance, delayed, errors, constraints } = form;
  let showPassword = false;
  let emailTouched = false;
  let passwordTouched = false;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>Sign up</title>`;
    });
    $$payload2.out.push(`<!---->`);
    Card($$payload2, {
      class: "mt-6",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Card_title($$payload4, {
              class: "text-2xl font-bold text-center",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Sign Up`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Create your account to get started. You'll receive a confirmation link
      via email.`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out.push(`<form method="post" class="grid gap-4"><!---->`);
            Form_field($$payload4, {
              form,
              name: "email",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Control($$payload5, {
                  children: invalid_default_snippet,
                  $$slots: {
                    default: ($$payload6, { attrs }) => {
                      $$payload6.out.push(`<!---->`);
                      Form_label($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->Email`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> `);
                      Input($$payload6, spread_props([
                        { onblur: () => emailTouched = true },
                        attrs,
                        store_get($$store_subs ??= {}, "$constraints", constraints).email,
                        {
                          get value() {
                            return store_get($$store_subs ??= {}, "$formData", formData).email;
                          },
                          set value($$value) {
                            store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).email = $$value);
                            $$settled = false;
                          }
                        }
                      ]));
                      $$payload6.out.push(`<!---->`);
                    }
                  }
                });
                $$payload5.out.push(`<!----> `);
                EmailValidationFeedback($$payload5, {
                  email: store_get($$store_subs ??= {}, "$formData", formData).email,
                  touched: emailTouched,
                  showConfirmationNotice: true
                });
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "password",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Control($$payload5, {
                  children: invalid_default_snippet,
                  $$slots: {
                    default: ($$payload6, { attrs }) => {
                      $$payload6.out.push(`<!---->`);
                      Form_label($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->Password`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <div class="relative">`);
                      Input($$payload6, spread_props([
                        {
                          type: showPassword ? "text" : "password",
                          onfocus: () => passwordTouched = true
                        },
                        attrs,
                        store_get($$store_subs ??= {}, "$constraints", constraints).password,
                        {
                          get value() {
                            return store_get($$store_subs ??= {}, "$formData", formData).password;
                          },
                          set value($$value) {
                            store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).password = $$value);
                            $$settled = false;
                          }
                        }
                      ]));
                      $$payload6.out.push(`<!----> <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors">`);
                      if (showPassword) {
                        $$payload6.out.push("<!--[-->");
                        Eye_off($$payload6, { size: 18 });
                      } else {
                        $$payload6.out.push("<!--[!-->");
                        Eye($$payload6, { size: 18 });
                      }
                      $$payload6.out.push(`<!--]--></button></div>`);
                    }
                  }
                });
                $$payload5.out.push(`<!----> `);
                PasswordStrengthIndicator($$payload5, {
                  password: store_get($$store_subs ??= {}, "$formData", formData).password,
                  showRequirements: passwordTouched
                });
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "confirmPassword",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Control($$payload5, {
                  children: invalid_default_snippet,
                  $$slots: {
                    default: ($$payload6, { attrs }) => {
                      $$payload6.out.push(`<!---->`);
                      Form_label($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->Confirm Password`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <div class="relative">`);
                      Input($$payload6, spread_props([
                        {
                          type: showPassword ? "text" : "password",
                          onfocus: () => true
                        },
                        attrs,
                        store_get($$store_subs ??= {}, "$constraints", constraints).confirmPassword,
                        {
                          get value() {
                            return store_get($$store_subs ??= {}, "$formData", formData).confirmPassword;
                          },
                          set value($$value) {
                            store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).confirmPassword = $$value);
                            $$settled = false;
                          }
                        }
                      ]));
                      $$payload6.out.push(`<!----> <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors">`);
                      if (showPassword) {
                        $$payload6.out.push("<!--[-->");
                        Eye_off($$payload6, { size: 18 });
                      } else {
                        $$payload6.out.push("<!--[!-->");
                        Eye($$payload6, { size: 18 });
                      }
                      $$payload6.out.push(`<!--]--></button></div>`);
                    }
                  }
                });
                $$payload5.out.push(`<!----> `);
                PasswordMatchIndicator($$payload5, {
                  password: store_get($$store_subs ??= {}, "$formData", formData).password,
                  confirmPassword: store_get($$store_subs ??= {}, "$formData", formData).confirmPassword,
                  get showPassword() {
                    return showPassword;
                  },
                  set showPassword($$value) {
                    showPassword = $$value;
                    $$settled = false;
                  }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> `);
            if (store_get($$store_subs ??= {}, "$errors", errors)._errors) {
              $$payload4.out.push("<!--[-->");
              $$payload4.out.push(`<div class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400">`);
              Circle_alert($$payload4, { size: 16, class: "flex-shrink-0" });
              $$payload4.out.push(`<!----> <span>${escape_html(store_get($$store_subs ??= {}, "$errors", errors)._errors[0])}</span></div>`);
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> `);
            Button($$payload4, {
              type: "submit",
              disabled: store_get($$store_subs ??= {}, "$delayed", delayed),
              class: "w-full",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$delayed", delayed)) {
                  $$payload5.out.push("<!--[-->");
                  $$payload5.out.push(`...`);
                } else {
                  $$payload5.out.push("<!--[!-->");
                  $$payload5.out.push(`Sign Up`);
                }
                $$payload5.out.push(`<!--]-->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----></form>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_footer($$payload3, {
          children: ($$payload4) => {
            $$payload4.out.push(`<div class="mt-4 mb-2">Have an account? <a class="underline" href="/login/sign_in">Sign in</a>.</div>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
