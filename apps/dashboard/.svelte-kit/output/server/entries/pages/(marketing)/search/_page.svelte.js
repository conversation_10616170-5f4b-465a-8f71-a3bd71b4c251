import { s as store_get, w as copy_payload, x as assign_payload, u as unsubscribe_stores, p as pop, a as push, t as ensure_array_like, h as head, c as attr, e as escape_html, f as stringify } from "../../../../chunks/index2.js";
import { p as page } from "../../../../chunks/stores.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import { L as Label, I as Input } from "../../../../chunks/input.js";
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let results = [];
  let searchQuery = decodeURIComponent(store_get($$store_subs ??= {}, "$page", page).url.hash.slice(1) ?? "");
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array = ensure_array_like(results);
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>Search</title>`;
      $$payload3.out.push(`<meta name="description" content="Search our website."/>`);
    });
    $$payload2.out.push(`<div class="py-8 lg:py-12 px-6 max-w-lg mx-auto"><div class="text-3xl lg:text-5xl font-medium text-primary flex gap-3 items-baseline text-center place-content-center"><div class="text-center leading-relaxed font-bold text-primary">Search</div></div> `);
    Label($$payload2, {
      class: "flex items-center gap-2 mt-10",
      children: ($$payload3) => {
        Input($$payload3, {
          id: "search-input",
          type: "text",
          class: "grow",
          placeholder: "Search",
          onfocus: () => 0,
          "aria-label": "Search input",
          get value() {
            return searchQuery;
          },
          set value($$value) {
            searchQuery = $$value;
            $$settled = false;
          }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----> `);
    if (searchQuery.length > 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center mt-10 text-xl">Loading...</div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> <div><!--[-->`);
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      let result = each_array[i];
      $$payload2.out.push(`<a${attr("href", result.item.path || "/")}${attr("id", `search-result-${stringify(i + 1)}`)} class="card my-6 bg-white shadow-xl flex-row overflow-hidden focus:border"><div class="flex-none w-6 md:w-32 bg-secondary"></div> <div class="py-6 px-6"><div class="text-xl">${escape_html(result.item.title)}</div> <div class="text-sm">${escape_html(result.item.path)}</div> <div class="text-muted-foreground">${escape_html(result.item.description)}</div></div></a>`);
    }
    $$payload2.out.push(`<!--]--></div> <div></div></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
