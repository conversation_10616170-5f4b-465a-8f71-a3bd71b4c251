import { h as head, p as pop, a as push } from "../../../../../chunks/index2.js";
import { g as getEnvironmentState } from "../../../../../chunks/environment.svelte.js";
function _page($$payload, $$props) {
  push();
  getEnvironmentState();
  const { data } = $$props;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Current Password Incorrect</title>`;
  });
  $$payload.out.push(`<h1 class="text-2xl font-bold mb-6">Current Password Incorrect</h1> <p>You attempted edit your account with an incorrect current password, and have
  been logged out.</p> <p class="mt-6">If you remember your password <a href="/login/sign_in" class="underline">sign in</a> and try again.</p> <p class="mt-6">If you forget your password <a href="/login/forgot_password" class="underline">reset it</a>.</p>`);
  pop();
}
export {
  _page as default
};
