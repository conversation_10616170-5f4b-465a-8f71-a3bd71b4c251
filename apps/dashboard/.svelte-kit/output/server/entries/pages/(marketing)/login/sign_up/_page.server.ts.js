import { s as signUpSchema } from "../../../../../chunks/schemas.js";
import { redirect } from "@sveltejs/kit";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/formData.js";
import "../../../../../chunks/superForm.js";
import { s as superValidate, f as fail, a as setError } from "../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../chunks/zod.js";
const load = async ({ locals: { safeGetSession } }) => {
  const { user } = await safeGetSession();
  if (user && !user.is_anonymous) {
    return redirect(300, "/find-env");
  }
  const form = await superValidate(zod(signUpSchema));
  return { form };
};
const actions = {
  default: async ({ request, locals: { supabase }, url }) => {
    const form = await superValidate(request, zod(signUpSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    if (!supabase) {
      console.error(
        "Supabase client is not initialized. Check environment variables."
      );
      return setError(
        form,
        "email",
        "Authentication service is not available. Please check server configuration."
      );
    }
    const { error: userError } = await supabase.auth.signUp({
      email: form.data.email,
      password: form.data.password,
      options: {
        emailRedirectTo: `${url.origin}/auth/callback`,
        data: {
          hasPassword: true
        }
      }
    });
    if (userError) {
      console.error({ userError });
      if (userError.message.includes("already registered")) {
        return setError(
          form,
          "Email already registered. Try signing in instead.",
          { status: 400 }
        );
      }
      return setError(form, "Something went wrong...", { status: 500 });
    }
    redirect(300, `/login/check_email?email=${form.data.email}`);
  }
};
export {
  actions,
  load
};
