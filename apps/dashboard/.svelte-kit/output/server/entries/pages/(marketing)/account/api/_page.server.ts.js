import { redirect } from "@sveltejs/kit";
const load = async ({ locals }) => {
  if (locals.environment) {
    return redirect(307, `/dashboard/${locals.environment.slug}/api`);
  }
  redirect(307, "/onboarding");
};
const actions = {
  updateProfile: async ({ locals, request }) => {
    if (locals.environment) {
      await request.formData();
      `/dashboard/${locals.environment.slug}/api`;
      return redirect(307, `/dashboard/${locals.environment.slug}`);
    }
    return redirect(307, "/onboarding");
  }
};
export {
  actions,
  load
};
