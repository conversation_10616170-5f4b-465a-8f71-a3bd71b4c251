import { fail } from "@sveltejs/kit";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/formData.js";
import "../../../../chunks/superForm.js";
import { s as superValidate, a as setError } from "../../../../chunks/superValidate.js";
import { z as zod } from "../../../../chunks/zod.js";
import { h as handlePostSubmit } from "../../../../chunks/contact-notifier.js";
import { a as contactSchema } from "../../../../chunks/schemas.js";
const load = async () => {
  const form = await superValidate(zod(contactSchema));
  return { form };
};
const actions = {
  submitContactUs: async ({ request, locals: { supabaseServiceRole } }) => {
    const form = await superValidate(request, zod(contactSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const { error: insertError } = await supabaseServiceRole.from("contact_requests").insert(form.data);
    if (insertError) {
      console.warn({ insertError });
      return setError(form, "Something went wrong", { status: 500 });
    }
    await handlePostSubmit(form.data);
    return { form };
  }
};
export {
  actions,
  load
};
