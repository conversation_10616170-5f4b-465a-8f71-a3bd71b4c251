import "clsx";
import { w as copy_payload, x as assign_payload, p as pop, a as push } from "../../../../chunks/index2.js";
import { s as superForm } from "../../../../chunks/superForm.js";
import "../../../../chunks/formData.js";
import "@sveltejs/kit";
import { z as zod } from "../../../../chunks/zod.js";
import { b as environmentSchema } from "../../../../chunks/schemas.js";
import "../../../../chunks/create.js";
import "../../../../chunks/index6.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import { g as getEnvironmentState } from "../../../../chunks/environment.svelte.js";
import { L as Loader_circle } from "../../../../chunks/loader-circle.js";
function _page($$payload, $$props) {
  push();
  let { data, form: actionForm } = $$props;
  getEnvironmentState();
  const form = superForm(data.form, { validators: zod(environmentSchema) });
  const { form: formData, enhance, errors, delayed } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<div class="min-h-screen flex items-center justify-center px-4"><div class="w-full max-w-[500px]">`);
    {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="flex items-center justify-center">`);
      Loader_circle($$payload2, { class: "animate-spin", size: 32 });
      $$payload2.out.push(`<!----></div>`);
    }
    $$payload2.out.push(`<!--]--></div></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
