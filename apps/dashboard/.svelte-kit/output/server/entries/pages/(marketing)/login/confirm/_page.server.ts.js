import { o as otpCodeSchema } from "../../../../../chunks/schemas.js";
import { redirect } from "@sveltejs/kit";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/formData.js";
import "../../../../../chunks/superForm.js";
import { s as superValidate, f as fail, a as setError } from "../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../chunks/zod.js";
const load = async ({ url }) => {
  const email = url.searchParams.get("email");
  const type = url.searchParams.get("type") || "email_change";
  if (!email) {
    return redirect(303, "/");
  }
  const form = await superValidate({ email }, zod(otpCodeSchema), {
    errors: false
  });
  return { form, type };
};
const actions = {
  default: async ({ request, locals: { supabase }, url }) => {
    const form = await superValidate(request, zod(otpCodeSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const otpType = url.searchParams.get("type") || "email_change";
    const supabaseOtpType = otpType === "signup" ? "signup" : "email_change";
    const { error } = await supabase.auth.verifyOtp({
      type: supabaseOtpType,
      token: form.data.code,
      email: form.data.email
    });
    if (error) {
      console.error(error);
      return setError(form, "Something went wrong", { status: 500 });
    }
    if (otpType === "signup") {
      redirect(300, "/login/sign_in?verified=true");
    } else {
      redirect(300, "/");
    }
  }
};
export {
  actions,
  load
};
