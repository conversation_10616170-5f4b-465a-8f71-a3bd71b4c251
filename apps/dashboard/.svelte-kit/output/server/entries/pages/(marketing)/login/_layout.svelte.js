import { d as attr_class, p as pop, a as push, f as stringify } from "../../../../chunks/index2.js";
function _layout($$payload, $$props) {
  push();
  let { children } = $$props;
  let isEurope = false;
  try {
    isEurope = Intl.DateTimeFormat().resolvedOptions().timeZone.startsWith("Europe/");
  } catch (e) {
  }
  $$payload.out.push(`<div class="content-center max-w-2xl mx-auto min-h-[70vh] pb-12 flex items-center place-content-center px-4"><div class="flex flex-col w-full max-w-[500px]"><div class="card-brutal p-8 mb-8">`);
  children($$payload);
  $$payload.out.push(`<!----></div> <div${attr_class(`mt-8 ${stringify(isEurope ? "block" : "hidden")} text-center`)}><span class="text-sm font-bold text-muted-foreground">🍪 Logging in uses Cookies 🍪</span></div></div></div>`);
  pop();
}
export {
  _layout as default
};
