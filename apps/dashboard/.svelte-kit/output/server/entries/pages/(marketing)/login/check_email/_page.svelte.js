import { h as head, p as pop, a as push, e as escape_html } from "../../../../../chunks/index2.js";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../../chunks/card-title.js";
import "clsx";
import "../../../../../chunks/index6.js";
import { B as Button } from "../../../../../chunks/button.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Check Your Email</title>`;
  });
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    class: "mt-6",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-2xl font-bold text-center",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Check Your Email`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            class: "text-center",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->We've sent a confirmation link to <strong>${escape_html(data.email)}</strong>`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        class: "text-center space-y-4",
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="space-y-2">`);
          if (data.type === "email_change") {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<p class="text-sm text-muted-foreground">Please check your email and click the confirmation link to verify your
          new email address.</p> <p class="text-sm text-muted-foreground">You may need to confirm the change on both your old and new email
          addresses.</p>`);
          } else {
            $$payload3.out.push("<!--[!-->");
            $$payload3.out.push(`<p class="text-sm text-muted-foreground">Please check your email and click the confirmation link to verify your
          account.</p> <p class="text-sm text-muted-foreground">After confirming your email, you'll be able to sign in with your
          credentials.</p>`);
          }
          $$payload3.out.push(`<!--]--></div> <div class="pt-4"><a href="/login/sign_in" class="w-full">`);
          Button($$payload3, {
            variant: "outline",
            class: "w-full",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Go to Sign In`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></a></div> <div class="text-xs text-muted-foreground"><p>Didn't receive the email? Check your spam folder or</p> <a href="/login/sign_up" class="underline hover:text-primary">try signing up again</a></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _page as default
};
