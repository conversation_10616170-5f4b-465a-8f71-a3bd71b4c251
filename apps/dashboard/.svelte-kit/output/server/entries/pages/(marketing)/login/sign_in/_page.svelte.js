import { h as head, s as store_get, u as unsubscribe_stores, p as pop, a as push } from "../../../../../chunks/index2.js";
import { A as Auth, s as sharedAppearance, o as oauthProviders } from "../../../../../chunks/login_config.js";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "clsx";
import "../../../../../chunks/state.svelte.js";
import { p as page } from "../../../../../chunks/stores.js";
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  let { supabase } = data;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Sign in</title>`;
  });
  if (store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("verified") == "true") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div role="alert" class="alert alert-success mb-5"><svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span>Email verified! Please sign in.</span></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("error")) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div role="alert" class="alert alert-error mb-5"><svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span>`);
    if (store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("error") === "auth_failed") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`Authentication failed. Please try again.`);
    } else {
      $$payload.out.push("<!--[!-->");
      if (store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("error") === "session_failed") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`Session creation failed. Please try signing in again.`);
      } else {
        $$payload.out.push("<!--[!-->");
        if (store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("error") === "email_not_confirmed") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`Please confirm your email before signing in. Check your inbox for the
        confirmation link.`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`An unexpected error occurred. Please try again.`);
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></span></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <h1 class="text-2xl font-bold mb-6">Sign In</h1> `);
  Auth($$payload, {
    supabaseClient: data.supabase,
    view: "sign_in",
    redirectTo: `${data.url}/auth/callback`,
    providers: oauthProviders,
    socialLayout: "horizontal",
    showLinks: false,
    appearance: sharedAppearance,
    additionalData: void 0
  });
  $$payload.out.push(`<!----> <div class="text-l text-primary mt-4"><a class="underline" href="/login/forgot_password">Forgot password?</a></div> <div class="text-l text-primary mt-3">Don't have an account? <a class="underline" href="/login/sign_up">Sign up</a>.</div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
