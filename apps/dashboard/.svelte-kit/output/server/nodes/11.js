import * as server from '../entries/pages/(admin)/dashboard/_envSlug_/(menu)/_page.server.ts.js';

export const index = 11;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/(admin)/dashboard/_envSlug_/(menu)/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/(admin)/dashboard/[envSlug]/(menu)/+page.server.ts";
export const imports = ["_app/immutable/nodes/11.OIBK75n7.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/DhRTwODG.js","_app/immutable/chunks/DDiqt3uM.js","_app/immutable/chunks/RnwjOPnl.js","_app/immutable/chunks/DWulv87v.js","_app/immutable/chunks/DtGADYZa.js","_app/immutable/chunks/C36Ip9GY.js","_app/immutable/chunks/DE2v8SHj.js","_app/immutable/chunks/B_FgA42l.js","_app/immutable/chunks/B82PTGnX.js","_app/immutable/chunks/rjRVMZXi.js","_app/immutable/chunks/DE1qutZV.js","_app/immutable/chunks/CF5x3pQ4.js","_app/immutable/chunks/18cO7kEJ.js","_app/immutable/chunks/jYZUw_FW.js","_app/immutable/chunks/CVCfOWck.js"];
export const stylesheets = [];
export const fonts = [];
