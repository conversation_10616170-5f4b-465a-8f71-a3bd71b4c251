import * as universal from '../entries/pages/(admin)/_layout.ts.js';
import * as server from '../entries/pages/(admin)/_layout.server.ts.js';

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/(admin)/+layout.ts";
export { server };
export const server_id = "src/routes/(admin)/+layout.server.ts";
export const imports = ["_app/immutable/nodes/2.C4t-sVf8.js","_app/immutable/chunks/CQYhC97h.js","_app/immutable/chunks/BiqrECSP.js","_app/immutable/chunks/C4iS2aBk.js","_app/immutable/chunks/DxIyR2FA.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/DDiqt3uM.js","_app/immutable/chunks/DtGADYZa.js"];
export const stylesheets = [];
export const fonts = [];
