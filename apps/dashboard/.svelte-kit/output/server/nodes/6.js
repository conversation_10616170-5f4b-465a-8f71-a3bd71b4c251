import * as universal from '../entries/pages/(marketing)/blog/_layout.ts.js';

export const index = 6;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/(marketing)/blog/+layout.ts";
export const imports = ["_app/immutable/nodes/6.C9QwTmXd.js","_app/immutable/chunks/DxIyR2FA.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/DDiqt3uM.js","_app/immutable/chunks/DtGADYZa.js"];
export const stylesheets = [];
export const fonts = [];
