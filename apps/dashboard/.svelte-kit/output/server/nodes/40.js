

export const index = 40;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/(marketing)/blog/(posts)/how_we_built_our_41kb_saas_website/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/40.CqnnIbAs.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/DhRTwODG.js","_app/immutable/chunks/DDiqt3uM.js"];
export const stylesheets = [];
export const fonts = [];
