

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.Bsp1OXZp.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/DhRTwODG.js","_app/immutable/chunks/DDiqt3uM.js","_app/immutable/chunks/DWulv87v.js","_app/immutable/chunks/B82PTGnX.js","_app/immutable/chunks/rjRVMZXi.js","_app/immutable/chunks/CF5x3pQ4.js","_app/immutable/chunks/DE1qutZV.js","_app/immutable/chunks/RnwjOPnl.js","_app/immutable/chunks/DtGADYZa.js"];
export const stylesheets = [];
export const fonts = [];
