class ContentService {
  constructor(supabase) {
    this.supabase = supabase;
  }
  // Document Operations
  async createDocument(data) {
    const { data: document, error } = await this.supabase.from("content_documents").insert(data).select().single();
    if (error) {
      console.error("Error creating document:", error);
      throw new Error(`Failed to create document: ${error.message}`);
    }
    return document;
  }
  async getDocument(documentId, userId) {
    const { data: document, error } = await this.supabase.from("content_documents").select("*").eq("id", documentId).eq("user_id", userId).single();
    if (error) {
      if (error.code === "PGRST116") {
        return null;
      }
      console.error("Error fetching document:", error);
      throw new Error(`Failed to fetch document: ${error.message}`);
    }
    return document;
  }
  async updateDocument(documentId, userId, updates) {
    const { data: document, error } = await this.supabase.from("content_documents").update(updates).eq("id", documentId).eq("user_id", userId).select().single();
    if (error) {
      console.error("Error updating document:", error);
      throw new Error(`Failed to update document: ${error.message}`);
    }
    return document;
  }
  async deleteDocument(documentId, userId) {
    const { error } = await this.supabase.from("content_documents").delete().eq("id", documentId).eq("user_id", userId);
    if (error) {
      console.error("Error deleting document:", error);
      throw new Error(`Failed to delete document: ${error.message}`);
    }
    return true;
  }
  async getUserDocuments(userId, environmentId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      status,
      contentType,
      orderBy = "updated_at",
      orderDirection = "desc"
    } = options;
    let query = this.supabase.from("content_documents").select("*").eq("user_id", userId).eq("environment_id", environmentId);
    if (status) {
      query = query.eq("status", status);
    }
    if (contentType) {
      query = query.eq("content_type", contentType);
    }
    query = query.order(orderBy, { ascending: orderDirection === "asc" }).range(offset, offset + limit - 1);
    const { data: documents, error } = await query;
    if (error) {
      console.error("Error fetching user documents:", error);
      throw new Error(`Failed to fetch documents: ${error.message}`);
    }
    return documents || [];
  }
  async searchDocuments(userId, environmentId, searchTerm, options = {}) {
    const { limit = 20, contentType } = options;
    let query = this.supabase.from("content_documents").select("*").eq("user_id", userId).eq("environment_id", environmentId).or(`title.ilike.%${searchTerm}%,content->>'text'.ilike.%${searchTerm}%`);
    if (contentType) {
      query = query.eq("content_type", contentType);
    }
    query = query.order("updated_at", { ascending: false }).limit(limit);
    const { data: documents, error } = await query;
    if (error) {
      console.error("Error searching documents:", error);
      throw new Error(`Failed to search documents: ${error.message}`);
    }
    return documents || [];
  }
  // Session Operations
  async createSession(data) {
    const { data: session, error } = await this.supabase.from("content_sessions").insert(data).select().single();
    if (error) {
      console.error("Error creating session:", error);
      throw new Error(`Failed to create session: ${error.message}`);
    }
    return session;
  }
  async getSession(sessionId, userId) {
    const { data: session, error } = await this.supabase.from("content_sessions").select("*").eq("id", sessionId).eq("user_id", userId).single();
    if (error) {
      if (error.code === "PGRST116") {
        return null;
      }
      console.error("Error fetching session:", error);
      throw new Error(`Failed to fetch session: ${error.message}`);
    }
    return session;
  }
  async updateSession(sessionId, userId, updates) {
    const { data: session, error } = await this.supabase.from("content_sessions").update(updates).eq("id", sessionId).eq("user_id", userId).select().single();
    if (error) {
      console.error("Error updating session:", error);
      throw new Error(`Failed to update session: ${error.message}`);
    }
    return session;
  }
  async getDocumentSessions(documentId, userId) {
    const { data: sessions, error } = await this.supabase.from("content_sessions").select("*").eq("document_id", documentId).eq("user_id", userId).order("created_at", { ascending: false });
    if (error) {
      console.error("Error fetching document sessions:", error);
      throw new Error(`Failed to fetch sessions: ${error.message}`);
    }
    return sessions || [];
  }
  // Citation Operations
  async addCitation(data) {
    const { data: citation, error } = await this.supabase.from("content_citations").insert(data).select().single();
    if (error) {
      console.error("Error adding citation:", error);
      throw new Error(`Failed to add citation: ${error.message}`);
    }
    return citation;
  }
  async getDocumentCitations(documentId) {
    const { data: citations, error } = await this.supabase.from("content_citations").select("*").eq("document_id", documentId).order("position_in_content", { ascending: true });
    if (error) {
      console.error("Error fetching citations:", error);
      throw new Error(`Failed to fetch citations: ${error.message}`);
    }
    return citations || [];
  }
  async updateCitation(citationId, updates) {
    const { data: citation, error } = await this.supabase.from("content_citations").update(updates).eq("id", citationId).select().single();
    if (error) {
      console.error("Error updating citation:", error);
      throw new Error(`Failed to update citation: ${error.message}`);
    }
    return citation;
  }
  async deleteCitation(citationId) {
    const { error } = await this.supabase.from("content_citations").delete().eq("id", citationId);
    if (error) {
      console.error("Error deleting citation:", error);
      throw new Error(`Failed to delete citation: ${error.message}`);
    }
    return true;
  }
  // Utility Methods
  async getDocumentStats(userId, environmentId) {
    const { data, error } = await this.supabase.from("content_documents").select("status, content_type").eq("user_id", userId).eq("environment_id", environmentId);
    if (error) {
      console.error("Error fetching document stats:", error);
      throw new Error(`Failed to fetch document stats: ${error.message}`);
    }
    const stats = {
      total: data?.length || 0,
      byStatus: {},
      byType: {}
    };
    data?.forEach((doc) => {
      stats.byStatus[doc.status] = (stats.byStatus[doc.status] || 0) + 1;
      stats.byType[doc.content_type] = (stats.byType[doc.content_type] || 0) + 1;
    });
    return stats;
  }
}
export {
  ContentService as C
};
