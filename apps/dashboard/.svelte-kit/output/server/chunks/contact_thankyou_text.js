import { A as fallback, e as escape_html, B as bind_props } from "./index2.js";
function Contact_thankyou_text($$payload, $$props) {
  const ssr = true;
  let first_name = fallback($$props["first_name"], "");
  $$payload.out.push(`<!---->Hi ${escape_html(first_name)},

Thanks for reaching out! We've received your message and an augmented marketer will reply within one business day.

In the meantime, you can read success stories at https://robynn.ai/#stories.

– The Robynn team`);
  bind_props($$props, { first_name, ssr });
}
export {
  Contact_thankyou_text as default
};
