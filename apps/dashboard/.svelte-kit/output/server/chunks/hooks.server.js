import { P as PUBLIC_SUPABASE_URL, a as PUBLIC_SUPABASE_ANON_KEY, S as SUPABASE_SERVICE_ROLE_KEY } from "./public.js";
import "@sveltejs/kit";
import { sequence } from "@sveltejs/kit/hooks";
import { createServerClient } from "@supabase/ssr";
import { createClient } from "@supabase/supabase-js";
const supabase = async ({ event, resolve }) => {
  const supabaseUrl = PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = SUPABASE_SERVICE_ROLE_KEY;
  event.locals.supabase = createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        getAll() {
          return event.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(
            ({ name, value, options }) => event.cookies.set(name, value, { ...options, path: "/" })
          );
        }
      }
    }
  );
  event.locals.supabaseServiceRole = createClient(
    supabaseUrl,
    serviceRoleKey,
    {
      auth: { persistSession: false }
    }
  );
  return resolve(event, {
    filterSerializedResponseHeaders(name) {
      return name === "content-range";
    }
  });
};
const auth = async ({ event, resolve }) => {
  event.locals.safeGetSession = async () => {
    if (!event.locals.supabase) {
      return { session: null, user: null };
    }
    const {
      data: { session }
    } = await event.locals.supabase.auth.getSession();
    if (!session) {
      const {
        data: { session: session2, user }
      } = await event.locals.supabase.auth.signInAnonymously();
      return { session: session2, user };
    } else {
      return { session, user: session.user };
    }
  };
  event.locals.auth = await event.locals.safeGetSession();
  return resolve(event);
};
const environment = async ({ event, resolve }) => {
  if (event.locals.supabase && event.locals.auth.user?.id && !event.locals.auth.user?.is_anonymous) {
    const { data } = await event.locals.supabase.from("environments_profiles").select("env:environments (*)").eq("profile_id", event.locals.auth.user.id);
    event.locals.environment = data?.[0] ? data[0].env : null;
  } else {
    event.locals.environment = null;
  }
  return resolve(event);
};
const handle = sequence(supabase, auth, environment);
export {
  handle
};
