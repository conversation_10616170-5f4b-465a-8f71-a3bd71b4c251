import { t as ensure_array_like, d as attr_class, f as stringify, p as pop, a as push, e as escape_html, c as attr } from "./index2.js";
import { p as pricingPlans } from "./pricing_plans.js";
import { C as Card, a as Card_content } from "./card-content.js";
import "clsx";
import { b as buttonVariants } from "./index6.js";
function Pricing_module($$payload, $$props) {
  push();
  let {
    highlightedPlanId = "",
    callToAction,
    currentPlanId = "",
    center = true
  } = $$props;
  const each_array = ensure_array_like(pricingPlans);
  $$payload.out.push(`<div${attr_class(`flex flex-col lg:flex-row gap-10 ${stringify(center ? "place-content-center" : "")} flex-wrap`)}><!--[-->`);
  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
    let plan = each_array[$$index_1];
    $$payload.out.push(`<!---->`);
    Card($$payload, {
      class: `${stringify(plan.id === highlightedPlanId ? "border-primary" : "border-gray-200")} shadow-xl flex-1 flex-grow min-w-[260px] max-w-[310px] p-6`,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        Card_content($$payload2, {
          children: ($$payload3) => {
            const each_array_1 = ensure_array_like(plan.features);
            $$payload3.out.push(`<div class="flex flex-col h-full"><div class="text-xl font-bold">${escape_html(plan.name)}</div> <p class="mt-2 text-sm text-muted-foreground leading-relaxed">${escape_html(plan.description)}</p> <div class="mt-auto pt-4 text-sm"><span class="font-bold">Plan Includes:</span> <ul class="list-disc list-inside mt-2 space-y-1"><!--[-->`);
            for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
              let feature = each_array_1[$$index];
              $$payload3.out.push(`<li>${escape_html(feature)}</li>`);
            }
            $$payload3.out.push(`<!--]--></ul></div> <div class="pt-8"><span class="text-4xl font-bold">${escape_html(plan.price)}</span> <span class="text-muted-forground">${escape_html(plan.priceIntervalName)}</span> <div class="mt-6 pt-4 flex-1 flex flex-row items-center">`);
            if (plan.id === currentPlanId) {
              $$payload3.out.push("<!--[-->");
              $$payload3.out.push(`<div${attr_class(`${stringify(buttonVariants({ variant: "outline" }))} no-animation w-[80%] mx-auto cursor-default`)}>Current Plan</div>`);
            } else {
              $$payload3.out.push("<!--[!-->");
              $$payload3.out.push(`<a${attr("href", "/account/subscribe/" + (plan?.stripe_price_id ?? "free_plan"))}${attr_class(`${stringify(buttonVariants({ variant: "default" }))} w-[80%] mx-auto`)}>${escape_html(callToAction)}</a>`);
            }
            $$payload3.out.push(`<!--]--></div></div></div>`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload.out.push(`<!---->`);
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
export {
  Pricing_module as P
};
