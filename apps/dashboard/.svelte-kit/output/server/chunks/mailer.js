import { Resend } from "resend";
import { a as private_env } from "./shared-server.js";
import { createClient } from "@supabase/supabase-js";
const __variableDynamicImportRuntimeHelper = (glob, path, segs) => {
  const v = glob[path];
  if (v) {
    return typeof v === "function" ? v() : Promise.resolve(v);
  }
  return new Promise((_, reject) => {
    (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
      reject.bind(
        null,
        new Error(
          "Unknown variable dynamic import: " + path + (path.split("/").length !== segs ? ". Note that variables only represent file names one level deep." : "")
        )
      )
    );
  });
};
const sendAdminEmail = async ({
  subject,
  body
}) => {
  if (!private_env.PRIVATE_ADMIN_EMAIL) {
    return;
  }
  try {
    const resend = new Resend(private_env.PRIVATE_RESEND_API_KEY);
    const resp = await resend.emails.send({
      from: private_env.PRIVATE_FROM_ADMIN_EMAIL || private_env.PRIVATE_ADMIN_EMAIL,
      to: [private_env.PRIVATE_ADMIN_EMAIL],
      subject: "ADMIN_MAIL: " + subject,
      text: body
    });
    if (resp.error) {
      console.log("Failed to send admin email, error:", resp.error);
    }
  } catch (e) {
    console.log("Failed to send admin email, error:", e);
  }
};
const sendUserEmail = async ({
  user,
  subject,
  from_email,
  template_name,
  template_properties
}) => {
  const email = user.email;
  if (!email) {
    console.log("No email for user. Aborting email. ", user.id);
    return;
  }
  const serverSupabase = createClient(
    private_env.PUBLIC_SUPABASE_URL,
    private_env.SUPABASE_SERVICE_ROLE_KEY,
    { auth: { persistSession: false } }
  );
  const { data: serviceUserData } = await serverSupabase.auth.admin.getUserById(
    user.id
  );
  const emailVerified = serviceUserData.user?.email_confirmed_at || serviceUserData.user?.user_metadata?.email_verified;
  if (!emailVerified) {
    console.log("User email not verified. Aborting email. ", user.id, email);
    return;
  }
  const { data: profile, error: profileError } = await serverSupabase.from("profiles").select("unsubscribed").eq("id", user.id).single();
  if (profileError) {
    console.log("Error fetching user profile. Aborting email. ", user.id, email);
    return;
  }
  if (profile?.unsubscribed) {
    console.log("User unsubscribed. Aborting email. ", user.id, email);
    return;
  }
  await sendTemplatedEmail({
    subject,
    to_emails: [email],
    from_email,
    template_name,
    template_properties
  });
};
const sendTemplatedEmail = async ({
  subject,
  to_emails,
  from_email,
  template_name,
  template_properties
}) => {
  if (!private_env.PRIVATE_RESEND_API_KEY) {
    return;
  }
  let plaintextBody = void 0;
  try {
    const { render } = await import("./index.js");
    const emailTemplate = await __variableDynamicImportRuntimeHelper(/* @__PURE__ */ Object.assign({ "./emails/contact_thankyou_text.svelte": () => import("./contact_thankyou_text.js"), "./emails/welcome_email_text.svelte": () => import("./welcome_email_text.js") }), `./emails/${template_name}_text.svelte`, 3).then((mod) => mod.default);
    const result = render(emailTemplate, { props: template_properties });
    plaintextBody = result.body;
  } catch (e) {
    plaintextBody = void 0;
  }
  let htmlBody = void 0;
  try {
    const { render } = await import("./index.js");
    const emailTemplate = await __variableDynamicImportRuntimeHelper(/* @__PURE__ */ Object.assign({ "./emails/contact_thankyou_html.svelte": () => import("./contact_thankyou_html.js"), "./emails/welcome_email_html.svelte": () => import("./welcome_email_html.js") }), `./emails/${template_name}_html.svelte`, 3).then((mod) => mod.default);
    const result = render(emailTemplate, { props: template_properties });
    htmlBody = result.body;
  } catch (e) {
    htmlBody = void 0;
  }
  if (!plaintextBody && !htmlBody) {
    console.log(
      "No email body: requires plaintextBody or htmlBody. Template: ",
      template_name
    );
    return;
  }
  try {
    const email = {
      from: from_email,
      to: to_emails,
      subject
    };
    if (plaintextBody) {
      email.text = plaintextBody;
    }
    if (htmlBody) {
      email.html = htmlBody;
    }
    const resend = new Resend(private_env.PRIVATE_RESEND_API_KEY);
    const resp = await resend.emails.send(email);
    if (resp.error) {
      console.log("Failed to send email, error:", resp.error);
    }
  } catch (e) {
    console.log("Failed to send email, error:", e);
  }
};
export {
  sendUserEmail as a,
  sendTemplatedEmail as b,
  sendAdminEmail as s
};
