import { b as sendTemplatedEmail, s as sendAdminEmail } from "./mailer.js";
import { a as private_env } from "./shared-server.js";
async function handlePostSubmit(row) {
  try {
    console.log("🔍 ENV CHECK:", {
      hasResendKey: !!private_env.PRIVATE_RESEND_API_KEY,
      hasSlackUrl: !!private_env.SLACK_WEBHOOK_URL,
      hasFromEmail: !!(private_env.PRIVATE_FROM_MARKETING_EMAIL || private_env.PRIVATE_FROM_ADMIN_EMAIL || private_env.PRIVATE_ADMIN_EMAIL),
      fromEmail: private_env.PRIVATE_FROM_MARKETING_EMAIL || private_env.PRIVATE_FROM_ADMIN_EMAIL || private_env.PRIVATE_ADMIN_EMAIL || "<EMAIL>"
    });
    await sendTemplatedEmail({
      subject: "We got your message – <PERSON><PERSON>",
      to_emails: [row.email],
      from_email: private_env.PRIVATE_FROM_MARKETING_EMAIL || private_env.PRIVATE_FROM_ADMIN_EMAIL || private_env.PRIVATE_ADMIN_EMAIL || "<EMAIL>",
      template_name: "contact_thankyou",
      template_properties: { first_name: row.first_name ?? "" }
    });
    if (private_env.SLACK_WEBHOOK_URL) {
      console.log("🔍 SLACK: Sending notification to webhook");
      await fetch(private_env.SLACK_WEBHOOK_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          text: "New contact form submission",
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🆕 New Contact Request"
              }
            },
            {
              type: "section",
              fields: [
                {
                  type: "mrkdwn",
                  text: `*Name:*
${row.first_name} ${row.last_name}`
                },
                {
                  type: "mrkdwn",
                  text: `*Email:*
${row.email}`
                },
                {
                  type: "mrkdwn",
                  text: `*Company:*
${row.company_name || "Not provided"}`
                },
                {
                  type: "mrkdwn",
                  text: `*Phone:*
${row.phone || "Not provided"}`
                }
              ]
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Message:*
${row.message_body || "No message"}`
              }
            }
          ]
        })
      }).catch((e) => console.error("Slack webhook failed", e));
    } else {
      console.log("🔍 SLACK: No webhook URL found");
    }
    await sendAdminEmail({
      subject: "New contact request",
      body: `Name: ${row.first_name} ${row.last_name}
Email: ${row.email}
Company: ${row.company_name ?? "—"}
Phone: ${row.phone ?? "—"}
Message:
${row.message_body ?? "—"}`
    });
  } catch (error) {
    console.error("Error in handlePostSubmit:", error);
  }
}
export {
  handlePostSubmit as h
};
