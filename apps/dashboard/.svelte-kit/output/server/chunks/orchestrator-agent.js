import { Agent, createTool } from "@mastra/core";
import { c as createLLMClient, w as webSearchTool } from "./seo-strategist.js";
import { z } from "zod";
import { a as private_env } from "./shared-server.js";
import Exa from "exa-js";
function createCompanyResearcherAgent(llmConfig) {
  const model = createLLMClient({
    provider: "anthropic",
    model: "claude-3-5-sonnet-20241022"
  });
  return new Agent({
    name: "Company Researcher",
    instructions: `
      You are an expert corporate research analyst with deep expertise in business intelligence, financial analysis, and market research. Your mission is to provide comprehensive, accurate, and actionable research reports about companies.

      **MOST IMPORTANT: CITATION NUMBERING SYSTEM**
      You MUST use sequential numbered citations [1], [2], [3], [4], [5], etc. throughout your report. Each unique source gets the next number in sequence. NEVER use [1] for multiple different sources. This is your TOP PRIORITY.

      **CITATION NUMBERING RULE: ALWAYS INCREMENT**
      - First citation in your report: [1]
      - Second citation in your report: [2]
      - Third citation in your report: [3]
      - Fourth citation in your report: [4]
      - Fifth citation in your report: [5]
      - And so on... NEVER go back to [1] for a different source

      **CRITICAL: Check for output format in the user's message**
      Look for format prefixes like "[Executive Summary Format]", "[Slide-ready Format]", or "[Competitive Battlecard Format]" and adapt your response accordingly:

      - **Executive Summary Format**: Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly.
      - **Slide-ready Format**: Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides.
      - **Competitive Battlecard Format**: Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons.

      When you research a company, you should:

      1. **Use the webSearch tool** to gather comprehensive information about the company
      2. **Analyze the search results** thoroughly to understand:
         - Company overview and business model
         - Financial performance and key metrics
         - Recent news and developments
         - Market position and competitive landscape
         - Leadership and key personnel
         - Growth opportunities and challenges

      3. **Provide a structured research report** that includes:
         - Executive Summary
         - Company Overview
         - Financial Analysis (if available)
         - Recent Developments
         - Market Position
         - Risk Assessment
         - Investment Perspective (if applicable)
         - Key Sources and References

      4. **Present insights clearly** with:
         - Factual accuracy based on search results
         - Professional analysis and interpretation
         - **Sequential numbered citations [1], [2], [3], [4], [5], etc.** for all claims and data points
         - Actionable insights and recommendations

      **CRITICAL CITATION REQUIREMENTS - FOLLOW EXACTLY:**

      **Citation Numbering System:**
      - Start with [1] for the first source you reference
      - Continue sequentially: [2], [3], [4], [5], [6], [7], [8], [9], [10], etc.
      - Each UNIQUE source gets its own sequential number
      - If you reference the same source multiple times, use the SAME number each time
      - NEVER repeat [1] for different sources - each new source gets the next number
      - COUNT CAREFULLY: First source = [1], Second source = [2], Third source = [3], Fourth source = [4], Fifth source = [5]
      - WRONG: [1], [1], [1], [1] for different sources
      - CORRECT: [1], [2], [3], [4] for different sources

      **Citation Placement:**
      - Place citations immediately after claims, statistics, or facts: "Company revenue reached $100M [1]"
      - Use citations after quotes: "The CEO stated 'We're growing rapidly' [2]"
      - Cite financial data: "The company raised $50M in Series A funding [3]"
      - Reference market information: "The market size is estimated at $2B [4]"

      **Sources and References Section:**
      - ALWAYS include this section at the very end of your report
      - Title it exactly: "## Sources and References"
      - List sources in numerical order: 1., 2., 3., 4., 5., etc.
      - Format each entry as: "1. Source Title - URL - Date"
      - Example:
        1. Company Announces $100M Series A - TechCrunch - March 15, 2024
        2. CEO Interview on Growth Strategy - Forbes - March 10, 2024
        3. Market Analysis Report - McKinsey - February 2024

      **Citation Examples:**
      - "The company achieved $100M ARR in 2024 [1] and plans to expand internationally [2]."
      - "According to recent reports, the market opportunity is valued at $5B [3]."
      - "The CEO mentioned in an interview that hiring is a top priority [4]."

      **IMPORTANT:** Count your citations carefully and ensure they increment properly: [1], [2], [3], [4], [5], etc. Do NOT use [1] for multiple different sources.

      **CITATION EXAMPLE - CORRECT WAY:**
      "DevRev announced $100M Series A funding [1]. The company has grown to 554 employees [2]. According to Crunchbase, DevRev was founded in 2020 [3]. The CEO stated in a recent interview that AI is core to their strategy [4]."

      Sources and References:
      1. DevRev Announces $100M Series A - Yahoo Finance - August 9, 2024
      2. DevRev Company Profile - GetLatka - January 1, 2025
      3. DevRev Startup Profile - Crunchbase - October 17, 2024
      4. CEO Interview on AI Strategy - TechCrunch - March 2024

      **CITATION EXAMPLE - WRONG WAY (DO NOT DO THIS):**
      "DevRev announced $100M Series A funding [1]. The company has grown to 554 employees [1]. According to Crunchbase, DevRev was founded in 2020 [1]. The CEO stated in a recent interview that AI is core to their strategy [1]." ← This is WRONG because different sources all use [1]

      **BEFORE YOU START WRITING:**
      1. Use the webSearch tool to gather comprehensive information
      2. The webSearch tool returns an "allSources" array with pre-numbered sources (citationNumber: 1, 2, 3, etc.)
      3. Use the citationNumber field directly for your citations:
         - First source in allSources array = [1]
         - Second source in allSources array = [2]
         - Third source in allSources array = [3]
         - Continue sequentially using the citationNumber field
      4. Write your report using these pre-assigned numbers consistently
      5. End with the "Sources and References" section listing all sources in numerical order

      **CRITICAL: Use the allSources array and its citationNumber field. DO NOT create your own numbering system.**

      **CITATION VERIFICATION CHECKLIST:**
      - ✓ Did I start with [1] for the first source?
      - ✓ Did I increment sequentially: [1], [2], [3], [4], [5]?
      - ✓ Did I avoid repeating [1] for different sources?
      - ✓ Did I use the same number when referencing the same source multiple times?
      - ✓ Did I include a "Sources and References" section at the end?
      - ✓ Are my reference numbers in the text consistent with the reference list?

      **STEP-BY-STEP CITATION PROCESS:**
      1. After using webSearch, look at the "allSources" array in the response
      2. Each source has a "citationNumber" field (1, 2, 3, 4, 5, etc.)
      3. As you write each sentence with a fact, ask: "Which source is this from?"
      4. Find that source in the allSources array and use its citationNumber: [1], [2], [3], etc.
      5. At the end, list all sources using their citationNumber order: 1. First source, 2. Second source, etc.

      Always use the webSearch tool first to gather current, comprehensive information before providing your analysis. Be thorough, objective, and professional in your research approach.
    `,
    model,
    tools: { webSearch: webSearchTool }
  });
}
const companyResearcherAgent = createCompanyResearcherAgent();
const contentGenerationSchema = z.object({
  topic: z.string().describe("The main topic or subject for the content"),
  contentType: z.enum([
    "article",
    "blog-post",
    "whitepaper",
    "social-media",
    "email",
    "documentation",
    "press-release",
    "case-study",
    "newsletter",
    "landing-page"
  ]).describe("Type of content to generate"),
  targetAudience: z.enum([
    "general",
    "technical",
    "executive",
    "marketing",
    "academic",
    "beginner",
    "intermediate",
    "expert"
  ]).describe("Target audience for the content"),
  tone: z.enum([
    "professional",
    "casual",
    "friendly",
    "authoritative",
    "conversational",
    "formal",
    "persuasive",
    "educational"
  ]).default("professional").describe("Tone and style of the content"),
  length: z.enum([
    "short",
    // 200-500 words
    "medium",
    // 500-1000 words  
    "long",
    // 1000-2000 words
    "extended"
    // 2000+ words
  ]).default("medium").describe("Desired length of the content"),
  keywords: z.array(z.string()).optional().describe("Optional keywords to include for SEO optimization"),
  includeIntroduction: z.boolean().default(true).describe("Whether to include an introduction section"),
  includeConclusion: z.boolean().default(true).describe("Whether to include a conclusion section"),
  sections: z.array(z.string()).optional().describe("Specific sections or topics to cover in the content")
});
const contentGenerationTool = createTool({
  id: "content-generation",
  description: "Generate high-quality content based on topic, type, audience, and style preferences",
  inputSchema: contentGenerationSchema,
  execute: async (context) => {
    const {
      topic,
      contentType,
      targetAudience,
      tone,
      length,
      keywords,
      includeIntroduction,
      includeConclusion,
      sections
    } = context.context;
    const wordCountTargets = {
      short: { min: 200, max: 500, target: 350 },
      medium: { min: 500, max: 1e3, target: 750 },
      long: { min: 1e3, max: 2e3, target: 1500 },
      extended: { min: 2e3, max: 4e3, target: 3e3 }
    };
    const targetWords = wordCountTargets[length];
    const contentStructures = {
      "article": {
        structure: ["Introduction", "Main Content", "Key Points", "Conclusion"],
        format: "informative article format with clear sections"
      },
      "blog-post": {
        structure: ["Hook", "Introduction", "Main Points", "Call to Action"],
        format: "engaging blog post with personal touch"
      },
      "whitepaper": {
        structure: ["Executive Summary", "Problem Statement", "Solution", "Benefits", "Conclusion"],
        format: "authoritative whitepaper with data and insights"
      },
      "social-media": {
        structure: ["Hook", "Main Message", "Call to Action"],
        format: "concise social media post with engagement focus"
      },
      "email": {
        structure: ["Subject Line", "Opening", "Main Message", "Call to Action", "Closing"],
        format: "professional email communication"
      },
      "documentation": {
        structure: ["Overview", "Instructions", "Examples", "Troubleshooting"],
        format: "clear technical documentation"
      },
      "press-release": {
        structure: ["Headline", "Lead", "Body", "Boilerplate", "Contact Info"],
        format: "professional press release format"
      },
      "case-study": {
        structure: ["Challenge", "Solution", "Implementation", "Results"],
        format: "detailed case study with metrics"
      },
      "newsletter": {
        structure: ["Header", "Main Stories", "Updates", "Call to Action"],
        format: "engaging newsletter format"
      },
      "landing-page": {
        structure: ["Headline", "Value Proposition", "Benefits", "Social Proof", "CTA"],
        format: "conversion-focused landing page"
      }
    };
    const contentStructure = contentStructures[contentType];
    let generationPrompt = `Generate ${contentType} content about "${topic}" for ${targetAudience} audience.

REQUIREMENTS:
- Content Type: ${contentType}
- Target Audience: ${targetAudience}
- Tone: ${tone}
- Length: ${length} (approximately ${targetWords.target} words)
- Format: ${contentStructure.format}

STRUCTURE:
${contentStructure.structure.map((section, index) => `${index + 1}. ${section}`).join("\n")}

CONTENT GUIDELINES:
- Write in ${tone} tone appropriate for ${targetAudience} audience
- Target approximately ${targetWords.target} words
- Use clear, engaging language
- Include relevant examples and insights
- Ensure content is valuable and actionable`;
    if (keywords && keywords.length > 0) {
      generationPrompt += `
- Naturally incorporate these keywords: ${keywords.join(", ")}`;
    }
    if (sections && sections.length > 0) {
      generationPrompt += `
- Cover these specific topics: ${sections.join(", ")}`;
    }
    if (!includeIntroduction) {
      generationPrompt += `
- Skip the introduction and start with main content`;
    }
    if (!includeConclusion) {
      generationPrompt += `
- Skip the conclusion section`;
    }
    generationPrompt += `

Generate the complete ${contentType} now:`;
    return {
      success: true,
      contentType,
      topic,
      targetAudience,
      tone,
      length,
      targetWordCount: targetWords.target,
      structure: contentStructure.structure,
      generationPrompt,
      metadata: {
        keywords: keywords || [],
        sections: sections || [],
        includeIntroduction,
        includeConclusion,
        estimatedReadingTime: Math.ceil(targetWords.target / 200)
        // Average reading speed
      }
    };
  }
});
const textSummarizationSchema = z.object({
  inputText: z.string().min(100).describe("The text content to be summarized (minimum 100 characters)"),
  summaryLength: z.enum([
    "brief",
    // 1-2 sentences
    "short",
    // 3-5 sentences  
    "medium",
    // 1-2 paragraphs
    "detailed"
    // 2-3 paragraphs
  ]).default("short").describe("Desired length of the summary"),
  summaryFormat: z.enum([
    "paragraph",
    "bullet-points",
    "executive-summary",
    "key-takeaways",
    "abstract",
    "highlights"
  ]).default("paragraph").describe("Format style for the summary output"),
  focusArea: z.enum([
    "main-points",
    "key-findings",
    "actionable-items",
    "conclusions",
    "methodology",
    "results",
    "recommendations"
  ]).default("main-points").describe("What aspect of the content to focus on in the summary"),
  targetAudience: z.enum([
    "general",
    "technical",
    "executive",
    "academic",
    "marketing"
  ]).default("general").describe("Target audience for the summary"),
  preserveKeyTerms: z.boolean().default(true).describe("Whether to preserve important technical terms and keywords"),
  includeStatistics: z.boolean().default(true).describe("Whether to include key statistics and numbers in the summary")
});
const textSummarizationTool = createTool({
  id: "text-summarization",
  description: "Create concise, focused summaries of text content in various formats and lengths",
  inputSchema: textSummarizationSchema,
  execute: async (context) => {
    const {
      inputText,
      summaryLength,
      summaryFormat,
      focusArea,
      targetAudience,
      preserveKeyTerms,
      includeStatistics
    } = context.context;
    const wordCount = inputText.split(/\s+/).length;
    const characterCount = inputText.length;
    const estimatedReadingTime = Math.ceil(wordCount / 200);
    const lengthTargets = {
      brief: { sentences: "1-2", words: "20-50", description: "Very concise overview" },
      short: { sentences: "3-5", words: "50-100", description: "Brief summary" },
      medium: { sentences: "6-10", words: "100-200", description: "Moderate detail" },
      detailed: { sentences: "10-15", words: "200-300", description: "Comprehensive summary" }
    };
    const targetLength = lengthTargets[summaryLength];
    const formatInstructions = {
      paragraph: "Write as flowing paragraphs with smooth transitions",
      "bullet-points": "Present as clear, concise bullet points with parallel structure",
      "executive-summary": "Write as a formal executive summary with key insights upfront",
      "key-takeaways": "List the most important takeaways in order of importance",
      abstract: "Write as an academic-style abstract with methodology and conclusions",
      highlights: "Present as highlighted key points with brief explanations"
    };
    const focusInstructions = {
      "main-points": "Focus on the primary arguments and central themes",
      "key-findings": "Emphasize discoveries, results, and important findings",
      "actionable-items": "Highlight specific actions, recommendations, and next steps",
      conclusions: "Focus on final outcomes, decisions, and conclusions reached",
      methodology: "Emphasize the approach, process, and methods used",
      results: "Concentrate on outcomes, data, and measurable results",
      recommendations: "Focus on suggested actions and strategic recommendations"
    };
    let summarizationPrompt = `Summarize the following text content:

ORIGINAL TEXT:
${inputText}

SUMMARIZATION REQUIREMENTS:
- Length: ${summaryLength} (${targetLength.sentences} sentences, approximately ${targetLength.words} words)
- Format: ${summaryFormat} - ${formatInstructions[summaryFormat]}
- Focus: ${focusArea} - ${focusInstructions[focusArea]}
- Audience: ${targetAudience}
- Preserve key terms: ${preserveKeyTerms ? "Yes" : "No"}
- Include statistics: ${includeStatistics ? "Yes" : "No"}

INSTRUCTIONS:
1. ${formatInstructions[summaryFormat]}
2. ${focusInstructions[focusArea]}
3. Write for ${targetAudience} audience level
4. Keep within ${targetLength.words} words`;
    if (preserveKeyTerms) {
      summarizationPrompt += `
5. Preserve important technical terms and domain-specific vocabulary`;
    }
    if (includeStatistics) {
      summarizationPrompt += `
6. Include key numbers, percentages, and statistical data`;
    }
    if (summaryFormat === "bullet-points") {
      summarizationPrompt += `

Format as bullet points with each point being a complete thought.`;
    } else if (summaryFormat === "executive-summary") {
      summarizationPrompt += `

Structure as: Key insight, supporting details, implications.`;
    } else if (summaryFormat === "key-takeaways") {
      summarizationPrompt += `

List takeaways in order of importance with brief explanations.`;
    }
    summarizationPrompt += `

Generate the ${summaryLength} ${summaryFormat} summary now:`;
    const complexityIndicators = {
      technicalTerms: (inputText.match(/\b[A-Z]{2,}\b/g) || []).length,
      longSentences: inputText.split(".").filter((s) => s.split(" ").length > 20).length,
      avgWordsPerSentence: wordCount / (inputText.split(".").length || 1)
    };
    const complexityScore = Math.min(10, Math.round(
      complexityIndicators.technicalTerms * 0.3 + complexityIndicators.longSentences * 0.4 + complexityIndicators.avgWordsPerSentence * 0.3
    ));
    return {
      success: true,
      inputAnalysis: {
        wordCount,
        characterCount,
        estimatedReadingTime,
        complexityScore,
        complexityLevel: complexityScore < 3 ? "Simple" : complexityScore < 7 ? "Moderate" : "Complex"
      },
      summaryConfig: {
        length: summaryLength,
        format: summaryFormat,
        focusArea,
        targetAudience,
        targetWordCount: targetLength.words,
        targetSentences: targetLength.sentences
      },
      summarizationPrompt,
      metadata: {
        compressionRatio: `${Math.round(parseInt(targetLength.words.split("-")[1]) / wordCount * 100)}%`,
        preserveKeyTerms,
        includeStatistics,
        formatInstructions: formatInstructions[summaryFormat],
        focusInstructions: focusInstructions[focusArea]
      }
    };
  }
});
const grammarStyleSchema = z.object({
  inputText: z.string().min(10).describe("The text content to be checked and corrected"),
  correctionType: z.enum([
    "grammar-only",
    "style-only",
    "comprehensive",
    "tone-adjustment",
    "clarity-improvement",
    "conciseness"
  ]).default("comprehensive").describe("Type of corrections to apply"),
  targetTone: z.enum([
    "professional",
    "casual",
    "friendly",
    "formal",
    "conversational",
    "authoritative",
    "persuasive",
    "academic"
  ]).default("professional").describe("Desired tone for the text"),
  writingStyle: z.enum([
    "business",
    "academic",
    "creative",
    "technical",
    "journalistic",
    "marketing",
    "legal",
    "medical"
  ]).default("business").describe("Writing style context for corrections"),
  targetAudience: z.enum([
    "general",
    "technical",
    "executive",
    "academic",
    "beginner",
    "expert"
  ]).default("general").describe("Target audience for the content"),
  preserveVoice: z.boolean().default(true).describe("Whether to preserve the original author's voice and style"),
  suggestAlternatives: z.boolean().default(true).describe("Whether to provide alternative phrasings for improvements"),
  checkReadability: z.boolean().default(true).describe("Whether to analyze and improve readability"),
  fixPassiveVoice: z.boolean().default(false).describe("Whether to convert passive voice to active voice where appropriate")
});
const grammarStyleTool = createTool({
  id: "grammar-style-correction",
  description: "Check and correct grammar, style, tone, and readability of text content",
  inputSchema: grammarStyleSchema,
  execute: async (context) => {
    const {
      inputText,
      correctionType,
      targetTone,
      writingStyle,
      targetAudience,
      preserveVoice,
      suggestAlternatives,
      checkReadability,
      fixPassiveVoice
    } = context.context;
    const wordCount = inputText.split(/\s+/).length;
    const sentenceCount = inputText.split(/[.!?]+/).filter((s) => s.trim().length > 0).length;
    const avgWordsPerSentence = wordCount / sentenceCount;
    const paragraphCount = inputText.split(/\n\s*\n/).filter((p) => p.trim().length > 0).length;
    const correctionInstructions = {
      "grammar-only": "Focus only on grammatical errors, punctuation, and spelling mistakes",
      "style-only": "Focus on style improvements, word choice, and sentence structure",
      "comprehensive": "Address grammar, style, clarity, and overall writing quality",
      "tone-adjustment": "Adjust the tone and voice to match the target tone",
      "clarity-improvement": "Focus on making the text clearer and easier to understand",
      "conciseness": "Make the text more concise while preserving meaning"
    };
    const toneCharacteristics = {
      professional: "Clear, respectful, competent, and business-appropriate",
      casual: "Relaxed, informal, approachable, and conversational",
      friendly: "Warm, welcoming, personable, and engaging",
      formal: "Structured, traditional, respectful, and ceremonial",
      conversational: "Natural, dialogue-like, engaging, and personal",
      authoritative: "Confident, knowledgeable, decisive, and commanding",
      persuasive: "Compelling, convincing, motivating, and influential",
      academic: "Scholarly, precise, objective, and research-oriented"
    };
    const styleGuidelines = {
      business: "Clear, concise, action-oriented, and results-focused",
      academic: "Formal, precise, evidence-based, and objective",
      creative: "Expressive, imaginative, engaging, and original",
      technical: "Precise, detailed, logical, and specification-focused",
      journalistic: "Factual, balanced, clear, and informative",
      marketing: "Persuasive, benefit-focused, engaging, and action-oriented",
      legal: "Precise, formal, comprehensive, and unambiguous",
      medical: "Accurate, professional, clear, and patient-focused"
    };
    let correctionPrompt = `Review and improve the following text:

ORIGINAL TEXT:
${inputText}

CORRECTION REQUIREMENTS:
- Correction Type: ${correctionType} - ${correctionInstructions[correctionType]}
- Target Tone: ${targetTone} - ${toneCharacteristics[targetTone]}
- Writing Style: ${writingStyle} - ${styleGuidelines[writingStyle]}
- Target Audience: ${targetAudience}
- Preserve Voice: ${preserveVoice ? "Yes - maintain author's unique style" : "No - prioritize clarity and correctness"}
- Suggest Alternatives: ${suggestAlternatives ? "Yes" : "No"}
- Check Readability: ${checkReadability ? "Yes" : "No"}
- Fix Passive Voice: ${fixPassiveVoice ? "Yes" : "No"}

INSTRUCTIONS:
1. ${correctionInstructions[correctionType]}
2. Ensure the tone is ${targetTone}: ${toneCharacteristics[targetTone]}
3. Follow ${writingStyle} writing style: ${styleGuidelines[writingStyle]}
4. Write for ${targetAudience} audience level`;
    if (preserveVoice) {
      correctionPrompt += `
5. Preserve the author's unique voice and personality`;
    }
    if (checkReadability) {
      correctionPrompt += `
6. Improve readability and sentence flow`;
    }
    if (fixPassiveVoice) {
      correctionPrompt += `
7. Convert passive voice to active voice where appropriate`;
    }
    if (suggestAlternatives) {
      correctionPrompt += `

For significant changes, provide the original phrase and your suggested improvement with a brief explanation.`;
    }
    if (correctionType === "conciseness") {
      correctionPrompt += `

Focus on eliminating redundancy, wordiness, and unnecessary phrases while preserving meaning.`;
    } else if (correctionType === "clarity-improvement") {
      correctionPrompt += `

Simplify complex sentences, clarify ambiguous statements, and improve logical flow.`;
    } else if (correctionType === "tone-adjustment") {
      correctionPrompt += `

Adjust word choice, sentence structure, and phrasing to achieve the ${targetTone} tone.`;
    }
    correctionPrompt += `

Provide the corrected text now:`;
    const readabilityMetrics = {
      avgWordsPerSentence,
      avgSentencesPerParagraph: sentenceCount / paragraphCount,
      readabilityLevel: avgWordsPerSentence < 15 ? "Easy" : avgWordsPerSentence < 20 ? "Moderate" : "Difficult"
    };
    const potentialIssues = [];
    if (avgWordsPerSentence > 25) potentialIssues.push("Long sentences may reduce readability");
    if (sentenceCount / paragraphCount > 8) potentialIssues.push("Paragraphs may be too long");
    if (inputText.includes("  ")) potentialIssues.push("Multiple spaces detected");
    if (inputText.match(/\b(very|really|quite|rather)\b/gi)) potentialIssues.push("Weak intensifiers detected");
    return {
      success: true,
      inputAnalysis: {
        wordCount,
        sentenceCount,
        paragraphCount,
        avgWordsPerSentence: Math.round(avgWordsPerSentence * 10) / 10,
        readabilityMetrics,
        potentialIssues
      },
      correctionConfig: {
        correctionType,
        targetTone,
        writingStyle,
        targetAudience,
        preserveVoice,
        suggestAlternatives,
        checkReadability,
        fixPassiveVoice
      },
      correctionPrompt,
      metadata: {
        toneDescription: toneCharacteristics[targetTone],
        styleDescription: styleGuidelines[writingStyle],
        correctionFocus: correctionInstructions[correctionType],
        estimatedImprovements: potentialIssues.length
      }
    };
  }
});
const outlineGenerationSchema = z.object({
  topic: z.string().min(3).describe("The main topic or subject for the outline"),
  contentType: z.enum([
    "article",
    "blog-post",
    "whitepaper",
    "presentation",
    "report",
    "essay",
    "research-paper",
    "case-study",
    "tutorial",
    "guide",
    "book-chapter",
    "proposal"
  ]).describe("Type of content the outline is for"),
  targetAudience: z.enum([
    "general",
    "technical",
    "executive",
    "academic",
    "beginner",
    "intermediate",
    "expert",
    "students",
    "professionals"
  ]).default("general").describe("Target audience for the content"),
  outlineDepth: z.enum([
    "shallow",
    // 1-2 levels (main sections only)
    "medium",
    // 2-3 levels (sections and subsections)
    "deep"
    // 3-4 levels (detailed hierarchy)
  ]).default("medium").describe("How detailed the outline should be"),
  structureType: z.enum([
    "chronological",
    "problem-solution",
    "cause-effect",
    "compare-contrast",
    "hierarchical",
    "sequential",
    "thematic",
    "argumentative"
  ]).default("hierarchical").describe("Organizational structure for the outline"),
  estimatedLength: z.enum([
    "short",
    // 500-1000 words
    "medium",
    // 1000-2500 words
    "long",
    // 2500-5000 words
    "extended"
    // 5000+ words
  ]).default("medium").describe("Estimated length of the final content"),
  includeIntroduction: z.boolean().default(true).describe("Whether to include introduction section"),
  includeConclusion: z.boolean().default(true).describe("Whether to include conclusion section"),
  keyPoints: z.array(z.string()).optional().describe("Specific key points or topics that must be covered"),
  researchQuestions: z.array(z.string()).optional().describe("Research questions the content should address"),
  includeCallToAction: z.boolean().default(false).describe("Whether to include a call-to-action section")
});
const outlineGenerationTool = createTool({
  id: "outline-generation",
  description: "Generate structured, hierarchical outlines for various types of content",
  inputSchema: outlineGenerationSchema,
  execute: async (context) => {
    const {
      topic,
      contentType,
      targetAudience,
      outlineDepth,
      structureType,
      estimatedLength,
      includeIntroduction,
      includeConclusion,
      keyPoints,
      researchQuestions,
      includeCallToAction
    } = context.context;
    const contentStructures = {
      article: {
        defaultSections: ["Introduction", "Background", "Main Content", "Analysis", "Conclusion"],
        format: "informative article with clear sections"
      },
      "blog-post": {
        defaultSections: ["Hook", "Introduction", "Main Points", "Personal Insights", "Call to Action"],
        format: "engaging blog post structure"
      },
      whitepaper: {
        defaultSections: ["Executive Summary", "Problem Statement", "Solution Overview", "Detailed Analysis", "Benefits", "Implementation", "Conclusion"],
        format: "authoritative whitepaper structure"
      },
      presentation: {
        defaultSections: ["Title Slide", "Agenda", "Problem/Opportunity", "Solution", "Benefits", "Next Steps"],
        format: "presentation slide structure"
      },
      report: {
        defaultSections: ["Executive Summary", "Methodology", "Findings", "Analysis", "Recommendations", "Conclusion"],
        format: "formal report structure"
      },
      essay: {
        defaultSections: ["Introduction", "Thesis Statement", "Body Paragraphs", "Supporting Evidence", "Conclusion"],
        format: "academic essay structure"
      },
      "research-paper": {
        defaultSections: ["Abstract", "Introduction", "Literature Review", "Methodology", "Results", "Discussion", "Conclusion", "References"],
        format: "academic research paper structure"
      },
      "case-study": {
        defaultSections: ["Overview", "Challenge", "Solution", "Implementation", "Results", "Lessons Learned"],
        format: "business case study structure"
      },
      tutorial: {
        defaultSections: ["Introduction", "Prerequisites", "Step-by-Step Instructions", "Examples", "Troubleshooting", "Summary"],
        format: "instructional tutorial structure"
      },
      guide: {
        defaultSections: ["Overview", "Getting Started", "Main Sections", "Best Practices", "Common Pitfalls", "Resources"],
        format: "comprehensive guide structure"
      },
      "book-chapter": {
        defaultSections: ["Chapter Introduction", "Key Concepts", "Detailed Exploration", "Examples", "Chapter Summary"],
        format: "book chapter structure"
      },
      proposal: {
        defaultSections: ["Executive Summary", "Problem Statement", "Proposed Solution", "Timeline", "Budget", "Benefits", "Next Steps"],
        format: "business proposal structure"
      }
    };
    const structureApproaches = {
      chronological: "Organize content in time-based sequence",
      "problem-solution": "Present problem first, then solution",
      "cause-effect": "Show relationships between causes and effects",
      "compare-contrast": "Compare different options or viewpoints",
      hierarchical: "Organize from general to specific or by importance",
      sequential: "Follow logical step-by-step progression",
      thematic: "Group content by themes or topics",
      argumentative: "Build logical argument with evidence"
    };
    const depthSpecs = {
      shallow: {
        levels: 2,
        description: "Main sections with brief subsections",
        detail: "High-level overview with key points"
      },
      medium: {
        levels: 3,
        description: "Sections, subsections, and key points",
        detail: "Balanced detail with clear hierarchy"
      },
      deep: {
        levels: 4,
        description: "Detailed hierarchy with specific talking points",
        detail: "Comprehensive breakdown with specific details"
      }
    };
    const contentStructure = contentStructures[contentType];
    const depthSpec = depthSpecs[outlineDepth];
    let outlinePrompt = `Generate a detailed ${outlineDepth} outline for a ${contentType} about "${topic}".

OUTLINE REQUIREMENTS:
- Content Type: ${contentType} (${contentStructure.format})
- Target Audience: ${targetAudience}
- Structure Type: ${structureType} (${structureApproaches[structureType]})
- Outline Depth: ${outlineDepth} (${depthSpec.levels} levels - ${depthSpec.description})
- Estimated Length: ${estimatedLength}

STRUCTURE APPROACH:
${structureApproaches[structureType]}

DEFAULT SECTIONS TO CONSIDER:
${contentStructure.defaultSections.map((section, index) => `${index + 1}. ${section}`).join("\n")}

OUTLINE SPECIFICATIONS:
- Create ${depthSpec.levels} levels of hierarchy
- Use clear, descriptive headings
- Include specific talking points for each section
- Ensure logical flow between sections
- ${depthSpec.detail}`;
    if (includeIntroduction) {
      outlinePrompt += `
- Include a compelling introduction section`;
    }
    if (includeConclusion) {
      outlinePrompt += `
- Include a strong conclusion section`;
    }
    if (includeCallToAction) {
      outlinePrompt += `
- Include a call-to-action section`;
    }
    if (keyPoints && keyPoints.length > 0) {
      outlinePrompt += `
- Must cover these key points: ${keyPoints.join(", ")}`;
    }
    if (researchQuestions && researchQuestions.length > 0) {
      outlinePrompt += `
- Address these research questions: ${researchQuestions.join("; ")}`;
    }
    outlinePrompt += `

FORMAT INSTRUCTIONS:
- Use hierarchical numbering (1., 1.1, 1.1.1, etc.)
- Each section should have a clear, descriptive title
- Include 2-4 key points or subtopics under each main section
- Add brief descriptions for complex topics
- Ensure the outline flows logically from start to finish

Generate the complete ${outlineDepth} outline now:`;
    const sectionEstimates = {
      short: { main: 3, sub: 8, total: 11 },
      medium: { main: 5, sub: 15, total: 20 },
      long: { main: 7, sub: 25, total: 32 },
      extended: { main: 10, sub: 40, total: 50 }
    };
    const estimatedSections = sectionEstimates[estimatedLength];
    return {
      success: true,
      outlineConfig: {
        topic,
        contentType,
        targetAudience,
        outlineDepth,
        structureType,
        estimatedLength,
        levels: depthSpec.levels
      },
      contentStructure: {
        format: contentStructure.format,
        defaultSections: contentStructure.defaultSections,
        structureApproach: structureApproaches[structureType]
      },
      estimatedSections,
      outlinePrompt,
      metadata: {
        includeIntroduction,
        includeConclusion,
        includeCallToAction,
        keyPoints: keyPoints || [],
        researchQuestions: researchQuestions || [],
        depthDescription: depthSpec.description,
        structureDescription: structureApproaches[structureType]
      }
    };
  }
});
const citationSchema = z.object({
  sources: z.array(
    z.object({
      url: z.string().url().describe("URL of the source"),
      title: z.string().optional().describe("Title of the source (will be fetched if not provided)"),
      author: z.string().optional().describe("Author name(s)"),
      publishDate: z.string().optional().describe("Publication date"),
      accessDate: z.string().optional().describe("Date accessed (defaults to today)"),
      sourceType: z.enum([
        "website",
        "journal-article",
        "book",
        "news-article",
        "blog-post",
        "report",
        "whitepaper",
        "academic-paper",
        "government-document",
        "social-media"
      ]).default("website").describe("Type of source being cited")
    })
  ).min(1).describe("Array of sources to generate citations for"),
  citationStyle: z.enum([
    "APA",
    "MLA",
    "Chicago",
    "Harvard",
    "IEEE",
    "Vancouver",
    "AMA"
  ]).default("APA").describe("Citation style format to use"),
  includeInText: z.boolean().default(true).describe("Whether to include in-text citation examples"),
  includeBibliography: z.boolean().default(true).describe("Whether to include full bibliography entries"),
  sortAlphabetically: z.boolean().default(true).describe("Whether to sort bibliography entries alphabetically"),
  validateUrls: z.boolean().default(true).describe("Whether to validate and fetch metadata from URLs"),
  generateDoi: z.boolean().default(false).describe("Whether to attempt to find DOI for academic sources")
});
const citationTool = createTool({
  id: "citation-management",
  description: "Generate properly formatted citations in various academic and professional styles",
  inputSchema: citationSchema,
  execute: async (context) => {
    const {
      sources,
      citationStyle,
      includeInText,
      includeBibliography,
      sortAlphabetically,
      validateUrls,
      generateDoi
    } = context.context;
    const citationFormats = {
      APA: {
        name: "American Psychological Association (7th Edition)",
        inTextFormat: "(Author, Year)",
        websiteFormat: "Author, A. A. (Year, Month Date). Title of webpage. Website Name. URL",
        journalFormat: "Author, A. A. (Year). Title of article. Journal Name, Volume(Issue), pages. DOI or URL"
      },
      MLA: {
        name: "Modern Language Association (9th Edition)",
        inTextFormat: "(Author Page)",
        websiteFormat: 'Author Last, First. "Title of Webpage." Website Name, Date, URL.',
        journalFormat: 'Author Last, First. "Title of Article." Journal Name, vol. #, no. #, Year, pp. ##-##.'
      },
      Chicago: {
        name: "Chicago Manual of Style (17th Edition)",
        inTextFormat: "(Author Year, Page)",
        websiteFormat: 'Author Last, First. "Title of Webpage." Website Name. Date. URL.',
        journalFormat: 'Author Last, First. "Title of Article." Journal Name Volume, no. Issue (Year): pages.'
      },
      Harvard: {
        name: "Harvard Referencing Style",
        inTextFormat: "(Author Year)",
        websiteFormat: "Author, A. (Year) 'Title of webpage', Website Name, Date, Available at: URL",
        journalFormat: "Author, A. (Year) 'Title of article', Journal Name, Volume(Issue), pp. pages."
      },
      IEEE: {
        name: "Institute of Electrical and Electronics Engineers",
        inTextFormat: "[Number]",
        websiteFormat: '[#] Author, "Title of webpage," Website Name, Date. [Online]. Available: URL',
        journalFormat: '[#] Author, "Title of article," Journal Name, vol. #, no. #, pp. ##-##, Year.'
      },
      Vancouver: {
        name: "Vancouver Referencing Style",
        inTextFormat: "(Number)",
        websiteFormat: "Author A. Title of webpage [Internet]. Website Name; Year [cited Date]. Available from: URL",
        journalFormat: "Author A. Title of article. Journal Name. Year;Volume(Issue):pages."
      },
      AMA: {
        name: "American Medical Association",
        inTextFormat: "(Number)",
        websiteFormat: "Author A. Title of webpage. Website Name. Published Date. Accessed Date. URL",
        journalFormat: "Author A. Title of article. Journal Name. Year;Volume(Issue):pages."
      }
    };
    const selectedFormat = citationFormats[citationStyle];
    const processedSources = [];
    let citationNumber = 1;
    for (const source of sources) {
      let processedSource = {
        ...source,
        citationNumber,
        accessDate: source.accessDate || (/* @__PURE__ */ new Date()).toISOString().split("T")[0]
      };
      if (validateUrls && source.url) {
        try {
          const searchResult = await webSearchTool.execute({
            context: {
              query: source.url,
              includeDetails: true,
              numResults: 1
            }
          });
          if (searchResult.generalInformation?.results?.[0]) {
            const metadata = searchResult.generalInformation.results[0];
            processedSource = {
              ...processedSource,
              title: processedSource.title || metadata.title,
              author: processedSource.author || metadata.author,
              publishDate: processedSource.publishDate || metadata.publishedDate
            };
          }
        } catch (error) {
          console.warn(`Failed to fetch metadata for ${source.url}:`, error);
        }
      }
      if (generateDoi && (source.sourceType === "journal-article" || source.sourceType === "academic-paper")) {
        processedSource.needsDoiLookup = true;
      }
      processedSources.push(processedSource);
      citationNumber++;
    }
    if (sortAlphabetically) {
      processedSources.sort((a, b) => {
        const authorA = a.author || a.title || a.url;
        const authorB = b.author || b.title || b.url;
        return authorA.localeCompare(authorB);
      });
    }
    let citationPrompt = `Generate ${citationStyle} style citations for the following sources:

CITATION STYLE: ${selectedFormat.name}
IN-TEXT FORMAT: ${selectedFormat.inTextFormat}
WEBSITE FORMAT: ${selectedFormat.websiteFormat}
JOURNAL FORMAT: ${selectedFormat.journalFormat}

SOURCES TO CITE:
${processedSources.map((source, index) => `
${index + 1}. URL: ${source.url}
   Title: ${source.title || "To be determined from URL"}
   Author: ${source.author || "Unknown"}
   Source Type: ${source.sourceType}
   Publish Date: ${source.publishDate || "Unknown"}
   Access Date: ${source.accessDate}
`).join("")}

REQUIREMENTS:
- Use ${citationStyle} citation style format exactly
- Include proper punctuation and formatting
- Handle missing information appropriately for ${citationStyle} style`;
    if (includeInText) {
      citationPrompt += `
- Provide in-text citation examples for each source`;
    }
    if (includeBibliography) {
      citationPrompt += `
- Generate complete bibliography/reference list entries`;
    }
    if (sortAlphabetically) {
      citationPrompt += `
- Sort bibliography entries alphabetically`;
    }
    citationPrompt += `

Generate the properly formatted ${citationStyle} citations now:`;
    const sourceTypeCount = processedSources.reduce((acc, source) => {
      acc[source.sourceType] = (acc[source.sourceType] || 0) + 1;
      return acc;
    }, {});
    const missingInfo = processedSources.reduce((acc, source) => {
      if (!source.title) acc.missingTitles++;
      if (!source.author) acc.missingAuthors++;
      if (!source.publishDate) acc.missingDates++;
      return acc;
    }, { missingTitles: 0, missingAuthors: <AUTHORS>
    return {
      success: true,
      citationConfig: {
        style: citationStyle,
        styleName: selectedFormat.name,
        sourceCount: processedSources.length,
        includeInText,
        includeBibliography,
        sortAlphabetically
      },
      sourceAnalysis: {
        sourceTypeBreakdown: sourceTypeCount,
        missingInformation: missingInfo,
        validationPerformed: validateUrls,
        doiLookupRequested: generateDoi
      },
      processedSources: processedSources.map((source) => ({
        citationNumber: source.citationNumber,
        url: source.url,
        title: source.title,
        author: source.author,
        sourceType: source.sourceType,
        publishDate: source.publishDate,
        accessDate: source.accessDate,
        needsDoiLookup: source.needsDoiLookup || false
      })),
      citationPrompt,
      metadata: {
        styleFormat: selectedFormat,
        totalSources: processedSources.length,
        completenessScore: Math.round((processedSources.length * 3 - missingInfo.missingTitles - missingInfo.missingAuthors - missingInfo.missingDates) / (processedSources.length * 3) * 100)
      }
    };
  }
});
const CONTENT_AGENT_SYSTEM_PROMPT = `
You are an expert Content Agent with advanced capabilities in content creation, editing, and optimization. Your mission is to assist users in creating high-quality, engaging, and well-structured content across various formats and industries.

## CORE CAPABILITIES

### 1. CONTENT GENERATION
- Create original content for articles, blog posts, whitepapers, social media, emails, and documentation
- Adapt tone, style, and complexity to match target audience and purpose
- Generate content that is informative, engaging, and actionable
- Incorporate SEO best practices and keyword optimization when requested

### 2. TEXT SUMMARIZATION
- Create concise summaries in various formats (paragraphs, bullet points, executive summaries)
- Maintain key information while reducing length significantly
- Adapt summary style to target audience and purpose
- Preserve important statistics, findings, and actionable insights

### 3. GRAMMAR & STYLE CORRECTION
- Fix grammatical errors, punctuation, and spelling mistakes
- Improve sentence structure, clarity, and readability
- Adjust tone and style to match desired voice and audience
- Enhance flow and coherence while preserving author's intent

### 4. OUTLINE GENERATION
- Create structured, hierarchical outlines for any content type
- Organize information logically using various structural approaches
- Generate outlines at different depth levels based on content complexity
- Ensure comprehensive coverage of topics with logical flow

### 5. CITATION MANAGEMENT
- Generate properly formatted citations in multiple academic and professional styles
- Validate source information and fetch metadata when possible
- Create both in-text citations and bibliography entries
- Ensure citation accuracy and completeness

### 6. WEB RESEARCH
- Conduct comprehensive research on topics using web search
- Find credible sources and current information
- Synthesize research findings into coherent insights
- Support content with factual, up-to-date information

## TOOLS AVAILABLE

1. **contentGeneration**: Generate original content based on topic, type, audience, and style preferences
2. **textSummarization**: Create concise summaries of existing text in various formats
3. **grammarStyleCorrection**: Check and improve grammar, style, tone, and readability
4. **outlineGeneration**: Generate structured outlines for content planning
5. **citationManagement**: Create properly formatted citations and bibliographies
6. **webSearch**: Research topics and find credible sources for content support

## INTERACTION GUIDELINES

### CONTENT CREATION WORKFLOW
1. **Understanding Requirements**: Always clarify the content type, target audience, tone, and purpose
2. **Research Phase**: Use web search to gather current, relevant information when needed
3. **Planning Phase**: Create outlines for longer content to ensure logical structure
4. **Creation Phase**: Generate content using appropriate tools and techniques
5. **Refinement Phase**: Review and improve content for clarity, style, and accuracy
6. **Citation Phase**: Add proper citations for any referenced sources

### QUALITY STANDARDS
- **Accuracy**: Ensure all information is factual and current
- **Clarity**: Write in clear, accessible language appropriate for the audience
- **Engagement**: Create content that captures and maintains reader interest
- **Structure**: Organize content with logical flow and clear hierarchy
- **Originality**: Generate unique content while properly citing sources
- **Completeness**: Address all aspects of the topic comprehensively

### RESPONSE FORMAT
- Provide clear, actionable responses
- Explain your reasoning and approach when helpful
- Offer alternatives and suggestions for improvement
- Include relevant metadata about content (word count, reading time, etc.)
- Suggest next steps or follow-up actions when appropriate

## SPECIALIZED CAPABILITIES

### AUTO-OUTLINE MODE
When users provide topic, content type, and audience parameters, automatically:
1. Generate a comprehensive outline using the outline generation tool
2. Structure the outline appropriately for the specified content type
3. Ensure the outline matches the target audience level
4. Provide the outline in a format ready for content development

### COLLABORATIVE EDITING
- Work iteratively with users to refine and improve content
- Provide specific suggestions for enhancement
- Maintain consistency in tone and style throughout revisions
- Track changes and explain improvements made

### MULTI-FORMAT ADAPTATION
- Adapt content between different formats (article to blog post, etc.)
- Maintain core message while adjusting structure and style
- Optimize content for specific platforms and mediums
- Ensure format-appropriate length and complexity

## TOOL PARAMETER EXTRACTION

**CRITICAL**: Always extract the following parameters from user requests:

### Content Generation Tool Parameters:
- **topic** (REQUIRED): Extract the main subject/topic from user request
- **contentType**: article, blog-post, whitepaper, etc.
- **targetAudience**: general, technical, executive, etc.
- **tone**: professional, casual, friendly, etc.
- **length**: short, medium, long, extended

### Outline Generation Tool Parameters:
- **topic** (REQUIRED): Extract the main subject/topic from user request
- **contentType**: article, blog-post, presentation, etc.
- **targetAudience**: general, technical, executive, etc.
- **outlineDepth**: shallow, medium, deep

### Parameter Extraction Examples:
- "Generate an outline about AI in healthcare" → topic: "AI in healthcare"
- "Create content on sustainable energy for executives" → topic: "sustainable energy", targetAudience: "executive"
- "Write a blog post about machine learning" → topic: "machine learning", contentType: "blog-post"

## BEST PRACTICES

1. **Always extract the topic parameter** - this is REQUIRED for content and outline tools
2. **Use appropriate tools** for each task rather than trying to do everything manually
3. **Provide context** for your recommendations and changes
4. **Maintain consistency** in tone, style, and formatting throughout content
5. **Cite sources properly** when using external information
6. **Consider SEO implications** for web-published content
7. **Optimize for readability** while maintaining depth and accuracy
8. **Suggest improvements** proactively when you identify opportunities

## ERROR HANDLING
- If a tool fails, explain the issue and provide alternative approaches
- When information is missing, clearly state assumptions made
- If content requirements conflict, ask for clarification
- Always provide the best possible output even with limited information

Remember: Your goal is to be a comprehensive content creation partner that helps users produce high-quality, effective content efficiently. Be proactive, thorough, and always focused on delivering value to the end reader.
`;
function createContentAgent(llmConfig) {
  const defaultConfig = {
    provider: "anthropic",
    model: "claude-3-5-sonnet-20241022"
  };
  const finalConfig = defaultConfig;
  const model = createLLMClient(finalConfig);
  return new Agent({
    name: "Content Agent",
    instructions: CONTENT_AGENT_SYSTEM_PROMPT,
    model,
    tools: {
      contentGeneration: contentGenerationTool,
      textSummarization: textSummarizationTool,
      grammarStyleCorrection: grammarStyleTool,
      outlineGeneration: outlineGenerationTool,
      citationManagement: citationTool,
      webSearch: webSearchTool
    }
  });
}
const contentAgent = createContentAgent();
const apolloSearchCompanySchema = z.object({
  domain: z.string().optional().describe("Company domain (e.g., coreweave.com)"),
  name: z.string().optional().describe("Company name (e.g., CoreWeave)")
});
const apolloSearchCompanyTool = createTool({
  id: "apollo_search_company",
  description: "Enrich target company data using Apollo API by domain or company name",
  inputSchema: apolloSearchCompanySchema,
  execute: async (context) => {
    const { domain, name } = context.context;
    const apiKey = private_env.APOLLO_API_KEY;
    if (!apiKey) {
      console.error("Apollo API credentials missing - returning mock data for testing");
      return {
        success: true,
        company: {
          id: "mock-company-id",
          name: name || "Sample Company",
          domain: domain || "sample.com",
          industry: "Technology",
          employees: "201-500",
          revenue: "$50M-$100M",
          description: "Mock company data for testing purposes",
          founded_year: 2020,
          headquarters: {
            city: "San Francisco",
            state: "CA",
            country: "United States"
          }
        },
        note: "Mock data returned - Apollo API credentials not configured"
      };
    }
    if (!domain && !name) {
      throw new Error("Either domain or company name must be provided");
    }
    try {
      const url = new URL("https://api.apollo.io/api/v1/organizations/enrich");
      if (domain) {
        url.searchParams.append("domain", domain);
      }
      if (name) {
        url.searchParams.append("name", name);
      }
      console.log("Making Apollo organization enrichment request for:", domain || name);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch(url.toString(), {
        method: "GET",
        headers: {
          "X-Api-Key": apiKey,
          "Content-Type": "application/json"
        },
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`Apollo API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      if (!data.organization) {
        throw new Error("No organization data found in Apollo response");
      }
      const org = data.organization;
      return {
        success: true,
        company: {
          id: org.id,
          name: org.name,
          domain: org.primary_domain,
          industry: org.industry,
          employees: org.estimated_num_employees ? `${org.estimated_num_employees}` : "Unknown",
          revenue: org.annual_revenue ? `$${org.annual_revenue}` : "Unknown",
          description: org.short_description || org.description,
          founded_year: org.founded_year,
          headquarters: {
            city: org.primary_city,
            state: org.primary_state,
            country: org.primary_country
          },
          linkedin_url: org.linkedin_url,
          website_url: org.website_url,
          phone: org.phone,
          technologies: org.technologies?.map((tech) => tech.name) || []
        }
      };
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        throw new Error("Apollo API request timeout after 30 seconds");
      }
      console.error("Apollo search company error:", error);
      throw new Error(`Apollo API error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const apolloFindCompaniesSchema = z.object({
  industry: z.string().optional().describe("Industry to search for (e.g., Technology, Software)"),
  employee_range: z.string().optional().describe("Employee count range (e.g., 201-500, 501-1000)"),
  revenue_range: z.string().optional().describe("Revenue range (e.g., $50M-$100M)"),
  location: z.string().optional().describe("Company location (e.g., San Francisco, CA)"),
  technologies: z.array(z.string()).optional().describe("Technologies used by the company"),
  keywords: z.array(z.string()).optional().describe("Keywords to search for in company descriptions"),
  limit: z.number().optional().default(10).describe("Maximum number of companies to return (max 25)")
});
const apolloFindCompaniesTool = createTool({
  id: "apollo_find_companies",
  description: "Discover matching companies based on attributes using Apollo API",
  inputSchema: apolloFindCompaniesSchema,
  execute: async (context) => {
    const { industry, employee_range, revenue_range, location, technologies, keywords, limit } = context.context;
    const apiKey = private_env.APOLLO_API_KEY;
    if (!apiKey) {
      console.error("Apollo API credentials missing - returning mock data for testing");
      const mockCompanies = Array.from({ length: Math.min(limit, 5) }, (_, i) => ({
        id: `mock-company-${i + 1}`,
        name: `Sample Company ${i + 1}`,
        domain: `sample${i + 1}.com`,
        industry: industry || "Technology",
        employees: employee_range || "201-500",
        revenue: revenue_range || "$50M-$100M",
        description: `Mock company ${i + 1} for testing purposes`,
        location: location || "San Francisco, CA",
        founded_year: 2020 - i,
        website_url: `https://sample${i + 1}.com`
      }));
      return {
        success: true,
        companies: mockCompanies,
        total_results: mockCompanies.length,
        note: "Mock data returned - Apollo API credentials not configured"
      };
    }
    try {
      const searchFilters = {};
      if (industry) {
        searchFilters.q_organization_keyword_tags = [industry];
      }
      if (employee_range) {
        const match = employee_range.match(/(\d+)-(\d+)/);
        if (match) {
          searchFilters.organization_num_employees_ranges = [`${match[1]},${match[2]}`];
        }
      }
      if (location) {
        searchFilters.q_organization_locations = [location];
      }
      if (technologies && technologies.length > 0) {
        searchFilters.organization_technology_names = technologies;
      }
      if (keywords && keywords.length > 0) {
        searchFilters.q_keywords = keywords.join(" ");
      }
      const requestData = {
        ...searchFilters,
        page: 1,
        per_page: Math.min(limit, 25),
        // Apollo API limit
        organization_locations: searchFilters.q_organization_locations,
        person_locations: void 0
        // Focus on organization search
      };
      console.log("Making Apollo organization search request with filters:", requestData);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch("https://api.apollo.io/api/v1/mixed_companies/search", {
        method: "POST",
        headers: {
          "X-Api-Key": apiKey,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`Apollo API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      if (!data.organizations) {
        throw new Error("No organizations data found in Apollo response");
      }
      const companies = data.organizations.map((org) => ({
        id: org.id,
        name: org.name,
        domain: org.primary_domain,
        industry: org.industry,
        employees: org.estimated_num_employees ? `${org.estimated_num_employees}` : "Unknown",
        revenue: org.annual_revenue ? `$${org.annual_revenue}` : "Unknown",
        description: org.short_description || org.description,
        location: [org.primary_city, org.primary_state, org.primary_country].filter(Boolean).join(", "),
        founded_year: org.founded_year,
        website_url: org.website_url,
        linkedin_url: org.linkedin_url,
        phone: org.phone,
        technologies: org.technologies?.map((tech) => tech.name) || []
      }));
      return {
        success: true,
        companies,
        total_results: data.pagination?.total_entries || companies.length,
        page: data.pagination?.page || 1,
        per_page: data.pagination?.per_page || limit
      };
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        throw new Error("Apollo API request timeout after 30 seconds");
      }
      console.error("Apollo find companies error:", error);
      throw new Error(`Apollo API error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const apolloFindContactsSchema = z.object({
  company_domains: z.array(z.string()).describe("Array of company domains to find contacts for"),
  job_titles: z.array(z.string()).optional().describe("Job titles to search for (e.g., CEO, CTO, VP)"),
  seniority_levels: z.array(z.string()).optional().describe("Seniority levels (e.g., senior, director, vp, c_suite)"),
  departments: z.array(z.string()).optional().describe("Departments (e.g., engineering, sales, marketing)"),
  limit_per_company: z.number().optional().default(3).describe("Maximum contacts per company (max 10)")
});
const apolloFindContactsTool = createTool({
  id: "apollo_find_contacts",
  description: "Retrieve contacts for companies using Apollo API",
  inputSchema: apolloFindContactsSchema,
  execute: async (context) => {
    const { company_domains, job_titles, seniority_levels, departments, limit_per_company } = context.context;
    const apiKey = private_env.APOLLO_API_KEY;
    if (!apiKey) {
      console.error("Apollo API credentials missing - returning mock data for testing");
      const mockContacts = {};
      company_domains.forEach((domain, domainIndex) => {
        mockContacts[domain] = Array.from({ length: Math.min(limit_per_company, 3) }, (_, i) => ({
          id: `mock-contact-${domainIndex}-${i + 1}`,
          first_name: `John${i + 1}`,
          last_name: `Doe${i + 1}`,
          title: job_titles?.[i] || "Software Engineer",
          email: `john.doe${i + 1}@${domain}`,
          linkedin_url: `https://linkedin.com/in/john-doe-${i + 1}`,
          company_name: `Company ${domainIndex + 1}`,
          seniority: seniority_levels?.[i] || "senior",
          department: departments?.[i] || "engineering"
        }));
      });
      return {
        success: true,
        contacts_by_company: mockContacts,
        total_contacts: company_domains.length * Math.min(limit_per_company, 3),
        note: "Mock data returned - Apollo API credentials not configured"
      };
    }
    try {
      const contactsByCompany = {};
      let totalContacts = 0;
      for (const domain of company_domains) {
        try {
          const searchFilters = {
            q_organization_domains: [domain],
            page: 1,
            per_page: Math.min(limit_per_company, 10)
            // Apollo API limit per request
          };
          if (job_titles && job_titles.length > 0) {
            searchFilters.person_titles = job_titles;
          }
          if (seniority_levels && seniority_levels.length > 0) {
            searchFilters.person_seniorities = seniority_levels;
          }
          if (departments && departments.length > 0) {
            searchFilters.q_person_departments = departments;
          }
          console.log(`Making Apollo people search request for domain: ${domain}`);
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 3e4);
          const response = await fetch("https://api.apollo.io/api/v1/mixed_people/search", {
            method: "POST",
            headers: {
              "X-Api-Key": apiKey,
              "Content-Type": "application/json"
            },
            body: JSON.stringify(searchFilters),
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          if (!response.ok) {
            console.warn(`Apollo API error for domain ${domain}: ${response.status} ${response.statusText}`);
            contactsByCompany[domain] = [];
            continue;
          }
          const data = await response.json();
          if (!data.people) {
            console.warn(`No people data found for domain: ${domain}`);
            contactsByCompany[domain] = [];
            continue;
          }
          const contacts = data.people.map((person) => ({
            id: person.id,
            first_name: person.first_name,
            last_name: person.last_name,
            name: person.name,
            title: person.title,
            email: person.email,
            linkedin_url: person.linkedin_url,
            company_name: person.organization?.name,
            company_domain: domain,
            seniority: person.seniority,
            department: person.departments?.[0],
            location: [person.city, person.state, person.country].filter(Boolean).join(", "),
            phone: person.phone_numbers?.[0]?.sanitized_number
          }));
          contactsByCompany[domain] = contacts;
          totalContacts += contacts.length;
          if (company_domains.indexOf(domain) < company_domains.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 1e3));
          }
        } catch (error) {
          console.warn(`Error fetching contacts for domain ${domain}:`, error);
          contactsByCompany[domain] = [];
        }
      }
      return {
        success: true,
        contacts_by_company: contactsByCompany,
        total_contacts: totalContacts,
        companies_processed: company_domains.length
      };
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        throw new Error("Apollo API request timeout after 30 seconds");
      }
      console.error("Apollo find contacts error:", error);
      throw new Error(`Apollo API error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const exaSearchEnhancedSchema = z.object({
  company_name: z.string().describe("Company name to research"),
  search_type: z.enum(["competitors", "intelligence", "both"]).default("both").describe("Type of search to perform"),
  industry: z.string().optional().describe("Industry context for better competitor matching"),
  numResults: z.number().default(15).describe("Number of search results to return (max 25)")
});
const exaSearchEnhancedTool = createTool({
  id: "exa_search",
  description: "Find competitors and deep company intelligence using Exa AI",
  inputSchema: exaSearchEnhancedSchema,
  execute: async (context) => {
    const { company_name, search_type, industry, numResults } = context.context;
    const apiKey = private_env.EXA_API_KEY;
    if (!apiKey) {
      console.error("Exa API credentials missing - returning mock data for testing");
      return {
        success: true,
        company_intelligence: {
          overview: `${company_name} is a technology company in the ${industry || "technology"} sector.`,
          business_model: "B2B SaaS platform",
          key_products: ["Product A", "Product B"],
          target_market: "Enterprise customers",
          funding: "Series B funded",
          recent_news: ["Recent product launch", "Partnership announcement"]
        },
        competitors: [
          {
            name: "Competitor 1",
            domain: "competitor1.com",
            similarity_reason: "Similar product offering",
            market_position: "Direct competitor"
          },
          {
            name: "Competitor 2",
            domain: "competitor2.com",
            similarity_reason: "Same target market",
            market_position: "Indirect competitor"
          }
        ],
        note: "Mock data returned - Exa API credentials not configured"
      };
    }
    const exa = new Exa(apiKey);
    try {
      let companyIntelligence = null;
      let competitors = [];
      if (search_type === "intelligence" || search_type === "both") {
        const intelligenceQueries = [
          `${company_name} company overview business model`,
          `${company_name} products services offerings`,
          `${company_name} funding revenue financial performance`,
          `${company_name} recent news updates announcements`,
          `${company_name} target market customers strategy`
        ];
        const intelligenceResults = await Promise.all(
          intelligenceQueries.map(async (query) => {
            try {
              return await exa.searchAndContents(query, {
                numResults: 3,
                text: true,
                category: "company"
              });
            } catch (error) {
              console.warn(`Intelligence search failed for query: ${query}`, error);
              return { results: [] };
            }
          })
        );
        const allIntelligenceResults = intelligenceResults.flatMap((result) => result.results || []);
        companyIntelligence = {
          overview: extractIntelligence(allIntelligenceResults, "overview"),
          business_model: extractIntelligence(allIntelligenceResults, "business model"),
          key_products: extractIntelligence(allIntelligenceResults, "products"),
          target_market: extractIntelligence(allIntelligenceResults, "target market"),
          funding: extractIntelligence(allIntelligenceResults, "funding"),
          recent_news: extractIntelligence(allIntelligenceResults, "news"),
          sources: allIntelligenceResults.map((result) => ({
            title: result.title,
            url: result.url,
            snippet: result.text?.substring(0, 200) + "..."
          }))
        };
      }
      if (search_type === "competitors" || search_type === "both") {
        const competitorQueries = [
          `${company_name} competitors alternatives similar companies`,
          `companies like ${company_name} ${industry || ""}`,
          `${company_name} vs competitors comparison`,
          `${industry || "technology"} companies similar to ${company_name}`
        ];
        const competitorResults = await Promise.all(
          competitorQueries.map(async (query) => {
            try {
              return await exa.searchAndContents(query, {
                numResults: Math.ceil(numResults / competitorQueries.length),
                text: true,
                category: "company"
              });
            } catch (error) {
              console.warn(`Competitor search failed for query: ${query}`, error);
              return { results: [] };
            }
          })
        );
        const allCompetitorResults = competitorResults.flatMap((result) => result.results || []);
        competitors = extractCompetitors(allCompetitorResults, company_name);
      }
      return {
        success: true,
        company_intelligence: companyIntelligence,
        competitors: competitors.slice(0, numResults),
        total_sources: (companyIntelligence?.sources?.length || 0) + competitors.length
      };
    } catch (error) {
      console.error("Exa enhanced search error:", error);
      throw new Error(`Exa API error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
function extractIntelligence(results, type) {
  const relevantResults = results.filter(
    (result) => result.text && result.text.toLowerCase().includes(type.toLowerCase())
  );
  if (relevantResults.length === 0) return "Information not available";
  const sentences = relevantResults.map((result) => result.text).join(" ").split(".").filter((sentence) => sentence.toLowerCase().includes(type.toLowerCase())).slice(0, 3).join(". ");
  return sentences || "Information not available";
}
function extractCompetitors(results, targetCompany) {
  const competitors = [];
  const seenDomains = /* @__PURE__ */ new Set();
  results.forEach((result) => {
    if (!result.text) return;
    const text = result.text.toLowerCase();
    const targetLower = targetCompany.toLowerCase();
    if (text.includes(targetLower) && !text.includes("vs") && !text.includes("competitor")) {
      return;
    }
    let domain = "";
    try {
      const url = new URL(result.url);
      domain = url.hostname.replace("www.", "");
    } catch (error) {
      return;
    }
    if (seenDomains.has(domain)) return;
    seenDomains.add(domain);
    let similarityReason = "Found in competitor research";
    if (text.includes("similar")) similarityReason = "Similar business model";
    if (text.includes("alternative")) similarityReason = "Alternative solution";
    if (text.includes("competitor")) similarityReason = "Direct competitor";
    competitors.push({
      name: result.title.split(" - ")[0].split(" | ")[0],
      // Clean up title
      domain,
      similarity_reason: similarityReason,
      market_position: text.includes("direct") ? "Direct competitor" : "Indirect competitor",
      source_url: result.url,
      description: result.text.substring(0, 200) + "..."
    });
  });
  return competitors.slice(0, 10);
}
const ORCHESTRATOR_SYSTEM_PROMPT = `
You are an expert Company Research Orchestrator agent. Your mission is to provide comprehensive company intelligence with competitor analysis and contact information in structured JSON format.

**WORKFLOW PROCESS:**
1. **Input Processing**: Accept domain name or company name (e.g., "coreweave.com", "singlestore.com", "coreweave", "singlestore")
2. **Company Enrichment**: Use apollo_search_company to gather initial company details
3. **Intelligence Gathering**: Use exa_search to find competitors and deep company intelligence
4. **Attribute Extraction**: Build company attribute list from gathered data for similarity matching
5. **Company Discovery**: Use apollo_find_companies to find 10 similar/competitor companies
6. **Contact Retrieval**: Use apollo_find_contacts to get up to 3 contacts per company (including original target)
7. **Response Formatting**: Return structured JSON response with companies and contact details

**TOOL USAGE GUIDELINES:**

**apollo_search_company**: Use this first to enrich the target company data
- Provide either domain (preferred) or company name
- Extract key attributes like industry, employee count, revenue, location

**exa_search**: Use this to find competitors and gather company intelligence
- Set search_type to "both" for comprehensive research
- Use the industry from apollo_search_company for better context
- Extract competitor domains and company insights

**apollo_find_companies**: Use this to discover similar companies
- Use attributes from the target company (industry, employee range, location, technologies)
- Limit to 10 companies maximum
- Focus on companies similar to the target

**apollo_find_contacts**: Use this last to get contacts for all companies
- Include the target company domain plus all discovered competitor domains
- Request contacts with senior titles (CEO, CTO, VP, Director)
- Limit to 3 contacts per company

**OUTPUT FORMAT:**
Always return a structured JSON response with this exact format:

{
  "targetCompany": {
    "name": "Company Name",
    "domain": "domain.com",
    "industry": "Industry",
    "employees": "Employee Range",
    "revenue": "Revenue Range",
    "description": "Company description",
    "location": "City, State, Country",
    "founded_year": 2020,
    "contacts": [
      {
        "name": "Full Name",
        "title": "Job Title",
        "email": "<EMAIL>",
        "linkedin_url": "LinkedIn URL"
      }
    ]
  },
  "competitors": [
    {
      "name": "Competitor Name",
      "domain": "competitor.com",
      "similarity": "Similarity reason",
      "industry": "Industry",
      "employees": "Employee Range",
      "description": "Company description",
      "contacts": [...]
    }
  ],
  "intelligence": {
    "market_insights": "Market analysis",
    "competitive_landscape": "Competitive positioning",
    "key_differentiators": "What makes target company unique"
  },
  "metadata": {
    "totalCompanies": 11,
    "totalContacts": 33,
    "processingTime": "Processing duration",
    "dataQuality": "High/Medium/Low"
  }
}

**IMPORTANT GUIDELINES:**
- Always start with apollo_search_company for the target company
- Use the target company's attributes to find similar companies
- Ensure all domains are included in the contact search
- Handle errors gracefully and provide partial results if needed
- Be thorough but efficient in your research process
- Focus on actionable business intelligence and quality contacts

Execute the workflow step by step and provide comprehensive company research results.
`;
function createOrchestratorAgent(llmConfig) {
  const model = createLLMClient({
    provider: "anthropic",
    model: "claude-3-5-sonnet-20241022"
  });
  return new Agent({
    name: "Orchestrator Agent",
    instructions: ORCHESTRATOR_SYSTEM_PROMPT,
    model,
    tools: {
      apollo_search_company: apolloSearchCompanyTool,
      apollo_find_companies: apolloFindCompaniesTool,
      apollo_find_contacts: apolloFindContactsTool,
      exa_search: exaSearchEnhancedTool
    }
  });
}
const orchestratorAgent = createOrchestratorAgent();
export {
  companyResearcherAgent as a,
  contentAgent as c,
  orchestratorAgent as o
};
