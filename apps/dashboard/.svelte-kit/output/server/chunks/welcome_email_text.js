import { A as fallback, e as escape_html, B as bind_props } from "./index2.js";
import { a as WebsiteBaseUrl } from "./config.js";
function Welcome_email_text($$payload, $$props) {
  const ssr = true;
  let companyName = fallback($$props["companyName"], "");
  $$payload.out.push(`<!---->Welcome to ${escape_html(companyName)}! 

This is a quick sample of a welcome email. You can customize this email to fit your needs.

To unsubscribe, visit: ${escape_html(WebsiteBaseUrl)}/account/settings/change_email_subscription`);
  bind_props($$props, { companyName, ssr });
}
export {
  Welcome_email_text as default
};
