import { createTool, Agent } from "@mastra/core";
import { createOpenAI } from "@ai-sdk/openai";
import { createAnthropic } from "@ai-sdk/anthropic";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { a as private_env } from "./shared-server.js";
import { z } from "zod";
import Exa from "exa-js";
function createLLMClient(config) {
  const { provider, model } = config;
  console.log("=== LLM CLIENT CONFIGURATION ===");
  console.log("Provider:", provider);
  console.log("Model:", model);
  console.log("Config source:", config);
  switch (provider) {
    case "openai":
      if (!private_env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY is required for OpenAI provider");
      }
      const openaiClient = createOpenAI({
        apiKey: private_env.OPENAI_API_KEY
      });
      return openaiClient(model);
    case "anthropic":
      if (!private_env.ANTHROPIC_API_KEY) {
        throw new Error("ANTHROPIC_API_KEY is required for Anthropic provider");
      }
      const anthropicClient = createAnthropic({
        apiKey: private_env.ANTHROPIC_API_KEY
      });
      return anthropicClient(model);
    case "google":
      if (!private_env.GOOGLE_GENERATIVE_AI_API_KEY) {
        throw new Error("GOOGLE_GENERATIVE_AI_API_KEY is required for Google provider");
      }
      const googleClient = createGoogleGenerativeAI({
        apiKey: private_env.GOOGLE_GENERATIVE_AI_API_KEY
      });
      return googleClient(model);
    case "deepseek":
      if (!private_env.DEEPSEEK_API_KEY) {
        throw new Error("DEEPSEEK_API_KEY is required for DeepSeek provider");
      }
      const deepseek = createOpenAICompatible({
        name: "deepseek",
        apiKey: private_env.DEEPSEEK_API_KEY,
        baseURL: "https://api.deepseek.com/v1"
      });
      return deepseek(model);
    default:
      throw new Error(`Unsupported LLM provider: ${provider}`);
  }
}
const webSearchSchema = z.object({
  query: z.string().describe("Company name or search query for research"),
  includeDetails: z.boolean().default(true).describe("Whether to include detailed content from search results"),
  numResults: z.number().default(10).describe("Number of search results to return (max 25)")
});
const webSearchTool = createTool({
  id: "web-search",
  description: "Search the web for comprehensive information about companies using Exa AI",
  inputSchema: webSearchSchema,
  execute: async (context) => {
    const { query, includeDetails, numResults } = context.context;
    const apiKey = private_env.EXA_API_KEY;
    if (!apiKey) {
      throw new Error("EXA_API_KEY environment variable is not set");
    }
    const exa = new Exa(apiKey);
    try {
      const searchResults = includeDetails ? await exa.searchAndContents(query, {
        numResults: Math.min(numResults, 25),
        text: true
      }) : await exa.search(query, {
        numResults: Math.min(numResults, 25)
      });
      let financialResults = null;
      let newsResults = null;
      let aboutResults = null;
      if (searchResults.results.length > 0) {
        try {
          financialResults = includeDetails ? await exa.searchAndContents(
            `${query} financial performance revenue earnings`,
            {
              numResults: 5,
              text: true
            }
          ) : await exa.search(
            `${query} financial performance revenue earnings`,
            {
              numResults: 5
            }
          );
        } catch (error) {
          console.warn("Financial search failed:", error);
        }
        try {
          newsResults = includeDetails ? await exa.searchAndContents(`${query} news recent updates`, {
            numResults: 5,
            text: true
          }) : await exa.search(`${query} news recent updates`, {
            numResults: 5
          });
        } catch (error) {
          console.warn("News search failed:", error);
        }
        try {
          aboutResults = includeDetails ? await exa.searchAndContents(
            `${query} company overview about mission`,
            {
              numResults: 3,
              text: true
            }
          ) : await exa.search(`${query} company overview about mission`, {
            numResults: 3
          });
        } catch (error) {
          console.warn("About search failed:", error);
        }
      }
      const allSources = [
        ...searchResults.results || [],
        ...financialResults?.results || [],
        ...newsResults?.results || [],
        ...aboutResults?.results || []
      ];
      const report = {
        query,
        searchType: "auto",
        totalResults: allSources.length,
        // UNIFIED SOURCES LIST - Use this for sequential citation numbering [1], [2], [3], etc.
        allSources: allSources.map((result, index) => ({
          citationNumber: index + 1,
          // This is the number to use for citations: [1], [2], [3], etc.
          title: result.title,
          url: result.url,
          author: result.author || "Unknown",
          publishedDate: result.publishedDate || "Unknown",
          summary: "Content found and processed",
          highlights: [],
          score: result.score || 0,
          text: includeDetails && result.text ? result.text.substring(0, 1e3) + "..." : null
        })),
        generalInformation: {
          results: searchResults.results.map((result) => ({
            title: result.title,
            url: result.url,
            author: result.author || "Unknown",
            publishedDate: result.publishedDate || "Unknown",
            summary: "Content found and processed",
            highlights: [],
            score: result.score || 0,
            text: includeDetails && result.text ? result.text.substring(0, 1e3) + "..." : null
          }))
        },
        financialInformation: financialResults ? {
          results: financialResults.results.map((result) => ({
            title: result.title,
            url: result.url,
            author: result.author || "Unknown",
            publishedDate: result.publishedDate || "Unknown",
            summary: "Financial content found and processed",
            highlights: [],
            score: result.score || 0,
            text: includeDetails && result.text ? result.text.substring(0, 1e3) + "..." : null
          }))
        } : null,
        recentNews: newsResults ? {
          results: newsResults.results.map((result) => ({
            title: result.title,
            url: result.url,
            author: result.author || "Unknown",
            publishedDate: result.publishedDate || "Unknown",
            summary: "News content found and processed",
            highlights: [],
            score: result.score || 0,
            text: includeDetails && result.text ? result.text.substring(0, 1e3) + "..." : null
          }))
        } : null,
        companyOverview: aboutResults ? {
          results: aboutResults.results.map((result) => ({
            title: result.title,
            url: result.url,
            author: result.author || "Unknown",
            publishedDate: result.publishedDate || "Unknown",
            summary: "Company overview content found and processed",
            highlights: [],
            score: result.score || 0,
            text: includeDetails && result.text ? result.text.substring(0, 1e3) + "..." : null
          }))
        } : null,
        researchSummary: generateResearchSummary(
          searchResults,
          financialResults,
          newsResults,
          aboutResults
        ),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      console.log(
        `Web search returning ${allSources.length} total sources for citation`
      );
      console.log("Source titles:", allSources.map((s) => s.title).slice(0, 5));
      return { report };
    } catch (error) {
      console.error("Web search failed:", error);
      throw new Error(
        `Web search failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }
});
function generateResearchSummary(generalResults, financialResults, newsResults, aboutResults) {
  const summaries = [];
  if (generalResults?.results?.length > 0) {
    summaries.push(
      `Found ${generalResults.results.length} general results about the company.`
    );
  }
  if (financialResults?.results?.length > 0) {
    summaries.push(
      `Located ${financialResults.results.length} financial-related sources.`
    );
  }
  if (newsResults?.results?.length > 0) {
    summaries.push(
      `Discovered ${newsResults.results.length} recent news articles.`
    );
  }
  if (aboutResults?.results?.length > 0) {
    summaries.push(
      `Found ${aboutResults.results.length} company overview sources.`
    );
  }
  return summaries.join(" ") || "No comprehensive results found for this query.";
}
const keywordVolumeSchema = z.object({
  keywords: z.array(z.string()).max(1e3).describe("Array of keywords to get search volume for (max 1000)"),
  location_code: z.number().optional().default(2840).describe("Location code for search data (default: 2840 for United States)"),
  language_code: z.string().optional().default("en").describe("Language code for search data (default: en for English)"),
  search_partners: z.boolean().optional().default(false).describe("Include Google search partners data")
});
const keywordVolumeTool = createTool({
  id: "get_keyword_volume",
  description: "Get monthly search volume data for a list of keywords using DataForSEO Google Ads API",
  inputSchema: keywordVolumeSchema,
  execute: async (context) => {
    const { keywords, location_code, language_code, search_partners } = context.context;
    const apiLogin = private_env.DATAFORSEO_LOGIN;
    const apiPassword = private_env.DATAFORSEO_PASSWORD;
    console.log("Keyword Volume Tool - Checking credentials:", {
      hasLogin: !!apiLogin,
      hasPassword: !!apiPassword,
      loginLength: apiLogin?.length || 0
    });
    if (!apiLogin || !apiPassword) {
      console.error("DataForSEO credentials missing - returning mock data for testing");
      const mockResults = keywords.map((keyword) => ({
        keyword,
        search_volume: Math.floor(Math.random() * 1e4) + 100,
        competition: ["LOW", "MEDIUM", "HIGH"][Math.floor(Math.random() * 3)],
        competition_index: Math.floor(Math.random() * 100),
        cpc: Math.random() * 5 + 0.5,
        location_code: 2840,
        language_code: "en"
      }));
      return {
        success: true,
        total_keywords: keywords.length,
        results_count: mockResults.length,
        cost: 0,
        results: mockResults,
        note: "Mock data returned - DataForSEO credentials not configured"
      };
    }
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString("base64");
    try {
      const requestData = [{
        location_code,
        language_code,
        keywords,
        search_partners
      }];
      console.log("Making DataForSEO search volume request for", keywords.length, "keywords");
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch("https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/live", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${credentials}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      console.log("DataForSEO search volume response status:", data.status_code, data.status_message);
      if (data.status_code !== 2e4) {
        console.error("DataForSEO API error response:", data);
        throw new Error(`DataForSEO API error: ${data.status_message}`);
      }
      const results = [];
      if (data.tasks && data.tasks[0] && data.tasks[0].result) {
        console.log("Processing", data.tasks[0].result.length, "search volume results");
        for (const item of data.tasks[0].result) {
          results.push({
            keyword: item.keyword,
            search_volume: item.search_volume || 0,
            competition: item.competition,
            competition_index: item.competition_index || 0,
            cpc: item.cpc || 0,
            location_code: item.location_code,
            language_code: item.language_code
          });
        }
      } else {
        console.warn("No search volume results found in response");
      }
      console.log("Search volume tool completed successfully, returning", results.length, "results");
      return {
        success: true,
        total_keywords: keywords.length,
        results_count: results.length,
        cost: data.cost || 0,
        results
      };
    } catch (error) {
      console.error("Keyword volume API error:", error);
      throw new Error(`Failed to get keyword volume data: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const keywordDifficultySchema = z.object({
  keywords: z.array(z.string()).max(1e3).describe("Array of keywords to get difficulty scores for (max 1000)"),
  location_code: z.number().optional().default(2840).describe("Location code for search data (default: 2840 for United States)"),
  language_code: z.string().optional().default("en").describe("Language code for search data (default: en for English)")
});
const keywordDifficultyTool = createTool({
  id: "get_keyword_difficulty",
  description: "Get SEO keyword difficulty scores (0-100) for a list of keywords using DataForSEO Labs API",
  inputSchema: keywordDifficultySchema,
  execute: async (context) => {
    const { keywords, location_code, language_code } = context.context;
    const apiLogin = private_env.DATAFORSEO_LOGIN;
    const apiPassword = private_env.DATAFORSEO_PASSWORD;
    console.log("Keyword Difficulty Tool - Checking credentials:", {
      hasLogin: !!apiLogin,
      hasPassword: !!apiPassword,
      loginLength: apiLogin?.length || 0
    });
    if (!apiLogin || !apiPassword) {
      console.error("DataForSEO credentials missing - returning mock data for testing");
      const mockResults = keywords.map((keyword) => ({
        keyword,
        keyword_difficulty: Math.floor(Math.random() * 80) + 10,
        // 10-90 range
        se_type: "google"
      }));
      return {
        success: true,
        total_keywords: keywords.length,
        results_count: mockResults.length,
        cost: 0,
        location_code,
        language_code,
        results: mockResults,
        note: "Mock data returned - DataForSEO credentials not configured"
      };
    }
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString("base64");
    try {
      const requestData = [{
        location_code,
        language_code,
        keywords
      }];
      console.log("Making DataForSEO keyword difficulty request for", keywords.length, "keywords");
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch("https://api.dataforseo.com/v3/dataforseo_labs/google/bulk_keyword_difficulty/live", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${credentials}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      if (data.status_code !== 2e4) {
        throw new Error(`DataForSEO API error: ${data.status_message}`);
      }
      const results = [];
      if (data.tasks && data.tasks[0] && data.tasks[0].result && data.tasks[0].result[0] && data.tasks[0].result[0].items) {
        for (const item of data.tasks[0].result[0].items) {
          results.push({
            keyword: item.keyword,
            keyword_difficulty: item.keyword_difficulty || 0,
            se_type: item.se_type
          });
        }
      }
      return {
        success: true,
        total_keywords: keywords.length,
        results_count: results.length,
        cost: data.cost || 0,
        location_code,
        language_code,
        results
      };
    } catch (error) {
      console.error("Keyword difficulty API error:", error);
      throw new Error(`Failed to get keyword difficulty data: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const relatedKeywordsSchema = z.object({
  keywords: z.array(z.string()).min(1).max(20).describe("Array of seed keywords to find related keywords for (1-20 keywords)"),
  location_code: z.number().optional().default(2840).describe("Location code for search data (default: 2840 for United States)"),
  language_code: z.string().optional().default("en").describe("Language code for search data (default: en for English)"),
  include_seed_keywords: z.boolean().optional().default(false).describe("Include seed keywords in results"),
  limit: z.number().optional().default(700).describe("Maximum number of keyword suggestions to return")
});
const relatedKeywordsTool = createTool({
  id: "get_related_keywords",
  description: "Discover related and long-tail keywords based on seed keywords using DataForSEO Keywords For Keywords API",
  inputSchema: relatedKeywordsSchema,
  execute: async (context) => {
    const { keywords, location_code, language_code, include_seed_keywords, limit } = context.context;
    const apiLogin = private_env.DATAFORSEO_LOGIN;
    const apiPassword = private_env.DATAFORSEO_PASSWORD;
    console.log("Related Keywords Tool - Checking credentials:", {
      hasLogin: !!apiLogin,
      hasPassword: !!apiPassword,
      seedKeywords: keywords.length
    });
    if (!apiLogin || !apiPassword) {
      console.error("DataForSEO credentials missing - returning mock data for testing");
      const mockResults = [];
      const prefixes = ["best", "how to", "top", "cheap", "buy", "review"];
      const suffixes = ["guide", "tips", "ideas", "near me", "online", "for beginners"];
      for (const seed of keywords) {
        if (include_seed_keywords) {
          mockResults.push({
            keyword: seed,
            search_volume: Math.floor(Math.random() * 5e4) + 1e3,
            competition: ["LOW", "MEDIUM", "HIGH"][Math.floor(Math.random() * 3)],
            competition_index: Math.floor(Math.random() * 100),
            cpc: Math.random() * 5 + 0.5,
            keyword_difficulty: Math.floor(Math.random() * 80) + 10
          });
        }
        for (const prefix of prefixes.slice(0, 3)) {
          mockResults.push({
            keyword: `${prefix} ${seed}`,
            search_volume: Math.floor(Math.random() * 1e4) + 100,
            competition: ["LOW", "MEDIUM", "HIGH"][Math.floor(Math.random() * 3)],
            competition_index: Math.floor(Math.random() * 100),
            cpc: Math.random() * 3 + 0.3,
            keyword_difficulty: Math.floor(Math.random() * 70) + 5
          });
        }
        for (const suffix of suffixes.slice(0, 3)) {
          mockResults.push({
            keyword: `${seed} ${suffix}`,
            search_volume: Math.floor(Math.random() * 5e3) + 50,
            competition: ["LOW", "MEDIUM"][Math.floor(Math.random() * 2)],
            competition_index: Math.floor(Math.random() * 70),
            cpc: Math.random() * 2 + 0.2,
            keyword_difficulty: Math.floor(Math.random() * 60) + 5
          });
        }
      }
      mockResults.sort((a, b) => b.search_volume - a.search_volume);
      return {
        success: true,
        seed_keywords: keywords,
        total_keywords: mockResults.length,
        results: mockResults.slice(0, limit),
        note: "Mock data returned - DataForSEO credentials not configured"
      };
    }
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString("base64");
    try {
      const requestData = [{
        keywords,
        location_code,
        language_code,
        include_seed_keywords,
        limit
      }];
      console.log("Making DataForSEO related keywords request for seed keywords:", keywords);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch("https://api.dataforseo.com/v3/keywords_data/google_ads/keywords_for_keywords/live", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${credentials}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      console.log("DataForSEO related keywords response status:", data.status_code, data.status_message);
      if (data.status_code !== 2e4) {
        console.error("DataForSEO API error response:", data);
        throw new Error(`DataForSEO API error: ${data.status_message}`);
      }
      const results = [];
      if (data.tasks && data.tasks[0] && data.tasks[0].result) {
        console.log("Processing related keywords results");
        for (const item of data.tasks[0].result) {
          if (item.keyword_data) {
            results.push({
              keyword: item.keyword,
              search_volume: item.keyword_data.keyword_info?.search_volume || 0,
              competition: item.keyword_data.keyword_info?.competition || "UNKNOWN",
              competition_index: item.keyword_data.keyword_info?.competition_index || 0,
              cpc: item.keyword_data.keyword_info?.cpc || 0,
              monthly_searches: item.keyword_data.keyword_info?.monthly_searches || []
            });
          }
        }
      } else {
        console.warn("No related keywords results found in response");
      }
      results.sort((a, b) => b.search_volume - a.search_volume);
      console.log("Related keywords tool completed successfully, returning", results.length, "results");
      return {
        success: true,
        seed_keywords: keywords,
        total_keywords: results.length,
        results: results.slice(0, limit),
        cost: data.cost || 0
      };
    } catch (error) {
      console.error("Related keywords API error:", error);
      throw new Error(`Failed to get related keywords: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const domainIntersectionSchema = z.object({
  target1: z.string().describe("First domain to compare (e.g., example.com)"),
  target2: z.string().describe("Second domain to compare (e.g., competitor.com)"),
  location_code: z.number().optional().default(2840).describe("Location code for search data (default: 2840 for United States)"),
  language_code: z.string().optional().default("en").describe("Language code for search data (default: en for English)"),
  include_serp_info: z.boolean().optional().default(true).describe("Include detailed SERP information"),
  limit: z.number().optional().default(1e3).describe("Maximum number of results to return")
});
const domainIntersectionTool = createTool({
  id: "get_domain_intersection",
  description: "Find keywords where two domains both rank in Google SERPs using DataForSEO Domain Intersection API",
  inputSchema: domainIntersectionSchema,
  execute: async (context) => {
    const { target1, target2, location_code, language_code, include_serp_info, limit } = context.context;
    const apiLogin = private_env.DATAFORSEO_LOGIN;
    const apiPassword = private_env.DATAFORSEO_PASSWORD;
    if (!apiLogin || !apiPassword) {
      console.log("DataForSEO credentials not configured - using mock data");
      const mockResults = [];
      const mockKeywords = [
        "project management software",
        "task tracking tools",
        "team collaboration",
        "agile project management",
        "kanban software",
        "scrum tools",
        "time tracking software",
        "resource planning",
        "gantt chart software"
      ];
      for (const keyword of mockKeywords) {
        mockResults.push({
          keyword,
          search_volume: Math.floor(Math.random() * 5e4) + 1e3,
          keyword_difficulty: Math.floor(Math.random() * 80) + 10,
          cpc: Math.random() * 10 + 0.5,
          competition: ["LOW", "MEDIUM", "HIGH"][Math.floor(Math.random() * 3)],
          target1_position: Math.floor(Math.random() * 50) + 1,
          target2_position: Math.floor(Math.random() * 50) + 1,
          target1_url: `https://${target1}/page-${Math.random().toString(36).substr(2, 9)}`,
          target2_url: `https://${target2}/page-${Math.random().toString(36).substr(2, 9)}`,
          etv: Math.random() * 1e3 + 50
        });
      }
      mockResults.sort((a, b) => b.search_volume - a.search_volume);
      return {
        success: true,
        target1,
        target2,
        total_keywords: mockResults.length,
        results: mockResults.slice(0, limit),
        note: "Mock data returned - DataForSEO credentials not configured",
        debug: {
          reason: "missing_credentials",
          apiLogin: !apiLogin ? "MISSING" : "OK",
          apiPassword: !apiPassword ? "MISSING" : "OK",
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }
      };
    }
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString("base64");
    try {
      const requestData = [{
        target1,
        target2,
        location_code,
        language_code,
        include_serp_info,
        limit: Math.min(limit, 25)
        // Limit to max 25 keywords to prevent context overflow
      }];
      console.log("Making DataForSEO domain intersection request for:", target1, "vs", target2);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch("https://api.dataforseo.com/v3/dataforseo_labs/google/domain_intersection/live", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${credentials}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      console.log("DataForSEO domain intersection response status:", data.status_code, data.status_message);
      if (data.status_code !== 2e4) {
        console.error("DataForSEO API error response:", data);
        throw new Error(`DataForSEO API error: ${data.status_message}`);
      }
      const results = [];
      if (data.tasks && data.tasks[0] && data.tasks[0].result && data.tasks[0].result[0] && data.tasks[0].result[0].items) {
        console.log("Processing domain intersection results");
        for (const item of data.tasks[0].result[0].items) {
          const target1Data = item.intersection_result?.["1"]?.organic || {};
          const target2Data = item.intersection_result?.["2"]?.organic || {};
          results.push({
            keyword: item.keyword_data?.keyword || item.keyword,
            search_volume: item.keyword_data?.keyword_info?.search_volume || 0,
            keyword_difficulty: item.keyword_data?.keyword_info?.competition || 0,
            cpc: item.keyword_data?.keyword_info?.cpc || 0,
            competition: item.keyword_data?.keyword_info?.competition_level || "UNKNOWN",
            target1_position: target1Data.position || null,
            target2_position: target2Data.position || null,
            target1_url: target1Data.url || null,
            target2_url: target2Data.url || null,
            etv: target1Data.etv || 0,
            monthly_searches: item.keyword_data?.keyword_info?.monthly_searches || []
          });
        }
      } else {
        console.warn("No domain intersection results found in response");
      }
      results.sort((a, b) => b.search_volume - a.search_volume);
      const limitedResults = results.slice(0, 10);
      console.log("Domain intersection completed:", limitedResults.length, "results");
      return {
        success: true,
        target1,
        target2,
        total_keywords: limitedResults.length,
        results: limitedResults,
        cost: data.cost || 0,
        debug: {
          reason: "real_api_data",
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }
      };
    } catch (error) {
      console.error("Domain intersection API error:", error);
      throw new Error(`Failed to get domain intersection data: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const keywordsForSiteSchema = z.object({
  target: z.string().describe("Domain or page URL to get keywords for (e.g., example.com or example.com/page)"),
  location_code: z.number().optional().default(2840).describe("Location code for search data (default: 2840 for United States)"),
  language_code: z.string().optional().default("en").describe("Language code for search data (default: en for English)"),
  search_partners: z.boolean().optional().default(false).describe("Include Google search partners data"),
  exclude_adult_keywords: z.boolean().optional().default(true).describe("Exclude adult keywords from results")
});
const keywordsForSiteTool = createTool({
  id: "get_keywords_for_site",
  description: "Get all keywords a website or webpage ranks for using DataForSEO Keywords for Site API",
  inputSchema: keywordsForSiteSchema,
  execute: async (context) => {
    const { target, location_code, language_code, search_partners, exclude_adult_keywords } = context.context;
    const apiLogin = private_env.DATAFORSEO_LOGIN;
    const apiPassword = private_env.DATAFORSEO_PASSWORD;
    if (!apiLogin || !apiPassword) {
      console.log("DataForSEO credentials not configured - using mock data");
      const mockResults = [];
      const mockKeywords = [
        "best project management tools",
        "free task management software",
        "team collaboration platforms",
        "agile project management",
        "scrum software comparison",
        "kanban board tools",
        "time tracking for teams",
        "resource planning software",
        "gantt chart maker online",
        "project management for small teams",
        "enterprise project management",
        "cloud based project tools"
      ];
      for (const keyword of mockKeywords) {
        mockResults.push({
          keyword,
          search_volume: Math.floor(Math.random() * 3e4) + 500,
          competition: ["LOW", "MEDIUM", "HIGH"][Math.floor(Math.random() * 3)],
          competition_index: Math.floor(Math.random() * 100),
          cpc: Math.random() * 8 + 0.3,
          monthly_searches: Array(12).fill(0).map(() => ({
            year: 2024,
            month: Math.floor(Math.random() * 12) + 1,
            search_volume: Math.floor(Math.random() * 5e3) + 100
          }))
        });
      }
      mockResults.sort((a, b) => b.search_volume - a.search_volume);
      return {
        success: true,
        target,
        total_keywords: mockResults.length,
        results: mockResults,
        note: "Mock data returned - DataForSEO credentials not configured",
        debug: {
          reason: "missing_credentials",
          apiLogin: !apiLogin ? "MISSING" : "OK",
          apiPassword: !apiPassword ? "MISSING" : "OK",
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }
      };
    }
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString("base64");
    try {
      const requestData = [{
        target,
        location_code,
        language_code,
        search_partners,
        exclude_adult_keywords,
        limit: 25
        // Limit to top 25 keywords to prevent context overflow
      }];
      console.log("Making DataForSEO keywords for site request for:", target);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e4);
      const response = await fetch("https://api.dataforseo.com/v3/keywords_data/google_ads/keywords_for_site/live", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${credentials}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      console.log("DataForSEO keywords for site response status:", data.status_code, data.status_message);
      if (data.status_code !== 2e4) {
        console.error("DataForSEO API error response:", data);
        throw new Error(`DataForSEO API error: ${data.status_message}`);
      }
      const results = [];
      if (data.tasks && data.tasks[0] && data.tasks[0].result) {
        console.log("Processing keywords for site results");
        for (const item of data.tasks[0].result) {
          results.push({
            keyword: item.keyword,
            search_volume: item.search_volume || 0,
            competition: item.competition || "UNKNOWN",
            competition_index: item.competition_index || 0,
            cpc: item.cpc || 0,
            monthly_searches: item.monthly_searches || [],
            keyword_annotations: item.keyword_annotations || {}
          });
        }
      } else {
        console.warn("No keywords for site results found in response");
      }
      results.sort((a, b) => b.search_volume - a.search_volume);
      const limitedResults = results.slice(0, 15);
      console.log("Keywords for site completed:", limitedResults.length, "results");
      return {
        success: true,
        target,
        total_keywords: limitedResults.length,
        results: limitedResults,
        cost: data.cost || 0,
        debug: {
          reason: "real_api_data",
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }
      };
    } catch (error) {
      console.error("Keywords for site API error:", error);
      throw new Error(`Failed to get keywords for site data: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});
const SEO_SYSTEM_PROMPT = `
You are an expert SEO strategist agent. Your mission is to perform keyword analysis for a client and generate a "Keyword Strategy Report."

You have access to the following tools:
1.  \`webSearch(query)\`: Use this for research, competitive analysis, and generating ideas.
2.  \`get_keyword_volume(keywords)\`: Use this to get monthly search volume for a list of keywords.
3.  \`get_keyword_difficulty(keywords)\`: Use this to get the SEO Keyword Difficulty score (0-100) for a list of keywords.
4.  \`get_related_keywords(keywords)\`: Use this to discover related and long-tail keywords based on seed keywords (max 20 seeds).
5.  \`get_domain_intersection(target1, target2)\`: Find keywords where two domains both rank in Google SERPs.
6.  \`get_keywords_for_site(target)\`: Get all keywords a website or webpage ranks for.

**IMPORTANT: Work efficiently and complete within 90 seconds. Be strategic about tool usage.**

**CRITICAL: Check for output format in the user's message**
Look for "Output format: [summary/table/blog]" at the end of the message and format your response accordingly:

- **summary**: Concise bullet points, key findings, top 5-10 keywords with brief insights
- **table**: Structured markdown tables with all keyword data, sortable by metrics
- **blog**: Full blog post format with introduction, detailed sections, and actionable content

**Step 1: Quick Research & Keyword Generation**
1.  Do ONE webSearch to understand the client's business and niche.
2.  Extract 3-5 core seed keywords from the user's query.
3.  Use \`get_related_keywords\` with these seeds to discover niche opportunities (especially long-tail keywords).
4.  From the results, select the most relevant 25-30 keywords focusing on:
    - Long-tail variations (3+ words)
    - Low competition opportunities
    - High commercial intent keywords
    
**For Niche Discovery Requests:**
When the user specifically asks to "Discover niche keywords", prioritize:
1. Use \`get_related_keywords\` extensively with the provided seed keywords
2. Focus on long-tail, low-competition keywords
3. Apply any filters mentioned (volume range, difficulty, location)
4. Return results using structured tags:

<NICHE_KEYWORDS>
| Keyword | Volume | Difficulty | Competition | CPC | Opportunity Score |
|---------|--------|------------|-------------|-----|-------------------|
| [niche keyword data rows sorted by opportunity score] |
</NICHE_KEYWORDS>

<NICHE_ANALYSIS>
## Niche Discovery Summary
• **Market Opportunity**: [Brief market assessment]
• **Competition Level**: [Overall competition analysis]
• **Recommended Focus**: [Top 3-5 niche areas to target]
</NICHE_ANALYSIS>

**For Competitor Gap Analysis Requests:**
When the user asks to "Analyze keyword gaps" or mentions competitors:
1. Use \`get_keywords_for_site\` for the user's domain and each competitor
2. **CRITICAL**: Process data efficiently - focus on top 15-20 opportunities
3. Identify keywords where competitors rank but user doesn't (missing opportunities)
4. **CRITICAL**: Only include actual search keywords - NO prices, tools, or non-search terms
5. Return results using structured tags:

<GAP_RESULTS>
| Keyword | Volume | Difficulty | Gap Type | Competitor Pos | Opportunity Score |
|---------|--------|------------|----------|----------------|-------------------|
| keyword name here | 1000 | 45 | Missing | 3 | 85.5 |
| another keyword | 2500 | 30 | Lower | 5 | 92.3 |
| [15-20 rows of actual search keywords] |
</GAP_RESULTS>

**IMPORTANT**:
- Use exact pipe-separated table format shown above
- Include numeric values only (no commas, no # symbols)  
- Gap Type must be either "Missing" or "Lower"
- Always include the header row exactly as shown
- Only include search terms that people would type into Google

**IMPORTANT**: 
- Return 15-20 relevant keywords minimum
- Be extremely concise 
- No lengthy explanations
- Only include actual search keywords (no prices, tools, or non-keyword content)
- Calculate opportunity score as: volume/(difficulty+1)

**Step 2: Get Keyword Data**
1.  Call \`get_keyword_volume\` with your keyword list.
2.  Call \`get_keyword_difficulty\` with the same list.
3.  Combine the data.

**Step 3: Analysis & Report**
1.  Calculate Priority Score: \`(Search Volume / (Keyword Difficulty + 1))\`
2.  Filter and sort by priority.
3.  Generate report based on the specified output format:

**For "summary" format:**
<KEYWORDS>
| Rank | Keyword | Volume | Difficulty | CPC | Priority Score |
|------|---------|--------|------------|-----|----------------|
| [Top 10 -15 keywords with data rows sorted by priority] |
</KEYWORDS>

<RECOMMENDATIONS>
## SEO Strategy Summary
• **Business Overview**: [Brief description]
• **Key Recommendations**: [3-5 bullet points]
• **Priority Actions**: [Immediate next steps]
</RECOMMENDATIONS>

**For "table" format:**
<KEYWORDS>
| Rank | Keyword | Volume | Difficulty | CPC | Priority Score |
|------|---------|--------|------------|-----|----------------|
| [Data rows sorted by priority] |
</KEYWORDS>

<ANALYSIS>
## Keyword Analysis Report
### Additional Metrics
[Include competition analysis, trend data if available]
</ANALYSIS>

**For "blog" format:**
<KEYWORDS>
| Rank | Keyword | Volume | Difficulty | CPC | Priority Score |
|------|---------|--------|------------|-----|----------------|
| [Data rows sorted by priority] |
</KEYWORDS>

<CONTENT>
# [Business Name] SEO Strategy: Complete Guide

## Introduction
[Engaging introduction about the business and SEO opportunity]

## Market Analysis
[Detailed market research findings]

## Keyword Opportunities
[Comprehensive keyword analysis with context]

## Content Strategy Recommendations
[Detailed content plan with examples]

## Implementation Roadmap
[Step-by-step action plan]

## Conclusion
[Summary and call-to-action]
</CONTENT>

**Keep it focused and efficient. Quality over quantity.**
`;
function createSEOStrategistAgent(llmConfig) {
  const defaultConfig = { provider: "openai", model: "gpt-4o-mini" };
  const finalConfig = llmConfig || defaultConfig;
  const model = createLLMClient(finalConfig);
  return new Agent({
    name: "SEO Strategist",
    instructions: SEO_SYSTEM_PROMPT,
    model,
    tools: {
      webSearch: webSearchTool,
      get_keyword_volume: keywordVolumeTool,
      get_keyword_difficulty: keywordDifficultyTool,
      get_related_keywords: relatedKeywordsTool,
      get_domain_intersection: domainIntersectionTool,
      get_keywords_for_site: keywordsForSiteTool
    }
  });
}
const envConfig = private_env.LLM_PROVIDER && private_env.LLM_MODEL ? {
  provider: private_env.LLM_PROVIDER,
  model: private_env.LLM_MODEL
} : void 0;
console.log("=== DEFAULT SEO AGENT CREATION ===");
console.log("Environment config detected:", envConfig);
console.log("env.LLM_PROVIDER:", private_env.LLM_PROVIDER);
console.log("env.LLM_MODEL:", private_env.LLM_MODEL);
const seoStrategistAgent = createSEOStrategistAgent(envConfig);
export {
  createLLMClient as c,
  seoStrategistAgent as s,
  webSearchTool as w
};
