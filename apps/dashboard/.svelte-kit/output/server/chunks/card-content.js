import { m as sanitize_props, C as rest_props, A as fallback, D as spread_attributes, v as clsx, b as slot, B as bind_props, p as pop, a as push } from "./index2.js";
import { c as cn } from "./utils.js";
function Card($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  let className = fallback($$props["class"], void 0);
  $$payload.out.push(`<div${spread_attributes(
    {
      class: clsx(cn("bg-card text-card-foreground rounded-lg border shadow-sm", className)),
      ...$$restProps
    },
    null
  )}><!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { class: className });
  pop();
}
function Card_content($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  let className = fallback($$props["class"], void 0);
  $$payload.out.push(`<div${spread_attributes({ class: clsx(cn("p-6 pt-0", className)), ...$$restProps }, null)}><!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { class: className });
  pop();
}
export {
  Card as C,
  Card_content as a
};
