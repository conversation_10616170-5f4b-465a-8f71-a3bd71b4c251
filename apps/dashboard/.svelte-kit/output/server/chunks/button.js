import { m as sanitize_props, C as rest_props, a as push, A as fallback, E as element, B as bind_props, p as pop, b as slot, D as spread_attributes, o as spread_props } from "./index2.js";
import "./create.js";
import "clsx";
import { b as buttonVariants } from "./index6.js";
import { c as cn } from "./utils.js";
function getAttrs(builders) {
  const attrs = {};
  builders.forEach((builder) => {
    Object.keys(builder).forEach((key) => {
      if (key !== "action") {
        attrs[key] = builder[key];
      }
    });
  });
  return attrs;
}
function Button$1($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["href", "type", "builders", "el"]);
  push();
  let href = fallback($$props["href"], () => void 0, true);
  let type = fallback($$props["type"], () => void 0, true);
  let builders = fallback($$props["builders"], () => [], true);
  let el = fallback($$props["el"], () => void 0, true);
  const attrs = { "data-button-root": "" };
  if (builders && builders.length) {
    $$payload.out.push("<!--[-->");
    element(
      $$payload,
      href ? "a" : "button",
      () => {
        $$payload.out.push(`${spread_attributes(
          {
            type: href ? void 0 : type,
            href,
            tabindex: "0",
            ...getAttrs(builders),
            ...$$restProps,
            ...attrs
          },
          null
        )}`);
      },
      () => {
        $$payload.out.push(`<!---->`);
        slot($$payload, $$props, "default", {}, null);
        $$payload.out.push(`<!---->`);
      }
    );
  } else {
    $$payload.out.push("<!--[!-->");
    element(
      $$payload,
      href ? "a" : "button",
      () => {
        $$payload.out.push(`${spread_attributes(
          {
            type: href ? void 0 : type,
            href,
            tabindex: "0",
            ...$$restProps,
            ...attrs
          },
          null
        )}`);
      },
      () => {
        $$payload.out.push(`<!---->`);
        slot($$payload, $$props, "default", {}, null);
        $$payload.out.push(`<!---->`);
      }
    );
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { href, type, builders, el });
  pop();
}
function Button($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "variant", "size", "builders"]);
  push();
  let className = fallback($$props["class"], void 0);
  let variant = fallback($$props["variant"], "default");
  let size = fallback($$props["size"], "default");
  let builders = fallback($$props["builders"], () => [], true);
  Button$1($$payload, spread_props([
    {
      builders,
      class: cn(buttonVariants({ variant, size, className })),
      type: "button"
    },
    $$restProps,
    {
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  bind_props($$props, { class: className, variant, size, builders });
  pop();
}
export {
  Button as B
};
