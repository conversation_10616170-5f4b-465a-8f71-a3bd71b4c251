import { z } from "zod";
const otpCodeSchema = z.object({
  email: z.string().email(),
  code: z.string().length(6, "Must be a 6-digit code").regex(/^\d+$/, "Must be a 6-digit code")
});
const signUpSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters long").max(16, "Password must be at most 16 characters long").regex(
    /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/,
    "For security sake, please include lowercase, uppercase letters and digits."
  ),
  confirmPassword: z.string()
}).refine(({ password, confirmPassword }) => password === confirmPassword, {
  message: "The passwords did not match"
});
const environmentSchema = z.object({
  name: z.string().min(1, "The environment name is required").max(30, "The environment name can be at most 72 charaters long").regex(/^[a-z0-9+$]/i, "Environment name must be alphanumeric")
});
const emailSchema = z.object({
  email: z.string().email()
});
const changePasswordSchema = z.object({
  currentPassword: z.string().min(
    1,
    "You must include your current password. If you forgot it, sign out then use 'forgot password' on the sign in page."
  ).optional(),
  newPassword1: z.string().min(6, "The new password must be at least 6 charaters long").max(72, "The new password can be at most 72 charaters long"),
  newPassword2: z.string().min(6, "The new password must be at least 6 charaters long").max(72, "The new password can be at most 72 charaters long")
}).refine(
  ({ newPassword1, newPassword2 }) => newPassword1 === newPassword2,
  "The passwords don't match"
);
const deleteAccountSchema = z.object({
  currentPassword: z.string().min(
    1,
    "You must provide your current password to delete your account. If you forgot it, sign out then use 'forgot password' on the sign in page."
  )
});
const contactSchema = z.object({
  first_name: z.string().min(2, "First name is required").max(500, "First name too long"),
  last_name: z.string().min(2, "Last name is required").max(500, "Last name too long"),
  email: z.string().email("Email is required").max(500, "Email too long"),
  company_name: z.string().max(500, "Company too long"),
  phone: z.string().max(100, "Phone number").optional(),
  message_body: z.string().max(2e3, "Message too long. Must be no more than 2000 character").default("")
});
z.object({
  name: z.string().min(2, "Name is required").max(100, "Name must be less than 100 characters"),
  email: z.string().email("Please enter a valid email address").max(255, "Email must be less than 255 characters"),
  website: z.string().url("Please enter a valid website URL").max(255, "Website URL must be less than 255 characters").optional().or(z.literal("")),
  description: z.string().min(10, "Please provide at least 10 characters describing your needs").max(2e3, "Description must be less than 2000 characters")
});
const profileSchema = z.object({
  full_name: z.string().min(1, "Name is required").max(50, "Name must be less than 50 characters"),
  company_name: z.string().min(
    1,
    "Company name is required. If this is a hobby project or personal app, please put your name."
  ).max(50, "Company name must be less than 50 characters"),
  website: z.string().min(
    1,
    "Company website is required. An app store URL is a good alternative if you don't have a website."
  ).max(50, "Name must be less than 50 characters")
});
export {
  contactSchema as a,
  environmentSchema as b,
  changePasswordSchema as c,
  deleteAccountSchema as d,
  emailSchema as e,
  otpCodeSchema as o,
  profileSchema as p,
  signUpSchema as s
};
