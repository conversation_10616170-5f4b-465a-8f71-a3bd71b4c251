import { m as sanitize_props, C as rest_props, a as push, A as fallback, D as spread_attributes, v as clsx, b as slot, B as bind_props, p as pop, E as element } from "./index2.js";
import { c as cn } from "./utils.js";
function Card_description($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  let className = fallback($$props["class"], void 0);
  $$payload.out.push(`<p${spread_attributes(
    {
      class: clsx(cn("text-muted-foreground text-sm", className)),
      ...$$restProps
    },
    null
  )}><!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></p>`);
  bind_props($$props, { class: className });
  pop();
}
function Card_header($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  let className = fallback($$props["class"], void 0);
  $$payload.out.push(`<div${spread_attributes(
    {
      class: clsx(cn("flex flex-col space-y-1.5 p-6", className)),
      ...$$restProps
    },
    null
  )}><!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { class: className });
  pop();
}
function Card_title($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "tag"]);
  push();
  let className = fallback($$props["class"], void 0);
  let tag = fallback($$props["tag"], "h3");
  element(
    $$payload,
    tag,
    () => {
      $$payload.out.push(`${spread_attributes(
        {
          class: clsx(cn("text-lg font-semibold leading-none tracking-tight", className)),
          ...$$restProps
        },
        null
      )}`);
    },
    () => {
      $$payload.out.push(`<!---->`);
      slot($$payload, $$props, "default", {}, null);
      $$payload.out.push(`<!---->`);
    }
  );
  bind_props($$props, { class: className, tag });
  pop();
}
export {
  Card_header as C,
  Card_title as a,
  Card_description as b
};
