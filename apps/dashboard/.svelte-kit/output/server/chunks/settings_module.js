import { m as sanitize_props, C as rest_props, a as push, A as fallback, D as spread_attributes, v as clsx, b as slot, B as bind_props, p as pop, w as copy_payload, x as assign_payload, e as escape_html, c as attr, s as store_get, t as ensure_array_like, y as invalid_default_snippet, o as spread_props, f as stringify, z as store_mutate, u as unsubscribe_stores, d as attr_class } from "./index2.js";
import { p as page } from "./stores.js";
import "./formData.js";
import { s as superForm, e as enhance } from "./superForm.js";
import "@sveltejs/kit";
import { a as zodClient } from "./zod.js";
import { C as Card, a as Card_content } from "./card-content.js";
import "clsx";
import { F as Form_field, C as Control, a as Form_label, b as Form_field_errors } from "./index8.js";
import { b as buttonVariants } from "./index6.js";
import { I as Input } from "./input.js";
import { a as alertVariants } from "./index3.js";
import { g as getEnvironmentState } from "./environment.svelte.js";
import { c as cn } from "./utils.js";
import { B as Button } from "./button.js";
function Alert($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "variant"]);
  push();
  let className = fallback($$props["class"], void 0);
  let variant = fallback($$props["variant"], "default");
  $$payload.out.push(`<div${spread_attributes(
    {
      class: clsx(cn(alertVariants({ variant }), className)),
      ...$$restProps,
      role: "alert"
    },
    null
  )}><!---->`);
  slot($$payload, $$props, "default", {}, null);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { class: className, variant });
  pop();
}
function Settings_module($$payload, $$props) {
  push();
  var $$store_subs;
  const env = getEnvironmentState();
  const fieldError = (liveForm, name) => {
    let errors2 = liveForm?.errorFields ?? [];
    return errors2.includes(name);
  };
  let showSuccess = false;
  let {
    data,
    schema,
    editable = false,
    dangerous = false,
    title = "",
    message = "",
    fields,
    formTarget = "",
    successTitle = "Success",
    successBody = "",
    editButtonTitle = "",
    editLink = "",
    saveButtonTitle = "Save"
  } = $$props;
  const form = data && schema ? superForm(data, {
    validators: zodClient(schema),
    onUpdated: ({ form: f }) => {
      if (f.valid) {
        showSuccess = true;
      }
    }
  }) : null;
  const formData = form?.form;
  const delayed = form?.delayed;
  const errors = form?.errors;
  form?.enhance ?? enhance;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Card($$payload2, {
      class: "p-6 pb-7 mt-8 max-w-xl flex flex-col md:flex-row shadow",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Card_content($$payload3, {
          children: ($$payload4) => {
            if (title) {
              $$payload4.out.push("<!--[-->");
              $$payload4.out.push(`<div class="text-xl font-bold mb-3 w-48 md:pr-8 flex-none">${escape_html(title)}</div>`);
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> <div class="w-full min-w-48">`);
            if (!showSuccess) {
              $$payload4.out.push("<!--[-->");
              if (message) {
                $$payload4.out.push("<!--[-->");
                $$payload4.out.push(`<!---->`);
                Alert($$payload4, {
                  variant: dangerous ? "destructive" : "default",
                  class: "mb-6",
                  children: ($$payload5) => {
                    if (dangerous) {
                      $$payload5.out.push("<!--[-->");
                      $$payload5.out.push(`<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                    }
                    $$payload5.out.push(`<!--]--> <span>${escape_html(message)}</span>`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              } else {
                $$payload4.out.push("<!--[!-->");
              }
              $$payload4.out.push(`<!--]--> `);
              if (editable) {
                $$payload4.out.push("<!--[-->");
                $$payload4.out.push(`<form class="form-widget flex flex-col" method="POST"${attr("action", formTarget)}>`);
                if (form && store_get($$store_subs ??= {}, "$errors", errors) && store_get($$store_subs ??= {}, "$formData", formData)) {
                  $$payload4.out.push("<!--[-->");
                  const each_array = ensure_array_like(fields);
                  $$payload4.out.push(`<!--[-->`);
                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                    let field = each_array[$$index];
                    $$payload4.out.push(`<!---->`);
                    Form_field($$payload4, {
                      form,
                      name: field.id,
                      children: ($$payload5) => {
                        $$payload5.out.push(`<!---->`);
                        Control($$payload5, {
                          children: invalid_default_snippet,
                          $$slots: {
                            default: ($$payload6, { attrs }) => {
                              if (field.label) {
                                $$payload6.out.push("<!--[-->");
                                $$payload6.out.push(`<!---->`);
                                Form_label($$payload6, {
                                  children: ($$payload7) => {
                                    $$payload7.out.push(`<!---->${escape_html(field.label)}`);
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload6.out.push(`<!---->`);
                              } else {
                                $$payload6.out.push("<!--[!-->");
                              }
                              $$payload6.out.push(`<!--]--> `);
                              if (editable) {
                                $$payload6.out.push("<!--[-->");
                                Input($$payload6, spread_props([
                                  attrs,
                                  {
                                    id: field.id,
                                    name: field.id,
                                    type: field.inputType ?? "text",
                                    disabled: !editable,
                                    placeholder: field.placeholder ?? field.label ?? "",
                                    class: `${stringify(fieldError(store_get($$store_subs ??= {}, "$page", page)?.form, field.id) ? "border-destructive" : "")} w-full max-w-xs mb-3 py-4`,
                                    maxlength: field.maxlength ? field.maxlength : null,
                                    get value() {
                                      return store_get($$store_subs ??= {}, "$formData", formData)[field.id];
                                    },
                                    set value($$value) {
                                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData)[field.id] = $$value);
                                      $$settled = false;
                                    }
                                  }
                                ]));
                                $$payload6.out.push(`<!----> <!---->`);
                                Form_field_errors($$payload6, {});
                                $$payload6.out.push(`<!---->`);
                              } else {
                                $$payload6.out.push("<!--[!-->");
                                $$payload6.out.push(`<div class="text-lg mb-3">${escape_html(field.initialValue)}</div>`);
                              }
                              $$payload6.out.push(`<!--]-->`);
                            }
                          }
                        });
                        $$payload5.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out.push(`<!---->`);
                  }
                  $$payload4.out.push(`<!--]--> `);
                  if (store_get($$store_subs ??= {}, "$errors", errors)._errors) {
                    $$payload4.out.push("<!--[-->");
                    $$payload4.out.push(`<p class="text-destructive text-sm font-bold mt-1">${escape_html(store_get($$store_subs ??= {}, "$errors", errors)._errors[0])}</p>`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                }
                $$payload4.out.push(`<!--]--> <div>`);
                Button($$payload4, {
                  type: "submit",
                  variant: "outline",
                  class: `ml-auto mt-3 min-w-[145px] ${stringify(dangerous ? "border-destructive" : "")}`,
                  disabled: store_get($$store_subs ??= {}, "$delayed", delayed),
                  children: ($$payload5) => {
                    if (store_get($$store_subs ??= {}, "$delayed", delayed)) {
                      $$payload5.out.push("<!--[-->");
                      $$payload5.out.push(`<span class="loading loading-spinner loading-md align-middle mx-3"></span>`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      $$payload5.out.push(`${escape_html(saveButtonTitle)}`);
                    }
                    $$payload5.out.push(`<!--]-->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----></div></form>`);
              } else {
                $$payload4.out.push("<!--[!-->");
                const each_array_1 = ensure_array_like(fields);
                $$payload4.out.push(`<!--[-->`);
                for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                  let field = each_array_1[$$index_1];
                  $$payload4.out.push(`<div class="mb-4"><h4 class="font-bold mb-2">${escape_html(field.label)}</h4> <p>${escape_html(field.initialValue)}</p></div>`);
                }
                $$payload4.out.push(`<!--]--> <a${attr("href", editLink)} class="mt-1">`);
                Button($$payload4, {
                  class: `${stringify(dangerous ? "border-destructive" : "")} min-w-[145px]`,
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->${escape_html(editButtonTitle)}`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----></a>`);
              }
              $$payload4.out.push(`<!--]-->`);
            } else {
              $$payload4.out.push("<!--[!-->");
              $$payload4.out.push(`<div><div class="text-l font-bold">${escape_html(successTitle)}</div> <div class="text-base">${escape_html(successBody)}</div></div> <a${attr("href", `/dashboard/${stringify(env.value?.name)}/settings`)}${attr_class(`${stringify(buttonVariants({ size: "sm" }))} mt-3 min-w-[145px]`)}>Return to Settings</a>`);
            }
            $$payload4.out.push(`<!--]--></div>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  Settings_module as S
};
