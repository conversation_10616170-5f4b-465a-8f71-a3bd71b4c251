import { A as fallback, e as escape_html, B as bind_props } from "./index2.js";
function Contact_thankyou_html($$payload, $$props) {
  const ssr = true;
  let first_name = fallback($$props["first_name"], "");
  $$payload.out.push(`<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;"><p>Hi ${escape_html(first_name)},</p> <p>Thanks for reaching out! We've received your message and an augmented marketer will reply within one business day.</p> <p>In the meantime, you can read <a href="https://robynn.ai/#stories" style="color: #0066cc;">success stories</a> from our customers.</p> <p>– The Robynn team</p></div>`);
  bind_props($$props, { first_name, ssr });
}
export {
  Contact_thankyou_html as default
};
