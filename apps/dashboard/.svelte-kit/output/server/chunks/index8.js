import { m as sanitize_props, o as spread_props, b as slot, l as setContext, a8 as hasContext, k as getContext, a as push, s as store_get, u as unsubscribe_stores, B as bind_props, p as pop, A as fallback, C as rest_props, D as spread_attributes, t as ensure_array_like, e as escape_html, y as invalid_default_snippet, v as clsx, d as attr_class } from "./index2.js";
import "clsx";
import { c as cn } from "./utils.js";
import { L as Label } from "./input.js";
import { I as Icon } from "./Icon.js";
import { w as writable } from "./index4.js";
import { n as nanoid } from "./index7.js";
import "./create.js";
import "./index6.js";
function Circle_alert($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  /**
   * @license lucide-svelte v0.436.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "10" }],
    ["line", { "x1": "12", "x2": "12", "y1": "8", "y2": "12" }],
    [
      "line",
      { "x1": "12", "x2": "12.01", "y1": "16", "y2": "16" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "circle-alert" },
    $$sanitized_props,
    {
      /**
       * @component @name CircleAlert
       * @description Lucide SVG icon component, renders SVG Element with children.
       *
       * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert
       * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
       *
       * @param {Object} props - Lucide icons props and any valid SVG attribute
       * @returns {FunctionalComponent} Svelte component
       *
       */
      iconNode,
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
}
const FORM_FIELD = Symbol("FORM_FIELD_CTX");
function setFormField(props) {
  setContext(FORM_FIELD, props);
  return props;
}
function getFormField() {
  if (!hasContext(FORM_FIELD)) {
    ctxError("Form.Field");
  }
  return getContext(FORM_FIELD);
}
const FORM_CONTROL = Symbol("FORM_CONTROL_CTX");
function setFormControl(props) {
  setContext(FORM_CONTROL, props);
  return props;
}
function getFormControl() {
  if (!hasContext(FORM_CONTROL)) {
    ctxError("<Control />");
  }
  return getContext(FORM_CONTROL);
}
function ctxError(ctx) {
  throw new Error(`Unable to find \`${ctx}\` context. Did you forget to wrap the component in a \`${ctx}\`?`);
}
function getAriaDescribedBy({ fieldErrorsId = void 0, descriptionId = void 0, errors }) {
  let describedBy = "";
  if (descriptionId) {
    describedBy += descriptionId + " ";
  }
  if (errors.length && fieldErrorsId) {
    describedBy += fieldErrorsId;
  }
  return describedBy ? describedBy.trim() : void 0;
}
function getAriaRequired(constraints) {
  if (!("required" in constraints))
    return void 0;
  return constraints.required ? "true" : void 0;
}
function getAriaInvalid(errors) {
  return errors && errors.length ? "true" : void 0;
}
function getDataFsError(errors) {
  return errors && errors.length ? "" : void 0;
}
function generateId() {
  return nanoid(5);
}
function extractErrorArray(errors) {
  if (Array.isArray(errors))
    return errors;
  if (typeof errors === "object" && "_errors" in errors) {
    if (errors._errors !== void 0)
      return errors._errors;
  }
  return [];
}
function getValueAtPath(path, obj) {
  const keys = path.split(/[[\].]/).filter(Boolean);
  let value = obj;
  for (const key of keys) {
    if (typeof value !== "object" || value === null) {
      return void 0;
    }
    value = value[key];
  }
  return value;
}
function Field($$payload, $$props) {
  push();
  var $$store_subs;
  let formErrors, formConstraints, formTainted, formData;
  let form = $$props["form"];
  let name = $$props["name"];
  const field = {
    name: writable(name),
    errors: writable([]),
    constraints: writable({}),
    tainted: writable(false),
    fieldErrorsId: writable(),
    descriptionId: writable(),
    form
  };
  const { tainted, errors } = field;
  setFormField(field);
  ({
    errors: formErrors,
    constraints: formConstraints,
    tainted: formTainted,
    form: formData
  } = form);
  field.name.set(name);
  field.errors.set(extractErrorArray(getValueAtPath(name, store_get($$store_subs ??= {}, "$formErrors", formErrors))));
  field.constraints.set(getValueAtPath(name, store_get($$store_subs ??= {}, "$formConstraints", formConstraints)) ?? {});
  field.tainted.set(store_get($$store_subs ??= {}, "$formTainted", formTainted) ? getValueAtPath(name, store_get($$store_subs ??= {}, "$formTainted", formTainted)) === true : false);
  $$payload.out.push(`<!---->`);
  slot(
    $$payload,
    $$props,
    "default",
    {
      value: store_get($$store_subs ??= {}, "$formData", formData)[name],
      errors: store_get($$store_subs ??= {}, "$errors", errors),
      tainted: store_get($$store_subs ??= {}, "$tainted", tainted),
      constraints: store_get($$store_subs ??= {}, "$formConstraints", formConstraints)[name]
    },
    null
  );
  $$payload.out.push(`<!---->`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { form, name });
  pop();
}
function Control$1($$payload, $$props) {
  push();
  var $$store_subs;
  let errorAttr, attrs, labelAttrs;
  let id = fallback($$props["id"], generateId, true);
  const { name, fieldErrorsId, descriptionId, errors, constraints } = getFormField();
  const controlContext = { id: writable(id), attrs: writable(), labelAttrs: writable() };
  const { id: idStore } = controlContext;
  setFormControl(controlContext);
  controlContext.id.set(id);
  errorAttr = getDataFsError(store_get($$store_subs ??= {}, "$errors", errors));
  attrs = {
    name: store_get($$store_subs ??= {}, "$name", name),
    id: store_get($$store_subs ??= {}, "$idStore", idStore),
    "data-fs-error": errorAttr,
    "aria-describedby": getAriaDescribedBy({
      fieldErrorsId: store_get($$store_subs ??= {}, "$fieldErrorsId", fieldErrorsId),
      descriptionId: store_get($$store_subs ??= {}, "$descriptionId", descriptionId),
      errors: store_get($$store_subs ??= {}, "$errors", errors)
    }),
    "aria-invalid": getAriaInvalid(store_get($$store_subs ??= {}, "$errors", errors)),
    "aria-required": getAriaRequired(store_get($$store_subs ??= {}, "$constraints", constraints)),
    "data-fs-control": ""
  };
  labelAttrs = {
    for: store_get($$store_subs ??= {}, "$idStore", idStore),
    "data-fs-label": "",
    "data-fs-error": errorAttr
  };
  controlContext.attrs.set(attrs);
  controlContext.labelAttrs.set(labelAttrs);
  $$payload.out.push(`<!---->`);
  slot($$payload, $$props, "default", { attrs }, null);
  $$payload.out.push(`<!---->`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { id });
  pop();
}
function Field_errors($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["id", "asChild", "el"]);
  push();
  var $$store_subs;
  let errorAttr, fieldErrorsAttrs, errorAttrs;
  const { fieldErrorsId, errors } = getFormField();
  let id = fallback($$props["id"], generateId, true);
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  errorAttr = getDataFsError(store_get($$store_subs ??= {}, "$errors", errors));
  fieldErrorsId.set(id);
  fieldErrorsAttrs = {
    id: store_get($$store_subs ??= {}, "$fieldErrorsId", fieldErrorsId),
    "data-fs-error": errorAttr,
    "data-fs-field-errors": "",
    "aria-live": "assertive",
    ...$$restProps
  };
  errorAttrs = { "data-fs-field-error": "", "data-fs-error": errorAttr };
  if (asChild) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<!---->`);
    slot(
      $$payload,
      $$props,
      "default",
      {
        errors: store_get($$store_subs ??= {}, "$errors", errors),
        fieldErrorsAttrs,
        errorAttrs
      },
      null
    );
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...fieldErrorsAttrs }, null)}><!---->`);
    slot(
      $$payload,
      $$props,
      "default",
      {
        errors: store_get($$store_subs ??= {}, "$errors", errors),
        fieldErrorsAttrs,
        errorAttrs
      },
      () => {
        const each_array = ensure_array_like(store_get($$store_subs ??= {}, "$errors", errors));
        $$payload.out.push(`<!--[-->`);
        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
          let error = each_array[$$index];
          $$payload.out.push(`<div${spread_attributes({ ...errorAttrs }, null)}>${escape_html(error)}</div>`);
        }
        $$payload.out.push(`<!--]-->`);
      }
    );
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { id, asChild, el });
  pop();
}
function Form_label($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  var $$store_subs;
  let className = fallback($$props["class"], void 0);
  const { labelAttrs } = getFormControl();
  Label($$payload, spread_props([
    store_get($$store_subs ??= {}, "$labelAttrs", labelAttrs),
    { class: cn("data-[fs-error]:text-destructive", className) },
    $$restProps,
    {
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        slot($$payload2, $$props, "default", { labelAttrs }, null);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { class: className });
  pop();
}
function Form_field_errors($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "errorClasses"]);
  push();
  let className = fallback($$props["class"], void 0);
  let errorClasses = fallback($$props["errorClasses"], void 0);
  Field_errors($$payload, spread_props([
    { class: cn("text-destructive text-sm font-medium", className) },
    $$restProps,
    {
      children: invalid_default_snippet,
      $$slots: {
        default: ($$payload2, { errors, fieldErrorsAttrs, errorAttrs }) => {
          $$payload2.out.push(`<!---->`);
          slot($$payload2, $$props, "default", { errors, fieldErrorsAttrs, errorAttrs }, () => {
            const each_array = ensure_array_like(errors);
            $$payload2.out.push(`<!--[-->`);
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let error = each_array[$$index];
              $$payload2.out.push(`<div${spread_attributes(
                {
                  ...errorAttrs,
                  class: clsx(cn("flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium", "dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400", errorClasses))
                },
                null
              )}>`);
              Circle_alert($$payload2, { size: 16, class: "flex-shrink-0" });
              $$payload2.out.push(`<!----> <span>${escape_html(error)}</span></div>`);
            }
            $$payload2.out.push(`<!--]-->`);
          });
          $$payload2.out.push(`<!---->`);
        }
      }
    }
  ]));
  bind_props($$props, { class: className, errorClasses });
  pop();
}
function Form_field($$payload, $$props) {
  push();
  let form = $$props["form"];
  let name = $$props["name"];
  let className = fallback($$props["class"], void 0);
  Field($$payload, {
    form,
    name,
    children: invalid_default_snippet,
    $$slots: {
      default: ($$payload2, { constraints, errors, tainted, value }) => {
        $$payload2.out.push(`<div${attr_class(clsx(cn("space-y-2", className)))}><!---->`);
        slot($$payload2, $$props, "default", { constraints, errors, tainted, value }, null);
        $$payload2.out.push(`<!----></div>`);
      }
    }
  });
  bind_props($$props, { form, name, class: className });
  pop();
}
const Control = Control$1;
export {
  Control as C,
  Form_field as F,
  Form_label as a,
  Form_field_errors as b,
  Circle_alert as c
};
