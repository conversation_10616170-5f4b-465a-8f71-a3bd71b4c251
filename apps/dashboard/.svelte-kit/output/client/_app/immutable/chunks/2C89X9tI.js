import{X as I,v as _,w as N,Z as R,a2 as x,a3 as D,a4 as F,E as S,a5 as p,a6 as C,_ as b,a7 as L,a8 as Z,a9 as q,aa as w,ab as z,z as H}from"./DDiqt3uM.js";function P(E,T,g=!1){_&&N();var r=E,t=null,s=null,e=Z,y=g?R:0,l=!1;const k=(n,a=!0)=>{l=!0,d(a,n)};var f=null;function o(){f!==null&&(f.lastChild.remove(),r.before(f),f=null);var n=e?t:s,a=e?s:t;n&&w(n),a&&z(a,()=>{e?s=null:t=null})}const d=(n,a)=>{if(e===(e=n))return;let u=!1;if(_){const A=x(r)===D;!!e===A&&(r=F(),S(r),p(!1),u=!0)}var v=q(),i=r;if(v&&(f=document.createDocumentFragment(),f.append(i=C())),e?t??(t=a&&b(()=>a(i))):s??(s=a&&b(()=>a(i))),v){var c=L,h=e?t:s,m=e?s:t;h&&c.skipped_effects.delete(h),m&&c.skipped_effects.add(m),c.add_callback(o)}else o();u&&p(!0)};I(()=>{l=!1,T(k),l||d(null,null)},y),_&&(r=H)}export{P as i};
