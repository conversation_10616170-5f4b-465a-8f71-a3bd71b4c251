import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as n,d as p,a as i}from"./DDiqt3uM.js";import{s as m}from"./iCEqKm8o.js";import{l as d,s as c}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function y(s,o){const t=d(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"}]];$(s,c({name:"zap"},()=>t,{get iconNode(){return r},children:(e,f)=>{var a=n(),l=p(a);m(l,o,"default",{},null),i(e,a)},$$slots:{default:!0}}))}export{y as Z};
