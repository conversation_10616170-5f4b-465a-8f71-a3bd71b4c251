import"./CWj6FrbW.js";import"./DhRTwODG.js";import{p as j,aY as B,a as v,e as I,c as A,s as O,h as k,aT as s,r as P,b as T,d as Y,i as u,k as Z,aZ as q}from"./DDiqt3uM.js";import{e as D,i as E}from"./OiKQa7Wx.js";import{s as F}from"./iCEqKm8o.js";import{e as G}from"./CzTs9bhI.js";import{a as _}from"./C36Ip9GY.js";import{i as H}from"./B_FgA42l.js";import{l as p,p as o}from"./C-ZVHnwW.js";/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};var K=B("<svg><!><!></svg>");function oe(b,e){const l=p(e,["children","$$slots","$$events","$$legacy"]),w=p(l,["name","color","size","strokeWidth","absoluteStrokeWidth","iconNode"]);j(e,!1);let d=o(e,"name",8,void 0),W=o(e,"color",8,"currentColor"),a=o(e,"size",8,24),c=o(e,"strokeWidth",8,2),h=o(e,"absoluteStrokeWidth",8,!1),x=o(e,"iconNode",24,()=>[]);const y=(...r)=>r.filter((t,n,m)=>!!t&&m.indexOf(t)===n).join(" ");H();var i=K();_(i,(r,t)=>({...J,...w,width:a(),height:a(),stroke:W(),"stroke-width":r,class:t}),[()=>(s(h()),s(c()),s(a()),k(()=>h()?Number(c())*24/Number(a()):c())),()=>(s(d()),s(l),k(()=>y("lucide-icon","lucide",d()?`lucide-${d()}`:"",l.class)))]);var f=A(i);D(f,1,x,E,(r,t)=>{var n=Z(()=>q(u(t),2));let m=()=>u(n)[0],N=()=>u(n)[1];var g=T(),C=Y(g);G(C,m,!0,(S,L)=>{_(S,()=>({...N()}))}),v(r,g)});var z=O(f);F(z,e,"default",{},null),P(i),v(b,i),I()}export{oe as I};
