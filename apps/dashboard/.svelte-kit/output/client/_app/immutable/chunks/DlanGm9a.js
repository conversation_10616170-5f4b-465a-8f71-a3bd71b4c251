import"./CWj6FrbW.js";import"./DhRTwODG.js";import{o as Ut}from"./RnwjOPnl.js";import{p as ve,l as we,j as V,m as oe,aT as b,g as Be,f as ne,h as L,i as v,c as ce,r as de,a as d,e as he,d as O,n as K,q as Q,t as X,s as D,ak as be,b as se,aY as ue}from"./DDiqt3uM.js";import{i as R}from"./2C89X9tI.js";import{s as Pe}from"./iCEqKm8o.js";import{s as Zt,c as Yt}from"./DE2v8SHj.js";import{i as me}from"./B_FgA42l.js";import{l as fe,p as m}from"./C-ZVHnwW.js";import{e as Me,s as ee}from"./DWulv87v.js";import{a as Te,r as qt}from"./C36Ip9GY.js";import{b as _t}from"./B9BVeOQN.js";import{b as Jt}from"./DUGxtfU6.js";import{e as Kt,i as Xt}from"./OiKQa7Wx.js";function Ye(a){return function(...e){var t=e[0];return t.preventDefault(),a==null?void 0:a.apply(this,e)}}var ut,re="colors",ge="sizes",x="space",Qt={gap:x,gridGap:x,columnGap:x,gridColumnGap:x,rowGap:x,gridRowGap:x,inset:x,insetBlock:x,insetBlockEnd:x,insetBlockStart:x,insetInline:x,insetInlineEnd:x,insetInlineStart:x,margin:x,marginTop:x,marginRight:x,marginBottom:x,marginLeft:x,marginBlock:x,marginBlockEnd:x,marginBlockStart:x,marginInline:x,marginInlineEnd:x,marginInlineStart:x,padding:x,paddingTop:x,paddingRight:x,paddingBottom:x,paddingLeft:x,paddingBlock:x,paddingBlockEnd:x,paddingBlockStart:x,paddingInline:x,paddingInlineEnd:x,paddingInlineStart:x,top:x,right:x,bottom:x,left:x,scrollMargin:x,scrollMarginTop:x,scrollMarginRight:x,scrollMarginBottom:x,scrollMarginLeft:x,scrollMarginX:x,scrollMarginY:x,scrollMarginBlock:x,scrollMarginBlockEnd:x,scrollMarginBlockStart:x,scrollMarginInline:x,scrollMarginInlineEnd:x,scrollMarginInlineStart:x,scrollPadding:x,scrollPaddingTop:x,scrollPaddingRight:x,scrollPaddingBottom:x,scrollPaddingLeft:x,scrollPaddingX:x,scrollPaddingY:x,scrollPaddingBlock:x,scrollPaddingBlockEnd:x,scrollPaddingBlockStart:x,scrollPaddingInline:x,scrollPaddingInlineEnd:x,scrollPaddingInlineStart:x,fontSize:"fontSizes",background:re,backgroundColor:re,backgroundImage:re,borderImage:re,border:re,borderBlock:re,borderBlockEnd:re,borderBlockStart:re,borderBottom:re,borderBottomColor:re,borderColor:re,borderInline:re,borderInlineEnd:re,borderInlineStart:re,borderLeft:re,borderLeftColor:re,borderRight:re,borderRightColor:re,borderTop:re,borderTopColor:re,caretColor:re,color:re,columnRuleColor:re,fill:re,outline:re,outlineColor:re,stroke:re,textDecorationColor:re,fontFamily:"fonts",fontWeight:"fontWeights",lineHeight:"lineHeights",letterSpacing:"letterSpacings",blockSize:ge,minBlockSize:ge,maxBlockSize:ge,inlineSize:ge,minInlineSize:ge,maxInlineSize:ge,width:ge,minWidth:ge,maxWidth:ge,height:ge,minHeight:ge,maxHeight:ge,flexBasis:ge,gridTemplateColumns:ge,gridTemplateRows:ge,borderWidth:"borderWidths",borderTopWidth:"borderWidths",borderRightWidth:"borderWidths",borderBottomWidth:"borderWidths",borderLeftWidth:"borderWidths",borderStyle:"borderStyles",borderTopStyle:"borderStyles",borderRightStyle:"borderStyles",borderBottomStyle:"borderStyles",borderLeftStyle:"borderStyles",borderRadius:"radii",borderTopLeftRadius:"radii",borderTopRightRadius:"radii",borderBottomRightRadius:"radii",borderBottomLeftRadius:"radii",boxShadow:"shadows",textShadow:"shadows",transition:"transitions",zIndex:"zIndices"},ea=(a,e)=>typeof e=="function"?{"()":Function.prototype.toString.call(e)}:e,qe=()=>{const a=Object.create(null);return(e,t,...r)=>{const l=(i=>JSON.stringify(i,ea))(e);return l in a?a[l]:a[l]=t(e,...r)}},nt=Symbol.for("sxs.internal"),dt=(a,e)=>Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)),pt=a=>{for(const e in a)return!0;return!1},{hasOwnProperty:ta}=Object.prototype,st=a=>a.includes("-")?a:a.replace(/[A-Z]/g,e=>"-"+e.toLowerCase()),aa=/\s+(?![^()]*\))/,je=a=>e=>a(...typeof e=="string"?String(e).split(aa):[e]),gt={appearance:a=>({WebkitAppearance:a,appearance:a}),backfaceVisibility:a=>({WebkitBackfaceVisibility:a,backfaceVisibility:a}),backdropFilter:a=>({WebkitBackdropFilter:a,backdropFilter:a}),backgroundClip:a=>({WebkitBackgroundClip:a,backgroundClip:a}),boxDecorationBreak:a=>({WebkitBoxDecorationBreak:a,boxDecorationBreak:a}),clipPath:a=>({WebkitClipPath:a,clipPath:a}),content:a=>({content:a.includes('"')||a.includes("'")||/^([A-Za-z]+\([^]*|[^]*-quote|inherit|initial|none|normal|revert|unset)$/.test(a)?a:`"${a}"`}),hyphens:a=>({WebkitHyphens:a,hyphens:a}),maskImage:a=>({WebkitMaskImage:a,maskImage:a}),maskSize:a=>({WebkitMaskSize:a,maskSize:a}),tabSize:a=>({MozTabSize:a,tabSize:a}),textSizeAdjust:a=>({WebkitTextSizeAdjust:a,textSizeAdjust:a}),userSelect:a=>({WebkitUserSelect:a,userSelect:a}),marginBlock:je((a,e)=>({marginBlockStart:a,marginBlockEnd:e||a})),marginInline:je((a,e)=>({marginInlineStart:a,marginInlineEnd:e||a})),maxSize:je((a,e)=>({maxBlockSize:a,maxInlineSize:e||a})),minSize:je((a,e)=>({minBlockSize:a,minInlineSize:e||a})),paddingBlock:je((a,e)=>({paddingBlockStart:a,paddingBlockEnd:e||a})),paddingInline:je((a,e)=>({paddingInlineStart:a,paddingInlineEnd:e||a}))},lt=/([\d.]+)([^]*)/,ra=(a,e)=>a.length?a.reduce((t,r)=>(t.push(...e.map(l=>l.includes("&")?l.replace(/&/g,/[ +>|~]/.test(r)&&/&.*&/.test(l)?`:is(${r})`:r):r+" "+l)),t),[]):e,na=(a,e)=>a in oa&&typeof e=="string"?e.replace(/^((?:[^]*[^\w-])?)(fit-content|stretch)((?:[^\w-][^]*)?)$/,(t,r,l,i)=>r+(l==="stretch"?`-moz-available${i};${st(a)}:${r}-webkit-fill-available`:`-moz-fit-content${i};${st(a)}:${r}fit-content`)+i):String(e),oa={blockSize:1,height:1,inlineSize:1,maxBlockSize:1,maxHeight:1,maxInlineSize:1,maxWidth:1,minBlockSize:1,minHeight:1,minInlineSize:1,minWidth:1,width:1},Se=a=>a?a+"-":"",bt=(a,e,t)=>a.replace(/([+-])?((?:\d+(?:\.\d*)?|\.\d+)(?:[Ee][+-]?\d+)?)?(\$|--)([$\w-]+)/g,(r,l,i,c,s)=>c=="$"==!!i?r:(l||c=="--"?"calc(":"")+"var(--"+(c==="$"?Se(e)+(s.includes("$")?"":Se(t))+s.replace(/\$/g,"-"):s)+")"+(l||c=="--"?"*"+(l||"")+(i||"1")+")":"")),la=/\s*,\s*(?![^()]*\))/,ia=Object.prototype.toString,He=(a,e,t,r,l)=>{let i,c,s;const o=(g,n,p)=>{let u,h;const C=S=>{for(u in S){const E=u.charCodeAt(0)===64,G=E&&Array.isArray(S[u])?S[u]:[S[u]];for(h of G){const j=/[A-Z]/.test(k=u)?k:k.replace(/-[^]/g,H=>H[1].toUpperCase()),ae=typeof h=="object"&&h&&h.toString===ia&&(!r.utils[j]||!n.length);if(j in r.utils&&!ae){const H=r.utils[j];if(H!==c){c=H,C(H(h)),c=null;continue}}else if(j in gt){const H=gt[j];if(H!==s){s=H,C(H(h)),s=null;continue}}if(E&&(N=u.slice(1)in r.media?"@media "+r.media[u.slice(1)]:u,u=N.replace(/\(\s*([\w-]+)\s*(=|<|<=|>|>=)\s*([\w-]+)\s*(?:(<|<=|>|>=)\s*([\w-]+)\s*)?\)/g,(H,T,I,$,B,z)=>{const P=lt.test(T),U=.0625*(P?-1:1),[q,ie]=P?[$,T]:[T,$];return"("+(I[0]==="="?"":I[0]===">"===P?"max-":"min-")+q+":"+(I[0]!=="="&&I.length===1?ie.replace(lt,(F,_,w)=>Number(_)+U*(I===">"?1:-1)+w):ie)+(B?") and ("+(B[0]===">"?"min-":"max-")+q+":"+(B.length===1?z.replace(lt,(F,_,w)=>Number(_)+U*(B===">"?-1:1)+w):z):"")+")"})),ae){const H=E?p.concat(u):[...p],T=E?[...n]:ra(n,u.split(la));i!==void 0&&l(ft(...i)),i=void 0,o(h,T,H)}else i===void 0&&(i=[[],n,p]),u=E||u.charCodeAt(0)!==36?u:`--${Se(r.prefix)}${u.slice(1).replace(/\$/g,"-")}`,h=ae?h:typeof h=="number"?h&&j in sa?String(h)+"px":String(h):bt(na(j,h??""),r.prefix,r.themeMap[j]),i[0].push(`${E?`${u} `:`${st(u)}:`}${h}`)}}var N,k};C(g),i!==void 0&&l(ft(...i)),i=void 0};o(a,e,t)},ft=(a,e,t)=>`${t.map(r=>`${r}{`).join("")}${e.length?`${e.join(",")}{`:""}${a.join(";")}${e.length?"}":""}${Array(t.length?t.length+1:0).join("}")}`,sa={animationDelay:1,animationDuration:1,backgroundSize:1,blockSize:1,border:1,borderBlock:1,borderBlockEnd:1,borderBlockEndWidth:1,borderBlockStart:1,borderBlockStartWidth:1,borderBlockWidth:1,borderBottom:1,borderBottomLeftRadius:1,borderBottomRightRadius:1,borderBottomWidth:1,borderEndEndRadius:1,borderEndStartRadius:1,borderInlineEnd:1,borderInlineEndWidth:1,borderInlineStart:1,borderInlineStartWidth:1,borderInlineWidth:1,borderLeft:1,borderLeftWidth:1,borderRadius:1,borderRight:1,borderRightWidth:1,borderSpacing:1,borderStartEndRadius:1,borderStartStartRadius:1,borderTop:1,borderTopLeftRadius:1,borderTopRightRadius:1,borderTopWidth:1,borderWidth:1,bottom:1,columnGap:1,columnRule:1,columnRuleWidth:1,columnWidth:1,containIntrinsicSize:1,flexBasis:1,fontSize:1,gap:1,gridAutoColumns:1,gridAutoRows:1,gridTemplateColumns:1,gridTemplateRows:1,height:1,inlineSize:1,inset:1,insetBlock:1,insetBlockEnd:1,insetBlockStart:1,insetInline:1,insetInlineEnd:1,insetInlineStart:1,left:1,letterSpacing:1,margin:1,marginBlock:1,marginBlockEnd:1,marginBlockStart:1,marginBottom:1,marginInline:1,marginInlineEnd:1,marginInlineStart:1,marginLeft:1,marginRight:1,marginTop:1,maxBlockSize:1,maxHeight:1,maxInlineSize:1,maxWidth:1,minBlockSize:1,minHeight:1,minInlineSize:1,minWidth:1,offsetDistance:1,offsetRotate:1,outline:1,outlineOffset:1,outlineWidth:1,overflowClipMargin:1,padding:1,paddingBlock:1,paddingBlockEnd:1,paddingBlockStart:1,paddingBottom:1,paddingInline:1,paddingInlineEnd:1,paddingInlineStart:1,paddingLeft:1,paddingRight:1,paddingTop:1,perspective:1,right:1,rowGap:1,scrollMargin:1,scrollMarginBlock:1,scrollMarginBlockEnd:1,scrollMarginBlockStart:1,scrollMarginBottom:1,scrollMarginInline:1,scrollMarginInlineEnd:1,scrollMarginInlineStart:1,scrollMarginLeft:1,scrollMarginRight:1,scrollMarginTop:1,scrollPadding:1,scrollPaddingBlock:1,scrollPaddingBlockEnd:1,scrollPaddingBlockStart:1,scrollPaddingBottom:1,scrollPaddingInline:1,scrollPaddingInlineEnd:1,scrollPaddingInlineStart:1,scrollPaddingLeft:1,scrollPaddingRight:1,scrollPaddingTop:1,shapeMargin:1,textDecoration:1,textDecorationThickness:1,textIndent:1,textUnderlineOffset:1,top:1,transitionDelay:1,transitionDuration:1,verticalAlign:1,width:1,wordSpacing:1},vt=a=>String.fromCharCode(a+(a>25?39:97)),Ie=a=>(e=>{let t,r="";for(t=Math.abs(e);t>52;t=t/52|0)r=vt(t%52)+r;return vt(t%52)+r})(((e,t)=>{let r=t.length;for(;r;)e=33*e^t.charCodeAt(--r);return e})(5381,JSON.stringify(a))>>>0),Ze=["themed","global","styled","onevar","resonevar","allvar","inline"],ca=a=>{if(a.href&&!a.href.startsWith(location.origin))return!1;try{return!!a.cssRules}catch{return!1}},da=a=>{let e;const t=()=>{const{cssRules:l}=e.sheet;return[].map.call(l,(i,c)=>{const{cssText:s}=i;let o="";if(s.startsWith("--sxs"))return"";if(l[c-1]&&(o=l[c-1].cssText).startsWith("--sxs")){if(!i.cssRules.length)return"";for(const g in e.rules)if(e.rules[g].group===i)return`--sxs{--sxs:${[...e.rules[g].cache].join(" ")}}${s}`;return i.cssRules.length?`${o}${s}`:""}return s}).join("")},r=()=>{if(e){const{rules:s,sheet:o}=e;if(!o.deleteRule){for(;Object(Object(o.cssRules)[0]).type===3;)o.cssRules.splice(0,1);o.cssRules=[]}for(const g in s)delete s[g]}const l=Object(a).styleSheets||[];for(const s of l)if(ca(s)){for(let o=0,g=s.cssRules;g[o];++o){const n=Object(g[o]);if(n.type!==1)continue;const p=Object(g[o+1]);if(p.type!==4)continue;++o;const{cssText:u}=n;if(!u.startsWith("--sxs"))continue;const h=u.slice(14,-3).trim().split(/\s+/),C=Ze[h[0]];C&&(e||(e={sheet:s,reset:r,rules:{},toString:t}),e.rules[C]={group:p,index:o,cache:new Set(h)})}if(e)break}if(!e){const s=(o,g)=>({type:g,cssRules:[],insertRule(n,p){this.cssRules.splice(p,0,s(n,{import:3,undefined:1}[(n.toLowerCase().match(/^@([a-z]+)/)||[])[1]]||4))},get cssText(){return o==="@media{}"?`@media{${[].map.call(this.cssRules,n=>n.cssText).join("")}}`:o}});e={sheet:a?(a.head||a).appendChild(document.createElement("style")).sheet:s("","text/css"),rules:{},reset:r,toString:t}}const{sheet:i,rules:c}=e;for(let s=Ze.length-1;s>=0;--s){const o=Ze[s];if(!c[o]){const g=Ze[s+1],n=c[g]?c[g].index:i.cssRules.length;i.insertRule("@media{}",n),i.insertRule(`--sxs{--sxs:${s}}`,n),c[o]={group:i.cssRules[n+1],index:n,cache:new Set([s])}}ua(c[o])}};return r(),e},ua=a=>{const e=a.group;let t=e.cssRules.length;a.apply=r=>{try{e.insertRule(r,t),++t}catch{}}},Ue=Symbol(),pa=qe(),ga=(a,e)=>pa(a,()=>(...t)=>{let r={type:null,composers:new Set};for(const l of t)if(l!=null)if(l[nt]){r.type==null&&(r.type=l[nt].type);for(const i of l[nt].composers)r.composers.add(i)}else l.constructor!==Object||l.$$typeof?r.type==null&&(r.type=l):r.composers.add(fa(l,a));return r.type==null&&(r.type="span"),r.composers.size||r.composers.add(["PJLV",{},[],[],{},[]]),va(a,r,e)}),fa=({variants:a,compoundVariants:e,defaultVariants:t,...r},l)=>{const i=`${Se(l.prefix)}c-${Ie(r)}`,c=[],s=[],o=Object.create(null),g=[];for(const u in t)o[u]=String(t[u]);if(typeof a=="object"&&a)for(const u in a){n=o,p=u,ta.call(n,p)||(o[u]="undefined");const h=a[u];for(const C in h){const S={[u]:String(C)};String(C)==="undefined"&&g.push(u);const N=h[C],k=[S,N,!pt(N)];c.push(k)}}var n,p;if(typeof e=="object"&&e)for(const u of e){let{css:h,...C}=u;h=typeof h=="object"&&h||{};for(const N in C)C[N]=String(C[N]);const S=[C,h,!pt(h)];s.push(S)}return[i,r,c,s,o,g]},va=(a,e,t)=>{const[r,l,i,c]=ha(e.composers),s=typeof e.type=="function"||e.type.$$typeof?(p=>{function u(){for(let h=0;h<u[Ue].length;h++){const[C,S]=u[Ue][h];p.rules[C].apply(S)}return u[Ue]=[],null}return u[Ue]=[],u.rules={},Ze.forEach(h=>u.rules[h]={apply:C=>u[Ue].push([h,C])}),u})(t):null,o=(s||t).rules,g=`.${r}${l.length>1?`:where(.${l.slice(1).join(".")})`:""}`,n=p=>{p=typeof p=="object"&&p||ma;const{css:u,...h}=p,C={};for(const k in i)if(delete h[k],k in p){let E=p[k];typeof E=="object"&&E?C[k]={"@initial":i[k],...E}:(E=String(E),C[k]=E!=="undefined"||c.has(k)?E:i[k])}else C[k]=i[k];const S=new Set([...l]);for(const[k,E,G,j]of e.composers){t.rules.styled.cache.has(k)||(t.rules.styled.cache.add(k),He(E,[`.${k}`],[],a,T=>{o.styled.apply(T)}));const ae=ht(G,C,a.media),H=ht(j,C,a.media,!0);for(const T of ae)if(T!==void 0)for(const[I,$,B]of T){const z=`${k}-${Ie($)}-${I}`;S.add(z);const P=(B?t.rules.resonevar:t.rules.onevar).cache,U=B?o.resonevar:o.onevar;P.has(z)||(P.add(z),He($,[`.${z}`],[],a,q=>{U.apply(q)}))}for(const T of H)if(T!==void 0)for(const[I,$]of T){const B=`${k}-${Ie($)}-${I}`;S.add(B),t.rules.allvar.cache.has(B)||(t.rules.allvar.cache.add(B),He($,[`.${B}`],[],a,z=>{o.allvar.apply(z)}))}}if(typeof u=="object"&&u){const k=`${r}-i${Ie(u)}-css`;S.add(k),t.rules.inline.cache.has(k)||(t.rules.inline.cache.add(k),He(u,[`.${k}`],[],a,E=>{o.inline.apply(E)}))}for(const k of String(p.className||"").trim().split(/\s+/))k&&S.add(k);const N=h.className=[...S].join(" ");return{type:e.type,className:N,selector:g,props:h,toString:()=>N,deferredInjector:s}};return dt(n,{className:r,selector:g,[nt]:e,toString:()=>(t.rules.styled.cache.has(r)||n(),r)})},ha=a=>{let e="";const t=[],r={},l=[];for(const[i,,,,c,s]of a){e===""&&(e=i),t.push(i),l.push(...s);for(const o in c){const g=c[o];(r[o]===void 0||g!=="undefined"||s.includes(g))&&(r[o]=g)}}return[e,t,r,new Set(l)]},ht=(a,e,t,r)=>{const l=[];e:for(let[i,c,s]of a){if(s)continue;let o,g=0,n=!1;for(o in i){const p=i[o];let u=e[o];if(u!==p){if(typeof u!="object"||!u)continue e;{let h,C,S=0;for(const N in u){if(p===String(u[N])){if(N!=="@initial"){const k=N.slice(1);(C=C||[]).push(k in t?t[k]:N.replace(/^@media ?/,"")),n=!0}g+=S,h=!0}++S}if(C&&C.length&&(c={["@media "+C.join(", ")]:c}),!h)continue e}}}(l[g]=l[g]||[]).push([r?"cv":`${o}-${i[o]}`,c,n])}return l},ma={},_a=qe(),ba=(a,e)=>_a(a,()=>(...t)=>{const r=()=>{for(let l of t){l=typeof l=="object"&&l||{};let i=Ie(l);if(!e.rules.global.cache.has(i)){if(e.rules.global.cache.add(i),"@import"in l){let c=[].indexOf.call(e.sheet.cssRules,e.rules.themed.group)-1;for(let s of[].concat(l["@import"]))s=s.includes('"')||s.includes("'")?s:`"${s}"`,e.sheet.insertRule(`@import ${s};`,c++);delete l["@import"]}He(l,[],[],a,c=>{e.rules.global.apply(c)})}}return""};return dt(r,{toString:r})}),ya=qe(),wa=(a,e)=>ya(a,()=>t=>{const r=`${Se(a.prefix)}k-${Ie(t)}`,l=()=>{if(!e.rules.global.cache.has(r)){e.rules.global.cache.add(r);const i=[];He(t,[],[],a,s=>i.push(s));const c=`@keyframes ${r}{${i.join("")}}`;e.rules.global.apply(c)}return r};return dt(l,{get name(){return l()},toString:l})}),xa=class{constructor(a,e,t,r){this.token=a==null?"":String(a),this.value=e==null?"":String(e),this.scale=t==null?"":String(t),this.prefix=r==null?"":String(r)}get computedValue(){return"var("+this.variable+")"}get variable(){return"--"+Se(this.prefix)+Se(this.scale)+this.token}toString(){return this.computedValue}},Sa=qe(),ka=(a,e)=>Sa(a,()=>(t,r)=>{r=typeof t=="object"&&t||Object(r);const l=`.${t=(t=typeof t=="string"?t:"")||`${Se(a.prefix)}t-${Ie(r)}`}`,i={},c=[];for(const o in r){i[o]={};for(const g in r[o]){const n=`--${Se(a.prefix)}${o}-${g}`,p=bt(String(r[o][g]),a.prefix,o);i[o][g]=new xa(g,p,o,a.prefix),c.push(`${n}:${p}`)}}const s=()=>{if(c.length&&!e.rules.themed.cache.has(t)){e.rules.themed.cache.add(t);const o=`${r===a.theme?":root,":""}.${t}{${c.join(";")}}`;e.rules.themed.apply(o)}return t};return{...i,get className(){return s()},selector:l,toString:s}}),$a=qe(),yt=a=>{let e=!1;const t=$a(a,r=>{e=!0;const l="prefix"in(r=typeof r=="object"&&r||{})?String(r.prefix):"",i=typeof r.media=="object"&&r.media||{},c=typeof r.root=="object"?r.root||null:globalThis.document||null,s=typeof r.theme=="object"&&r.theme||{},o={prefix:l,media:i,theme:s,themeMap:typeof r.themeMap=="object"&&r.themeMap||{...Qt},utils:typeof r.utils=="object"&&r.utils||{}},g=da(c),n={css:ga(o,g),globalCss:ba(o,g),keyframes:wa(o,g),createTheme:ka(o,g),reset(){g.reset(),n.theme.toString()},theme:{},sheet:g,config:o,prefix:l,getCssText:g.toString,toString:g.toString};return String(n.theme=n.createTheme(s)),n});return e||t.reset(),t},wt=()=>ut||(ut=yt()),Ca=(...a)=>wt().createTheme(...a),Oe=(...a)=>wt().css(...a),Ba={default:{colors:{brand:"hsl(153 60.0% 53.0%)",brandAccent:"hsl(154 54.8% 45.1%)",brandButtonText:"white",defaultButtonBackground:"white",defaultButtonBackgroundHover:"#eaeaea",defaultButtonBorder:"lightgray",defaultButtonText:"gray",dividerBackground:"#eaeaea",inputBackground:"transparent",inputBorder:"lightgray",inputBorderHover:"gray",inputBorderFocus:"gray",inputText:"black",inputLabelText:"gray",inputPlaceholder:"darkgray",messageText:"#2b805a",messageBackground:"#e7fcf1",messageBorder:"#d0f3e1",messageTextDanger:"#ff6369",messageBackgroundDanger:"#fff8f8",messageBorderDanger:"#822025",anchorTextColor:"gray",anchorTextHoverColor:"darkgray"},space:{spaceSmall:"4px",spaceMedium:"8px",spaceLarge:"16px",labelBottomMargin:"8px",anchorBottomMargin:"4px",emailInputSpacing:"4px",socialAuthSpacing:"4px",buttonPadding:"10px 15px",inputPadding:"10px 15px"},fontSizes:{baseBodySize:"13px",baseInputSize:"14px",baseLabelSize:"14px",baseButtonSize:"14px"},fonts:{bodyFontFamily:"ui-sans-serif, sans-serif",buttonFontFamily:"ui-sans-serif, sans-serif",inputFontFamily:"ui-sans-serif, sans-serif",labelFontFamily:"ui-sans-serif, sans-serif"},borderWidths:{buttonBorderWidth:"1px",inputBorderWidth:"1px"},radii:{borderRadiusButton:"4px",buttonBorderRadius:"4px",inputBorderRadius:"4px"}},dark:{colors:{brandButtonText:"white",defaultButtonBackground:"#2e2e2e",defaultButtonBackgroundHover:"#3e3e3e",defaultButtonBorder:"#3e3e3e",defaultButtonText:"white",dividerBackground:"#2e2e2e",inputBackground:"#1e1e1e",inputBorder:"#3e3e3e",inputBorderHover:"gray",inputBorderFocus:"gray",inputText:"white",inputPlaceholder:"darkgray",messageText:"#85e0b7",messageBackground:"#072719",messageBorder:"#2b805a",messageBackgroundDanger:"#1f1315"}}},J={SIGN_IN:"sign_in",SIGN_UP:"sign_up",FORGOTTEN_PASSWORD:"forgotten_password",MAGIC_LINK:"magic_link",UPDATE_PASSWORD:"update_password",VERIFY_OTP:"verify_otp"},Pa="supabase-auth-ui",La={ROOT:"root",SIGN_IN:J.SIGN_IN,SIGN_UP:J.SIGN_UP,FORGOTTEN_PASSWORD:J.FORGOTTEN_PASSWORD,MAGIC_LINK:J.MAGIC_LINK,UPDATE_PASSWORD:J.UPDATE_PASSWORD,anchor:"ui-anchor",button:"ui-button",container:"ui-container",divider:"ui-divider",input:"ui-input",label:"ui-label",loader:"ui-loader",message:"ui-message"};function Re(a,e,t){var r,l;const i=[],c=La[a];return i.push(t!=null&&t.prependedClassName?(t==null?void 0:t.prependedClassName)+"_"+c:Pa+"_"+c),(r=t==null?void 0:t.className)!=null&&r[a]&&i.push((l=t==null?void 0:t.className)==null?void 0:l[a]),((t==null?void 0:t.extend)===void 0||(t==null?void 0:t.extend)===!0)&&i.push(e),i}function ct(a,e){let t;if(a&&e&&typeof a=="object"&&typeof e=="object"){if(Array.isArray(e))for(t=0;t<e.length;t++)a[t]=ct(a[t],e[t]);else for(t in e)a[t]=ct(a[t],e[t]);return a}return e}function it(a,...e){let t=e.length;for(let r=0;r<t;r++)a=ct(a,e[r]);return a}function za(a,e){return a.replace(/{{(\w+)}}/g,(t,r)=>e.hasOwnProperty(r)?e[r]:t)}var Ia={sign_up:{email_label:"Email address",password_label:"Create a Password",email_input_placeholder:"Your email address",password_input_placeholder:"Your password",button_label:"Sign up",loading_button_label:"Signing up ...",social_provider_text:"Sign in with {{provider}}",link_text:"Don't have an account? Sign up",confirmation_text:"Check your email for the confirmation link"},sign_in:{email_label:"Email address",password_label:"Your Password",email_input_placeholder:"Your email address",password_input_placeholder:"Your password",button_label:"Sign in",loading_button_label:"Signing in ...",social_provider_text:"Sign in with {{provider}}",link_text:"Already have an account? Sign in"},magic_link:{email_input_label:"Email address",email_input_placeholder:"Your email address",button_label:"Send Magic Link",loading_button_label:"Sending Magic Link ...",link_text:"Send a magic link email",confirmation_text:"Check your email for the magic link"},forgotten_password:{email_label:"Email address",password_label:"Your Password",email_input_placeholder:"Your email address",button_label:"Send reset password instructions",loading_button_label:"Sending reset instructions ...",link_text:"Forgot your password?",confirmation_text:"Check your email for the password reset link"},update_password:{password_label:"New password",password_input_placeholder:"Your new password",button_label:"Update password",loading_button_label:"Updating password ...",confirmation_text:"Your password has been updated"},verify_otp:{email_input_label:"Email address",email_input_placeholder:"Your email address",phone_input_label:"Phone number",phone_input_placeholder:"Your phone number",token_input_label:"Token",token_input_placeholder:"Your Otp token",button_label:"Verify token",loading_button_label:"Signing in ..."}},Ma=ne("<a><!></a>");function ke(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["href","appearance"]);ve(e,!1);const l=oe(),i=Oe({fontFamily:"$bodyFontFamily",fontSize:"$baseBodySize",marginBottom:"$anchorBottomMargin",color:"$anchorTextColor",display:"block",textAlign:"center",textDecoration:"underline","&:hover":{color:"$anchorTextHoverColor"}});let c=m(e,"href",8),s=m(e,"appearance",24,()=>({}));we(()=>b(s()),()=>{V(l,Re("anchor",i(),s()))}),Be(),me();var o=Ma();Te(o,n=>({href:c(),...r,style:(b(s()),L(()=>{var p,u;return(u=(p=s())==null?void 0:p.style)==null?void 0:u.anchor})),class:n}),[()=>(v(l),L(()=>v(l).join(" ")))]);var g=ce(o);Pe(g,e,"default",{},null),de(o),Me("click",o,function(n){_t.call(this,e,n)}),d(a,o),he()}var Ta=ne("<button><!></button>");function Ge(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["color","appearance","loading"]);ve(e,!1);const l=oe();let i=m(e,"color",8,"default"),c=m(e,"appearance",24,()=>({})),s=m(e,"loading",8,!1);we(()=>(b(i()),b(c())),()=>{V(l,Re("button",i(),c()))}),Be(),me();var o=Ta();Te(o,n=>({...r,disabled:s(),style:(b(c()),L(()=>{var p,u;return(u=(p=c())==null?void 0:p.style)==null?void 0:u.button})),class:n}),[()=>(v(l),L(()=>v(l).join(" ")))],void 0,"svelte-ll2s9h");var g=ce(o);Pe(g,e,"default",{},null),de(o),Me("click",o,function(n){_t.call(this,e,n)}),d(a,o),he()}var Ra=ne("<div><!></div>");function ye(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["direction","gap","appearance"]);ve(e,!1);const l=oe(),i=Oe({display:"flex",gap:"4px",variants:{direction:{horizontal:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(48px, 1fr))"},vertical:{flexDirection:"column",margin:"8px 0"}},gap:{small:{gap:"4px"},medium:{gap:"8px"},large:{gap:"16px"}}}});let c=m(e,"direction",8,"horizontal"),s=m(e,"gap",8,"small"),o=m(e,"appearance",24,()=>({}));we(()=>(b(c()),b(s()),b(o())),()=>{V(l,Re("container",i({direction:c(),gap:s()}),o()))}),Be(),me();var g=Ra();Te(g,p=>({...r,style:(b(o()),L(()=>{var u,h;return(h=(u=o())==null?void 0:u.style)==null?void 0:h.container})),class:p}),[()=>(v(l),L(()=>v(l).join(" ")))]);var n=ce(g);Pe(n,e,"default",{},null),de(g),d(a,g),he()}var Wa=ne("<input/>");function $e(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["value","appearance"]);ve(e,!1);const l=oe(),i=Oe({fontFamily:"$inputFontFamily",background:"$inputBackground",borderRadius:"$inputBorderRadius",padding:"$inputPadding",cursor:"text",borderWidth:"$inputBorderWidth",borderColor:"$inputBorder",borderStyle:"solid",fontSize:"$baseInputSize",width:"100%",color:"$inputText",boxSizing:"border-box","&:hover":{borderColor:"$inputBorderHover",outline:"none"},"&:focus":{borderColor:"$inputBorderFocus",outline:"none"},"&::placeholder":{color:"$inputPlaceholder",letterSpacing:"initial"},transitionProperty:"background-color, border",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)",transitionDuration:"100ms",variants:{type:{default:{letterSpacing:"0px"},password:{letterSpacing:"0px"}}}});let c=m(e,"value",12,void 0),s=m(e,"appearance",24,()=>({}));we(()=>b(s()),()=>{V(l,Re("input",i({type:"default"}),s()))}),Be(),me();var o=Wa();qt(o),Te(o,g=>({...r,style:(b(s()),L(()=>{var n,p;return(p=(n=s())==null?void 0:n.style)==null?void 0:p.input})),class:g}),[()=>(v(l),L(()=>v(l).join(" ")))]),Jt(o,c),d(a,o),he()}var Na=ne("<label><!></label>");function Ce(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["appearance"]);ve(e,!1);const l=oe(),i=Oe({fontFamily:"$labelFontFamily",fontSize:"$baseLabelSize",marginBottom:"$labelBottomMargin",color:"$inputLabelText",display:"block"});let c=m(e,"appearance",24,()=>({}));we(()=>b(c()),()=>{V(l,Re("label",i(),c()))}),Be(),me();var s=Na();Te(s,g=>({...r,style:(b(c()),L(()=>{var n,p;return(p=(n=c())==null?void 0:n.style)==null?void 0:p.label})),class:g}),[()=>(v(l),L(()=>v(l).join(" ")))]);var o=ce(s);Pe(o,e,"default",{},null),de(s),d(a,s),he()}var Va=ne("<span><!></span>");function xe(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["color","appearance"]);ve(e,!1);const l=oe(),i=Oe({fontFamily:"$bodyFontFamily",fontSize:"$baseInputSize",marginBottom:"$labelBottomMargin",display:"block",textAlign:"center",borderRadius:"0.375rem",padding:"1.5rem 1rem",lineHeight:"1rem",variants:{color:{default:{color:"$messageText",backgroundColor:"$messageBackground",border:"1px solid $messageBorder"},danger:{color:"$messageTextDanger",backgroundColor:"$messageBackgroundDanger",border:"1px solid $messageBorderDanger"}}}});let c=m(e,"color",8,"default"),s=m(e,"appearance",24,()=>({}));we(()=>(b(c()),b(s())),()=>{V(l,Re("message",i({color:c()}),s()))}),Be(),me();var o=Va();Te(o,n=>({...r,style:(b(s()),L(()=>{var p,u;return(u=(p=s())==null?void 0:p.style)==null?void 0:u.message})),class:n}),[()=>(v(l),L(()=>v(l).join(" ")))]);var g=ce(o);Pe(g,e,"default",{},null),de(o),d(a,o),he()}var Ea=ne("<div><!> <!></div> <div><!> <!></div> <!>",1),Da=ne("<!> <!>",1),Fa=ne("<!> <!>",1),Aa=ne("<!> <!> <!>",1),ja=ne('<form method="post" class="svelte-nm5p4o"><!> <!> <!></form>');function mt(a,e){ve(e,!1);let t=m(e,"authView",12,"sign_in"),r=m(e,"email",12,""),l=m(e,"password",12,""),i=m(e,"supabaseClient",8),c=m(e,"redirectTo",8,void 0),s=m(e,"additionalData",8,void 0),o=m(e,"showLinks",8,!1),g=m(e,"magicLink",8,!0),n=m(e,"i18n",8),p=m(e,"appearance",8),u=oe(""),h=oe(""),C=oe(!1),S=t()==="sign_in"?"sign_in":"sign_up";async function N(){var T;switch(V(C,!0),V(h,""),V(u,""),t()){case J.SIGN_IN:const{error:I}=await i().auth.signInWithPassword({email:r(),password:l()});I&&V(h,I.message),V(C,!1);break;case J.SIGN_UP:let $={emailRedirectTo:c()};s()&&($.data=s());const{data:{user:B,session:z},error:P}=await i().auth.signUp({email:r(),password:l(),options:$});P?V(h,P.message):B&&!z&&V(u,(T=n().sign_up)==null?void 0:T.confirmation_text);break}V(C,!1)}me();var k=ja(),E=ce(k);ye(E,{direction:"vertical",gap:"large",get appearance(){return p()},children:(T,I)=>{var $=Aa(),B=O($);ye(B,{direction:"vertical",gap:"large",get appearance(){return p()},children:(q,ie)=>{var F=Ea(),_=O(F),w=ce(_);Ce(w,{for:"email",get appearance(){return p()},children:(Z,le)=>{K();var Y=Q();X(()=>ee(Y,(b(n()),L(()=>{var te,pe;return(pe=(te=n())==null?void 0:te[S])==null?void 0:pe.email_label})))),d(Z,Y)},$$slots:{default:!0}});var f=D(w,2);{let Z=be(()=>(b(n()),L(()=>{var le,Y;return(Y=(le=n())==null?void 0:le[S])==null?void 0:Y.email_input_placeholder})));$e(f,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return v(Z)},autocomplete:"email",get appearance(){return p()},get value(){return r()},set value(le){r(le)},$$legacy:!0})}de(_);var y=D(_,2),M=ce(y);Ce(M,{for:"password",get appearance(){return p()},children:(Z,le)=>{K();var Y=Q();X(()=>ee(Y,(b(n()),L(()=>{var te,pe;return(pe=(te=n())==null?void 0:te[S])==null?void 0:pe.password_label})))),d(Z,Y)},$$slots:{default:!0}});var W=D(M,2);{let Z=be(()=>(b(n()),L(()=>{var Y,te;return(te=(Y=n())==null?void 0:Y[S])==null?void 0:te.password_input_placeholder}))),le=be(()=>(b(t()),b(J),L(()=>t()===J.SIGN_IN?"current-password":"new-password")));$e(W,{id:"password",type:"password",name:"password",get placeholder(){return v(Z)},get autocomplete(){return v(le)},get appearance(){return p()},get value(){return l()},set value(Y){l(Y)},$$legacy:!0})}de(y);var A=D(y,2);Pe(A,e,"default",{},null),d(q,F)},$$slots:{default:!0}});var z=D(B,2);Ge(z,{type:"submit",color:"primary",get loading(){return v(C)},get appearance(){return p()},children:(q,ie)=>{K();var F=Q();X(()=>ee(F,(v(C),b(n()),L(()=>{var _,w,f,y;return v(C)?(w=(_=n())==null?void 0:_[S])==null?void 0:w.loading_button_label:(y=(f=n())==null?void 0:f[S])==null?void 0:y.button_label})))),d(q,F)},$$slots:{default:!0}});var P=D(z,2);{var U=q=>{ye(q,{direction:"vertical",gap:"small",get appearance(){return p()},children:(ie,F)=>{var _=Fa(),w=O(_);{var f=A=>{ke(A,{href:"#auth-magic-link",get appearance(){return p()},$$events:{click:Z=>{Z.preventDefault(),t(J.MAGIC_LINK)}},children:(Z,le)=>{K();var Y=Q();X(()=>ee(Y,(b(n()),L(()=>{var te,pe;return(pe=(te=n())==null?void 0:te.magic_link)==null?void 0:pe.link_text})))),d(Z,Y)},$$slots:{default:!0}})};R(w,A=>{b(t()),b(J),b(g()),L(()=>t()===J.SIGN_IN&&g())&&A(f)})}var y=D(w,2);{var M=A=>{var Z=Da(),le=O(Z);ke(le,{href:"#auth-forgot-password",get appearance(){return p()},$$events:{click:te=>{te.preventDefault(),t(J.FORGOTTEN_PASSWORD)}},children:(te,pe)=>{K();var Le=Q();X(()=>ee(Le,(b(n()),L(()=>{var ze,_e;return(_e=(ze=n())==null?void 0:ze.forgotten_password)==null?void 0:_e.link_text})))),d(te,Le)},$$slots:{default:!0}});var Y=D(le,2);ke(Y,{href:"#auth-sign-up",get appearance(){return p()},$$events:{click:te=>{te.preventDefault(),t(J.SIGN_UP)}},children:(te,pe)=>{K();var Le=Q();X(()=>ee(Le,(b(n()),L(()=>{var ze,_e;return(_e=(ze=n())==null?void 0:ze.sign_up)==null?void 0:_e.link_text})))),d(te,Le)},$$slots:{default:!0}}),d(A,Z)},W=A=>{ke(A,{href:"#auth-sign-in",get appearance(){return p()},$$events:{click:Z=>{Z.preventDefault(),t(J.SIGN_IN)}},children:(Z,le)=>{K();var Y=Q();X(()=>ee(Y,(b(n()),L(()=>{var te,pe;return(pe=(te=n())==null?void 0:te.sign_in)==null?void 0:pe.link_text})))),d(Z,Y)},$$slots:{default:!0}})};R(y,A=>{b(t()),b(J),L(()=>t()===J.SIGN_IN)?A(M):A(W,!1)})}d(ie,_)},$$slots:{default:!0}})};R(P,q=>{o()&&q(U)})}d(T,$)},$$slots:{default:!0}});var G=D(E,2);{var j=T=>{xe(T,{get appearance(){return p()},children:(I,$)=>{K();var B=Q();X(()=>ee(B,v(u))),d(I,B)},$$slots:{default:!0}})};R(G,T=>{v(u)&&T(j)})}var ae=D(G,2);{var H=T=>{xe(T,{color:"danger",get appearance(){return p()},children:(I,$)=>{K();var B=Q();X(()=>ee(B,v(h))),d(I,B)},$$slots:{default:!0}})};R(ae,T=>{v(h)&&T(H)})}de(k),Me("submit",k,Ye(N)),d(a,k),he()}var Ha=ne("<div><!> <!></div> <!>",1),Oa=ne("<!> <!> <!> <!>",1),Ga=ne('<form id="auth-forgot-password" method="post" class="svelte-nm5p4o"><!></form>');function Ua(a,e){ve(e,!1);let t=m(e,"i18n",8),r=m(e,"supabaseClient",8),l=m(e,"authView",12),i=m(e,"redirectTo",8,void 0),c=m(e,"email",12,""),s=m(e,"showLinks",8,!1),o=m(e,"appearance",8),g=oe(""),n=oe(""),p=oe(!1);async function u(){var N;V(p,!0),V(n,""),V(g,"");const{error:S}=await r().auth.resetPasswordForEmail(c(),{redirectTo:i()});S?V(n,S.message):V(g,(N=t().forgotten_password)==null?void 0:N.confirmation_text),V(p,!1)}me();var h=Ga(),C=ce(h);ye(C,{direction:"vertical",gap:"large",get appearance(){return o()},children:(S,N)=>{var k=Oa(),E=O(k);ye(E,{direction:"vertical",gap:"large",get appearance(){return o()},children:($,B)=>{var z=Ha(),P=O(z),U=ce(P);Ce(U,{for:"email",get appearance(){return o()},children:(F,_)=>{K();var w=Q();X(()=>ee(w,(b(t()),L(()=>{var f,y;return(y=(f=t())==null?void 0:f.forgotten_password)==null?void 0:y.email_label})))),d(F,w)},$$slots:{default:!0}});var q=D(U,2);{let F=be(()=>(b(t()),L(()=>{var _,w;return(w=(_=t())==null?void 0:_.forgotten_password)==null?void 0:w.email_input_placeholder})));$e(q,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return v(F)},autocomplete:"email",get appearance(){return o()},get value(){return c()},set value(_){c(_)},$$legacy:!0})}de(P);var ie=D(P,2);Ge(ie,{type:"submit",color:"primary",get loading(){return v(p)},get appearance(){return o()},children:(F,_)=>{K();var w=Q();X(()=>ee(w,(v(p),b(t()),L(()=>{var f,y,M,W;return v(p)?(y=(f=t())==null?void 0:f.forgotten_password)==null?void 0:y.loading_button_label:(W=(M=t())==null?void 0:M.forgotten_password)==null?void 0:W.button_label})))),d(F,w)},$$slots:{default:!0}}),d($,z)},$$slots:{default:!0}});var G=D(E,2);{var j=$=>{ke($,{href:"#auth-magic-link",get appearance(){return o()},$$events:{click:B=>{B.preventDefault(),l(J.SIGN_IN)}},children:(B,z)=>{K();var P=Q();X(()=>ee(P,(b(t()),L(()=>{var U,q;return(q=(U=t())==null?void 0:U.sign_in)==null?void 0:q.link_text})))),d(B,P)},$$slots:{default:!0}})};R(G,$=>{s()&&$(j)})}var ae=D(G,2);{var H=$=>{xe($,{get appearance(){return o()},children:(B,z)=>{K();var P=Q();X(()=>ee(P,v(g))),d(B,P)},$$slots:{default:!0}})};R(ae,$=>{v(g)&&$(H)})}var T=D(ae,2);{var I=$=>{xe($,{color:"danger",get appearance(){return o()},children:(B,z)=>{K();var P=Q();X(()=>ee(P,v(n))),d(B,P)},$$slots:{default:!0}})};R(T,$=>{v(n)&&$(I)})}d(S,k)},$$slots:{default:!0}}),de(h),Me("submit",h,Ye(u)),d(a,h),he()}var Za=ne("<div><!> <!></div> <!>",1),Ya=ne("<!> <!> <!> <!>",1),qa=ne('<form id="auth-magic-link" method="post" class="svelte-nm5p4o"><!></form>');function Ja(a,e){ve(e,!1);let t=m(e,"i18n",8),r=m(e,"supabaseClient",8),l=m(e,"authView",12),i=m(e,"redirectTo",8,void 0),c=m(e,"appearance",8),s=m(e,"showLinks",8,!1),o=m(e,"email",12,""),g=oe(""),n=oe(""),p=oe(!1);async function u(){var N;V(p,!0),V(n,""),V(g,"");const{error:S}=await r().auth.signInWithOtp({email:o(),options:{emailRedirectTo:i()}});S?V(n,S.message):V(g,(N=t().magic_link)==null?void 0:N.confirmation_text),V(p,!1)}me();var h=qa(),C=ce(h);ye(C,{direction:"vertical",gap:"large",get appearance(){return c()},children:(S,N)=>{var k=Ya(),E=O(k);ye(E,{direction:"vertical",gap:"large",get appearance(){return c()},children:($,B)=>{var z=Za(),P=O(z),U=ce(P);Ce(U,{for:"email",get appearance(){return c()},children:(F,_)=>{K();var w=Q();X(()=>ee(w,(b(t()),L(()=>{var f,y;return(y=(f=t())==null?void 0:f.magic_link)==null?void 0:y.email_input_label})))),d(F,w)},$$slots:{default:!0}});var q=D(U,2);{let F=be(()=>(b(t()),L(()=>{var _,w;return(w=(_=t())==null?void 0:_.magic_link)==null?void 0:w.email_input_placeholder})));$e(q,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return v(F)},autocomplete:"email",get appearance(){return c()},get value(){return o()},set value(_){o(_)},$$legacy:!0})}de(P);var ie=D(P,2);Ge(ie,{type:"submit",color:"primary",get loading(){return v(p)},get appearance(){return c()},children:(F,_)=>{K();var w=Q();X(()=>ee(w,(v(p),b(t()),L(()=>{var f,y,M,W;return v(p)?(y=(f=t())==null?void 0:f.magic_link)==null?void 0:y.loading_button_label:(W=(M=t())==null?void 0:M.magic_link)==null?void 0:W.button_label})))),d(F,w)},$$slots:{default:!0}}),d($,z)},$$slots:{default:!0}});var G=D(E,2);{var j=$=>{ke($,{href:"#auth-sign-in",get appearance(){return c()},$$events:{click:B=>{B.preventDefault(),l(J.SIGN_IN)}},children:(B,z)=>{K();var P=Q();X(()=>ee(P,(b(t()),L(()=>{var U,q;return(q=(U=t())==null?void 0:U.sign_in)==null?void 0:q.link_text})))),d(B,P)},$$slots:{default:!0}})};R(G,$=>{s()&&$(j)})}var ae=D(G,2);{var H=$=>{xe($,{get appearance(){return c()},children:(B,z)=>{K();var P=Q();X(()=>ee(P,v(g))),d(B,P)},$$slots:{default:!0}})};R(ae,$=>{v(g)&&$(H)})}var T=D(ae,2);{var I=$=>{xe($,{color:"danger",get appearance(){return c()},children:(B,z)=>{K();var P=Q();X(()=>ee(P,v(n))),d(B,P)},$$slots:{default:!0}})};R(T,$=>{v(n)&&$(I)})}d(S,k)},$$slots:{default:!0}}),de(h),Me("submit",h,Ye(u)),d(a,h),he()}var Ka=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path></svg>'),Xa=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#039be5" d="M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z"></path><path fill="#fff" d="M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z"></path></svg>'),Qa=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#03A9F4" d="M42,12.429c-1.323,0.586-2.746,0.977-4.247,1.162c1.526-0.906,2.7-2.351,3.251-4.058c-1.428,0.837-3.01,1.452-4.693,1.776C34.967,9.884,33.05,9,30.926,9c-4.08,0-7.387,3.278-7.387,7.32c0,0.572,0.067,1.129,0.193,1.67c-6.138-0.308-11.582-3.226-15.224-7.654c-0.64,1.082-1,2.349-1,3.686c0,2.541,1.301,4.778,3.285,6.096c-1.211-0.037-2.351-0.374-3.349-0.914c0,0.022,0,0.055,0,0.086c0,3.551,2.547,6.508,5.923,7.181c-0.617,0.169-1.269,0.263-1.941,0.263c-0.477,0-0.942-0.054-1.392-0.135c0.94,2.902,3.667,5.023,6.898,5.086c-2.528,1.96-5.712,3.134-9.174,3.134c-0.598,0-1.183-0.034-1.761-0.104C9.268,36.786,13.152,38,17.321,38c13.585,0,21.017-11.156,21.017-20.834c0-0.317-0.01-0.633-0.025-0.945C39.763,15.197,41.013,13.905,42,12.429"></path></svg>'),er=ue('<svg fill="gray" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="21px" height="21px" class="svelte-10a6av0"> <path d="M 15.904297 1.078125 C 15.843359 1.06875 15.774219 1.0746094 15.699219 1.0996094 C 14.699219 1.2996094 13.600391 1.8996094 12.900391 2.5996094 C 12.300391 3.1996094 11.800781 4.1996094 11.800781 5.0996094 C 11.800781 5.2996094 11.999219 5.5 12.199219 5.5 C 13.299219 5.4 14.399609 4.7996094 15.099609 4.0996094 C 15.699609 3.2996094 16.199219 2.4 16.199219 1.5 C 16.199219 1.275 16.087109 1.10625 15.904297 1.078125 z M 16.199219 5.4003906 C 14.399219 5.4003906 13.600391 6.5 12.400391 6.5 C 11.100391 6.5 9.9003906 5.5 8.4003906 5.5 C 6.3003906 5.5 3.0996094 7.4996094 3.0996094 12.099609 C 2.9996094 16.299609 6.8 21 9 21 C 10.3 21 10.600391 20.199219 12.400391 20.199219 C 14.200391 20.199219 14.600391 21 15.900391 21 C 17.400391 21 18.500391 19.399609 19.400391 18.099609 C 19.800391 17.399609 20.100391 17.000391 20.400391 16.400391 C 20.600391 16.000391 20.4 15.600391 20 15.400391 C 17.4 14.100391 16.900781 9.9003906 19.800781 8.4003906 C 20.300781 8.1003906 20.4 7.4992188 20 7.1992188 C 18.9 6.1992187 17.299219 5.4003906 16.199219 5.4003906 z"></path></svg>'),tr=ue('<svg fill="gray" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="21px" height="21px" class="svelte-10a6av0"> <path d="M15,3C8.373,3,3,8.373,3,15c0,5.623,3.872,10.328,9.092,11.63C12.036,26.468,12,26.28,12,26.047v-2.051 c-0.487,0-1.303,0-1.508,0c-0.821,0-1.551-0.353-1.905-1.009c-0.393-0.729-0.461-1.844-1.435-2.526 c-0.289-0.227-0.069-0.486,0.264-0.451c0.615,0.174,1.125,0.596,1.605,1.222c0.478,0.627,0.703,0.769,1.596,0.769 c0.433,0,1.081-0.025,1.691-0.121c0.328-0.833,0.895-1.6,1.588-1.962c-3.996-0.411-5.903-2.399-5.903-5.098 c0-1.162,0.495-2.286,1.336-3.233C9.053,10.647,8.706,8.73,9.435,8c1.798,0,2.885,1.166,3.146,1.481C13.477,9.174,14.461,9,15.495,9 c1.036,0,2.024,0.174,2.922,0.483C18.675,9.17,19.763,8,21.565,8c0.732,0.731,0.381,2.656,0.102,3.594 c0.836,0.945,1.328,2.066,1.328,3.226c0,2.697-1.904,4.684-5.894,5.097C18.199,20.49,19,22.1,19,23.313v2.734 c0,0.104-0.023,0.179-0.035,0.268C23.641,24.676,27,20.236,27,15C27,8.373,21.627,3,15,3z"></path></svg>'),ar=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#e53935" d="M24 43L16 20 32 20z"></path><path fill="#ff7043" d="M24 43L42 20 32 20z"></path><path fill="#e53935" d="M37 5L42 20 32 20z"></path><path fill="#ffa726" d="M24 43L42 20 45 28z"></path><path fill="#ff7043" d="M24 43L6 20 16 20z"></path><path fill="#e53935" d="M11 5L6 20 16 20z"></path><path fill="#ffa726" d="M24 43L6 20 3 28z"></path></svg>'),rr=ue('<svg xmlns="http://www.w3.org/2000/svg" width="21px" height="21px" viewBox="0 0 62.42 62.42" class="svelte-10a6av0"><defs><linearGradient id="New_Gradient_Swatch_1" x1="64.01" y1="30.27" x2="32.99" y2="54.48" gradientUnits="userSpaceOnUse"><stop offset="0.18" stop-color="#0052cc"></stop><stop offset="1" stop-color="#2684ff"></stop></linearGradient></defs><title>Bitbucket-blue</title><g id="Layer_2" data-name="Layer 2"><g id="Blue" transform="translate(0 -3.13)"><path d="M2,6.26A2,2,0,0,0,0,8.58L8.49,60.12a2.72,2.72,0,0,0,2.66,2.27H51.88a2,2,0,0,0,2-1.68L62.37,8.59a2,2,0,0,0-2-2.32ZM37.75,43.51h-13L21.23,25.12H40.9Z" fill="#2684ff"></path><path d="M59.67,25.12H40.9L37.75,43.51h-13L9.4,61.73a2.71,2.71,0,0,0,1.75.66H51.89a2,2,0,0,0,2-1.68Z" fill="url(#New_Gradient_Swatch_1)"></path></g></g></svg>'),nr=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#536dfe" d="M39.248,10.177c-2.804-1.287-5.812-2.235-8.956-2.778c-0.057-0.01-0.114,0.016-0.144,0.068	c-0.387,0.688-0.815,1.585-1.115,2.291c-3.382-0.506-6.747-0.506-10.059,0c-0.3-0.721-0.744-1.603-1.133-2.291	c-0.03-0.051-0.087-0.077-0.144-0.068c-3.143,0.541-6.15,1.489-8.956,2.778c-0.024,0.01-0.045,0.028-0.059,0.051	c-5.704,8.522-7.267,16.835-6.5,25.044c0.003,0.04,0.026,0.079,0.057,0.103c3.763,2.764,7.409,4.442,10.987,5.554	c0.057,0.017,0.118-0.003,0.154-0.051c0.846-1.156,1.601-2.374,2.248-3.656c0.038-0.075,0.002-0.164-0.076-0.194	c-1.197-0.454-2.336-1.007-3.432-1.636c-0.087-0.051-0.094-0.175-0.014-0.234c0.231-0.173,0.461-0.353,0.682-0.534	c0.04-0.033,0.095-0.04,0.142-0.019c7.201,3.288,14.997,3.288,22.113,0c0.047-0.023,0.102-0.016,0.144,0.017	c0.22,0.182,0.451,0.363,0.683,0.536c0.08,0.059,0.075,0.183-0.012,0.234c-1.096,0.641-2.236,1.182-3.434,1.634	c-0.078,0.03-0.113,0.12-0.075,0.196c0.661,1.28,1.415,2.498,2.246,3.654c0.035,0.049,0.097,0.07,0.154,0.052	c3.595-1.112,7.241-2.79,11.004-5.554c0.033-0.024,0.054-0.061,0.057-0.101c0.917-9.491-1.537-17.735-6.505-25.044	C39.293,10.205,39.272,10.187,39.248,10.177z M16.703,30.273c-2.168,0-3.954-1.99-3.954-4.435s1.752-4.435,3.954-4.435	c2.22,0,3.989,2.008,3.954,4.435C20.658,28.282,18.906,30.273,16.703,30.273z M31.324,30.273c-2.168,0-3.954-1.99-3.954-4.435	s1.752-4.435,3.954-4.435c2.22,0,3.989,2.008,3.954,4.435C35.278,28.282,33.544,30.273,31.324,30.273z"></path></svg>'),or=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><linearGradient id="k8yl7~hDat~FaoWq8WjN6a" x1="-1254.397" x2="-1261.911" y1="877.268" y2="899.466" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#114a8b"></stop><stop offset="1" stop-color="#0669bc"></stop></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6a)" d="M17.634,6h11.305L17.203,40.773c-0.247,0.733-0.934,1.226-1.708,1.226H6.697 c-0.994,0-1.8-0.806-1.8-1.8c0-0.196,0.032-0.39,0.094-0.576L15.926,7.227C16.173,6.494,16.86,6,17.634,6L17.634,6z"></path><path fill="#0078d4" d="M34.062,29.324H16.135c-0.458-0.001-0.83,0.371-0.831,0.829c0,0.231,0.095,0.451,0.264,0.608 l11.52,10.752C27.423,41.826,27.865,42,28.324,42h10.151L34.062,29.324z"></path><linearGradient id="k8yl7~hDat~FaoWq8WjN6b" x1="-1252.05" x2="-1253.788" y1="887.612" y2="888.2" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-opacity=".3"></stop><stop offset=".071" stop-opacity=".2"></stop><stop offset=".321" stop-opacity=".1"></stop><stop offset=".623" stop-opacity=".05"></stop><stop offset="1" stop-opacity="0"></stop></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6b)" d="M17.634,6c-0.783-0.003-1.476,0.504-1.712,1.25L5.005,39.595 c-0.335,0.934,0.151,1.964,1.085,2.299C6.286,41.964,6.493,42,6.702,42h9.026c0.684-0.122,1.25-0.603,1.481-1.259l2.177-6.416 l7.776,7.253c0.326,0.27,0.735,0.419,1.158,0.422h10.114l-4.436-12.676l-12.931,0.003L28.98,6H17.634z"></path><linearGradient id="k8yl7~hDat~FaoWq8WjN6c" x1="-1252.952" x2="-1244.704" y1="876.6" y2="898.575" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#3ccbf4"></stop><stop offset="1" stop-color="#2892df"></stop></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6c)" d="M32.074,7.225C31.827,6.493,31.141,6,30.368,6h-12.6c0.772,0,1.459,0.493,1.705,1.224 l10.935,32.399c0.318,0.942-0.188,1.963-1.13,2.281C29.093,41.968,28.899,42,28.703,42h12.6c0.994,0,1.8-0.806,1.8-1.801 c0-0.196-0.032-0.39-0.095-0.575L32.074,7.225z"></path></svg>'),lr=ue('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M472.136 163.959H408.584C407.401 163.959 406.218 163.327 405.666 162.3L354.651 73.6591C354.02 72.632 352.916 72 351.654 72H143.492C142.309 72 141.126 72.632 140.574 73.6591L87.5084 165.618L36.414 254.259C35.862 255.286 35.862 256.55 36.414 257.656L87.5084 346.297L140.495 438.335C141.047 439.362 142.23 440.073 143.413 439.994H351.654C352.837 439.994 354.02 439.362 354.651 438.335L405.745 349.694C406.297 348.667 407.48 347.956 408.663 348.035H472.215C474.344 348.035 476 346.297 476 344.243V167.83C475.921 165.697 474.186 163.959 472.136 163.959ZM228.728 349.694L212.721 377.345C212.485 377.74 212.091 378.135 211.696 378.372C211.223 378.609 210.75 378.767 210.198 378.767H178.422C177.318 378.767 176.293 378.214 175.82 377.187L128.431 294.787L123.779 286.65L106.748 257.498C106.511 257.103 106.353 256.629 106.432 256.076C106.432 255.602 106.59 255.049 106.827 254.654L123.937 224.949L175.899 134.886C176.451 133.938 177.476 133.306 178.501 133.306H210.198C210.75 133.306 211.302 133.464 211.854 133.701C212.248 133.938 212.643 134.254 212.879 134.728L228.886 162.537C229.359 163.485 229.28 164.67 228.728 165.539L177.397 254.654C177.16 255.049 177.081 255.523 177.081 255.918C177.081 256.392 177.239 256.787 177.397 257.182L228.728 346.218C229.438 347.403 229.359 348.667 228.728 349.694V349.694ZM388.083 257.498L371.051 286.65L366.399 294.787L319.011 377.187C318.459 378.135 317.512 378.767 316.409 378.767H284.632C284.08 378.767 283.607 378.609 283.134 378.372C282.74 378.135 282.346 377.819 282.109 377.345L266.103 349.694C265.393 348.667 265.393 347.403 266.024 346.376L317.355 257.34C317.591 256.945 317.67 256.471 317.67 256.076C317.67 255.602 317.513 255.207 317.355 254.812L266.024 165.697C265.472 164.749 265.393 163.643 265.866 162.695L281.873 134.886C282.109 134.491 282.503 134.096 282.898 133.859C283.371 133.543 283.923 133.464 284.553 133.464H316.409C317.512 133.464 318.538 134.017 319.011 135.044L370.972 225.107L388.083 254.812C388.319 255.286 388.477 255.76 388.477 256.234C388.477 256.55 388.319 257.024 388.083 257.498V257.498Z" fill="#008AAA"></path></svg>'),ir=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#0288D1" d="M42,37c0,2.762-2.238,5-5,5H11c-2.761,0-5-2.238-5-5V11c0-2.762,2.239-5,5-5h26c2.762,0,5,2.238,5,5V37z"></path><path fill="#FFF" d="M12 19H17V36H12zM14.485 17h-.028C12.965 17 12 15.888 12 14.499 12 13.08 12.995 12 14.514 12c1.521 0 2.458 1.08 2.486 2.499C17 15.887 16.035 17 14.485 17zM36 36h-5v-9.099c0-2.198-1.225-3.698-3.192-3.698-1.501 0-2.313 1.012-2.707 1.99C24.957 25.543 25 26.511 25 27v9h-5V19h5v2.616C25.721 20.5 26.85 19 29.738 19c3.578 0 6.261 2.25 6.261 7.274L36 36 36 36z"></path></svg>'),sr=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" fill-rule="evenodd" clip-rule="evenodd" class="svelte-10a6av0"><path fill="#fff" fill-rule="evenodd" d="M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z" clip-rule="evenodd"></path><path fill="#fff" fill-rule="evenodd" d="M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619 l23.971-1.387c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463 C13.171,14.718,12.862,15.181,12.862,16.182L12.862,16.182z" clip-rule="evenodd"></path><path fill="#424242" fill-rule="evenodd" d="M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619l23.971-1.387 c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463C13.171,14.718,12.862,15.181,12.862,16.182 L12.862,16.182z M36.526,17.413c0.154,0.694,0,1.387-0.695,1.465l-1.155,0.23v16.943c-1.003,0.539-1.928,0.847-2.698,0.847 c-1.234,0-1.543-0.385-2.467-1.54l-7.555-11.86v11.475l2.391,0.539c0,0,0,1.386-1.929,1.386l-5.317,0.308 c-0.154-0.308,0-1.078,0.539-1.232l1.388-0.385V20.418l-1.927-0.154c-0.155-0.694,0.23-1.694,1.31-1.772l5.704-0.385l7.862,12.015 V19.493l-2.005-0.23c-0.154-0.848,0.462-1.464,1.233-1.54L36.526,17.413z M7.389,5.862l21.968-1.618 c2.698-0.231,3.392-0.076,5.087,1.155l7.013,4.929C42.614,11.176,43,11.407,43,12.33v27.032c0,1.694-0.617,2.696-2.775,2.849 l-25.512,1.541c-1.62,0.077-2.391-0.154-3.239-1.232l-5.164-6.7C5.385,34.587,5,33.664,5,32.585V8.556 C5,7.171,5.617,6.015,7.389,5.862z" clip-rule="evenodd"></path></svg>'),cr=ue('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#33d375" d="M33,8c0-2.209-1.791-4-4-4s-4,1.791-4,4c0,1.254,0,9.741,0,11c0,2.209,1.791,4,4,4s4-1.791,4-4	C33,17.741,33,9.254,33,8z"></path><path fill="#33d375" d="M43,19c0,2.209-1.791,4-4,4c-1.195,0-4,0-4,0s0-2.986,0-4c0-2.209,1.791-4,4-4S43,16.791,43,19z"></path><path fill="#40c4ff" d="M8,14c-2.209,0-4,1.791-4,4s1.791,4,4,4c1.254,0,9.741,0,11,0c2.209,0,4-1.791,4-4s-1.791-4-4-4	C17.741,14,9.254,14,8,14z"></path><path fill="#40c4ff" d="M19,4c2.209,0,4,1.791,4,4c0,1.195,0,4,0,4s-2.986,0-4,0c-2.209,0-4-1.791-4-4S16.791,4,19,4z"></path><path fill="#e91e63" d="M14,39.006C14,41.212,15.791,43,18,43s4-1.788,4-3.994c0-1.252,0-9.727,0-10.984	c0-2.206-1.791-3.994-4-3.994s-4,1.788-4,3.994C14,29.279,14,37.754,14,39.006z"></path><path fill="#e91e63" d="M4,28.022c0-2.206,1.791-3.994,4-3.994c1.195,0,4,0,4,0s0,2.981,0,3.994c0,2.206-1.791,3.994-4,3.994	S4,30.228,4,28.022z"></path><path fill="#ffc107" d="M39,33c2.209,0,4-1.791,4-4s-1.791-4-4-4c-1.254,0-9.741,0-11,0c-2.209,0-4,1.791-4,4s1.791,4,4,4	C29.258,33,37.746,33,39,33z"></path><path fill="#ffc107" d="M28,43c-2.209,0-4-1.791-4-4c0-1.195,0-4,0-4s2.986,0,4,0c2.209,0,4,1.791,4,4S30.209,43,28,43z"></path></svg>'),dr=ue('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M255.498 31.0034C131.513 31.0034 31 131.515 31 255.502C31 379.492 131.513 480 255.498 480C379.497 480 480 379.495 480 255.502C480 131.522 379.497 31.0135 255.495 31.0135L255.498 31V31.0034ZM358.453 354.798C354.432 361.391 345.801 363.486 339.204 359.435C286.496 327.237 220.139 319.947 141.993 337.801C134.463 339.516 126.957 334.798 125.24 327.264C123.516 319.731 128.217 312.225 135.767 310.511C221.284 290.972 294.639 299.384 353.816 335.549C360.413 339.596 362.504 348.2 358.453 354.798ZM385.932 293.67C380.864 301.903 370.088 304.503 361.858 299.438C301.512 262.345 209.528 251.602 138.151 273.272C128.893 276.067 119.118 270.851 116.309 261.61C113.521 252.353 118.74 242.597 127.981 239.782C209.512 215.044 310.87 227.026 380.17 269.612C388.4 274.68 391 285.456 385.935 293.676V293.673L385.932 293.67ZM388.293 230.016C315.935 187.039 196.56 183.089 127.479 204.055C116.387 207.42 104.654 201.159 101.293 190.063C97.9326 178.964 104.189 167.241 115.289 163.87C194.59 139.796 326.418 144.446 409.723 193.902C419.722 199.826 422.995 212.71 417.068 222.675C411.168 232.653 398.247 235.943 388.303 230.016H388.293V230.016Z" fill="#1ED760"></path></svg>'),ur=ue('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M416 240L352 304H288L232 360V304H160V64H416V240Z" fill="white"></path><path d="M144 32L64 112V400H160V480L240 400H304L448 256V32H144ZM416 240L352 304H288L232 360V304H160V64H416V240Z" fill="#9146FF"></path><path d="M368 120H336V216H368V120Z" fill="#9146FF"></path><path d="M280 120H248V216H280V120Z" fill="#9146FF"></path></svg>'),pr=ue('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M33 256.043C33 264.556 35.3159 273.069 39.4845 280.202L117.993 415.493C126.098 429.298 138.373 440.572 153.657 445.634C183.764 455.528 214.797 442.873 229.618 417.333L248.609 384.661L173.806 256.043L252.777 119.831L271.768 87.1591C277.557 77.2654 284.968 69.4424 294 63H285.894H172.185C150.878 63 131.193 74.2742 120.54 92.6812L39.7161 231.884C35.3159 239.016 33 247.53 33 256.043Z" fill="#6363F1"></path><path d="M480 256.058C480 247.539 477.684 239.021 473.516 231.883L393.849 94.6596C379.028 69.3331 347.995 56.4396 317.888 66.34C302.603 71.4053 290.329 82.6871 282.224 96.5015L264.391 127.354L339.194 256.058L260.223 392.131L241.232 424.825C235.443 434.495 228.032 442.553 219 449H227.106H340.815C362.122 449 381.807 437.718 392.46 419.299L473.284 280.003C477.684 272.866 480 264.577 480 256.058Z" fill="#6363F1"></path></svg>'),gr=ue('<svg xmlns="http://www.w3.org/2000/svg" width="21px" height="21px" viewBox="0 0 256 256" class="svelte-10a6av0"><path fill="#FFE812" d="M256 236c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20V20C0 8.954 8.954 0 20 0h216c11.046 0 20 8.954 20 20v216z"></path><path d="M128 36C70.562 36 24 72.713 24 118c0 29.279 19.466 54.97 48.748 69.477-1.593 5.494-10.237 35.344-10.581 37.689 0 0-.207 1.762.934 2.434s2.483.15 2.483.15c3.272-.457 37.943-24.811 43.944-29.04 5.995.849 12.168 1.29 18.472 1.29 57.438 0 104-36.712 104-82 0-45.287-46.562-82-104-82z"></path><path fill="#FFE812" d="M70.5 146.625c-3.309 0-6-2.57-6-5.73V105.25h-9.362c-3.247 0-5.888-2.636-5.888-5.875s2.642-5.875 5.888-5.875h30.724c3.247 0 5.888 2.636 5.888 5.875s-2.642 5.875-5.888 5.875H76.5v35.645c0 3.16-2.691 5.73-6 5.73zM123.112 146.547c-2.502 0-4.416-1.016-4.993-2.65l-2.971-7.778-18.296-.001-2.973 7.783c-.575 1.631-2.488 2.646-4.99 2.646a9.155 9.155 0 0 1-3.814-.828c-1.654-.763-3.244-2.861-1.422-8.52l14.352-37.776c1.011-2.873 4.082-5.833 7.99-5.922 3.919.088 6.99 3.049 8.003 5.928l14.346 37.759c1.826 5.672.236 7.771-1.418 8.532a9.176 9.176 0 0 1-3.814.827c-.001 0 0 0 0 0zm-11.119-21.056L106 108.466l-5.993 17.025h11.986zM138 145.75c-3.171 0-5.75-2.468-5.75-5.5V99.5c0-3.309 2.748-6 6.125-6s6.125 2.691 6.125 6v35.25h12.75c3.171 0 5.75 2.468 5.75 5.5s-2.579 5.5-5.75 5.5H138zM171.334 146.547c-3.309 0-6-2.691-6-6V99.5c0-3.309 2.691-6 6-6s6 2.691 6 6v12.896l16.74-16.74c.861-.861 2.044-1.335 3.328-1.335 1.498 0 3.002.646 4.129 1.772 1.051 1.05 1.678 2.401 1.764 3.804.087 1.415-.384 2.712-1.324 3.653l-13.673 13.671 14.769 19.566a5.951 5.951 0 0 1 1.152 4.445 5.956 5.956 0 0 1-2.328 3.957 5.94 5.94 0 0 1-3.609 1.211 5.953 5.953 0 0 1-4.793-2.385l-14.071-18.644-2.082 2.082v13.091a6.01 6.01 0 0 1-6.002 6.003z"></path></svg>');function fr(a,e){let t=m(e,"provider",8);var r=se(),l=O(r);{var i=s=>{var o=Ka();d(s,o)},c=s=>{var o=se(),g=O(o);{var n=u=>{var h=Xa();d(u,h)},p=u=>{var h=se(),C=O(h);{var S=k=>{var E=Qa();d(k,E)},N=k=>{var E=se(),G=O(E);{var j=H=>{var T=er(),I=ce(T,!0);I.nodeValue=" ",K(),de(T),d(H,T)},ae=H=>{var T=se(),I=O(T);{var $=z=>{var P=tr(),U=ce(P,!0);U.nodeValue=" ",K(),de(P),d(z,P)},B=z=>{var P=se(),U=O(P);{var q=F=>{var _=ar();d(F,_)},ie=F=>{var _=se(),w=O(_);{var f=M=>{var W=rr();d(M,W)},y=M=>{var W=se(),A=O(W);{var Z=Y=>{var te=nr();d(Y,te)},le=Y=>{var te=se(),pe=O(te);{var Le=_e=>{var Je=or();d(_e,Je)},ze=_e=>{var Je=se(),xt=O(Je);{var St=We=>{var Ke=lr();d(We,Ke)},kt=We=>{var Ke=se(),$t=O(Ke);{var Ct=Ne=>{var Xe=ir();d(Ne,Xe)},Bt=Ne=>{var Xe=se(),Pt=O(Xe);{var Lt=Ve=>{var Qe=sr();d(Ve,Qe)},zt=Ve=>{var Qe=se(),It=O(Qe);{var Mt=Ee=>{var et=cr();d(Ee,et)},Tt=Ee=>{var et=se(),Rt=O(et);{var Wt=De=>{var tt=dr();d(De,tt)},Nt=De=>{var tt=se(),Vt=O(tt);{var Et=Fe=>{var at=ur();d(Fe,at)},Dt=Fe=>{var at=se(),Ft=O(at);{var At=Ae=>{var rt=pr();d(Ae,rt)},jt=Ae=>{var rt=se(),Ht=O(rt);{var Ot=ot=>{var Gt=gr();d(ot,Gt)};R(Ht,ot=>{t()==="kakao"&&ot(Ot)},!0)}d(Ae,rt)};R(Ft,Ae=>{t()==="workos"?Ae(At):Ae(jt,!1)},!0)}d(Fe,at)};R(Vt,Fe=>{t()==="twitch"?Fe(Et):Fe(Dt,!1)},!0)}d(De,tt)};R(Rt,De=>{t()==="spotify"?De(Wt):De(Nt,!1)},!0)}d(Ee,et)};R(It,Ee=>{t()==="slack"?Ee(Mt):Ee(Tt,!1)},!0)}d(Ve,Qe)};R(Pt,Ve=>{t()==="notion"?Ve(Lt):Ve(zt,!1)},!0)}d(Ne,Xe)};R($t,Ne=>{t()==="linkedin"?Ne(Ct):Ne(Bt,!1)},!0)}d(We,Ke)};R(xt,We=>{t()==="keycloak"?We(St):We(kt,!1)},!0)}d(_e,Je)};R(pe,_e=>{t()==="azure"?_e(Le):_e(ze,!1)},!0)}d(Y,te)};R(A,Y=>{t()==="discord"?Y(Z):Y(le,!1)},!0)}d(M,W)};R(w,M=>{t()==="bitbucket"?M(f):M(y,!1)},!0)}d(F,_)};R(U,F=>{t()==="gitlab"?F(q):F(ie,!1)},!0)}d(z,P)};R(I,z=>{t()==="github"?z($):z(B,!1)},!0)}d(H,T)};R(G,H=>{t()==="apple"?H(j):H(ae,!1)},!0)}d(k,E)};R(C,k=>{t()==="twitter"?k(S):k(N,!1)},!0)}d(u,h)};R(g,u=>{t()==="facebook"?u(n):u(p,!1)},!0)}d(s,o)};R(l,s=>{t()==="google"?s(i):s(c,!1)})}d(a,r)}var vr=ne("<div><!></div>");function hr(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["appearance"]);ve(e,!1);const l=Oe({background:"$dividerBackground",display:"block",margin:"16px 0",height:"1px",width:"100%"});let i=m(e,"appearance",24,()=>({}));const c=Re("divider",l(),i());me();var s=vr();Te(s,g=>({...r,style:(b(i()),L(()=>{var n,p;return(p=(n=i())==null?void 0:n.style)==null?void 0:p.divider})),class:g}),[()=>L(()=>c.join(" "))]);var o=ce(s);Pe(o,e,"default",{},null),de(s),d(a,s),he()}var mr=ne("<!> <!>",1),_r=ne("<!> <!>",1);function br(a,e){ve(e,!1);const t=oe();let r=m(e,"supabaseClient",8),l=m(e,"socialLayout",8),i=m(e,"redirectTo",8,void 0),c=m(e,"onlyThirdPartyProviders",8),s=m(e,"i18n",8),o=m(e,"providers",24,()=>[]),g=m(e,"providerScopes",8),n=m(e,"queryParams",8),p=m(e,"appearance",8),u=oe(!1);async function h(G){var ae;V(u,!0);const{error:j}=await r().auth.signInWithOAuth({provider:G,options:{redirectTo:i(),scopes:(ae=g())==null?void 0:ae[G],queryParams:n()}});j&&j.message,V(u,!1)}function C(G){return G[0].toUpperCase()+G.slice(1).toLowerCase()}let S=G=>{var j;return za((j=s().sign_in)==null?void 0:j.social_provider_text,{provider:C(G)})};we(()=>b(l()),()=>{V(t,l()==="vertical")}),Be(),me();var N=se(),k=O(N);{var E=G=>{var j=_r(),ae=O(j);ye(ae,{direction:"vertical",gap:"large",get appearance(){return p()},children:(I,$)=>{{let B=be(()=>v(t)?"vertical":"horizontal"),z=be(()=>v(t)?"small":"medium");ye(I,{get direction(){return v(B)},get gap(){return v(z)},get appearance(){return p()},children:(P,U)=>{var q=se(),ie=O(q);Kt(ie,1,o,Xt,(F,_)=>{{let w=be(()=>(v(_),L(()=>S(v(_)))));Ge(F,{get"aria-label"(){return v(w)},type:"submit",color:"default",get loading(){return v(u)},get appearance(){return p()},$$events:{click:()=>h(v(_))},children:(f,y)=>{var M=mr(),W=O(M);fr(W,{get provider(){return v(_)}});var A=D(W,2);{var Z=le=>{var Y=Q();X(te=>ee(Y,te),[()=>(v(_),L(()=>S(v(_))))]),d(le,Y)};R(A,le=>{v(t)&&le(Z)})}d(f,M)},$$slots:{default:!0}})}}),d(P,q)},$$slots:{default:!0}})}},$$slots:{default:!0}});var H=D(ae,2);{var T=I=>{hr(I,{get appearance(){return p()}})};R(H,I=>{c()||I(T)})}d(G,j)};R(k,G=>{b(o()),L(()=>o().length)&&G(E)})}d(a,N),he()}var yr=ne("<div><!> <!></div> <!>",1),wr=ne("<!> <!> <!> <!>",1),xr=ne('<form id="auth-update-password" method="post" class="svelte-nm5p4o"><!></form>');function Sr(a,e){ve(e,!1);let t=m(e,"i18n",8),r=m(e,"supabaseClient",8),l=m(e,"authView",12),i=m(e,"appearance",8),c=m(e,"showLinks",8,!1),s=oe(""),o=oe(""),g=oe(""),n=oe(!1);async function p(){var N;V(n,!0),V(g,""),V(o,"");const{data:C,error:S}=await r().auth.updateUser({password:v(s)});S?V(g,S.message):V(o,(N=t().update_password)==null?void 0:N.confirmation_text),V(n,!1)}me();var u=xr(),h=ce(u);ye(h,{direction:"vertical",gap:"large",get appearance(){return i()},children:(C,S)=>{var N=wr(),k=O(N);ye(k,{direction:"vertical",gap:"large",get appearance(){return i()},children:(I,$)=>{var B=yr(),z=O(B),P=ce(z);Ce(P,{for:"password",get appearance(){return i()},children:(ie,F)=>{K();var _=Q();X(()=>ee(_,(b(t()),L(()=>{var w,f;return(f=(w=t())==null?void 0:w.update_password)==null?void 0:f.password_label})))),d(ie,_)},$$slots:{default:!0}});var U=D(P,2);{let ie=be(()=>(b(t()),L(()=>{var F,_;return(_=(F=t())==null?void 0:F.update_password)==null?void 0:_.password_input_placeholder})));$e(U,{id:"password",type:"password",name:"password",autofocus:!0,get placeholder(){return v(ie)},autocomplete:"password",get appearance(){return i()},get value(){return v(s)},set value(F){V(s,F)},$$legacy:!0})}de(z);var q=D(z,2);Ge(q,{type:"submit",color:"primary",get loading(){return v(n)},get appearance(){return i()},children:(ie,F)=>{K();var _=Q();X(()=>ee(_,(v(n),b(t()),L(()=>{var w,f,y,M;return v(n)?(f=(w=t())==null?void 0:w.update_password)==null?void 0:f.loading_button_label:(M=(y=t())==null?void 0:y.update_password)==null?void 0:M.button_label})))),d(ie,_)},$$slots:{default:!0}}),d(I,B)},$$slots:{default:!0}});var E=D(k,2);{var G=I=>{ke(I,{href:"#auth-magic-link",get appearance(){return i()},$$events:{click:$=>{$.preventDefault(),l(J.SIGN_IN)}},children:($,B)=>{K();var z=Q();X(()=>ee(z,(b(t()),L(()=>{var P,U;return(U=(P=t())==null?void 0:P.sign_in)==null?void 0:U.link_text})))),d($,z)},$$slots:{default:!0}})};R(E,I=>{c()&&I(G)})}var j=D(E,2);{var ae=I=>{xe(I,{get appearance(){return i()},children:($,B)=>{K();var z=Q();X(()=>ee(z,v(o))),d($,z)},$$slots:{default:!0}})};R(j,I=>{v(o)&&I(ae)})}var H=D(j,2);{var T=I=>{xe(I,{color:"danger",get appearance(){return i()},children:($,B)=>{K();var z=Q();X(()=>ee(z,v(g))),d($,z)},$$slots:{default:!0}})};R(H,I=>{v(g)&&I(T)})}d(C,N)},$$slots:{default:!0}}),de(u),Me("submit",u,Ye(p)),d(a,u),he()}var kr=ne("<div><!> <!></div>"),$r=ne("<div><!> <!></div>"),Cr=ne("<!> <div><!> <!></div> <!> <!> <!> <!>",1),Br=ne('<form id="auth-magic-link" method="post" class="svelte-nm5p4o"><!></form>');function Pr(a,e){ve(e,!1);let t=m(e,"i18n",8),r=m(e,"supabaseClient",8),l=m(e,"authView",12),i=m(e,"otpType",8,"email"),c=m(e,"appearance",8),s=m(e,"showLinks",8,!1),o=m(e,"email",12,""),g=m(e,"phone",12,""),n=m(e,"token",12,""),p=oe(""),u=oe(""),h=oe(!1);async function C(){V(h,!0),V(u,""),V(p,"");let k={email:o(),token:n(),type:i()};["sms","phone_change"].includes(i())&&(k={phone:g(),token:n(),type:i()});const{error:E}=await r().auth.verifyOtp(k);E&&V(u,E.message),V(h,!1)}me();var S=Br(),N=ce(S);ye(N,{direction:"vertical",gap:"large",get appearance(){return c()},children:(k,E)=>{var G=Cr(),j=O(G);{var ae=_=>{var w=kr(),f=ce(w);Ce(f,{for:"phone",get appearance(){return c()},children:(M,W)=>{K();var A=Q();X(()=>ee(A,(b(t()),L(()=>{var Z,le;return(le=(Z=t())==null?void 0:Z.verify_otp)==null?void 0:le.phone_input_label})))),d(M,A)},$$slots:{default:!0}});var y=D(f,2);{let M=be(()=>(b(t()),L(()=>{var W,A;return(A=(W=t())==null?void 0:W.verify_otp)==null?void 0:A.phone_input_placeholder})));$e(y,{id:"phone",type:"text",name:"phone",autofocus:!0,get placeholder(){return v(M)},autocomplete:"phone",get appearance(){return c()},get value(){return g()},set value(W){g(W)},$$legacy:!0})}de(w),d(_,w)},H=_=>{var w=$r(),f=ce(w);Ce(f,{for:"email",get appearance(){return c()},children:(M,W)=>{K();var A=Q();X(()=>ee(A,(b(t()),L(()=>{var Z,le;return(le=(Z=t())==null?void 0:Z.verify_otp)==null?void 0:le.email_input_label})))),d(M,A)},$$slots:{default:!0}});var y=D(f,2);{let M=be(()=>(b(t()),L(()=>{var W,A;return(A=(W=t())==null?void 0:W.verify_otp)==null?void 0:A.email_input_placeholder})));$e(y,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return v(M)},autocomplete:"email",get appearance(){return c()},get value(){return o()},set value(W){o(W)},$$legacy:!0})}de(w),d(_,w)};R(j,_=>{b(i()),L(()=>["sms","phone_change"].includes(i()))?_(ae):_(H,!1)})}var T=D(j,2),I=ce(T);Ce(I,{for:"token",get appearance(){return c()},children:(_,w)=>{K();var f=Q();X(()=>ee(f,(b(t()),L(()=>{var y,M;return(M=(y=t())==null?void 0:y.verify_otp)==null?void 0:M.token_input_label})))),d(_,f)},$$slots:{default:!0}});var $=D(I,2);{let _=be(()=>(b(t()),L(()=>{var w,f;return(f=(w=t())==null?void 0:w.verify_otp)==null?void 0:f.token_input_placeholder})));$e($,{id:"token",type:"text",name:"token",get placeholder(){return v(_)},autocomplete:"token",get appearance(){return c()},get value(){return n()},set value(w){n(w)},$$legacy:!0})}de(T);var B=D(T,2);Ge(B,{type:"submit",color:"primary",get loading(){return v(h)},get appearance(){return c()},children:(_,w)=>{K();var f=Q();X(()=>ee(f,(v(h),b(t()),L(()=>{var y,M,W,A;return v(h)?(M=(y=t())==null?void 0:y.verify_otp)==null?void 0:M.loading_button_label:(A=(W=t())==null?void 0:W.verify_otp)==null?void 0:A.button_label})))),d(_,f)},$$slots:{default:!0}});var z=D(B,2);{var P=_=>{ke(_,{href:"#auth-sign-in",get appearance(){return c()},$$events:{click:w=>{w.preventDefault(),l(J.SIGN_IN)}},children:(w,f)=>{K();var y=Q();X(()=>ee(y,(b(t()),L(()=>{var M,W;return(W=(M=t())==null?void 0:M.sign_in)==null?void 0:W.link_text})))),d(w,y)},$$slots:{default:!0}})};R(z,_=>{s()&&_(P)})}var U=D(z,2);{var q=_=>{xe(_,{get appearance(){return c()},children:(w,f)=>{K();var y=Q();X(()=>ee(y,v(p))),d(w,y)},$$slots:{default:!0}})};R(U,_=>{v(p)&&_(q)})}var ie=D(U,2);{var F=_=>{xe(_,{color:"danger",get appearance(){return c()},children:(w,f)=>{K();var y=Q();X(()=>ee(y,v(u))),d(w,y)},$$slots:{default:!0}})};R(ie,_=>{v(u)&&_(F)})}d(k,G)},$$slots:{default:!0}}),de(S),Me("submit",S,Ye(C)),d(a,S),he()}var Lr=ne("<div><!> <!> <!> <!> <!> <!> <!></div>");function Or(a,e){ve(e,!1);const t=oe(),r=oe(),l=oe();let i=m(e,"supabaseClient",8),c=m(e,"socialLayout",8,"vertical"),s=m(e,"providers",24,()=>[]),o=m(e,"providerScopes",8,void 0),g=m(e,"queryParams",8,void 0),n=m(e,"view",12,"sign_in"),p=m(e,"redirectTo",8,void 0),u=m(e,"onlyThirdPartyProviders",8,!1),h=m(e,"magicLink",8,!1),C=m(e,"showLinks",8,!0),S=m(e,"appearance",24,()=>({})),N=m(e,"theme",8,"default"),k=m(e,"localization",24,()=>({})),E=m(e,"otpType",8,"email"),G=m(e,"additionalData",8,void 0);Ut(()=>{const{data:f}=i().auth.onAuthStateChange(y=>{y==="PASSWORD_RECOVERY"?n("update_password"):y==="USER_UPDATED"&&n("sign_in")})}),we(()=>b(k()),()=>{V(t,it(Ia,k().variables??{}))}),we(()=>b(S()),()=>{var f,y,M,W;yt({theme:it(((y=(f=S())==null?void 0:f.theme)==null?void 0:y.default)??{},((W=(M=S())==null?void 0:M.variables)==null?void 0:W.default)??{})})}),we(()=>(b(S()),b(N())),()=>{var f,y,M,W;V(r,Ca(it((y=(f=S())==null?void 0:f.theme)==null?void 0:y[N()],((W=(M=S())==null?void 0:M.variables)==null?void 0:W[N()])??{})))}),we(()=>b(n()),()=>{V(l,n()==="sign_in"||n()==="sign_up"||n()==="magic_link")}),Be(),me();var j=Lr(),ae=ce(j);{var H=f=>{br(f,{get appearance(){return S()},get supabaseClient(){return i()},get providers(){return s()},get providerScopes(){return o()},get queryParams(){return g()},get socialLayout(){return c()},get redirectTo(){return p()},get onlyThirdPartyProviders(){return u()},get i18n(){return v(t)}})};R(ae,f=>{v(l)&&f(H)})}var T=D(ae,2);{var I=f=>{var y=se(),M=O(y);{var W=A=>{mt(A,{get appearance(){return S()},get supabaseClient(){return i()},get redirectTo(){return p()},get magicLink(){return h()},get showLinks(){return C()},get i18n(){return v(t)},get additionalData(){return G()},get authView(){return n()},set authView(Z){n(Z)},$$legacy:!0})};R(M,A=>{u()||A(W)})}d(f,y)};R(T,f=>{b(n()),b(J),L(()=>n()===J.SIGN_IN)&&f(I)})}var $=D(T,2);{var B=f=>{var y=se(),M=O(y);{var W=A=>{mt(A,{get appearance(){return S()},get supabaseClient(){return i()},get redirectTo(){return p()},get magicLink(){return h()},get showLinks(){return C()},get additionalData(){return G()},get i18n(){return v(t)},get authView(){return n()},set authView(Z){n(Z)},children:(Z,le)=>{var Y=se(),te=O(Y);Pe(te,e,"default",{},null),d(Z,Y)},$$slots:{default:!0},$$legacy:!0})};R(M,A=>{u()||A(W)})}d(f,y)};R($,f=>{b(n()),b(J),L(()=>n()===J.SIGN_UP)&&f(B)})}var z=D($,2);{var P=f=>{Ua(f,{get i18n(){return v(t)},get supabaseClient(){return i()},get showLinks(){return C()},get appearance(){return S()},get redirectTo(){return p()},get authView(){return n()},set authView(y){n(y)},$$legacy:!0})};R(z,f=>{b(n()),b(J),L(()=>n()===J.FORGOTTEN_PASSWORD)&&f(P)})}var U=D(z,2);{var q=f=>{Ja(f,{get i18n(){return v(t)},get supabaseClient(){return i()},get appearance(){return S()},get redirectTo(){return p()},get showLinks(){return C()},get authView(){return n()},set authView(y){n(y)},$$legacy:!0})};R(U,f=>{b(n()),b(J),L(()=>n()===J.MAGIC_LINK)&&f(q)})}var ie=D(U,2);{var F=f=>{Sr(f,{get i18n(){return v(t)},get supabaseClient(){return i()},get appearance(){return S()},get showLinks(){return C()},get authView(){return n()},set authView(y){n(y)},$$legacy:!0})};R(ie,f=>{b(n()),b(J),L(()=>n()===J.UPDATE_PASSWORD)&&f(F)})}var _=D(ie,2);{var w=f=>{Pr(f,{get i18n(){return v(t)},get supabaseClient(){return i()},get appearance(){return S()},get showLinks(){return C()},get otpType(){return E()},get authView(){return n()},set authView(y){n(y)},$$legacy:!0})};R(_,f=>{b(n()),b(J),L(()=>n()===J.VERIFY_OTP)&&f(w)})}de(j),X(()=>Zt(j,1,Yt(N()!=="default"?v(r):""))),d(a,j),he()}const Gr=[],Ur={theme:Ba,variables:{default:{colors:{brand:"hsl(var(--primary))",brandAccent:"hsl(var(--primary))",inputText:"hsl(var(--primary))",brandButtonText:"hsl(var(--primary-foreground))",messageText:"hsl(var(--foreground))",dividerBackground:"hsl(var(--foreground))",inputLabelText:"hsl(var(--foreground))",defaultButtonText:"hsl(var(--primary-foreground))",anchorTextColor:"hsl(var(--foreground))"},fontSizes:{baseInputSize:"16px"}}},className:{button:"authBtn"}};export{Or as A,Gr as o,Ur as s};
