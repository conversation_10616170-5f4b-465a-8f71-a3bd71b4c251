var un=Object.defineProperty;var de=t=>{throw TypeError(t)};var on=(t,e,n)=>e in t?un(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var Dt=(t,e,n)=>on(t,typeof e!="symbol"?e+"":e,n),Wt=(t,e,n)=>e.has(t)||de("Cannot "+n);var v=(t,e,n)=>(Wt(t,e,"read from private field"),n?n.call(t):e.get(t)),x=(t,e,n)=>e.has(t)?de("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),O=(t,e,n,r)=>(Wt(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),Et=(t,e,n)=>(Wt(t,e,"access private method"),n);var cn=Array.isArray,_n=Array.prototype.indexOf,fr=Array.from,we=Object.defineProperty,bt=Object.getOwnPropertyDescriptor,vn=Object.getOwnPropertyDescriptors,hn=Object.prototype,pn=Array.prototype,Se=Object.getPrototypeOf,me=Object.isExtensible;function ir(t){return typeof t=="function"}const lr=()=>{};function ur(t){return t()}function Re(t){for(var e=0;e<t.length;e++)t[e]()}function dn(){var t,e,n=new Promise((r,a)=>{t=r,e=a});return{promise:n,resolve:t,reject:e}}function or(t,e){if(Array.isArray(t))return t;if(!(Symbol.iterator in t))return Array.from(t);const n=[];for(const r of t)if(n.push(r),n.length===e)break;return n}const R=2,te=4,Bt=8,Ot=16,H=32,dt=64,Oe=128,C=256,Mt=512,y=1024,S=2048,Z=4096,X=8192,wt=16384,ee=32768,ke=65536,ye=1<<17,wn=1<<18,ne=1<<19,re=1<<20,Zt=1<<21,ae=1<<22,tt=1<<23,et=Symbol("$state"),cr=Symbol("legacy props"),_r=Symbol(""),se=new class extends Error{constructor(){super(...arguments);Dt(this,"name","StaleReactionError");Dt(this,"message","The reaction that called `getAbortSignal()` was re-run or destroyed")}},hr=1,fe=3,Ie=8;function mn(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function pr(){throw new Error("https://svelte.dev/e/invalid_default_snippet")}function yn(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function En(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function gn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function bn(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function Tn(t){throw new Error("https://svelte.dev/e/effect_orphan")}function An(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function dr(){throw new Error("https://svelte.dev/e/get_abort_signal_outside_reaction")}function wr(){throw new Error("https://svelte.dev/e/hydration_failed")}function mr(t){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}function yr(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function xn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function Nn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Sn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const Er=1,gr=2,br=4,Tr=8,Ar=16,xr=1,Nr=2,Sr=4,Rr=8,Or=16,kr=1,Ir=2,Cr=4,Ce=1,Rn=2,On="[",kn="[!",In="]",ie={},g=Symbol(),Dr="http://www.w3.org/1999/xhtml",Pr="http://www.w3.org/2000/svg",Mr="@attach";function le(t){console.warn("https://svelte.dev/e/hydration_mismatch")}function Fr(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let D=!1;function Lr(t){D=t}let m;function _t(t){if(t===null)throw le(),ie;return m=t}function Cn(){return _t(st(m))}function qr(t){if(D){if(st(m)!==null)throw le(),ie;m=t}}function jr(t=1){if(D){for(var e=t,n=m;e--;)n=st(n);m=n}}function Yr(){for(var t=0,e=m;;){if(e.nodeType===Ie){var n=e.data;if(n===In){if(t===0)return e;t-=1}else(n===On||n===kn)&&(t+=1)}var r=st(e);e.remove(),e=r}}function Hr(t){if(!t||t.nodeType!==Ie)throw le(),ie;return t.data}function De(t){return t===this.v}function Dn(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function Pe(t){return!Dn(t,this.v)}let Vt=!1;function Ur(){Vt=!0}let w=null;function Ft(t){w=t}function Br(t){return $t().get(t)}function Vr(t,e){return $t().set(t,e),e}function $r(t){return $t().has(t)}function Gr(){return $t()}function Kr(t,e=!1,n){w={p:w,c:null,e:null,s:t,x:null,l:Vt&&!e?{s:null,u:null,$:[]}:null}}function Wr(t){var e=w,n=e.e;if(n!==null){e.e=null;for(var r of n)Xe(r)}return w=e.p,{}}function kt(){return!Vt||w!==null&&w.l===null}function $t(t){return w===null&&yn(),w.c??(w.c=new Map(Pn(w)||void 0))}function Pn(t){let e=t.p;for(;e!==null;){const n=e.c;if(n!==null)return n;e=e.p}return null}const Mn=new WeakMap;function Fn(t){var e=h;if(e===null)return _.f|=tt,t;if(e.f&ee)ue(t,e);else{if(!(e.f&Oe))throw!e.parent&&t instanceof Error&&Me(t),t;e.b.error(t)}}function ue(t,e){for(;e!==null;){if(e.f&Oe)try{e.b.error(t);return}catch(n){t=n}e=e.parent}throw t instanceof Error&&Me(t),t}function Me(t){const e=Mn.get(t);e&&(we(t,"message",{value:e.message}),we(t,"stack",{value:e.stack}))}const Ln=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let Tt=[],At=[];function Fe(){var t=Tt;Tt=[],Re(t)}function Le(){var t=At;At=[],Re(t)}function Xr(t){Tt.length===0&&queueMicrotask(Fe),Tt.push(t)}function Zr(t){At.length===0&&Ln(Le),At.push(t)}function qn(){Tt.length>0&&Fe(),At.length>0&&Le()}function jn(){for(var t=h.b;t!==null&&!t.has_pending_snippet();)t=t.parent;return t===null&&mn(),t}function oe(t){var e=R|S,n=_!==null&&_.f&R?_:null;return h===null||n!==null&&n.f&C?e|=C:h.f|=ne,{ctx:w,deps:null,effects:null,equals:De,f:e,fn:t,reactions:null,rv:0,v:g,wv:0,parent:n??h,ac:null}}function Yn(t,e){let n=h;n===null&&En();var r=n.b,a=void 0,f=_e(g),s=null,u=!_;return Xn(()=>{try{var i=t()}catch(p){i=Promise.reject(p)}var l=()=>i;a=(s==null?void 0:s.then(l,l))??Promise.resolve(i),s=a;var o=k,c=r.pending;u&&(r.update_pending_count(1),c||o.increment());const d=(p,b=void 0)=>{s=null,c||o.activate(),b?b!==se&&(f.f|=tt,qt(f,b)):(f.f&tt&&(f.f^=tt),qt(f,p)),u&&(r.update_pending_count(-1),c||o.decrement()),Ye()};if(a.then(d,p=>d(null,p||"unknown")),o)return()=>{queueMicrotask(()=>o.neuter())}}),new Promise(i=>{function l(o){function c(){o===a?i(f):l(a)}o.then(c,c)}l(a)})}function zr(t){const e=oe(t);return nn(e),e}function Hn(t){const e=oe(t);return e.equals=Pe,e}function qe(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)at(e[n])}}function Un(t){for(var e=t.parent;e!==null;){if(!(e.f&R))return e;e=e.parent}return null}function ce(t){var e,n=h;jt(Un(t));try{qe(t),e=sn(t)}finally{jt(n)}return e}function je(t){var e=ce(t);if(t.equals(e)||(t.v=e,t.wv=rn()),!mt)if(G!==null)G.set(t,t.v);else{var n=(K||t.f&C)&&t.deps!==null?Z:y;E(t,n)}}function Bn(t,e,n){const r=kt()?oe:Hn;if(e.length===0){n(t.map(r));return}var a=k,f=h,s=Vn(),u=jn();Promise.all(e.map(i=>Yn(i))).then(i=>{a==null||a.activate(),s();try{n([...t.map(r),...i])}catch(l){f.f&wt||ue(l,f)}a==null||a.deactivate(),Ye()}).catch(i=>{u.error(i)})}function Vn(){var t=h,e=_,n=w;return function(){jt(t),pt(e),Ft(n)}}function Ye(){jt(null),pt(null),Ft(null)}const Pt=new Set;let k=null,G=null,Ee=new Set,Lt=[];function He(){const t=Lt.shift();Lt.length>0&&queueMicrotask(He),t()}let ft=[],Gt=null,zt=!1;var Nt,ut,ot,V,St,Rt,J,ct,$,j,Q,Y,Ue,Be,Jt;const Ut=class Ut{constructor(){x(this,Y);x(this,Nt,new Map);x(this,ut,new Map);x(this,ot,new Set);x(this,V,0);x(this,St,null);x(this,Rt,!1);x(this,J,[]);x(this,ct,[]);x(this,$,[]);x(this,j,[]);x(this,Q,[]);Dt(this,"skipped_effects",new Set)}capture(e,n){v(this,ut).has(e)||v(this,ut).set(e,n),v(this,Nt).set(e,e.v)}activate(){k=this}deactivate(){k=null;for(const e of Ee)if(Ee.delete(e),e(),k!==null)break}neuter(){O(this,Rt,!0)}flush(){ft.length>0?this.flush_effects():Et(this,Y,Jt).call(this),k===this&&(v(this,V)===0&&Pt.delete(this),this.deactivate())}flush_effects(){var e=it;zt=!0;try{var n=0;for(Ae(!0);ft.length>0;){if(n++>1e3){var r,a;Gn()}Et(this,Y,Ue).call(this,ft),nt.clear()}}finally{zt=!1,Ae(e),Gt=null}}increment(){O(this,V,v(this,V)+1)}decrement(){if(O(this,V,v(this,V)-1),v(this,V)===0){for(const e of v(this,$))E(e,S),W(e);for(const e of v(this,j))E(e,S),W(e);for(const e of v(this,Q))E(e,S),W(e);O(this,$,[]),O(this,j,[]),this.flush()}else this.deactivate()}add_callback(e){v(this,ot).add(e)}settled(){return(v(this,St)??O(this,St,dn())).promise}static ensure(e=!0){if(k===null){const n=k=new Ut;Pt.add(k),e&&Ut.enqueue(()=>{k===n&&n.flush()})}return k}static enqueue(e){Lt.length===0&&queueMicrotask(He),Lt.unshift(e)}};Nt=new WeakMap,ut=new WeakMap,ot=new WeakMap,V=new WeakMap,St=new WeakMap,Rt=new WeakMap,J=new WeakMap,ct=new WeakMap,$=new WeakMap,j=new WeakMap,Q=new WeakMap,Y=new WeakSet,Ue=function(e){var f;ft=[];var n=null;if(Pt.size>1){n=new Map,G=new Map;for(const[s,u]of v(this,Nt))n.set(s,{v:s.v,wv:s.wv}),s.v=u;for(const s of Pt)if(s!==this)for(const[u,i]of v(s,ut))n.has(u)||(n.set(u,{v:u.v,wv:u.wv}),u.v=i)}for(const s of e)Et(this,Y,Be).call(this,s);if(v(this,J).length===0&&v(this,V)===0){var r=v(this,$),a=v(this,j);O(this,$,[]),O(this,j,[]),O(this,Q,[]),Et(this,Y,Jt).call(this),ge(r),ge(a),(f=v(this,St))==null||f.resolve()}else{for(const s of v(this,$))E(s,y);for(const s of v(this,j))E(s,y);for(const s of v(this,Q))E(s,y)}if(n){for(const[s,{v:u,wv:i}]of n)s.wv<=i&&(s.v=u);G=null}for(const s of v(this,J))lt(s);for(const s of v(this,ct))lt(s);O(this,J,[]),O(this,ct,[])},Be=function(e){var o;e.f^=y;for(var n=e.first;n!==null;){var r=n.f,a=(r&(H|dt))!==0,f=a&&(r&y)!==0,s=f||(r&X)!==0||this.skipped_effects.has(n);if(!s&&n.fn!==null){if(a)n.f^=y;else if(r&te)v(this,j).push(n);else if(It(n))if(r&ae){var u=(o=n.b)!=null&&o.pending?v(this,ct):v(this,J);u.push(n)}else n.f&Ot&&v(this,Q).push(n),lt(n);var i=n.first;if(i!==null){n=i;continue}}var l=n.parent;for(n=n.next;n===null&&l!==null;)n=l.next,l=l.parent}},Jt=function(){if(!v(this,Rt))for(const e of v(this,ot))e();v(this,ot).clear()};let vt=Ut;function $n(t){var e;const n=vt.ensure(!1);for(t&&(n.flush_effects(),e=t());;){if(qn(),ft.length===0)return n===k&&n.flush(),Gt=null,e;n.flush_effects()}}function Gn(){try{An()}catch(t){ue(t,Gt)}}function ge(t){var e=t.length;if(e!==0){for(var n=0;n<e;n++){var r=t[n];if(!(r.f&(wt|X))&&It(r)){var a=Yt;if(lt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?Qe(r):r.fn=null),Yt>a&&r.f&re)break}}for(;n<e;n+=1)W(t[n])}}function W(t){for(var e=Gt=t;e.parent!==null;){e=e.parent;var n=e.f;if(zt&&e===h&&n&Ot)return;if(n&(dt|H)){if(!(n&y))return;e.f^=y}}ft.push(e)}const nt=new Map;function _e(t,e){var n={f:0,v:t,reactions:null,equals:De,rv:0,wv:0};return n}function U(t,e){const n=_e(t);return nn(n),n}function Jr(t,e=!1,n=!0){var a;const r=_e(t);return e||(r.equals=Pe),Vt&&n&&w!==null&&w.l!==null&&((a=w.l).s??(a.s=[])).push(r),r}function Qr(t,e){return q(t,ve(()=>z(t))),e}function q(t,e,n=!1){_!==null&&(!M||_.f&ye)&&kt()&&_.f&(R|Ot|ae|ye)&&!(A!=null&&A.includes(t))&&Sn();let r=n?gt(e):e;return qt(t,r)}function qt(t,e){if(!t.equals(e)){var n=t.v;mt?nt.set(t,e):nt.set(t,n),t.v=e,vt.ensure().capture(t,n),t.f&R&&(t.f&S&&ce(t),E(t,t.f&C?Z:y)),t.wv=rn(),Ve(t,S),kt()&&h!==null&&h.f&y&&!(h.f&(H|dt))&&(I===null?er([t]):I.push(t))}return e}function ta(t,e=1){var n=z(t),r=e===1?n++:n--;return q(t,n),r}function Xt(t){q(t,t.v+1)}function Ve(t,e){var n=t.reactions;if(n!==null)for(var r=kt(),a=n.length,f=0;f<a;f++){var s=n[f],u=s.f;!r&&s===h||(u&S||E(s,e),u&R?Ve(s,Z):u&S||W(s))}}function gt(t){if(typeof t!="object"||t===null||et in t)return t;const e=Se(t);if(e!==hn&&e!==pn)return t;var n=new Map,r=cn(t),a=U(0),f=rt,s=u=>{if(rt===f)return u();var i=_,l=rt;pt(null),Ne(f);var o=u();return pt(i),Ne(l),o};return r&&n.set("length",U(t.length)),new Proxy(t,{defineProperty(u,i,l){(!("value"in l)||l.configurable===!1||l.enumerable===!1||l.writable===!1)&&xn();var o=n.get(i);return o===void 0?o=s(()=>{var c=U(l.value);return n.set(i,c),c}):q(o,l.value,!0),!0},deleteProperty(u,i){var l=n.get(i);if(l===void 0){if(i in u){const o=s(()=>U(g));n.set(i,o),Xt(a)}}else q(l,g),Xt(a);return!0},get(u,i,l){var p;if(i===et)return t;var o=n.get(i),c=i in u;if(o===void 0&&(!c||(p=bt(u,i))!=null&&p.writable)&&(o=s(()=>{var b=gt(c?u[i]:g),yt=U(b);return yt}),n.set(i,o)),o!==void 0){var d=z(o);return d===g?void 0:d}return Reflect.get(u,i,l)},getOwnPropertyDescriptor(u,i){var l=Reflect.getOwnPropertyDescriptor(u,i);if(l&&"value"in l){var o=n.get(i);o&&(l.value=z(o))}else if(l===void 0){var c=n.get(i),d=c==null?void 0:c.v;if(c!==void 0&&d!==g)return{enumerable:!0,configurable:!0,value:d,writable:!0}}return l},has(u,i){var d;if(i===et)return!0;var l=n.get(i),o=l!==void 0&&l.v!==g||Reflect.has(u,i);if(l!==void 0||h!==null&&(!o||(d=bt(u,i))!=null&&d.writable)){l===void 0&&(l=s(()=>{var p=o?gt(u[i]):g,b=U(p);return b}),n.set(i,l));var c=z(l);if(c===g)return!1}return o},set(u,i,l,o){var pe;var c=n.get(i),d=i in u;if(r&&i==="length")for(var p=l;p<c.v;p+=1){var b=n.get(p+"");b!==void 0?q(b,g):p in u&&(b=s(()=>U(g)),n.set(p+"",b))}if(c===void 0)(!d||(pe=bt(u,i))!=null&&pe.writable)&&(c=s(()=>U(void 0)),q(c,gt(l)),n.set(i,c));else{d=c.v!==g;var yt=s(()=>gt(l));q(c,yt)}var Ct=Reflect.getOwnPropertyDescriptor(u,i);if(Ct!=null&&Ct.set&&Ct.set.call(o,l),!d){if(r&&typeof i=="string"){var he=n.get("length"),Kt=Number(i);Number.isInteger(Kt)&&Kt>=he.v&&q(he,Kt+1)}Xt(a)}return!0},ownKeys(u){z(a);var i=Reflect.ownKeys(u).filter(c=>{var d=n.get(c);return d===void 0||d.v!==g});for(var[l,o]of n)o.v!==g&&!(l in u)&&i.push(l);return i},setPrototypeOf(){Nn()}})}function be(t){try{if(t!==null&&typeof t=="object"&&et in t)return t[et]}catch{}return t}function ea(t,e){return Object.is(be(t),be(e))}var Te,Kn,$e,Ge,Ke;function na(){if(Te===void 0){Te=window,Kn=document,$e=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;Ge=bt(e,"firstChild").get,Ke=bt(e,"nextSibling").get,me(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),me(n)&&(n.__t=void 0)}}function ht(t=""){return document.createTextNode(t)}function P(t){return Ge.call(t)}function st(t){return Ke.call(t)}function ra(t,e){if(!D)return P(t);var n=P(m);if(n===null)n=m.appendChild(ht());else if(e&&n.nodeType!==fe){var r=ht();return n==null||n.before(r),_t(r),r}return _t(n),n}function aa(t,e){if(!D){var n=P(t);return n instanceof Comment&&n.data===""?st(n):n}return m}function sa(t,e=1,n=!1){let r=D?m:t;for(var a;e--;)a=r,r=st(r);if(!D)return r;if(n&&(r==null?void 0:r.nodeType)!==fe){var f=ht();return r===null?a==null||a.after(f):r.before(f),_t(f),f}return _t(r),r}function fa(t){t.textContent=""}function ia(){return!1}function We(t){h===null&&_===null&&Tn(),_!==null&&_.f&C&&h===null&&bn(),mt&&gn()}function Wn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function L(t,e,n,r=!0){var a=h;a!==null&&a.f&X&&(t|=X);var f={ctx:w,deps:null,nodes_start:null,nodes_end:null,f:t|S,first:null,fn:e,last:null,next:null,parent:a,b:a&&a.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{lt(f),f.f|=ee}catch(i){throw at(f),i}else e!==null&&W(f);var s=n&&f.deps===null&&f.first===null&&f.nodes_start===null&&f.teardown===null&&(f.f&ne)===0;if(!s&&r&&(a!==null&&Wn(f,a),_!==null&&_.f&R)){var u=_;(u.effects??(u.effects=[])).push(f)}return f}function la(t){const e=L(Bt,null,!1);return E(e,y),e.teardown=t,e}function ua(t){We();var e=h.f,n=!_&&(e&H)!==0&&(e&ee)===0;if(n){var r=w;(r.e??(r.e=[])).push(t)}else return Xe(t)}function Xe(t){return L(te|re,t,!1)}function oa(t){return We(),L(Bt|re,t,!0)}function ca(t){vt.ensure();const e=L(dt,t,!0);return(n={})=>new Promise(r=>{n.outro?Jn(e,()=>{at(e),r(void 0)}):(at(e),r(void 0))})}function _a(t){return L(te,t,!1)}function va(t,e){var n=w,r={effect:null,ran:!1,deps:t};n.l.$.push(r),r.effect=Ze(()=>{t(),!r.ran&&(r.ran=!0,ve(e))})}function ha(){var t=w;Ze(()=>{for(var e of t.l.$){e.deps();var n=e.effect;n.f&y&&E(n,Z),It(n)&&lt(n),e.ran=!1}})}function Xn(t){return L(ae|ne,t,!0)}function Ze(t,e=0){return L(Bt|e,t,!0)}function pa(t,e=[],n=[]){Bn(e,n,r=>{L(Bt,()=>t(...r.map(z)),!0)})}function da(t,e=0){var n=L(Ot|e,t,!0);return n}function wa(t,e=!0){return L(H,t,!0,e)}function ze(t){var e=t.teardown;if(e!==null){const n=mt,r=_;xe(!0),pt(null);try{e.call(null)}finally{xe(n),pt(r)}}}function Je(t,e=!1){var a;var n=t.first;for(t.first=t.last=null;n!==null;){(a=n.ac)==null||a.abort(se);var r=n.next;n.f&dt?n.parent=null:at(n,e),n=r}}function Zn(t){for(var e=t.first;e!==null;){var n=e.next;e.f&H||at(e),e=n}}function at(t,e=!0){var n=!1;(e||t.f&wn)&&t.nodes_start!==null&&t.nodes_end!==null&&(zn(t.nodes_start,t.nodes_end),n=!0),Je(t,e&&!n),Ht(t,0),E(t,wt);var r=t.transitions;if(r!==null)for(const f of r)f.stop();ze(t);var a=t.parent;a!==null&&a.first!==null&&Qe(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=t.ac=null}function zn(t,e){for(;t!==null;){var n=t===e?null:st(t);t.remove(),t=n}}function Qe(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function Jn(t,e){var n=[];tn(t,n,!0),Qn(n,()=>{at(t),e&&e()})}function Qn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function tn(t,e,n){if(!(t.f&X)){if(t.f^=X,t.transitions!==null)for(const s of t.transitions)(s.is_global||n)&&e.push(s);for(var r=t.first;r!==null;){var a=r.next,f=(r.f&ke)!==0||(r.f&H)!==0;tn(r,e,f?n:!1),r=a}}}function ma(t){en(t,!0)}function en(t,e){if(t.f&X){t.f^=X,t.f&y||(E(t,S),W(t));for(var n=t.first;n!==null;){var r=n.next,a=(n.f&ke)!==0||(n.f&H)!==0;en(n,a?e:!1),n=r}if(t.transitions!==null)for(const f of t.transitions)(f.is_global||e)&&f.in()}}let B=null;function tr(t){var e=B;try{if(B=new Set,ve(t),e!==null)for(var n of B)e.add(n);return B}finally{B=e}}function ya(t){for(var e of tr(t))qt(e,e.v)}let it=!1;function Ae(t){it=t}let mt=!1;function xe(t){mt=t}let _=null,M=!1;function pt(t){_=t}let h=null;function jt(t){h=t}let A=null;function nn(t){_!==null&&(A===null?A=[t]:A.push(t))}let T=null,N=0,I=null;function er(t){I=t}let Yt=1,xt=0,rt=xt;function Ne(t){rt=t}let K=!1;function rn(){return++Yt}function It(t){var c;var e=t.f;if(e&S)return!0;if(e&Z){var n=t.deps,r=(e&C)!==0;if(n!==null){var a,f,s=(e&Mt)!==0,u=r&&h!==null&&!K,i=n.length;if((s||u)&&(h===null||!(h.f&wt))){var l=t,o=l.parent;for(a=0;a<i;a++)f=n[a],(s||!((c=f==null?void 0:f.reactions)!=null&&c.includes(l)))&&(f.reactions??(f.reactions=[])).push(l);s&&(l.f^=Mt),u&&o!==null&&!(o.f&C)&&(l.f^=C)}for(a=0;a<i;a++)if(f=n[a],It(f)&&je(f),f.wv>t.wv)return!0}(!r||h!==null&&!K)&&E(t,y)}return!1}function an(t,e,n=!0){var r=t.reactions;if(r!==null&&!(A!=null&&A.includes(t)))for(var a=0;a<r.length;a++){var f=r[a];f.f&R?an(f,e,!1):e===f&&(n?E(f,S):f.f&y&&E(f,Z),W(f))}}function sn(t){var b;var e=T,n=N,r=I,a=_,f=K,s=A,u=w,i=M,l=rt,o=t.f;T=null,N=0,I=null,K=(o&C)!==0&&(M||!it||_===null),_=o&(H|dt)?null:t,A=null,Ft(t.ctx),M=!1,rt=++xt,t.ac!==null&&(t.ac.abort(se),t.ac=null);try{t.f|=Zt;var c=(0,t.fn)(),d=t.deps;if(T!==null){var p;if(Ht(t,N),d!==null&&N>0)for(d.length=N+T.length,p=0;p<T.length;p++)d[N+p]=T[p];else t.deps=d=T;if(!K||o&R&&t.reactions!==null)for(p=N;p<d.length;p++)((b=d[p]).reactions??(b.reactions=[])).push(t)}else d!==null&&N<d.length&&(Ht(t,N),d.length=N);if(kt()&&I!==null&&!M&&d!==null&&!(t.f&(R|Z|S)))for(p=0;p<I.length;p++)an(I[p],t);return a!==null&&a!==t&&(xt++,I!==null&&(r===null?r=I:r.push(...I))),t.f&tt&&(t.f^=tt),c}catch(yt){return Fn(yt)}finally{t.f^=Zt,T=e,N=n,I=r,_=a,K=f,A=s,Ft(u),M=i,rt=l}}function nr(t,e){let n=e.reactions;if(n!==null){var r=_n.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&e.f&R&&(T===null||!T.includes(e))&&(E(e,Z),e.f&(C|Mt)||(e.f^=Mt),qe(e),Ht(e,0))}function Ht(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)nr(t,n[r])}function lt(t){var e=t.f;if(!(e&wt)){E(t,y);var n=h,r=it;h=t,it=!0;try{e&Ot?Zn(t):Je(t),ze(t);var a=sn(t);t.teardown=typeof a=="function"?a:null,t.wv=Yt;var f}finally{it=r,h=n}}}async function Ea(){await Promise.resolve(),$n()}function ga(){return vt.ensure().settled()}function z(t){var e=t.f,n=(e&R)!==0;if(B==null||B.add(t),_!==null&&!M){var r=h!==null&&(h.f&wt)!==0;if(!r&&!(A!=null&&A.includes(t))){var a=_.deps;if(_.f&Zt)t.rv<xt&&(t.rv=xt,T===null&&a!==null&&a[N]===t?N++:T===null?T=[t]:(!K||!T.includes(t))&&T.push(t));else{(_.deps??(_.deps=[])).push(t);var f=t.reactions;f===null?t.reactions=[_]:f.includes(_)||f.push(_)}}}else if(n&&t.deps===null&&t.effects===null){var s=t,u=s.parent;u!==null&&!(u.f&C)&&(s.f^=C)}if(mt){if(nt.has(t))return nt.get(t);if(n){s=t;var i=s.v;return(!(s.f&y)&&s.reactions!==null||fn(s))&&(i=ce(s)),nt.set(s,i),i}}else if(n){if(s=t,G!=null&&G.has(s))return G.get(s);It(s)&&je(s)}if(t.f&tt)throw t.v;return t.v}function fn(t){if(t.v===g)return!0;if(t.deps===null)return!1;for(const e of t.deps)if(nt.has(e)||e.f&R&&fn(e))return!0;return!1}function ve(t){var e=M;try{return M=!0,t()}finally{M=e}}const rr=-7169;function E(t,e){t.f=t.f&rr|e}function ba(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(et in t)Qt(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&et in n&&Qt(n)}}}function Qt(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{Qt(t[r],e)}catch{}const n=Se(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=vn(n);for(let a in r){const f=r[a].get;if(f)try{f.call(t)}catch{}}}}}function ln(t){var e=document.createElement("template");return e.innerHTML=t.replaceAll("<!>","<!---->"),e.content}function F(t,e){var n=h;n.nodes_start===null&&(n.nodes_start=t,n.nodes_end=e)}function Ta(t,e){var n=(e&Ce)!==0,r=(e&Rn)!==0,a,f=!t.startsWith("<!>");return()=>{if(D)return F(m,null),m;a===void 0&&(a=ln(f?t:"<!>"+t),n||(a=P(a)));var s=r||$e?document.importNode(a,!0):a.cloneNode(!0);if(n){var u=P(s),i=s.lastChild;F(u,i)}else F(s,s);return s}}function ar(t,e,n="svg"){var r=!t.startsWith("<!>"),a=(e&Ce)!==0,f=`<${n}>${r?t:"<!>"+t}</${n}>`,s;return()=>{if(D)return F(m,null),m;if(!s){var u=ln(f),i=P(u);if(a)for(s=document.createDocumentFragment();P(i);)s.appendChild(P(i));else s=P(i)}var l=s.cloneNode(!0);if(a){var o=P(l),c=l.lastChild;F(o,c)}else F(l,l);return l}}function Aa(t,e){return ar(t,e,"svg")}function xa(t=""){if(!D){var e=ht(t+"");return F(e,e),e}var n=m;return n.nodeType!==fe&&(n.before(n=ht()),_t(n)),F(n,n),n}function Na(){if(D)return F(m,null),m;var t=document.createDocumentFragment(),e=document.createComment(""),n=ht();return t.append(e,n),F(e,n),t}function Sa(t,e){if(D){h.nodes_end=m,Cn();return}t!==null&&t.before(e)}export{Kn as $,st as A,le as B,Ie as C,F as D,_t as E,ln as F,P as G,ie as H,U as I,yn as J,w as K,cn as L,_ as M,dr as N,mr as O,Vt as P,$n as Q,Gr as R,Br as S,$r as T,Vr as U,ga as V,Ea as W,da as X,la as Y,ke as Z,wa as _,Sa as a,Fr as a$,lr as a0,at as a1,Hr as a2,kn as a3,Yr as a4,Lr as a5,ht as a6,k as a7,g as a8,ia as a9,Cr as aA,kr as aB,Ir as aC,Ur as aD,br as aE,In as aF,Er as aG,gr as aH,qt as aI,fr as aJ,Ar as aK,X as aL,Tr as aM,tn as aN,fa as aO,Qn as aP,oa as aQ,Re as aR,ur as aS,ba as aT,Dn as aU,hr as aV,Pr as aW,kt as aX,Aa as aY,or as aZ,Te as a_,ma as aa,Jn as ab,_a as ac,Ze as ad,Xr as ae,et as af,bt as ag,yr as ah,Sr as ai,oe as aj,Hn as ak,gt as al,wt as am,_e as an,Rr as ao,Nr as ap,xr as aq,ta as ar,jt as as,ir as at,cr as au,Or as av,mt as aw,we as ax,Ot as ay,ee as az,Na as b,ea as b0,Bn as b1,_r as b2,Dr as b3,Se as b4,Mr as b5,vn as b6,Zr as b7,pt as b8,wn as b9,On as ba,na as bb,wr as bc,ca as bd,Qr as be,ya as bf,ra as c,aa as d,Wr as e,Ta as f,ha as g,ve as h,z as i,q as j,zr as k,va as l,Jr as m,jr as n,pr as o,Kr as p,xa as q,qr as r,sa as s,pa as t,ua as u,D as v,Cn as w,h as x,zn as y,m as z};
