var ot=e=>{throw TypeError(e)};var Ht=(e,t,n)=>t.has(e)||ot("Cannot "+n);var S=(e,t,n)=>(Ht(e,t,"read from private field"),n?n.call(e):t.get(e)),L=(e,t,n)=>t.has(e)?ot("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n);import{o as st,s as Kt}from"./RnwjOPnl.js";import{w as Fe}from"./rjRVMZXi.js";import{I as P,i as x,j as C}from"./DDiqt3uM.js";class ke{constructor(t,n){this.status=t,typeof n=="string"?this.body={message:n}:n?this.body=n:this.body={message:`Error: ${t}`}}toString(){return JSON.stringify(this.body)}}class qe{constructor(t,n){this.status=t,this.location=n}}class Ge extends Error{constructor(t,n,a){super(a),this.status=t,this.text=n}}new URL("sveltekit-internal://");function Wt(e,t){return e==="/"||t==="ignore"?e:t==="never"?e.endsWith("/")?e.slice(0,-1):e:t==="always"&&!e.endsWith("/")?e+"/":e}function Yt(e){return e.split("%25").map(decodeURI).join("%25")}function Jt(e){for(const t in e)e[t]=decodeURIComponent(e[t]);return e}function Le({href:e}){return e.split("#")[0]}function Xt(e,t,n,a=!1){const r=new URL(e);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(i,o){if(o==="get"||o==="getAll"||o==="has")return l=>(n(l),i[o](l));t();const c=Reflect.get(i,o);return typeof c=="function"?c.bind(i):c}}),enumerable:!0,configurable:!0});const s=["href","pathname","search","toString","toJSON"];a&&s.push("hash");for(const i of s)Object.defineProperty(r,i,{get(){return t(),e[i]},enumerable:!0,configurable:!0});return r}function Zt(...e){let t=5381;for(const n of e)if(typeof n=="string"){let a=n.length;for(;a;)t=t*33^n.charCodeAt(--a)}else if(ArrayBuffer.isView(n)){const a=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let r=a.length;for(;r;)t=t*33^a[--r]}else throw new TypeError("value must be a string or TypedArray");return(t>>>0).toString(36)}function Qt(e){const t=atob(e),n=new Uint8Array(t.length);for(let a=0;a<t.length;a++)n[a]=t.charCodeAt(a);return n.buffer}const en=window.fetch;window.fetch=(e,t)=>((e instanceof Request?e.method:(t==null?void 0:t.method)||"GET")!=="GET"&&W.delete(He(e)),en(e,t));const W=new Map;function tn(e,t){const n=He(e,t),a=document.querySelector(n);if(a!=null&&a.textContent){let{body:r,...s}=JSON.parse(a.textContent);const i=a.getAttribute("data-ttl");return i&&W.set(n,{body:r,init:s,ttl:1e3*Number(i)}),a.getAttribute("data-b64")!==null&&(r=Qt(r)),Promise.resolve(new Response(r,s))}return window.fetch(e,t)}function nn(e,t,n){if(W.size>0){const a=He(e,n),r=W.get(a);if(r){if(performance.now()<r.ttl&&["default","force-cache","only-if-cached",void 0].includes(n==null?void 0:n.cache))return new Response(r.body,r.init);W.delete(a)}}return window.fetch(t,n)}function He(e,t){let a=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(t!=null&&t.headers||t!=null&&t.body){const r=[];t.headers&&r.push([...new Headers(t.headers)].join(",")),t.body&&(typeof t.body=="string"||ArrayBuffer.isView(t.body))&&r.push(t.body),a+=`[data-hash="${Zt(...r)}"]`}return a}const rn=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function an(e){const t=[];return{pattern:e==="/"?/^\/$/:new RegExp(`^${sn(e).map(a=>{const r=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(a);if(r)return t.push({name:r[1],matcher:r[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const s=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(a);if(s)return t.push({name:s[1],matcher:s[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!a)return;const i=a.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,l)=>{if(l%2){if(c.startsWith("x+"))return Pe(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return Pe(String.fromCharCode(...c.slice(2).split("-").map(d=>parseInt(d,16))));const p=rn.exec(c),[,u,m,f,_]=p;return t.push({name:f,matcher:_,optional:!!u,rest:!!m,chained:m?l===1&&i[0]==="":!1}),m?"(.*?)":u?"([^/]*)?":"([^/]+?)"}return Pe(c)}).join("")}).join("")}/?$`),params:t}}function on(e){return!/^\([^)]+\)$/.test(e)}function sn(e){return e.slice(1).split("/").filter(on)}function cn(e,t,n){const a={},r=e.slice(1),s=r.filter(o=>o!==void 0);let i=0;for(let o=0;o<t.length;o+=1){const c=t[o];let l=r[o-i];if(c.chained&&c.rest&&i&&(l=r.slice(o-i,o+1).filter(p=>p).join("/"),i=0),l===void 0){c.rest&&(a[c.name]="");continue}if(!c.matcher||n[c.matcher](l)){a[c.name]=l;const p=t[o+1],u=r[o+1];p&&!p.rest&&p.optional&&u&&c.chained&&(i=0),!p&&!u&&Object.keys(a).length===s.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return a}function Pe(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function ln({nodes:e,server_loads:t,dictionary:n,matchers:a}){const r=new Set(t);return Object.entries(n).map(([o,[c,l,p]])=>{const{pattern:u,params:m}=an(o),f={id:o,exec:_=>{const d=u.exec(_);if(d)return cn(d,m,a)},errors:[1,...p||[]].map(_=>e[_]),layouts:[0,...l||[]].map(i),leaf:s(c)};return f.errors.length=f.layouts.length=Math.max(f.errors.length,f.layouts.length),f});function s(o){const c=o<0;return c&&(o=~o),[c,e[o]]}function i(o){return o===void 0?o:[r.has(o),e[o]]}}function bt(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}function it(e,t,n=JSON.stringify){const a=n(t);try{sessionStorage[e]=a}catch{}}var _t;const I=((_t=globalThis.__sveltekit_osyjjw)==null?void 0:_t.base)??"";var mt;const fn=((mt=globalThis.__sveltekit_osyjjw)==null?void 0:mt.assets)??I,un="1753155278445",St="sveltekit:snapshot",At="sveltekit:scroll",kt="sveltekit:states",dn="sveltekit:pageurl",F="sveltekit:history",Z="sveltekit:navigation",V={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},he=location.origin;function Ke(e){if(e instanceof URL)return e;let t=document.baseURI;if(!t){const n=document.getElementsByTagName("base");t=n.length?n[0].href:document.URL}return new URL(e,t)}function Ee(){return{x:pageXOffset,y:pageYOffset}}function B(e,t){return e.getAttribute(`data-sveltekit-${t}`)}const ct={...V,"":V.hover};function Et(e){let t=e.assignedSlot??e.parentNode;return(t==null?void 0:t.nodeType)===11&&(t=t.host),t}function Rt(e,t){for(;e&&e!==t;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=Et(e)}}function $e(e,t,n){let a;try{if(a=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI),n&&a.hash.match(/^#[^/]/)){const o=location.hash.split("#")[1]||"/";a.hash=`#${o}${a.hash}`}}catch{}const r=e instanceof SVGAElement?e.target.baseVal:e.target,s=!a||!!r||Re(a,t,n)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),i=(a==null?void 0:a.origin)===he&&e.hasAttribute("download");return{url:a,external:s,target:r,download:i}}function me(e){let t=null,n=null,a=null,r=null,s=null,i=null,o=e;for(;o&&o!==document.documentElement;)a===null&&(a=B(o,"preload-code")),r===null&&(r=B(o,"preload-data")),t===null&&(t=B(o,"keepfocus")),n===null&&(n=B(o,"noscroll")),s===null&&(s=B(o,"reload")),i===null&&(i=B(o,"replacestate")),o=Et(o);function c(l){switch(l){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:ct[a??"off"],preload_data:ct[r??"off"],keepfocus:c(t),noscroll:c(n),reload:c(s),replace_state:c(i)}}function lt(e){const t=Fe(e);let n=!0;function a(){n=!0,t.update(i=>i)}function r(i){n=!1,t.set(i)}function s(i){let o;return t.subscribe(c=>{(o===void 0||n&&c!==o)&&i(o=c)})}return{notify:a,set:r,subscribe:s}}const jt={v:()=>{}};function hn(){const{set:e,subscribe:t}=Fe(!1);let n;async function a(){clearTimeout(n);try{const r=await fetch(`${fn}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!r.ok)return!1;const i=(await r.json()).version!==un;return i&&(e(!0),jt.v(),clearTimeout(n)),i}catch{return!1}}return{subscribe:t,check:a}}function Re(e,t,n){return e.origin!==he||!e.pathname.startsWith(t)?!0:n?!(e.pathname===t+"/"||e.pathname===t+"/index.html"||e.protocol==="file:"&&e.pathname.replace(/\/[^/]+\.html?$/,"")===t):!1}function Wn(e){}function Yn(e){const t=new DataView(e);let n="";for(let a=0;a<e.byteLength;a++)n+=String.fromCharCode(t.getUint8(a));return gn(n)}function ft(e){const t=pn(e),n=new ArrayBuffer(t.length),a=new DataView(n);for(let r=0;r<n.byteLength;r++)a.setUint8(r,t.charCodeAt(r));return n}const Ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function pn(e){e.length%4===0&&(e=e.replace(/==?$/,""));let t="",n=0,a=0;for(let r=0;r<e.length;r++)n<<=6,n|=Ot.indexOf(e[r]),a+=6,a===24&&(t+=String.fromCharCode((n&16711680)>>16),t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255),n=a=0);return a===12?(n>>=4,t+=String.fromCharCode(n)):a===18&&(n>>=2,t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255)),t}function gn(e){let t="";for(let n=0;n<e.length;n+=3){const a=[void 0,void 0,void 0,void 0];a[0]=e.charCodeAt(n)>>2,a[1]=(e.charCodeAt(n)&3)<<4,e.length>n+1&&(a[1]|=e.charCodeAt(n+1)>>4,a[2]=(e.charCodeAt(n+1)&15)<<2),e.length>n+2&&(a[2]|=e.charCodeAt(n+2)>>6,a[3]=e.charCodeAt(n+2)&63);for(let r=0;r<a.length;r++)typeof a[r]>"u"?t+="=":t+=Ot[a[r]]}return t}const _n=-1,mn=-2,yn=-3,wn=-4,vn=-5,bn=-6;function Jn(e,t){return Tt(JSON.parse(e),t)}function Tt(e,t){if(typeof e=="number")return r(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const n=e,a=Array(n.length);function r(s,i=!1){if(s===_n)return;if(s===yn)return NaN;if(s===wn)return 1/0;if(s===vn)return-1/0;if(s===bn)return-0;if(i)throw new Error("Invalid input");if(s in a)return a[s];const o=n[s];if(!o||typeof o!="object")a[s]=o;else if(Array.isArray(o))if(typeof o[0]=="string"){const c=o[0],l=t==null?void 0:t[c];if(l)return a[s]=l(r(o[1]));switch(c){case"Date":a[s]=new Date(o[1]);break;case"Set":const p=new Set;a[s]=p;for(let f=1;f<o.length;f+=1)p.add(r(o[f]));break;case"Map":const u=new Map;a[s]=u;for(let f=1;f<o.length;f+=2)u.set(r(o[f]),r(o[f+1]));break;case"RegExp":a[s]=new RegExp(o[1],o[2]);break;case"Object":a[s]=Object(o[1]);break;case"BigInt":a[s]=BigInt(o[1]);break;case"null":const m=Object.create(null);a[s]=m;for(let f=1;f<o.length;f+=2)m[o[f]]=r(o[f+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const f=globalThis[c],_=o[1],d=ft(_),h=new f(d);a[s]=h;break}case"ArrayBuffer":{const f=o[1],_=ft(f);a[s]=_;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(o.length);a[s]=c;for(let l=0;l<o.length;l+=1){const p=o[l];p!==mn&&(c[l]=r(p))}}else{const c={};a[s]=c;for(const l in o){const p=o[l];c[l]=r(p)}}return a[s]}return r(0)}const Ut=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...Ut];const Sn=new Set([...Ut]);[...Sn];function An(e){return e.filter(t=>t!=null)}const kn="x-sveltekit-invalidated",En="x-sveltekit-trailing-slash";function ye(e){return e instanceof ke||e instanceof Ge?e.status:500}function Rn(e){return e instanceof Ge?e.text:"Internal Error"}let k,Q,xe;const jn=st.toString().includes("$$")||/function \w+\(\) \{\}/.test(st.toString());var re,ae,oe,se,ie,ce,le,fe,yt,ue,wt,de,vt;jn?(k={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},Q={current:null},xe={current:!1}):(k=new(yt=class{constructor(){L(this,re,P({}));L(this,ae,P(null));L(this,oe,P(null));L(this,se,P({}));L(this,ie,P({id:null}));L(this,ce,P({}));L(this,le,P(-1));L(this,fe,P(new URL("https://example.com")))}get data(){return x(S(this,re))}set data(t){C(S(this,re),t)}get form(){return x(S(this,ae))}set form(t){C(S(this,ae),t)}get error(){return x(S(this,oe))}set error(t){C(S(this,oe),t)}get params(){return x(S(this,se))}set params(t){C(S(this,se),t)}get route(){return x(S(this,ie))}set route(t){C(S(this,ie),t)}get state(){return x(S(this,ce))}set state(t){C(S(this,ce),t)}get status(){return x(S(this,le))}set status(t){C(S(this,le),t)}get url(){return x(S(this,fe))}set url(t){C(S(this,fe),t)}},re=new WeakMap,ae=new WeakMap,oe=new WeakMap,se=new WeakMap,ie=new WeakMap,ce=new WeakMap,le=new WeakMap,fe=new WeakMap,yt),Q=new(wt=class{constructor(){L(this,ue,P(null))}get current(){return x(S(this,ue))}set current(t){C(S(this,ue),t)}},ue=new WeakMap,wt),xe=new(vt=class{constructor(){L(this,de,P(!1))}get current(){return x(S(this,de))}set current(t){C(S(this,de),t)}},de=new WeakMap,vt),jt.v=()=>xe.current=!0);function We(e){Object.assign(k,e)}const On="/__data.json",Tn=".html__data.json";function Un(e){return e.endsWith(".html")?e.replace(/\.html$/,Tn):e.replace(/\/$/,"")+On}const{onMount:In,tick:De}=Kt,Ln=new Set(["icon","shortcut icon","apple-touch-icon"]),z=bt(At)??{},ee=bt(St)??{},D={url:lt({}),page:lt({}),navigating:Fe(null),updated:hn()};function Ye(e){z[e]=Ee()}function Pn(e,t){let n=e+1;for(;z[n];)delete z[n],n+=1;for(n=t+1;ee[n];)delete ee[n],n+=1}function H(e){return location.href=e.href,new Promise(()=>{})}async function It(){if("serviceWorker"in navigator){const e=await navigator.serviceWorker.getRegistration(I||"/");e&&await e.update()}}function ut(){}let Je,Me,we,N,Ve,A;const ve=[],be=[];let T=null;const _e=new Map,Xe=new Set,xn=new Set,Y=new Set;let y={branch:[],error:null,url:null},Ze=!1,Se=!1,dt=!0,te=!1,K=!1,Lt=!1,je=!1,q,R,U,$;const J=new Set;let Ce;async function er(e,t,n){var s,i,o,c;document.URL!==location.href&&(location.href=location.href),A=e,await((i=(s=e.hooks).init)==null?void 0:i.call(s)),Je=ln(e),N=document.documentElement,Ve=t,Me=e.nodes[0],we=e.nodes[1],Me(),we(),R=(o=history.state)==null?void 0:o[F],U=(c=history.state)==null?void 0:c[Z],R||(R=U=Date.now(),history.replaceState({...history.state,[F]:R,[Z]:U},""));const a=z[R];function r(){a&&(history.scrollRestoration="manual",scrollTo(a.x,a.y))}n?(r(),await Bn(Ve,n)):(await X({type:"enter",url:Ke(A.hash?Fn(new URL(location.href)):location.href),replace_state:!0}),r()),zn()}async function Pt(){if(await(Ce||(Ce=Promise.resolve())),!Ce)return;Ce=null;const e=$={},t=await ge(y.url,!0);T=null;const n=t&&await tt(t);if(!(!n||e!==$)){if(n.type==="redirect")return pe(new URL(n.location,y.url).href,{},1,e);n.props.page&&Object.assign(k,n.props.page),y=n.state,xt(),q.$set(n.props),We(n.props.page)}}function xt(){ve.length=0,je=!1}function Ct(e){be.some(t=>t==null?void 0:t.snapshot)&&(ee[e]=be.map(t=>{var n;return(n=t==null?void 0:t.snapshot)==null?void 0:n.capture()}))}function Nt(e){var t;(t=ee[e])==null||t.forEach((n,a)=>{var r,s;(s=(r=be[a])==null?void 0:r.snapshot)==null||s.restore(n)})}function ht(){Ye(R),it(At,z),Ct(U),it(St,ee)}async function pe(e,t,n,a){return X({type:"goto",url:Ke(e),keepfocus:t.keepFocus,noscroll:t.noScroll,replace_state:t.replaceState,state:t.state,redirect_count:n,nav_token:a,accept:()=>{t.invalidateAll&&(je=!0),t.invalidate&&t.invalidate.forEach(Bt)}})}async function Cn(e){if(e.id!==(T==null?void 0:T.id)){const t={};J.add(t),T={id:e.id,token:t,promise:tt({...e,preload:t}).then(n=>(J.delete(t),n.type==="loaded"&&n.state.error&&(T=null),n))}}return T.promise}async function Ne(e){var n;const t=(n=await ge(e,!1))==null?void 0:n.route;t&&await Promise.all([...t.layouts,t.leaf].map(a=>a==null?void 0:a[1]()))}function $t(e,t,n){var r;y=e.state;const a=document.querySelector("style[data-sveltekit]");if(a&&a.remove(),Object.assign(k,e.props.page),q=new A.root({target:t,props:{...e.props,stores:D,components:be},hydrate:n,sync:!1}),Nt(U),n){const s={from:null,to:{params:y.params,route:{id:((r=y.route)==null?void 0:r.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};Y.forEach(i=>i(s))}Se=!0}function ne({url:e,params:t,branch:n,status:a,error:r,route:s,form:i}){let o="never";if(I&&(e.pathname===I||e.pathname===I+"/"))o="always";else for(const f of n)(f==null?void 0:f.slash)!==void 0&&(o=f.slash);e.pathname=Wt(e.pathname,o),e.search=e.search;const c={type:"loaded",state:{url:e,params:t,branch:n,error:r,route:s},props:{constructors:An(n).map(f=>f.node.component),page:Te(k)}};i!==void 0&&(c.props.form=i);let l={},p=!k,u=0;for(let f=0;f<Math.max(n.length,y.branch.length);f+=1){const _=n[f],d=y.branch[f];(_==null?void 0:_.data)!==(d==null?void 0:d.data)&&(p=!0),_&&(l={...l,..._.data},p&&(c.props[`data_${u}`]=l),u+=1)}return(!y.url||e.href!==y.url.href||y.error!==r||i!==void 0&&i!==k.form||p)&&(c.props.page={error:r,params:t,route:{id:(s==null?void 0:s.id)??null},state:{},status:a,url:new URL(e),form:i??null,data:p?l:k.data}),c}async function Qe({loader:e,parent:t,url:n,params:a,route:r,server_data_node:s}){var p,u,m;let i=null,o=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},l=await e();if((p=l.universal)!=null&&p.load){let f=function(...d){for(const h of d){const{href:v}=new URL(h,n);c.dependencies.add(v)}};const _={route:new Proxy(r,{get:(d,h)=>(o&&(c.route=!0),d[h])}),params:new Proxy(a,{get:(d,h)=>(o&&c.params.add(h),d[h])}),data:(s==null?void 0:s.data)??null,url:Xt(n,()=>{o&&(c.url=!0)},d=>{o&&c.search_params.add(d)},A.hash),async fetch(d,h){d instanceof Request&&(h={body:d.method==="GET"||d.method==="HEAD"?void 0:await d.blob(),cache:d.cache,credentials:d.credentials,headers:[...d.headers].length>0?d==null?void 0:d.headers:void 0,integrity:d.integrity,keepalive:d.keepalive,method:d.method,mode:d.mode,redirect:d.redirect,referrer:d.referrer,referrerPolicy:d.referrerPolicy,signal:d.signal,...h});const{resolved:v,promise:j}=Dt(d,h,n);return o&&f(v.href),j},setHeaders:()=>{},depends:f,parent(){return o&&(c.parent=!0),t()},untrack(d){o=!1;try{return d()}finally{o=!0}}};i=await l.universal.load.call(null,_)??null}return{node:l,loader:e,server:s,universal:(u=l.universal)!=null&&u.load?{type:"data",data:i,uses:c}:null,data:i??(s==null?void 0:s.data)??null,slash:((m=l.universal)==null?void 0:m.trailingSlash)??(s==null?void 0:s.slash)}}function Dt(e,t,n){let a=e instanceof Request?e.url:e;const r=new URL(a,n);r.origin===n.origin&&(a=r.href.slice(n.origin.length));const s=Se?nn(a,r.href,t):tn(a,t);return{resolved:r,promise:s}}function pt(e,t,n,a,r,s){if(je)return!0;if(!r)return!1;if(r.parent&&e||r.route&&t||r.url&&n)return!0;for(const i of r.search_params)if(a.has(i))return!0;for(const i of r.params)if(s[i]!==y.params[i])return!0;for(const i of r.dependencies)if(ve.some(o=>o(new URL(i))))return!0;return!1}function et(e,t){return(e==null?void 0:e.type)==="data"?e:(e==null?void 0:e.type)==="skip"?t??null:null}function Nn(e,t){if(!e)return new Set(t.searchParams.keys());const n=new Set([...e.searchParams.keys(),...t.searchParams.keys()]);for(const a of n){const r=e.searchParams.getAll(a),s=t.searchParams.getAll(a);r.every(i=>s.includes(i))&&s.every(i=>r.includes(i))&&n.delete(a)}return n}function gt({error:e,url:t,route:n,params:a}){return{type:"loaded",state:{error:e,url:t,route:n,params:a,branch:[]},props:{page:Te(k),constructors:[]}}}async function tt({id:e,invalidating:t,url:n,params:a,route:r,preload:s}){if((T==null?void 0:T.id)===e)return J.delete(T.token),T.promise;const{errors:i,layouts:o,leaf:c}=r,l=[...o,c];i.forEach(g=>g==null?void 0:g().catch(()=>{})),l.forEach(g=>g==null?void 0:g[1]().catch(()=>{}));let p=null;const u=y.url?e!==Ae(y.url):!1,m=y.route?r.id!==y.route.id:!1,f=Nn(y.url,n);let _=!1;const d=l.map((g,w)=>{var M;const b=y.branch[w],E=!!(g!=null&&g[0])&&((b==null?void 0:b.loader)!==g[1]||pt(_,m,u,f,(M=b.server)==null?void 0:M.uses,a));return E&&(_=!0),E});if(d.some(Boolean)){try{p=await Ft(n,d)}catch(g){const w=await G(g,{url:n,params:a,route:{id:e}});return J.has(s)?gt({error:w,url:n,params:a,route:r}):Oe({status:ye(g),error:w,url:n,route:r})}if(p.type==="redirect")return p}const h=p==null?void 0:p.nodes;let v=!1;const j=l.map(async(g,w)=>{var Ue;if(!g)return;const b=y.branch[w],E=h==null?void 0:h[w];if((!E||E.type==="skip")&&g[1]===(b==null?void 0:b.loader)&&!pt(v,m,u,f,(Ue=b.universal)==null?void 0:Ue.uses,a))return b;if(v=!0,(E==null?void 0:E.type)==="error")throw E;return Qe({loader:g[1],url:n,params:a,route:r,parent:async()=>{var at;const rt={};for(let Ie=0;Ie<w;Ie+=1)Object.assign(rt,(at=await j[Ie])==null?void 0:at.data);return rt},server_data_node:et(E===void 0&&g[0]?{type:"skip"}:E??null,g[0]?b==null?void 0:b.server:void 0)})});for(const g of j)g.catch(()=>{});const O=[];for(let g=0;g<l.length;g+=1)if(l[g])try{O.push(await j[g])}catch(w){if(w instanceof qe)return{type:"redirect",location:w.location};if(J.has(s))return gt({error:await G(w,{params:a,url:n,route:{id:r.id}}),url:n,params:a,route:r});let b=ye(w),E;if(h!=null&&h.includes(w))b=w.status??b,E=w.error;else if(w instanceof ke)E=w.body;else{if(await D.updated.check())return await It(),await H(n);E=await G(w,{params:a,url:n,route:{id:r.id}})}const M=await Mt(g,O,i);return M?ne({url:n,params:a,branch:O.slice(0,M.idx).concat(M.node),status:b,error:E,route:r}):await zt(n,{id:r.id},E,b)}else O.push(void 0);return ne({url:n,params:a,branch:O,status:200,error:null,route:r,form:t?void 0:null})}async function Mt(e,t,n){for(;e--;)if(n[e]){let a=e;for(;!t[a];)a-=1;try{return{idx:a+1,node:{node:await n[e](),loader:n[e],data:{},server:null,universal:null}}}catch{continue}}}async function Oe({status:e,error:t,url:n,route:a}){const r={};let s=null;if(A.server_loads[0]===0)try{const o=await Ft(n,[!0]);if(o.type!=="data"||o.nodes[0]&&o.nodes[0].type!=="data")throw 0;s=o.nodes[0]??null}catch{(n.origin!==he||n.pathname!==location.pathname||Ze)&&await H(n)}try{const o=await Qe({loader:Me,url:n,params:r,route:a,parent:()=>Promise.resolve({}),server_data_node:et(s)}),c={node:await we(),loader:we,universal:null,server:null,data:null};return ne({url:n,params:r,branch:[o,c],status:e,error:t,route:null})}catch(o){if(o instanceof qe)return pe(new URL(o.location,location.href),{},0);throw o}}async function $n(e){const t=e.href;if(_e.has(t))return _e.get(t);let n;try{const a=(async()=>{let r=await A.hooks.reroute({url:new URL(e),fetch:async(s,i)=>Dt(s,i,e).promise})??e;if(typeof r=="string"){const s=new URL(e);A.hash?s.hash=r:s.pathname=r,r=s}return r})();_e.set(t,a),n=await a}catch{_e.delete(t);return}return n}async function ge(e,t){if(e&&!Re(e,I,A.hash)){const n=await $n(e);if(!n)return;const a=Dn(n);for(const r of Je){const s=r.exec(a);if(s)return{id:Ae(e),invalidating:t,route:r,params:Jt(s),url:e}}}}function Dn(e){return Yt(A.hash?e.hash.replace(/^#/,"").replace(/[?#].+/,""):e.pathname.slice(I.length))||"/"}function Ae(e){return(A.hash?e.hash.replace(/^#/,""):e.pathname)+e.search}function Vt({url:e,type:t,intent:n,delta:a}){let r=!1;const s=nt(y,n,e,t);a!==void 0&&(s.navigation.delta=a);const i={...s.navigation,cancel:()=>{r=!0,s.reject(new Error("navigation cancelled"))}};return te||Xe.forEach(o=>o(i)),r?null:s}async function X({type:e,url:t,popped:n,keepfocus:a,noscroll:r,replace_state:s,state:i={},redirect_count:o=0,nav_token:c={},accept:l=ut,block:p=ut}){const u=$;$=c;const m=await ge(t,!1),f=e==="enter"?nt(y,m,t,e):Vt({url:t,type:e,delta:n==null?void 0:n.delta,intent:m});if(!f){p(),$===c&&($=u);return}const _=R,d=U;l(),te=!0,Se&&f.navigation.type!=="enter"&&D.navigating.set(Q.current=f.navigation);let h=m&&await tt(m);if(!h){if(Re(t,I,A.hash))return await H(t);h=await zt(t,{id:null},await G(new Ge(404,"Not Found",`Not found: ${t.pathname}`),{url:t,params:{},route:{id:null}}),404)}if(t=(m==null?void 0:m.url)||t,$!==c)return f.reject(new Error("navigation aborted")),!1;if(h.type==="redirect")if(o>=20)h=await Oe({status:500,error:await G(new Error("Redirect loop"),{url:t,params:{},route:{id:null}}),url:t,route:{id:null}});else return await pe(new URL(h.location,t).href,{},o+1,c),!1;else h.props.page.status>=400&&await D.updated.check()&&(await It(),await H(t));if(xt(),Ye(_),Ct(d),h.props.page.url.pathname!==t.pathname&&(t.pathname=h.props.page.url.pathname),i=n?n.state:i,!n){const g=s?0:1,w={[F]:R+=g,[Z]:U+=g,[kt]:i};(s?history.replaceState:history.pushState).call(history,w,"",t),s||Pn(R,U)}if(T=null,h.props.page.state=i,Se){y=h.state,h.props.page&&(h.props.page.url=t);const g=(await Promise.all(Array.from(xn,w=>w(f.navigation)))).filter(w=>typeof w=="function");if(g.length>0){let w=function(){g.forEach(b=>{Y.delete(b)})};g.push(w),g.forEach(b=>{Y.add(b)})}q.$set(h.props),We(h.props.page),Lt=!0}else $t(h,Ve,!1);const{activeElement:v}=document;await De();const j=n?n.scroll:r?Ee():null;if(dt){const g=t.hash&&document.getElementById(Gt(t));j?scrollTo(j.x,j.y):g?g.scrollIntoView():scrollTo(0,0)}const O=document.activeElement!==v&&document.activeElement!==document.body;!a&&!O&&Be(t),dt=!0,h.props.page&&Object.assign(k,h.props.page),te=!1,e==="popstate"&&Nt(U),f.fulfil(void 0),Y.forEach(g=>g(f.navigation)),D.navigating.set(Q.current=null)}async function zt(e,t,n,a){return e.origin===he&&e.pathname===location.pathname&&!Ze?await Oe({status:a,error:n,url:e,route:t}):await H(e)}function Mn(){let e,t,n;N.addEventListener("mousemove",o=>{const c=o.target;clearTimeout(e),e=setTimeout(()=>{s(c,V.hover)},20)});function a(o){o.defaultPrevented||s(o.composedPath()[0],V.tap)}N.addEventListener("mousedown",a),N.addEventListener("touchstart",a,{passive:!0});const r=new IntersectionObserver(o=>{for(const c of o)c.isIntersecting&&(Ne(new URL(c.target.href)),r.unobserve(c.target))},{threshold:0});async function s(o,c){const l=Rt(o,N),p=l===t&&c>=n;if(!l||p)return;const{url:u,external:m,download:f}=$e(l,I,A.hash);if(m||f)return;const _=me(l),d=u&&Ae(y.url)===Ae(u);if(!(_.reload||d))if(c<=_.preload_data){t=l,n=V.tap;const h=await ge(u,!1);if(!h)return;Cn(h)}else c<=_.preload_code&&(t=l,n=c,Ne(u))}function i(){r.disconnect();for(const o of N.querySelectorAll("a")){const{url:c,external:l,download:p}=$e(o,I,A.hash);if(l||p)continue;const u=me(o);u.reload||(u.preload_code===V.viewport&&r.observe(o),u.preload_code===V.eager&&Ne(c))}}Y.add(i),i()}function G(e,t){if(e instanceof ke)return e.body;const n=ye(e),a=Rn(e);return A.hooks.handleError({error:e,event:t,status:n,message:a})??{message:a}}function Vn(e,t){In(()=>(e.add(t),()=>{e.delete(t)}))}function tr(e){Vn(Xe,e)}function nr(e,t={}){return e=new URL(Ke(e)),e.origin!==he?Promise.reject(new Error("goto: invalid URL")):pe(e,t,0)}function rr(e){return Bt(e),Pt()}function Bt(e){if(typeof e=="function")ve.push(e);else{const{href:t}=new URL(e,location.href);ve.push(n=>n.href===t)}}function ar(){return je=!0,Pt()}async function or(e){if(e.type==="error"){const t=new URL(location.href),{branch:n,route:a}=y;if(!a)return;const r=await Mt(y.branch.length,n,a.errors);if(r){const s=ne({url:t,params:y.params,branch:n.slice(0,r.idx).concat(r.node),status:e.status??500,error:e.error,route:a});y=s.state,q.$set(s.props),We(s.props.page),De().then(()=>Be(y.url))}}else e.type==="redirect"?await pe(e.location,{invalidateAll:!0},0):(k.form=e.data,k.status=e.status,q.$set({form:null,page:Te(k)}),await De(),q.$set({form:e.data}),e.type==="success"&&Be(k.url))}function zn(){var t;history.scrollRestoration="manual",addEventListener("beforeunload",n=>{let a=!1;if(ht(),!te){const r=nt(y,void 0,null,"leave"),s={...r.navigation,cancel:()=>{a=!0,r.reject(new Error("navigation cancelled"))}};Xe.forEach(i=>i(s))}a?(n.preventDefault(),n.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&ht()}),(t=navigator.connection)!=null&&t.saveData||Mn(),N.addEventListener("click",async n=>{if(n.button||n.which!==1||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||n.defaultPrevented)return;const a=Rt(n.composedPath()[0],N);if(!a)return;const{url:r,external:s,target:i,download:o}=$e(a,I,A.hash);if(!r)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=me(a);if(!(a instanceof SVGAElement)&&r.protocol!==location.protocol&&!(r.protocol==="https:"||r.protocol==="http:")||o)return;const[p,u]=(A.hash?r.hash.replace(/^#/,""):r.href).split("#"),m=p===Le(location);if(s||c.reload&&(!m||!u)){Vt({url:r,type:"link"})?te=!0:n.preventDefault();return}if(u!==void 0&&m){const[,f]=y.url.href.split("#");if(f===u){if(n.preventDefault(),u===""||u==="top"&&a.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const _=a.ownerDocument.getElementById(decodeURIComponent(u));_&&(_.scrollIntoView(),_.focus())}return}if(K=!0,Ye(R),e(r),!c.replace_state)return;K=!1}n.preventDefault(),await new Promise(f=>{requestAnimationFrame(()=>{setTimeout(f,0)}),setTimeout(f,100)}),await X({type:"link",url:r,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??r.href===location.href})}),N.addEventListener("submit",n=>{if(n.defaultPrevented)return;const a=HTMLFormElement.prototype.cloneNode.call(n.target),r=n.submitter;if(((r==null?void 0:r.formTarget)||a.target)==="_blank"||((r==null?void 0:r.formMethod)||a.method)!=="get")return;const o=new URL((r==null?void 0:r.hasAttribute("formaction"))&&(r==null?void 0:r.formAction)||a.action);if(Re(o,I,!1))return;const c=n.target,l=me(c);if(l.reload)return;n.preventDefault(),n.stopPropagation();const p=new FormData(c),u=r==null?void 0:r.getAttribute("name");u&&p.append(u,(r==null?void 0:r.getAttribute("value"))??""),o.search=new URLSearchParams(p).toString(),X({type:"form",url:o,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??o.href===location.href})}),addEventListener("popstate",async n=>{var a;if(!ze){if((a=n.state)!=null&&a[F]){const r=n.state[F];if($={},r===R)return;const s=z[r],i=n.state[kt]??{},o=new URL(n.state[dn]??location.href),c=n.state[Z],l=y.url?Le(location)===Le(y.url):!1;if(c===U&&(Lt||l)){i!==k.state&&(k.state=i),e(o),z[R]=Ee(),s&&scrollTo(s.x,s.y),R=r;return}const u=r-R;await X({type:"popstate",url:o,popped:{state:i,scroll:s,delta:u},accept:()=>{R=r,U=c},block:()=>{history.go(-u)},nav_token:$})}else if(!K){const r=new URL(location.href);e(r),A.hash&&location.reload()}}}),addEventListener("hashchange",()=>{K&&(K=!1,history.replaceState({...history.state,[F]:++R,[Z]:U},"",location.href))});for(const n of document.querySelectorAll("link"))Ln.has(n.rel)&&(n.href=n.href);addEventListener("pageshow",n=>{n.persisted&&D.navigating.set(Q.current=null)});function e(n){y.url=k.url=n,D.page.set(Te(k)),D.page.notify()}}async function Bn(e,{status:t=200,error:n,node_ids:a,params:r,route:s,server_route:i,data:o,form:c}){Ze=!0;const l=new URL(location.href);let p;({params:r={},route:s={id:null}}=await ge(l,!1)||{}),p=Je.find(({id:f})=>f===s.id);let u,m=!0;try{const f=a.map(async(d,h)=>{const v=o[h];return v!=null&&v.uses&&(v.uses=qt(v.uses)),Qe({loader:A.nodes[d],url:l,params:r,route:s,parent:async()=>{const j={};for(let O=0;O<h;O+=1)Object.assign(j,(await f[O]).data);return j},server_data_node:et(v)})}),_=await Promise.all(f);if(p){const d=p.layouts;for(let h=0;h<d.length;h++)d[h]||_.splice(h,0,void 0)}u=ne({url:l,params:r,branch:_,status:t,error:n,form:c,route:p??null})}catch(f){if(f instanceof qe){await H(new URL(f.location,location.href));return}u=await Oe({status:ye(f),error:await G(f,{url:l,params:r,route:s}),url:l,route:s}),e.textContent="",m=!1}u.props.page&&(u.props.page.state={}),$t(u,e,m)}async function Ft(e,t){var s;const n=new URL(e);n.pathname=Un(e.pathname),e.pathname.endsWith("/")&&n.searchParams.append(En,"1"),n.searchParams.append(kn,t.map(i=>i?"1":"0").join(""));const a=window.fetch,r=await a(n.href,{});if(!r.ok){let i;throw(s=r.headers.get("content-type"))!=null&&s.includes("application/json")?i=await r.json():r.status===404?i="Not Found":r.status===500&&(i="Internal Error"),new ke(r.status,i)}return new Promise(async i=>{var m;const o=new Map,c=r.body.getReader(),l=new TextDecoder;function p(f){return Tt(f,{...A.decoders,Promise:_=>new Promise((d,h)=>{o.set(_,{fulfil:d,reject:h})})})}let u="";for(;;){const{done:f,value:_}=await c.read();if(f&&!u)break;for(u+=!_&&u?`
`:l.decode(_,{stream:!0});;){const d=u.indexOf(`
`);if(d===-1)break;const h=JSON.parse(u.slice(0,d));if(u=u.slice(d+1),h.type==="redirect")return i(h);if(h.type==="data")(m=h.nodes)==null||m.forEach(v=>{(v==null?void 0:v.type)==="data"&&(v.uses=qt(v.uses),v.data=p(v.data))}),i(h);else if(h.type==="chunk"){const{id:v,data:j,error:O}=h,g=o.get(v);o.delete(v),O?g.reject(p(O)):g.fulfil(p(j))}}}})}function qt(e){return{dependencies:new Set((e==null?void 0:e.dependencies)??[]),params:new Set((e==null?void 0:e.params)??[]),parent:!!(e!=null&&e.parent),route:!!(e!=null&&e.route),url:!!(e!=null&&e.url),search_params:new Set((e==null?void 0:e.search_params)??[])}}let ze=!1;function Be(e){const t=document.querySelector("[autofocus]");if(t)t.focus();else{const n=Gt(e);if(n&&document.getElementById(n)){const{x:r,y:s}=Ee();setTimeout(()=>{const i=history.state;ze=!0,location.replace(`#${n}`),A.hash&&location.replace(e.hash),history.replaceState(i,"",e.hash),scrollTo(r,s),ze=!1})}else{const r=document.body,s=r.getAttribute("tabindex");r.tabIndex=-1,r.focus({preventScroll:!0,focusVisible:!1}),s!==null?r.setAttribute("tabindex",s):r.removeAttribute("tabindex")}const a=getSelection();if(a&&a.type!=="None"){const r=[];for(let s=0;s<a.rangeCount;s+=1)r.push(a.getRangeAt(s));setTimeout(()=>{if(a.rangeCount===r.length){for(let s=0;s<a.rangeCount;s+=1){const i=r[s],o=a.getRangeAt(s);if(i.commonAncestorContainer!==o.commonAncestorContainer||i.startContainer!==o.startContainer||i.endContainer!==o.endContainer||i.startOffset!==o.startOffset||i.endOffset!==o.endOffset)return}a.removeAllRanges()}})}}}function nt(e,t,n,a){var c,l;let r,s;const i=new Promise((p,u)=>{r=p,s=u});return i.catch(()=>{}),{navigation:{from:{params:e.params,route:{id:((c=e.route)==null?void 0:c.id)??null},url:e.url},to:n&&{params:(t==null?void 0:t.params)??null,route:{id:((l=t==null?void 0:t.route)==null?void 0:l.id)??null},url:n},willUnload:!t,type:a,complete:i},fulfil:r,reject:s}}function Te(e){return{data:e.data,error:e.error,form:e.form,params:e.params,route:e.route,state:e.state,status:e.status,url:e.url}}function Fn(e){const t=new URL(e);return t.hash=decodeURIComponent(e.hash),t}function Gt(e){let t;if(A.hash){const[,,n]=e.hash.split("#",3);t=n??""}else t=e.hash.slice(1);return decodeURIComponent(t)}const sr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),ir=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),cr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),lr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),fr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),ur=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),dr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),hr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),pr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),gr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),_r=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{ke as H,yn as N,wn as P,_n as U,sr as _,vn as a,bn as b,mn as c,A as d,Yn as e,ar as f,nr as g,or as h,rr as i,tr as j,er as k,Wn as l,ir as m,cr as n,lr as o,Jn as p,fr as q,ur as r,D as s,dr as t,hr as u,pr as v,gr as w,_r as x};
