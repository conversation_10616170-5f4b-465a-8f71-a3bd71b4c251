import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as n,d as i,a as p}from"./DDiqt3uM.js";import{s as l}from"./iCEqKm8o.js";import{l as m,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function x(s,r){const t=m(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e=[["circle",{cx:"11",cy:"11",r:"8"}],["path",{d:"m21 21-4.3-4.3"}]];$(s,d({name:"search"},()=>t,{get iconNode(){return e},children:(a,f)=>{var o=n(),c=i(o);l(c,r,"default",{},null),p(a,o)},$$slots:{default:!0}}))}export{x as S};
