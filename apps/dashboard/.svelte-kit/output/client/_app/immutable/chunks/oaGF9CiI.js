import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as i,d as p,a as m}from"./DDiqt3uM.js";import{s as l}from"./iCEqKm8o.js";import{l as c,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function C(t,o){const s=c(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e=[["path",{d:"m9 18 6-6-6-6"}]];$(t,d({name:"chevron-right"},()=>s,{get iconNode(){return e},children:(a,f)=>{var r=i(),n=p(r);l(n,o,"default",{},null),m(a,r)},$$slots:{default:!0}}))}export{C};
