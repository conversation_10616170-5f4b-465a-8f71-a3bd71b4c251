import{K as d,aQ as g,u as c,h as m,aR as l,aS as b,i as p,aT as h,aj as v}from"./DDiqt3uM.js";function x(n=!1){const s=d,e=s.l.u;if(!e)return;let f=()=>h(s.s);if(n){let a=0,t={};const _=v(()=>{let i=!1;const r=s.s;for(const o in r)r[o]!==t[o]&&(t[o]=r[o],i=!0);return i&&a++,a});f=()=>p(_)}e.b.length&&g(()=>{u(s,f),l(e.b)}),c(()=>{const a=m(()=>e.m.map(b));return()=>{for(const t of a)typeof t=="function"&&t()}}),e.a.length&&c(()=>{u(s,f),l(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}export{x as i};
