import{t as u,v as o,w as l,x as g,y,z as h,C as p,A as w,B as E,H as O,D as v,E as R,F as b,G as f}from"./DDiqt3uM.js";function N(c,m,i=!1,_=!1,C=!1){var n=c,t="";u(()=>{var s=g;if(t===(t=m()??"")){o&&l();return}if(s.nodes_start!==null&&(y(s.nodes_start,s.nodes_end),s.nodes_start=s.nodes_end=null),t!==""){if(o){h.data;for(var e=l(),d=e;e!==null&&(e.nodeType!==p||e.data!=="");)d=e,e=w(e);if(e===null)throw E(),O;v(h,d),n=R(e);return}var r=t+"";i?r=`<svg>${r}</svg>`:_&&(r=`<math>${r}</math>`);var a=b(r);if((i||_)&&(a=f(a)),v(f(a),a.lastChild),i||_)for(;f(a);)n.before(f(a));else n.before(a)}})}export{N as h};
