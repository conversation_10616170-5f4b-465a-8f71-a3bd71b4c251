import{X as p,v as i,w as d,F as l,D as u,Y as v,Z as h,_ as m,a0 as y,a1 as g,z as c,G as b}from"./DDiqt3uM.js";function E(t,s,...n){var a=t,e=y,r;p(()=>{e!==(e=s())&&(r&&(g(r),r=null),r=m(()=>e(a,...n)))},h),i&&(a=c)}function F(t){return(s,...n)=>{var o;var a=t(...n),e;if(i)e=c,d();else{var r=a.render().trim(),_=l(r);e=b(_),s.before(e)}const f=(o=a.setup)==null?void 0:o.call(a,e);u(e,e),typeof f=="function"&&v(f)}}export{F as c,E as s};
