import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as n,d as l,a as m}from"./DDiqt3uM.js";import{s as p}from"./iCEqKm8o.js";import{l as d,s as c}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function y(r,t){const s=d(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"}]];$(r,c({name:"mail"},()=>s,{get iconNode(){return a},children:(e,f)=>{var o=n(),i=l(o);p(i,t,"default",{},null),m(e,o)},$$slots:{default:!0}}))}export{y as M};
