import{d as p,g,w as h,a as b}from"./rjRVMZXi.js";import{a as R,o as v}from"./RnwjOPnl.js";function O(e){return Object.keys(e).reduce((n,t)=>e[t]===void 0?n:n+`${t}:${e[t]};`,"")}function j(e){return e?!0:void 0}O({position:"absolute",opacity:0,"pointer-events":"none",margin:0,transform:"translateX(-100%)"});function q(e){if(e!==null)return""}function y(e){function n(t){return t(e),()=>{}}return{subscribe:n}}function V(e){if(!P)return null;const n=document.querySelector(`[data-melt-id="${e}"]`);return m(n)?n:null}const E=e=>new Proxy(e,{get(n,t,r){return Reflect.get(n,t,r)},ownKeys(n){return Reflect.ownKeys(n).filter(t=>t!=="action")}}),A=e=>typeof e=="function";S("empty");function S(e,n){const{stores:t,action:r,returned:a}=n??{},s=(()=>{if(t&&a)return p(t,i=>{const o=a(i);if(A(o)){const c=(...f)=>E({...o(...f),[`data-melt-${e}`]:"",action:r??l});return c.action=r??l,c}return E({...o,[`data-melt-${e}`]:"",action:r??l})});{const i=a,o=i==null?void 0:i();if(A(o)){const c=(...f)=>E({...o(...f),[`data-melt-${e}`]:"",action:r??l});return c.action=r??l,y(c)}return y(E({...o,[`data-melt-${e}`]:"",action:r??l}))}})(),u=r??(()=>{});return u.subscribe=s.subscribe,u}function Y(e){const n=s=>s?`${e}-${s}`:e,t=s=>`data-melt-${e}${s?`-${s}`:""}`,r=s=>`[data-melt-${e}${s?`-${s}`:""}]`;return{name:n,attribute:t,selector:r,getEl:s=>document.querySelector(r(s))}}const P=typeof document<"u",$=e=>typeof e=="function";function x(e){return e instanceof Element}function m(e){return e instanceof HTMLElement}function B(e){const n=e.getAttribute("aria-disabled"),t=e.getAttribute("disabled"),r=e.hasAttribute("data-disabled");return!!(n==="true"||t!==null||r)}function T(e){return e!==null&&typeof e=="object"}function _(e){return T(e)&&"subscribe"in e}function k(...e){return(...n)=>{for(const t of e)typeof t=="function"&&t(...n)}}function l(){}function w(e,n,t,r){const a=Array.isArray(n)?n:[n];return a.forEach(s=>e.addEventListener(s,t,r)),()=>{a.forEach(s=>e.removeEventListener(s,t,r))}}function X(e,n,t,r){const a=Array.isArray(n)?n:[n];if(typeof t=="function"){const s=M(u=>t(u));return a.forEach(u=>e.addEventListener(u,s,r)),()=>{a.forEach(u=>e.removeEventListener(u,s,r))}}return()=>void 0}function L(e){const n=e.currentTarget;if(!m(n))return null;const t=new CustomEvent(`m-${e.type}`,{detail:{originalEvent:e},cancelable:!0});return n.dispatchEvent(t),t}function M(e){return n=>{const t=L(n);if(!(t!=null&&t.defaultPrevented))return e(n)}}const z=e=>{try{v(e)}catch{return e}},W=e=>{try{R(e)}catch{return e}};function N(e,...n){const t={};for(const r of Object.keys(e))n.includes(r)||(t[r]=e[r]);return t}function D(e){return{...e,get:()=>g(e)}}D.writable=function(e){const n=h(e);let t=e;return{subscribe:n.subscribe,set(r){n.set(r),t=r},update(r){const a=r(t);n.set(a),t=a},get(){return t}}};D.derived=function(e,n){const t=new Map,r=()=>{const s=Array.isArray(e)?e.map(u=>u.get()):e.get();return n(s)};return{get:r,subscribe:s=>{const u=[];return(Array.isArray(e)?e:[e]).forEach(o=>{u.push(o.subscribe(()=>{s(r())}))}),s(r()),t.set(s,u),()=>{const o=t.get(s);if(o)for(const c of o)c();t.delete(s)}}}};const d={ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",ARROW_UP:"ArrowUp",END:"End",ENTER:"Enter",ESCAPE:"Escape",HOME:"Home",PAGE_DOWN:"PageDown",PAGE_UP:"PageUp",SPACE:" ",TAB:"Tab"},F=[d.ARROW_DOWN,d.PAGE_UP,d.HOME],K=[d.ARROW_UP,d.PAGE_DOWN,d.END],J=[...F,...K],Q=[d.ENTER,d.SPACE];function U(e,n){let t;const r=p(e,s=>{t==null||t(),t=n(s)}).subscribe(l),a=()=>{r(),t==null||t()};return W(a),a}b(void 0,e=>{function n(r){e(r),e(void 0)}return w(document,"pointerup",n,{passive:!1,capture:!0})});const C=b(void 0,e=>{function n(r){r&&r.key===d.ESCAPE&&e(r),e(void 0)}return w(document,"keydown",n,{passive:!1})}),Z=(e,n={})=>{let t=l;function r(a={}){t();const s={enabled:!0,...a},u=_(s.enabled)?s.enabled:b(s.enabled);t=k(C.subscribe(i=>{var c;if(!i||!g(u))return;const o=i.target;if(!(!m(o)||o.closest("[data-escapee]")!==e)){if(i.preventDefault(),s.ignore){if($(s.ignore)){if(s.ignore(i))return}else if(Array.isArray(s.ignore)&&s.ignore.length>0&&s.ignore.some(f=>f&&o===f))return}(c=s.handler)==null||c.call(s,i)}}),U(u,i=>{i?e.dataset.escapee="":delete e.dataset.escapee}))}return r(n),{update:r,destroy(){e.removeAttribute("data-escapee"),t()}}};b(!1),b(!1),b(void 0);const H={isDateDisabled:void 0,isDateUnavailable:void 0,value:void 0,preventDeselect:!1,numberOfMonths:1,pagedNavigation:!1,weekStartsOn:0,fixedWeeks:!1,calendarLabel:"Event Date",locale:"en",minValue:void 0,maxValue:void 0,disabled:!1,readonly:!1,weekdayFormat:"narrow"};({...N(H,"isDateDisabled","isDateUnavailable","value","locale","disabled","readonly","minValue","maxValue","weekdayFormat")});export{J as F,Q as S,X as a,$ as b,x as c,w as d,k as e,Y as f,U as g,P as h,m as i,j,d as k,V as l,S as m,l as n,N as o,q as p,B as q,z as r,O as s,Z as u,D as w};
