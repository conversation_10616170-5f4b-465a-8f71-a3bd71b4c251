import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as i,d as n,a as l}from"./DDiqt3uM.js";import{s as p}from"./iCEqKm8o.js";import{l as m,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function k(e,r){const s=m(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"m9 12 2 2 4-4"}]];$(e,d({name:"circle-check"},()=>s,{get iconNode(){return t},children:(c,f)=>{var o=i(),a=n(o);p(a,r,"default",{},null),l(c,o)},$$slots:{default:!0}}))}export{k as C};
