import{X as Y,a1 as C,_ as j,ac as I,v as k,L as z,a$ as D,b0 as F,Y as X,b1 as J,b2 as Q,b3 as W,b4 as Z,i as m,b5 as x,b6 as rr,a5 as q,b7 as ar}from"./DDiqt3uM.js";import{l as er,f as ir,g as sr,d as tr,j as ur,n as fr,k as or,o as lr}from"./DWulv87v.js";import{t as cr,c as nr,s as _r}from"./DE2v8SHj.js";function dr(r,a){var e=void 0,i;Y(()=>{e!==(e=a())&&(i&&(C(i),i=null),e&&(i=j(()=>{I(()=>e(r))})))})}function M(r,a={},e,i){for(var s in e){var f=e[s];a[s]!==f&&(e[s]==null?r.style.removeProperty(s):r.style.setProperty(s,f,i))}}function vr(r,a,e,i){var s=r.__style;if(k||s!==a){var f=cr(a,i);(!k||f!==r.getAttribute("style"))&&(f==null?r.removeAttribute("style"):r.style.cssText=f),r.__style=a}else i&&(Array.isArray(i)?(M(r,e==null?void 0:e[0],i[0]),M(r,e==null?void 0:e[1],i[1],"important")):M(r,e,i));return i}function L(r,a,e=!1){if(r.multiple){if(a==null)return;if(!z(a))return D();for(var i of r.options)i.selected=a.includes(T(i));return}for(i of r.options){var s=T(i);if(F(s,a)){i.selected=!0;return}}(!e||a!==void 0)&&(r.selectedIndex=-1)}function H(r){var a=new MutationObserver(()=>{L(r,r.__value)});a.observe(r,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),X(()=>{a.disconnect()})}function Sr(r,a,e=a){var i=!0;er(r,"change",s=>{var f=s?"[selected]":":checked",d;if(r.multiple)d=[].map.call(r.querySelectorAll(f),T);else{var v=r.querySelector(f)??r.querySelector("option:not([disabled])");d=v&&T(v)}e(d)}),I(()=>{var s=a();if(L(r,s,i),i&&s===void 0){var f=r.querySelector(":checked");f!==null&&(s=T(f),e(s))}r.__value=s,i=!1}),H(r)}function T(r){return"__value"in r?r.__value:r.value}const E=Symbol("class"),N=Symbol("style"),U=Symbol("is custom element"),V=Symbol("is html");function Er(r){if(k){var a=!1,e=()=>{if(!a){if(a=!0,r.hasAttribute("value")){var i=r.value;O(r,"value",null),r.value=i}if(r.hasAttribute("checked")){var s=r.checked;O(r,"checked",null),r.checked=s}}};r.__on_r=e,ar(e),or()}}function Nr(r,a){var e=P(r);e.value===(e.value=a??void 0)||r.value===a&&(a!==0||r.nodeName!=="PROGRESS")||(r.value=a??"")}function br(r,a){a?r.hasAttribute("selected")||r.setAttribute("selected",""):r.removeAttribute("selected")}function O(r,a,e,i){var s=P(r);k&&(s[a]=r.getAttribute(a),a==="src"||a==="srcset"||a==="href"&&r.nodeName==="LINK")||s[a]!==(s[a]=e)&&(a==="loading"&&(r[Q]=e),e==null?r.removeAttribute(a):typeof e!="string"&&B(r).includes(a)?r[a]=e:r.setAttribute(a,e))}function hr(r,a,e,i,s=!1){var f=P(r),d=f[U],v=!f[V];let b=k&&d;b&&q(!1);var o=a||{},y=r.tagName==="OPTION";for(var S in a)S in e||(e[S]=null);e.class?e.class=nr(e.class):(i||e[E])&&(e.class=null),e[N]&&(e.style??(e.style=null));var h=B(r);for(const t in e){let u=e[t];if(y&&t==="value"&&u==null){r.value=r.__value="",o[t]=u;continue}if(t==="class"){var w=r.namespaceURI==="http://www.w3.org/1999/xhtml";_r(r,w,u,i,a==null?void 0:a[E],e[E]),o[t]=u,o[E]=e[E];continue}if(t==="style"){vr(r,u,a==null?void 0:a[N],e[N]),o[t]=u,o[N]=e[N];continue}var A=o[t];if(!(u===A&&!(u===void 0&&r.hasAttribute(t)))){o[t]=u;var c=t[0]+t[1];if(c!=="$$")if(c==="on"){const n={},g="$$"+t;let l=t.slice(2);var p=lr(l);if(ir(l)&&(l=l.slice(0,-7),n.capture=!0),!p&&A){if(u!=null)continue;r.removeEventListener(l,o[g],n),o[g]=null}if(u!=null)if(p)r[`__${l}`]=u,tr([l]);else{let G=function(K){o[t].call(this,K)};o[g]=sr(l,r,G,n)}else p&&(r[`__${l}`]=void 0)}else if(t==="style")O(r,t,u);else if(t==="autofocus")ur(r,!!u);else if(!d&&(t==="__value"||t==="value"&&u!=null))r.value=r.__value=u;else if(t==="selected"&&y)br(r,u);else{var _=t;v||(_=fr(_));var $=_==="defaultValue"||_==="defaultChecked";if(u==null&&!d&&!$)if(f[t]=null,_==="value"||_==="checked"){let n=r;const g=a===void 0;if(_==="value"){let l=n.defaultValue;n.removeAttribute(_),n.defaultValue=l,n.value=n.__value=g?l:null}else{let l=n.defaultChecked;n.removeAttribute(_),n.defaultChecked=l,n.checked=g?l:!1}}else r.removeAttribute(t);else $||h.includes(_)&&(d||typeof u!="string")?r[_]=u:typeof u!="function"&&O(r,_,u)}}}return b&&q(!0),o}function Tr(r,a,e=[],i=[],s,f=!1){J(e,i,d=>{var v=void 0,b={},o=r.nodeName==="SELECT",y=!1;if(Y(()=>{var h=a(...d.map(m)),w=hr(r,v,h,s,f);y&&o&&"value"in h&&L(r,h.value);for(let c of Object.getOwnPropertySymbols(b))h[c]||C(b[c]);for(let c of Object.getOwnPropertySymbols(h)){var A=h[c];c.description===x&&(!v||A!==v[c])&&(b[c]&&C(b[c]),b[c]=j(()=>dr(r,()=>A))),w[c]=A}v=w}),o){var S=r;I(()=>{L(S,v.value,!0),H(S)})}y=!0})}function P(r){return r.__attributes??(r.__attributes={[U]:r.nodeName.includes("-"),[V]:r.namespaceURI===W})}var R=new Map;function B(r){var a=R.get(r.nodeName);if(a)return a;R.set(r.nodeName,a=[]);for(var e,i=r,s=Element.prototype;s!==i;){e=rr(i);for(var f in e)e[f].set&&a.push(f);i=Z(i)}return a}export{Tr as a,vr as b,Nr as c,Sr as d,Er as r,O as s};
