import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as d,d as c,a as i}from"./DDiqt3uM.js";import{s as l}from"./iCEqKm8o.js";import{l as p,s as $}from"./C-ZVHnwW.js";import{I as h}from"./CkoRhfQ8.js";function y(o,t){const r=p(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s=[["path",{d:"M12 8V4H8"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2"}],["path",{d:"M2 14h2"}],["path",{d:"M20 14h2"}],["path",{d:"M15 13v2"}],["path",{d:"M9 13v2"}]];h(o,$({name:"bot"},()=>r,{get iconNode(){return s},children:(a,m)=>{var e=d(),n=c(e);l(n,t,"default",{},null),i(a,e)},$$slots:{default:!0}}))}function x(o,t){const r=p(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"}],["circle",{cx:"12",cy:"7",r:"4"}]];h(o,$({name:"user"},()=>r,{get iconNode(){return s},children:(a,m)=>{var e=d(),n=c(e);l(n,t,"default",{},null),i(a,e)},$$slots:{default:!0}}))}export{y as B,x as U};
