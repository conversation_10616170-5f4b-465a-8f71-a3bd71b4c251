import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as i,d as n,a as l}from"./DDiqt3uM.js";import{s as p}from"./iCEqKm8o.js";import{l as m,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function x(s,r){const e=m(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t=[["circle",{cx:"12",cy:"12",r:"10"}]];$(s,d({name:"circle"},()=>e,{get iconNode(){return t},children:(a,f)=>{var o=i(),c=n(o);p(c,r,"default",{},null),l(a,o)},$$slots:{default:!0}}))}export{x as C};
