const x=t=>t;function g(t){const o=t-1;return o*o*o+1}function h(t){const o=typeof t=="string"&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return o?[parseFloat(o[1]),o[2]||"px"]:[t,"px"]}function b(t,{delay:o=0,duration:p=400,easing:i=x}={}){const c=+getComputedStyle(t).opacity;return{delay:o,duration:p,easing:i,css:n=>`opacity: ${n*c}`}}function v(t,{delay:o=0,duration:p=400,easing:i=g,x:c=0,y:n=0,opacity:d=0}={}){const a=getComputedStyle(t),$=+a.opacity,r=a.transform==="none"?"":a.transform,e=$*(1-d),[y,l]=h(c),[u,m]=h(n);return{delay:o,duration:p,easing:i,css:(_,f)=>`
			transform: ${r} translate(${(1-_)*y}${l}, ${(1-_)*u}${m});
			opacity: ${$-e*f}`}}function F(t,{delay:o=0,duration:p=400,easing:i=g,axis:c="y"}={}){const n=getComputedStyle(t),d=+n.opacity,a=c==="y"?"height":"width",$=parseFloat(n[a]),r=c==="y"?["top","bottom"]:["left","right"],e=r.map(s=>`${s[0].toUpperCase()}${s.slice(1)}`),y=parseFloat(n[`padding${e[0]}`]),l=parseFloat(n[`padding${e[1]}`]),u=parseFloat(n[`margin${e[0]}`]),m=parseFloat(n[`margin${e[1]}`]),_=parseFloat(n[`border${e[0]}Width`]),f=parseFloat(n[`border${e[1]}Width`]);return{delay:o,duration:p,easing:i,css:s=>`overflow: hidden;opacity: ${Math.min(s*20,1)*d};${a}: ${s*$}px;padding-${r[0]}: ${s*y}px;padding-${r[1]}: ${s*l}px;margin-${r[0]}: ${s*u}px;margin-${r[1]}: ${s*m}px;border-${r[0]}-width: ${s*_}px;border-${r[1]}-width: ${s*f}px;min-${a}: 0`}}function w(t,{delay:o=0,duration:p=400,easing:i=g,start:c=0,opacity:n=0}={}){const d=getComputedStyle(t),a=+d.opacity,$=d.transform==="none"?"":d.transform,r=1-c,e=a*(1-n);return{delay:o,duration:p,easing:i,css:(y,l)=>`
			transform: ${$} scale(${1-r*l});
			opacity: ${a-e*l}
		`}}export{b as a,w as b,v as f,F as s};
