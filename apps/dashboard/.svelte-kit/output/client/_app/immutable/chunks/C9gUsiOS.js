import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as p,d as c,a as i}from"./DDiqt3uM.js";import{s as d}from"./iCEqKm8o.js";import{l,s as m}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function M(o,s){const t=l(s,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["circle",{cx:"9",cy:"7",r:"4"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}]];$(o,m({name:"users"},()=>t,{get iconNode(){return a},children:(e,f)=>{var r=p(),n=c(r);d(n,s,"default",{},null),i(e,r)},$$slots:{default:!0}}))}export{M as U};
