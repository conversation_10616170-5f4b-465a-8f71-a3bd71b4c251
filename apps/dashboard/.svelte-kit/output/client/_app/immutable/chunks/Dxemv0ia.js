import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as c,d as i,a as p}from"./DDiqt3uM.js";import{s as l}from"./iCEqKm8o.js";import{l as m,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function x(e,o){const s=m(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"}],["circle",{cx:"12",cy:"12",r:"3"}]];$(e,d({name:"eye"},()=>s,{get iconNode(){return t},children:(a,f)=>{var r=c(),n=i(r);l(n,o,"default",{},null),p(a,r)},$$slots:{default:!0}}))}export{x as E};
