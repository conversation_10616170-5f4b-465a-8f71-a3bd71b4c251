import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as p,d as l,a as i}from"./DDiqt3uM.js";import{s as d}from"./iCEqKm8o.js";import{l as m,s as c}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function y(t,o){const r=m(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["path",{d:"M5 12h14"}],["path",{d:"M12 5v14"}]];$(t,c({name:"plus"},()=>r,{get iconNode(){return a},children:(e,f)=>{var s=p(),n=l(s);d(n,o,"default",{},null),i(e,s)},$$slots:{default:!0}}))}export{y as P};
