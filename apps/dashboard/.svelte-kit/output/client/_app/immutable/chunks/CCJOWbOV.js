import{o as a,s as e,l as n}from"./BjLTCHJw.js";const r=a({email:e().email(),code:e().length(6,"Must be a 6-digit code").regex(/^\d+$/,"Must be a 6-digit code")}),m=a({email:e().email("Invalid email address"),password:e().min(8,"Password must be at least 8 characters long").max(16,"Password must be at most 16 characters long").regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/,"For security sake, please include lowercase, uppercase letters and digits."),confirmPassword:e()}).refine(({password:s,confirmPassword:t})=>s===t,{message:"The passwords did not match"}),i=a({name:e().min(1,"The environment name is required").max(30,"The environment name can be at most 72 charaters long").regex(/^[a-z0-9+$]/i,"Environment name must be alphanumeric")}),c=a({email:e().email()}),l=a({currentPassword:e().min(1,"You must include your current password. If you forgot it, sign out then use 'forgot password' on the sign in page.").optional(),newPassword1:e().min(6,"The new password must be at least 6 charaters long").max(72,"The new password can be at most 72 charaters long"),newPassword2:e().min(6,"The new password must be at least 6 charaters long").max(72,"The new password can be at most 72 charaters long")}).refine(({newPassword1:s,newPassword2:t})=>s===t,"The passwords don't match"),d=a({currentPassword:e().min(1,"You must provide your current password to delete your account. If you forgot it, sign out then use 'forgot password' on the sign in page.")}),h=a({first_name:e().min(2,"First name is required").max(500,"First name too long"),last_name:e().min(2,"Last name is required").max(500,"Last name too long"),email:e().email("Email is required").max(500,"Email too long"),company_name:e().max(500,"Company too long"),phone:e().max(100,"Phone number").optional(),message_body:e().max(2e3,"Message too long. Must be no more than 2000 character").default("")}),u=a({name:e().min(2,"Name is required").max(100,"Name must be less than 100 characters"),email:e().email("Please enter a valid email address").max(255,"Email must be less than 255 characters"),website:e().url("Please enter a valid website URL").max(255,"Website URL must be less than 255 characters").optional().or(n("")),description:e().min(10,"Please provide at least 10 characters describing your needs").max(2e3,"Description must be less than 2000 characters")}),p=a({full_name:e().min(1,"Name is required").max(50,"Name must be less than 50 characters"),company_name:e().min(1,"Company name is required. If this is a hobby project or personal app, please put your name.").max(50,"Company name must be less than 50 characters"),website:e().min(1,"Company website is required. An app store URL is a good alternative if you don't have a website.").max(50,"Name must be less than 50 characters")});export{i as a,h as b,l as c,d,c as e,u as m,r as o,p,m as s};
