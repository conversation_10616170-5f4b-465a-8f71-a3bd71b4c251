import{ag as h,ah as j,ai as R,i as _,aj as B,ak as Y,al as y,j as K,x as S,am as M,an as N,ao as U,h as q,P as $,ap as z,aq as C,ar as I,as as m,at as p,af as E,au as A,av as G,aw as Z}from"./DDiqt3uM.js";import{c as F}from"./B82PTGnX.js";const H={get(e,r){if(!e.exclude.includes(r))return _(e.version),r in e.special?e.special[r]():e.props[r]},set(e,r,i){if(!(r in e.special)){var n=S;try{m(e.parent_effect),e.special[r]=Q({get[r](){return e.props[r]}},r,R)}finally{m(n)}}return e.special[r](i),I(e.version),!0},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},deleteProperty(e,r){return e.exclude.includes(r)||(e.exclude.push(r),I(e.version)),!0},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function X(e,r){return new Proxy({props:e,exclude:r,special:{},version:N(0),parent_effect:S},H)}const J={get(e,r){let i=e.props.length;for(;i--;){let n=e.props[i];if(p(n)&&(n=n()),typeof n=="object"&&n!==null&&r in n)return n[r]}},set(e,r,i){let n=e.props.length;for(;n--;){let s=e.props[n];p(s)&&(s=s());const u=h(s,r);if(u&&u.set)return u.set(i),!0}return!1},getOwnPropertyDescriptor(e,r){let i=e.props.length;for(;i--;){let n=e.props[i];if(p(n)&&(n=n()),typeof n=="object"&&n!==null&&r in n){const s=h(n,r);return s&&!s.configurable&&(s.configurable=!0),s}}},has(e,r){if(r===E||r===A)return!1;for(let i of e.props)if(p(i)&&(i=i()),i!=null&&r in i)return!0;return!1},ownKeys(e){const r=[];for(let i of e.props)if(p(i)&&(i=i()),!!i){for(const n in i)r.includes(n)||r.push(n);for(const n of Object.getOwnPropertySymbols(i))r.includes(n)||r.push(n)}return r}};function k(...e){return new Proxy({props:e},J)}function Q(e,r,i,n){var O;var s=!$||(i&z)!==0,u=(i&U)!==0,D=(i&G)!==0,a=n,v=!0,w=()=>(v&&(v=!1,a=D?q(n):n),a),l;if(u){var g=E in e||A in e;l=((O=h(e,r))==null?void 0:O.set)??(g&&r in e?t=>e[r]=t:void 0)}var o,b=!1;u?[o,b]=F(()=>e[r]):o=e[r],o===void 0&&n!==void 0&&(o=w(),l&&(s&&j(),l(o)));var f;if(s?f=()=>{var t=e[r];return t===void 0?w():(v=!0,t)}:f=()=>{var t=e[r];return t!==void 0&&(a=void 0),t===void 0?a:t},s&&!(i&R))return f;if(l){var L=e.$$legacy;return function(t,d){return arguments.length>0?((!s||!d||L||b)&&l(d?f():t),t):f()}}var P=!1,c=(i&C?B:Y)(()=>(P=!1,f()));u&&_(c);var T=S;return function(t,d){if(arguments.length>0){const x=d?_(c):s&&u?y(t):t;return K(c,x),P=!0,a!==void 0&&(a=x),t}return Z&&P||T.f&M?c.v:_(c)}}export{X as l,Q as p,k as s};
