import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as p,d as c,a as i}from"./DDiqt3uM.js";import{s as d}from"./iCEqKm8o.js";import{l,s as m}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function x(r,o){const s=l(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"}]];$(r,m({name:"copy"},()=>s,{get iconNode(){return e},children:(a,f)=>{var t=p(),n=c(t);d(n,o,"default",{},null),i(a,t)},$$slots:{default:!0}}))}export{x as C};
