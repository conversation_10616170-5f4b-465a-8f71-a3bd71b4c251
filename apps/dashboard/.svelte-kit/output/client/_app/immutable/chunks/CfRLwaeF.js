import"./CWj6FrbW.js";import"./DhRTwODG.js";import{p as f,f as m,h as p,aT as e,c as v,r as u,a as _,e as $}from"./DDiqt3uM.js";import{s as h}from"./iCEqKm8o.js";import{a as g}from"./C36Ip9GY.js";import{i as b}from"./B_FgA42l.js";import{l as r,p as y}from"./C-ZVHnwW.js";import{c as o}from"./Bf9nHHn7.js";var C=m("<div><!></div>");function A(d,a){const l=r(a,["children","$$slots","$$events","$$legacy"]),n=r(l,["class"]);f(a,!1);let t=y(a,"class",8,void 0);b();var s=C();g(s,c=>({class:c,...n}),[()=>(e(o),e(t()),p(()=>o("bg-card text-card-foreground rounded-lg border shadow-sm",t())))]);var i=v(s);h(i,a,"default",{},null),u(s),_(d,s),$()}var x=m("<div><!></div>");function B(d,a){const l=r(a,["children","$$slots","$$events","$$legacy"]),n=r(l,["class"]);f(a,!1);let t=y(a,"class",8,void 0);b();var s=x();g(s,c=>({class:c,...n}),[()=>(e(o),e(t()),p(()=>o("p-6 pt-0",t())))]);var i=v(s);h(i,a,"default",{},null),u(s),_(d,s),$()}export{A as C,B as a};
