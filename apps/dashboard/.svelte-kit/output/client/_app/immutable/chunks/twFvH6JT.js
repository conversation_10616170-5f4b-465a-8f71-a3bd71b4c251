function Pt(t,e){t.indexOf(e)===-1&&t.push(e)}const vt=(t,e,n)=>Math.min(Math.max(n,t),e),m={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},M=t=>typeof t=="number",I=t=>Array.isArray(t)&&!M(t[0]),Wt=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t};function Dt(t,e){return I(t)?t[Wt(0,t.length,e)]:t}const wt=(t,e,n)=>-n*t+n*e+t,Et=()=>{},w=t=>t,Y=(t,e,n)=>e-t===0?1:(n-t)/(e-t);function Ot(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Y(0,e,s);t.push(wt(n,1,i))}}function bt(t){const e=[0];return Ot(e,t-1),e}function St(t,e=bt(t.length),n=w){const s=t.length,i=s-e.length;return i>0&&Ot(e,i),r=>{let o=0;for(;o<s-2&&!(r<e[o+1]);o++);let a=vt(0,1,Y(e[o],e[o+1],r));return a=Dt(n,o)(a),wt(t[o],t[o+1],a)}}const At=t=>Array.isArray(t)&&M(t[0]),tt=t=>typeof t=="object"&&!!t.createAnimation,S=t=>typeof t=="function",it=t=>typeof t=="string",C={ms:t=>t*1e3,s:t=>t/1e3};function Rt(t,e){return e?t*(1e3/e):0}const xt=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Ht=1e-7,Ct=12;function Ft(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=xt(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>Ht&&++a<Ct);return o}function H(t,e,n,s){if(t===e&&n===s)return w;const i=r=>Ft(r,0,1,t,n);return r=>r===0||r===1?r:xt(i(r),e,s)}const Vt=(t,e="end")=>n=>{n=e==="end"?Math.min(n,.999):Math.max(n,.001);const s=n*t,i=e==="end"?Math.floor(s):Math.ceil(s);return vt(0,1,i/t)},_t={ease:H(.25,.1,.25,1),"ease-in":H(.42,0,1,1),"ease-in-out":H(.42,0,.58,1),"ease-out":H(0,0,.58,1)},$t=/\((.*?)\)/;function ct(t){if(S(t))return t;if(At(t))return H(...t);const e=_t[t];if(e)return e;if(t.startsWith("steps")){const n=$t.exec(t);if(n){const s=n[1].split(",");return Vt(parseFloat(s[0]),s[1].trim())}}return w}class jt{constructor(e,n=[0,1],{easing:s,duration:i=m.duration,delay:r=m.delay,endDelay:o=m.endDelay,repeat:a=m.repeat,offset:f,direction:l="normal",autoplay:u=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=w,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise((c,v)=>{this.resolve=c,this.reject=v}),s=s||m.easing,tt(s)){const c=s.createAnimation(n);s=c.easing,n=c.keyframes||n,i=c.duration||i}this.repeat=a,this.easing=I(s)?w:ct(s),this.updateDuration(i);const h=St(n,f,I(s)?s.map(ct):w);this.tick=c=>{var v;r=r;let g=0;this.pauseTime!==void 0?g=this.pauseTime:g=(c-this.startTime)*this.rate,this.t=g,g/=1e3,g=Math.max(g-r,0),this.playState==="finished"&&this.pauseTime===void 0&&(g=this.totalDuration);const T=g/this.duration;let _=Math.floor(T),E=T%1;!E&&T>=1&&(E=1),E===1&&_--;const $=_%2;(l==="reverse"||l==="alternate"&&$||l==="alternate-reverse"&&!$)&&(E=1-E);const W=g>=this.totalDuration?1:Math.min(E,1),z=h(this.easing(W));e(z),this.pauseTime===void 0&&(this.playState==="finished"||g>=this.totalDuration+o)?(this.playState="finished",(v=this.resolve)===null||v===void 0||v.call(this,z)):this.playState!=="idle"&&(this.frameRequestId=requestAnimationFrame(this.tick))},u&&this.play()}play(){const e=performance.now();this.playState="running",this.pauseTime!==void 0?this.startTime=e-this.pauseTime:this.startTime||(this.startTime=e),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var e;this.playState="idle",this.frameRequestId!==void 0&&cancelAnimationFrame(this.frameRequestId),(e=this.reject)===null||e===void 0||e.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(e){this.duration=e,this.totalDuration=e*(this.repeat+1)}get currentTime(){return this.t}set currentTime(e){this.pauseTime!==void 0||this.rate===0?this.pauseTime=e:this.startTime=performance.now()-e/this.rate}get playbackRate(){return this.rate}set playbackRate(e){this.rate=e}}class qt{setAnimation(e){this.animation=e,e==null||e.finished.then(()=>this.clearAnimation()).catch(()=>{})}clearAnimation(){this.animation=this.generator=void 0}}const X=new WeakMap;function Tt(t){return X.has(t)||X.set(t,{transforms:[],values:new Map}),X.get(t)}function Bt(t,e){return t.has(e)||t.set(e,new qt),t.get(e)}const Nt=["","X","Y","Z"],Ut=["translate","scale","rotate","skew"],G={x:"translateX",y:"translateY",z:"translateZ"},lt={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},Gt={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:lt,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:w},skew:lt},V=new Map,rt=t=>`--motion-${t}`,K=["x","y","z"];Ut.forEach(t=>{Nt.forEach(e=>{K.push(t+e),V.set(rt(t+e),Gt[t])})});const Kt=(t,e)=>K.indexOf(t)-K.indexOf(e),Yt=new Set(K),zt=t=>Yt.has(t),kt=(t,e)=>{G[e]&&(e=G[e]);const{transforms:n}=Tt(t);Pt(n,e),t.style.transform=Xt(n)},Xt=t=>t.sort(Kt).reduce(Zt,"").trim(),Zt=(t,e)=>`${t} ${e}(var(${rt(e)}))`,et=t=>t.startsWith("--"),ut=new Set;function Jt(t){if(!ut.has(t)){ut.add(t);try{const{syntax:e,initialValue:n}=V.has(t)?V.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch{}}}const Z=(t,e)=>document.createElement("div").animate(t,e),ft={cssRegisterProperty:()=>typeof CSS<"u"&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{Z({opacity:[1]})}catch{return!1}return!0},finished:()=>!!Z({opacity:[0,1]},{duration:.001}).finished,linearEasing:()=>{try{Z({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0}},J={},L={};for(const t in ft)L[t]=()=>(J[t]===void 0&&(J[t]=ft[t]()),J[t]);const Qt=.015,te=(t,e)=>{let n="";const s=Math.round(e/Qt);for(let i=0;i<s;i++)n+=t(Y(0,s-1,i))+", ";return n.substring(0,n.length-2)},dt=(t,e)=>S(t)?L.linearEasing()?`linear(${te(t,e)})`:m.easing:At(t)?ee(t):t,ee=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`;function ne(t,e){for(let n=0;n<t.length;n++)t[n]===null&&(t[n]=n?t[n-1]:e());return t}const se=t=>Array.isArray(t)?t:[t];function nt(t){return G[t]&&(t=G[t]),zt(t)?rt(t):t}const B={get:(t,e)=>{e=nt(e);let n=et(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&n!==0){const s=V.get(e);s&&(n=s.initialValue)}return n},set:(t,e,n)=>{e=nt(e),et(e)?t.style.setProperty(e,n):t.style[e]=n}};function Lt(t,e=!0){if(!(!t||t.playState==="finished"))try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch{}}function ie(t,e){var n;let s=(e==null?void 0:e.toDefaultUnit)||w;const i=t[t.length-1];if(it(i)){const r=((n=i.match(/(-?[\d.]+)([a-z%]*)/))===null||n===void 0?void 0:n[2])||"";r&&(s=o=>o+r)}return s}function re(){return window.__MOTION_DEV_TOOLS_RECORD}function oe(t,e,n,s={},i){const r=re(),o=s.record!==!1&&r;let a,{duration:f=m.duration,delay:l=m.delay,endDelay:u=m.endDelay,repeat:h=m.repeat,easing:c=m.easing,persist:v=!1,direction:g,offset:T,allowWebkitAcceleration:_=!1,autoplay:E=!0}=s;const $=Tt(t),W=zt(e);let z=L.waapi();W&&kt(t,e);const y=nt(e),j=Bt($.values,y),O=V.get(y);return Lt(j.animation,!(tt(c)&&j.generator)&&s.record!==!1),()=>{const q=()=>{var d,D;return(D=(d=B.get(t,y))!==null&&d!==void 0?d:O==null?void 0:O.initialValue)!==null&&D!==void 0?D:0};let p=ne(se(n),q);const at=ie(p,O);if(tt(c)){const d=c.createAnimation(p,e!=="opacity",q,y,j);c=d.easing,p=d.keyframes||p,f=d.duration||f}if(et(y)&&(L.cssRegisterProperty()?Jt(y):z=!1),W&&!L.linearEasing()&&(S(c)||I(c)&&c.some(S))&&(z=!1),z){O&&(p=p.map(A=>M(A)?O.toDefaultUnit(A):A)),p.length===1&&(!L.partialKeyframes()||o)&&p.unshift(q());const d={delay:C.ms(l),duration:C.ms(f),endDelay:C.ms(u),easing:I(c)?void 0:dt(c,f),direction:g,iterations:h+1,fill:"both"};a=t.animate({[y]:p,offset:T,easing:I(c)?c.map(A=>dt(A,f)):void 0},d),a.finished||(a.finished=new Promise((A,Mt)=>{a.onfinish=A,a.oncancel=Mt}));const D=p[p.length-1];a.finished.then(()=>{v||(B.set(t,y,D),a.cancel())}).catch(Et),_||(a.playbackRate=1.000001)}else if(i&&W)p=p.map(d=>typeof d=="string"?parseFloat(d):d),p.length===1&&p.unshift(parseFloat(q())),a=new i(d=>{B.set(t,y,at?at(d):d)},p,Object.assign(Object.assign({},s),{duration:f,easing:c}));else{const d=p[p.length-1];B.set(t,y,O&&M(d)?O.toDefaultUnit(d):d)}return o&&r(t,e,p,{duration:f,delay:l,easing:c,repeat:h,offset:T},"motion-one"),j.setAnimation(a),a&&!E&&a.pause(),a}}const ae=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function ot(t,e){return typeof t=="string"?t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}const ce=t=>t(),le=(t,e,n=m.duration)=>new Proxy({animations:t.map(ce).filter(Boolean),duration:n,options:e},fe),ue=t=>t.animations[0],fe={get:(t,e)=>{const n=ue(t);switch(e){case"duration":return t.duration;case"currentTime":return C.s((n==null?void 0:n[e])||0);case"playbackRate":case"playState":return n==null?void 0:n[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(de)).catch(Et)),t.finished;case"stop":return()=>{t.animations.forEach(s=>Lt(s))};case"forEachNative":return s=>{t.animations.forEach(i=>s(i,t))};default:return typeof(n==null?void 0:n[e])>"u"?void 0:()=>t.animations.forEach(s=>s[e]())}},set:(t,e,n)=>{switch(e){case"currentTime":n=C.ms(n);case"playbackRate":for(let s=0;s<t.animations.length;s++)t.animations[s][e]=n;return!0}return!1}},de=t=>t.finished;function he(t,e,n){return S(t)?t(e,n):t}function pe(t){return function(n,s,i={}){n=ot(n);const r=n.length,o=[];for(let a=0;a<r;a++){const f=n[a];for(const l in s){const u=ae(i,l);u.delay=he(u.delay,a,r);const h=oe(f,l,s[l],u,t);o.push(h)}}return le(o,i,i.duration)}}const P=pe(jt);function ge(t,e){var n={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(n[s]=t[s]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,s=Object.getOwnPropertySymbols(t);i<s.length;i++)e.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(t,s[i])&&(n[s[i]]=t[s[i]]);return n}const me={any:0,all:1};function ye(t,e,{root:n,margin:s,amount:i="any"}={}){if(typeof IntersectionObserver>"u")return()=>{};const r=ot(t),o=new WeakMap,a=l=>{l.forEach(u=>{const h=o.get(u.target);if(u.isIntersecting!==!!h)if(u.isIntersecting){const c=e(u);S(c)?o.set(u.target,c):f.unobserve(u.target)}else h&&(h(u),o.delete(u.target))})},f=new IntersectionObserver(a,{root:n,rootMargin:s,threshold:typeof i=="number"?i:me[i]});return r.forEach(l=>f.observe(l)),()=>f.disconnect()}const N=new WeakMap;let b;function ve(t,e){if(e){const{inlineSize:n,blockSize:s}=e[0];return{width:n,height:s}}else return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}function we({target:t,contentRect:e,borderBoxSize:n}){var s;(s=N.get(t))===null||s===void 0||s.forEach(i=>{i({target:t,contentSize:e,get size(){return ve(t,n)}})})}function Ee(t){t.forEach(we)}function Oe(){typeof ResizeObserver>"u"||(b=new ResizeObserver(Ee))}function be(t,e){b||Oe();const n=ot(t);return n.forEach(s=>{let i=N.get(s);i||(i=new Set,N.set(s,i)),i.add(e),b==null||b.observe(s)}),()=>{n.forEach(s=>{const i=N.get(s);i==null||i.delete(e),i!=null&&i.size||b==null||b.unobserve(s)})}}const U=new Set;let F;function Se(){F=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};U.forEach(n=>n(e))},window.addEventListener("resize",F)}function Ae(t){return U.add(t),F||Se(),()=>{U.delete(t),!U.size&&F&&(F=void 0)}}function xe(t,e){return S(t)?Ae(t):be(t,e)}const Te=50,ht=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),ze=()=>({time:0,x:ht(),y:ht()}),Le={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function pt(t,e,n,s){const i=n[e],{length:r,position:o}=Le[e],a=i.current,f=n.time;i.current=t[`scroll${o}`],i.scrollLength=t[`scroll${r}`]-t[`client${r}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=Y(0,i.scrollLength,i.current);const l=s-f;i.velocity=l>Te?0:Rt(i.current-a,l)}function Ie(t,e,n){pt(t,"x",e,n),pt(t,"y",e,n),e.time=n}function Me(t,e){let n={x:0,y:0},s=t;for(;s&&s!==e;)if(s instanceof HTMLElement)n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if(s instanceof SVGGraphicsElement&&"getBBox"in s){const{top:i,left:r}=s.getBBox();for(n.x+=r,n.y+=i;s&&s.tagName!=="svg";)s=s.parentNode}return n}const Pe={All:[[0,0],[1,1]]},st={start:0,center:.5,end:1};function gt(t,e,n=0){let s=0;if(st[t]!==void 0&&(t=st[t]),it(t)){const i=parseFloat(t);t.endsWith("px")?s=i:t.endsWith("%")?t=i/100:t.endsWith("vw")?s=i/100*document.documentElement.clientWidth:t.endsWith("vh")?s=i/100*document.documentElement.clientHeight:t=i}return M(t)&&(s=e*t),n+s}const We=[0,0];function De(t,e,n,s){let i=Array.isArray(t)?t:We,r=0,o=0;return M(t)?i=[t,t]:it(t)&&(t=t.trim(),t.includes(" ")?i=t.split(" "):i=[t,st[t]?t:"0"]),r=gt(i[0],n,s),o=gt(i[1],e),r-o}const Re={x:0,y:0};function He(t,e,n){let{offset:s=Pe.All}=n;const{target:i=t,axis:r="y"}=n,o=r==="y"?"height":"width",a=i!==t?Me(i,t):Re,f=i===t?{width:t.scrollWidth,height:t.scrollHeight}:{width:i.clientWidth,height:i.clientHeight},l={width:t.clientWidth,height:t.clientHeight};e[r].offset.length=0;let u=!e[r].interpolate;const h=s.length;for(let c=0;c<h;c++){const v=De(s[c],l[o],f[o],a[r]);!u&&v!==e[r].interpolatorOffsets[c]&&(u=!0),e[r].offset[c]=v}u&&(e[r].interpolate=St(bt(h),e[r].offset),e[r].interpolatorOffsets=[...e[r].offset]),e[r].progress=e[r].interpolate(e[r].current)}function Ce(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!=t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}function Fe(t,e,n,s={}){const i=s.axis||"y";return{measure:()=>Ce(t,s.target,n),update:r=>{Ie(t,n,r),(s.offset||s.target)&&He(t,n,s)},notify:S(e)?()=>e(n):Ve(e,n[i])}}function Ve(t,e){return t.pause(),t.forEachNative((n,{easing:s})=>{var i,r;if(n.updateDuration)s||(n.easing=w),n.updateDuration(1);else{const o={duration:1e3};s||(o.easing="linear"),(r=(i=n.effect)===null||i===void 0?void 0:i.updateTiming)===null||r===void 0||r.call(i,o)}}),()=>{t.currentTime=e.progress}}const R=new WeakMap,mt=new WeakMap,Q=new WeakMap,yt=t=>t===document.documentElement?window:t;function It(t,e={}){var{container:n=document.documentElement}=e,s=ge(e,["container"]);let i=Q.get(n);i||(i=new Set,Q.set(n,i));const r=ze(),o=Fe(n,t,r,s);if(i.add(o),!R.has(n)){const l=()=>{const h=performance.now();for(const c of i)c.measure();for(const c of i)c.update(h);for(const c of i)c.notify()};R.set(n,l);const u=yt(n);window.addEventListener("resize",l,{passive:!0}),n!==document.documentElement&&mt.set(n,xe(n,l)),u.addEventListener("scroll",l,{passive:!0})}const a=R.get(n),f=requestAnimationFrame(a);return()=>{var l;typeof t!="function"&&t.stop(),cancelAnimationFrame(f);const u=Q.get(n);if(!u||(u.delete(o),u.size))return;const h=R.get(n);R.delete(n),h&&(yt(n).removeEventListener("scroll",h),(l=mt.get(n))===null||l===void 0||l(),window.removeEventListener("resize",h))}}const x={duration:{fast:.2,normal:.4},easing:{easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out"},stagger:{normal:.1}},k=()=>window.matchMedia("(prefers-reduced-motion: reduce)").matches,_e=(t,e={})=>{if(k())return;const{speed:n=.5,direction:s="up",offset:i=["start end","end start"]}=e,r=typeof t=="string"?document.querySelector(t):t;if(!r)return;const o=s==="up"?-n:n;It(P(r,{transform:[`translateY(${-50*o}px)`,`translateY(${50*o}px)`]}),{target:r,offset:i})},$e=(t,e={})=>{if(k())return;const{delay:n=0,stagger:s=x.stagger.normal,duration:i=x.duration.normal}=e;document.querySelectorAll(t).forEach((o,a)=>{P(o,{opacity:0,transform:"translateY(30px) scale(0.95)"},{duration:0}),ye(o,()=>{P(o,{opacity:1,transform:"translateY(0px) scale(1)"},{duration:i,delay:n+a*s,easing:x.easing.easeOut})},{margin:"0px 0px -10% 0px"})})},je=t=>{if(k())return;const e=document.querySelector(t);if(!e)return;_e(e,{speed:.3,direction:"up"});const n=e.querySelector(".hero-content");n&&It(P(n,{opacity:[1,.3],transform:["scale(1)","scale(0.95)"]}),{target:e,offset:["start start","end start"]})},qe=t=>{if(k())return;document.querySelectorAll(t).forEach(n=>{let s=null;n.addEventListener("mouseenter",()=>{s&&s.stop(),s=P(n,{transform:"scale(1.05)"},{duration:x.duration.fast,easing:x.easing.easeOut})}),n.addEventListener("mouseleave",()=>{s&&s.stop(),s=P(n,{transform:"scale(1)"},{duration:x.duration.fast,easing:x.easing.easeOut})})})};export{x as A,P as a,$e as b,qe as c,je as h,ye as i,k as p,It as s};
