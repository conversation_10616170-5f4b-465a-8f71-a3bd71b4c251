import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as p,d,a as i}from"./DDiqt3uM.js";import{s as m}from"./iCEqKm8o.js";import{l,s as c}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function y(s,o){const t=l(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e=[["path",{d:"m6 9 6 6 6-6"}]];$(s,c({name:"chevron-down"},()=>t,{get iconNode(){return e},children:(n,f)=>{var r=p(),a=d(r);m(a,o,"default",{},null),i(n,r)},$$slots:{default:!0}}))}export{y as C};
