import"./CWj6FrbW.js";import"./DhRTwODG.js";import{p as F,b as v,d as b,a as m,e as G,aT as g,h as H,i as K,ak as L}from"./DDiqt3uM.js";import{s as q}from"./iCEqKm8o.js";import{i as I}from"./B_FgA42l.js";import{b as n}from"./B9BVeOQN.js";import{l as E,p as f,s as M}from"./C-ZVHnwW.js";import"./BGh_Dfnt.js";import{e as s}from"./DWulv87v.js";import{i as Q}from"./2C89X9tI.js";import{e as T}from"./CzTs9bhI.js";import{a as R}from"./D-ywOz1J.js";import{a as V}from"./C36Ip9GY.js";import{b as A}from"./Dqu9JXqq.js";import{b as C}from"./ChutyBgo.js";import{c as D}from"./Bf9nHHn7.js";function j(u,a){const l=[];return a.builders.forEach(o=>{const c=o.action(u);c&&l.push(c)}),{destroy:()=>{l.forEach(o=>{o.destroy&&o.destroy()})}}}function S(u){const a={};return u.forEach(l=>{Object.keys(l).forEach(o=>{o!=="action"&&(a[o]=l[o])})}),a}function U(u,a){const l=E(a,["children","$$slots","$$events","$$legacy"]),o=E(l,["href","type","builders","el"]);F(a,!1);let c=f(a,"href",24,()=>{}),y=f(a,"type",24,()=>{}),e=f(a,"builders",24,()=>[]),d=f(a,"el",28,()=>{});const w={"data-button-root":""};I();var r=v(),O=b(r);{var z=h=>{var _=v(),B=b(_);T(B,()=>c()?"a":"button",!1,(i,P)=>{A(i,t=>d(t),()=>d()),R(i,(t,J)=>j==null?void 0:j(t,J),()=>({builders:e()})),V(i,t=>({type:c()?void 0:y(),href:c(),tabindex:"0",...t,...o,...w}),[()=>S(e())]),s("click",i,function(t){n.call(this,a,t)}),s("change",i,function(t){n.call(this,a,t)}),s("keydown",i,function(t){n.call(this,a,t)}),s("keyup",i,function(t){n.call(this,a,t)}),s("mouseenter",i,function(t){n.call(this,a,t)}),s("mouseleave",i,function(t){n.call(this,a,t)}),s("mousedown",i,function(t){n.call(this,a,t)}),s("pointerdown",i,function(t){n.call(this,a,t)}),s("mouseup",i,function(t){n.call(this,a,t)}),s("pointerup",i,function(t){n.call(this,a,t)});var k=v(),N=b(k);q(N,a,"default",{},null),m(P,k)}),m(h,_)},x=h=>{var _=v(),B=b(_);T(B,()=>c()?"a":"button",!1,(i,P)=>{A(i,t=>d(t),()=>d()),V(i,()=>({type:c()?void 0:y(),href:c(),tabindex:"0",...o,...w})),s("click",i,function(t){n.call(this,a,t)}),s("change",i,function(t){n.call(this,a,t)}),s("keydown",i,function(t){n.call(this,a,t)}),s("keyup",i,function(t){n.call(this,a,t)}),s("mouseenter",i,function(t){n.call(this,a,t)}),s("mouseleave",i,function(t){n.call(this,a,t)}),s("mousedown",i,function(t){n.call(this,a,t)}),s("pointerdown",i,function(t){n.call(this,a,t)}),s("mouseup",i,function(t){n.call(this,a,t)}),s("pointerup",i,function(t){n.call(this,a,t)});var k=v(),N=b(k);q(N,a,"default",{},null),m(P,k)}),m(h,_)};Q(O,h=>{g(e()),H(()=>e()&&e().length)?h(z):h(x,!1)})}m(u,r),G()}function ft(u,a){const l=E(a,["children","$$slots","$$events","$$legacy"]),o=E(l,["class","variant","size","builders"]);F(a,!1);let c=f(a,"class",8,void 0),y=f(a,"variant",8,"default"),e=f(a,"size",8,"default"),d=f(a,"builders",24,()=>[]);I();{let w=L(()=>(g(D),g(C),g(y()),g(e()),g(c()),H(()=>D(C({variant:y(),size:e(),className:c()})))));U(u,M({get builders(){return d()},get class(){return K(w)},type:"button"},()=>o,{$$events:{click(r){n.call(this,a,r)},keydown(r){n.call(this,a,r)}},children:(r,O)=>{var z=v(),x=b(z);q(x,a,"default",{},null),m(r,z)},$$slots:{default:!0}}))}G()}export{ft as B};
