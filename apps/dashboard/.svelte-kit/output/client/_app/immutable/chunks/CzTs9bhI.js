import{v as t,w as c,z as l,aV as y,X as h,Z as x,_ as N,aW as T,D as b,G as A,a6 as C,a5 as d,E as u,x as S,ab as w,aa as D,a1 as F}from"./DDiqt3uM.js";import{i as G,c as m}from"./DWulv87v.js";function k(E,g,p,i,M,P){let v=t;t&&c();var n,r,e=null;t&&l.nodeType===y&&(e=l,c());var _=t?l:E,s;h(()=>{const a=g()||null;var o=p||a==="svg"?T:null;a!==n&&(s&&(a===null?w(s,()=>{s=null,r=null}):a===r?D(s):(F(s),m(!1))),a&&a!==r&&(s=N(()=>{if(e=t?e:o?document.createElementNS(o,a):document.createElement(a),b(e,e),i){t&&G(a)&&e.append(document.createComment(""));var f=t?A(e):e.appendChild(C());t&&(f===null?d(!1):u(f)),i(e,f)}S.nodes_end=e,_.before(e)})),n=a,n&&(r=n),m(!0))},x),v&&(d(!0),u(_))}export{k as e};
