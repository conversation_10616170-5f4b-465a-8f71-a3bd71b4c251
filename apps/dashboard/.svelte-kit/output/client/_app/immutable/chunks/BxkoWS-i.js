import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as l,d as c,a as i}from"./DDiqt3uM.js";import{s as d}from"./iCEqKm8o.js";import{l as $,s as p}from"./C-ZVHnwW.js";import{I as u}from"./CkoRhfQ8.js";function x(t,e){const o=$(e,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["line",{x1:"12",x2:"12",y1:"20",y2:"10"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16"}]];u(t,p({name:"chart-no-axes-column-increasing"},()=>o,{get iconNode(){return a},children:(r,m)=>{var n=l(),s=c(n);d(s,e,"default",{},null),i(r,n)},$$slots:{default:!0}}))}function N(t,e){const o=$(e,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"}],["path",{d:"M20 3v4"}],["path",{d:"M22 5h-4"}],["path",{d:"M4 17v2"}],["path",{d:"M5 18H3"}]];u(t,p({name:"sparkles"},()=>o,{get iconNode(){return a},children:(r,m)=>{var n=l(),s=c(n);d(s,e,"default",{},null),i(r,n)},$$slots:{default:!0}}))}function z(t,e){const o=$(e,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["circle",{cx:"12",cy:"12",r:"10"}],["circle",{cx:"12",cy:"12",r:"6"}],["circle",{cx:"12",cy:"12",r:"2"}]];u(t,p({name:"target"},()=>o,{get iconNode(){return a},children:(r,m)=>{var n=l(),s=c(n);d(s,e,"default",{},null),i(r,n)},$$slots:{default:!0}}))}function M(t,e){const o=$(e,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17"}],["polyline",{points:"16 7 22 7 22 13"}]];u(t,p({name:"trending-up"},()=>o,{get iconNode(){return a},children:(r,m)=>{var n=l(),s=c(n);d(s,e,"default",{},null),i(r,n)},$$slots:{default:!0}}))}export{x as C,N as S,z as T,M as a};
