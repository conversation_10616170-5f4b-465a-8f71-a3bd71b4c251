import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as c,d as l,a as i}from"./DDiqt3uM.js";import{s as p}from"./iCEqKm8o.js";import{l as m,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function k(s,o){const t=m(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e=[["circle",{cx:"12",cy:"12",r:"10"}],["polyline",{points:"12 6 12 12 16 14"}]];$(s,d({name:"clock"},()=>t,{get iconNode(){return e},children:(a,f)=>{var r=c(),n=l(r);p(n,o,"default",{},null),i(a,r)},$$slots:{default:!0}}))}export{k as C};
