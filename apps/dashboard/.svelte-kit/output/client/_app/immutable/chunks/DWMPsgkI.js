import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as i,d as l,a as c}from"./DDiqt3uM.js";import{s as p}from"./iCEqKm8o.js";import{l as d,s as m}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function I(e,o){const s=d(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56"}]];$(e,m({name:"loader-circle"},()=>s,{get iconNode(){return t},children:(a,f)=>{var r=i(),n=l(r);p(n,o,"default",{},null),c(a,r)},$$slots:{default:!0}}))}export{I as L};
