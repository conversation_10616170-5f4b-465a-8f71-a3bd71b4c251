const l={name:"Full Stack Starter Pack Blog"},a=[{title:"How we built a beautiful 41kb App website with this template",description:"How to use this template you to bootstrap your own site.",link:"/blog/how_we_built_our_41kb_saas_website",date:"2024-03-10"},{title:"Example Blog Post 2",description:"Even more example content!",link:"/blog/awesome_post",date:"2022-9-23"},{title:"Example Blog Post",description:"A sample blog post, showing our blog engine",link:"/blog/example_blog_post",date:"2023-03-13"}];for(const t of a)if(!t.parsedDate){const e=t.date.split("-");t.parsedDate=new Date(parseInt(e[0]),parseInt(e[1])-1,parseInt(e[2]))}const i=a.sort((t,e)=>{var o,s;return(((o=e.parsedDate)==null?void 0:o.getTime())??0)-(((s=t.parsedDate)==null?void 0:s.getTime())??0)});export{l as b,i as s};
