import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as i,d as l,a as m}from"./DDiqt3uM.js";import{s as p}from"./iCEqKm8o.js";import{l as c,s as d}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function v(s,o){const r=c(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t=[["line",{x1:"4",x2:"20",y1:"12",y2:"12"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18"}]];$(s,d({name:"menu"},()=>r,{get iconNode(){return t},children:(n,f)=>{var e=i(),a=l(e);p(a,o,"default",{},null),m(n,e)},$$slots:{default:!0}}))}export{v as M};
