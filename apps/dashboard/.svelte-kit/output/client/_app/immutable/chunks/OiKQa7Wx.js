import{a6 as j,X as se,aE as ee,E as X,v as H,G as ue,w as te,i as Z,ak as ve,a2 as de,a3 as _e,a4 as $,a5 as Y,z as R,C as oe,aF as ce,_ as J,a9 as he,aG as q,aH as F,a7 as Ee,aI as k,m as me,an as y,aJ as ae,L as pe,aa as re,ab as Ae,aK as Te,aL as z,ae as Ie,a1 as ne,aM as we,A as xe,aN as Ce,aO as Ne,aP as Me,x as De}from"./DDiqt3uM.js";function Re(l,r){return r}function He(l,r,e){for(var u=l.items,v=[],d=r.length,s=0;s<d;s++)Ce(r[s].e,v,!0);var h=d>0&&v.length===0&&e!==null;if(h){var A=e.parentNode;Ne(A),A.append(e),u.clear(),x(l,r[0].prev,r[d-1].next)}Me(v,()=>{for(var p=0;p<d;p++){var o=r[p];h||(u.delete(o.k),x(l,o.prev,o.next)),ne(o.e,!h)}})}function Le(l,r,e,u,v,d=null){var s=l,h={flags:r,items:new Map,first:null},A=(r&ee)!==0;if(A){var p=l;s=H?X(ue(p)):p.appendChild(j())}H&&te();var o=null,N=!1,T=new Map,M=ve(()=>{var _=e();return pe(_)?_:_==null?[]:ae(_)}),n,t;function i(){Se(t,n,h,T,s,v,r,u,e),d!==null&&(n.length===0?o?re(o):o=J(()=>d(s)):o!==null&&Ae(o,()=>{o=null}))}se(()=>{t??(t=De),n=Z(M);var _=n.length;if(N&&_===0)return;N=_===0;let m=!1;if(H){var I=de(s)===_e;I!==(_===0)&&(s=$(),X(s),Y(!1),m=!0)}if(H){for(var C=null,c,a=0;a<_;a++){if(R.nodeType===oe&&R.data===ce){s=R,m=!0,Y(!1);break}var f=n[a],E=u(f,a);c=K(R,h,C,null,f,E,a,v,r,e),h.items.set(E,c),C=c}_>0&&X($())}if(H)_===0&&d&&(o=J(()=>d(s)));else if(he()){var S=new Set,L=Ee;for(a=0;a<_;a+=1){f=n[a],E=u(f,a);var D=h.items.get(E)??T.get(E);D?r&(q|F)&&fe(D,f,a,r):(c=K(null,h,null,null,f,E,a,v,r,e,!0),T.set(E,c)),S.add(E)}for(const[w,b]of h.items)S.has(w)||L.skipped_effects.add(b.e);L.add_callback(i)}else i();m&&Y(!0),Z(M)}),H&&(s=R)}function Se(l,r,e,u,v,d,s,h,A){var P,U,g,Q;var p=(s&we)!==0,o=(s&(q|F))!==0,N=r.length,T=e.items,M=e.first,n=M,t,i=null,_,m=[],I=[],C,c,a,f;if(p)for(f=0;f<N;f+=1)C=r[f],c=h(C,f),a=T.get(c),a!==void 0&&((P=a.a)==null||P.measure(),(_??(_=new Set)).add(a));for(f=0;f<N;f+=1){if(C=r[f],c=h(C,f),a=T.get(c),a===void 0){var E=u.get(c);if(E!==void 0){u.delete(c),T.set(c,E);var S=i?i.next:n;x(e,i,E),x(e,E,S),B(E,S,v),i=E}else{var L=n?n.e.nodes_start:v;i=K(L,e,i,i===null?e.first:i.next,C,c,f,d,s,A)}T.set(c,i),m=[],I=[],n=i.next;continue}if(o&&fe(a,C,f,s),a.e.f&z&&(re(a.e),p&&((U=a.a)==null||U.unfix(),(_??(_=new Set)).delete(a))),a!==n){if(t!==void 0&&t.has(a)){if(m.length<I.length){var D=I[0],w;i=D.prev;var b=m[0],G=m[m.length-1];for(w=0;w<m.length;w+=1)B(m[w],D,v);for(w=0;w<I.length;w+=1)t.delete(I[w]);x(e,b.prev,G.next),x(e,i,b),x(e,G,D),n=D,i=G,f-=1,m=[],I=[]}else t.delete(a),B(a,n,v),x(e,a.prev,a.next),x(e,a,i===null?e.first:i.next),x(e,i,a),i=a;continue}for(m=[],I=[];n!==null&&n.k!==c;)n.e.f&z||(t??(t=new Set)).add(n),I.push(n),n=n.next;if(n===null)continue;a=n}m.push(a),i=a,n=a.next}if(n!==null||t!==void 0){for(var O=t===void 0?[]:ae(t);n!==null;)n.e.f&z||O.push(n),n=n.next;var V=O.length;if(V>0){var ie=s&ee&&N===0?v:null;if(p){for(f=0;f<V;f+=1)(g=O[f].a)==null||g.measure();for(f=0;f<V;f+=1)(Q=O[f].a)==null||Q.fix()}He(e,O,ie)}}p&&Ie(()=>{var W;if(_!==void 0)for(a of _)(W=a.a)==null||W.apply()}),l.first=e.first&&e.first.e,l.last=i&&i.e;for(var le of u.values())ne(le.e);u.clear()}function fe(l,r,e,u){u&q&&k(l.v,r),u&F?k(l.i,e):l.i=e}function K(l,r,e,u,v,d,s,h,A,p,o){var N=(A&q)!==0,T=(A&Te)===0,M=N?T?me(v,!1,!1):y(v):v,n=A&F?y(s):s,t={i:n,v:M,k:d,a:null,e:null,prev:e,next:u};try{if(l===null){var i=document.createDocumentFragment();i.append(l=j())}return t.e=J(()=>h(l,M,n,p),H),t.e.prev=e&&e.e,t.e.next=u&&u.e,e===null?o||(r.first=t):(e.next=t,e.e.next=t.e),u!==null&&(u.prev=t,u.e.prev=t.e),t}finally{}}function B(l,r,e){for(var u=l.next?l.next.e.nodes_start:e,v=r?r.e.nodes_start:e,d=l.e.nodes_start;d!==null&&d!==u;){var s=xe(d);v.before(d),d=s}}function x(l,r,e){r===null?l.first=e:(r.next=e,r.e.next=e&&e.e),e!==null&&(e.prev=r,e.e.prev=r&&r.e)}export{Le as e,Re as i};
