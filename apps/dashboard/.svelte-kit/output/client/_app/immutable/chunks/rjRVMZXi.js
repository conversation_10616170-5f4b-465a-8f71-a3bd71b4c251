import{a0 as b,h as m,aU as q,aR as x}from"./DDiqt3uM.js";function h(e,r,n){if(e==null)return r(void 0),n&&n(void 0),b;const t=m(()=>e.subscribe(r,n));return t.unsubscribe?()=>t.unsubscribe():t}const a=[];function z(e,r){return{subscribe:A(e,r).subscribe}}function A(e,r=b){let n=null;const t=new Set;function o(u){if(q(e,u)&&(e=u,n)){const i=!a.length;for(const s of t)s[1](),a.push(s,e);if(i){for(let s=0;s<a.length;s+=2)a[s][0](a[s+1]);a.length=0}}}function f(u){o(u(e))}function l(u,i=b){const s=[u,i];return t.add(s),t.size===1&&(n=r(o,f)||b),u(e),()=>{t.delete(s),t.size===0&&n&&(n(),n=null)}}return{set:o,update:f,subscribe:l}}function B(e,r,n){const t=!Array.isArray(e),o=t?[e]:e;if(!o.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const f=r.length<2;return z(n,(l,u)=>{let i=!1;const s=[];let d=0,p=b;const y=()=>{if(d)return;p();const c=r(t?s[0]:s,l,u);f?l(c):p=typeof c=="function"?c:b},_=o.map((c,g)=>h(c,w=>{s[g]=w,d&=~(1<<g),i&&y()},()=>{d|=1<<g}));return i=!0,y(),function(){x(_),p(),i=!1}})}function E(e){return{subscribe:e.subscribe.bind(e)}}function R(e){let r;return h(e,n=>r=n)(),r}export{z as a,B as d,R as g,E as r,h as s,A as w};
