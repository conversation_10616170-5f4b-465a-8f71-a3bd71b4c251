import"./CWj6FrbW.js";import"./DhRTwODG.js";import{p as n,f,h as d,aT as r,c as u,r as _,a as h,e as v}from"./DDiqt3uM.js";import{s as $}from"./iCEqKm8o.js";import{a as g}from"./C36Ip9GY.js";import{i as x}from"./B_FgA42l.js";import{l as e,p as y}from"./C-ZVHnwW.js";import{c as o}from"./Bf9nHHn7.js";var C=f("<p><!></p>");function w(p,s){const i=e(s,["children","$$slots","$$events","$$legacy"]),m=e(i,["class"]);n(s,!1);let t=y(s,"class",8,void 0);x();var a=C();g(a,l=>({class:l,...m}),[()=>(r(o),r(t()),d(()=>o("text-muted-foreground text-sm",t())))]);var c=u(a);$(c,s,"default",{},null),_(a),h(p,a),v()}export{w as C};
