import"./CWj6FrbW.js";import"./DhRTwODG.js";import{b as p,d as i,a as m}from"./DDiqt3uM.js";import{s as d}from"./iCEqKm8o.js";import{l,s as c}from"./C-ZVHnwW.js";import{I as $}from"./CkoRhfQ8.js";function y(r,o){const s=l(o,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e=[["path",{d:"M18 6 6 18"}],["path",{d:"m6 6 12 12"}]];$(r,c({name:"x"},()=>s,{get iconNode(){return e},children:(a,f)=>{var t=p(),n=i(t);d(n,o,"default",{},null),m(a,t)},$$slots:{default:!0}}))}export{y as X};
