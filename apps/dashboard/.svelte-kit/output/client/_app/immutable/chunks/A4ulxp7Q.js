import{x,Z as B,ay as L,az as M,ac as U,h as j,aA as q,at as z,ae as G,a0 as w,aB as K,aC as P}from"./DDiqt3uM.js";import{l as W}from"./BGGTUj09.js";import{b as Z,w as p}from"./DWulv87v.js";function A(a,r){p(()=>{a.dispatchEvent(new CustomEvent(r))})}function g(a){if(a==="float")return"cssFloat";if(a==="offset")return"cssOffset";if(a.startsWith("--"))return a;const r=a.split("-");return r.length===1?r[0]:r[0]+r.slice(1).map(i=>i[0].toUpperCase()+i.slice(1)).join("")}function S(a){const r={},i=a.split(";");for(const t of i){const[v,_]=t.split(":");if(!v||_===void 0)break;const d=g(v.trim());r[d]=_.trim()}return r}const D=a=>a;function V(a,r,i,t){var v=(a&K)!==0,_=(a&P)!==0,d=v&&_,b=(a&q)!==0,y=d?"both":v?"in":"out",h,n=r.inert,N=r.style.overflow,f,s;function T(){return p(()=>h??(h=i()(r,(t==null?void 0:t())??{},{direction:y})))}var e={is_global:b,in(){var u;if(r.inert=n,!v){s==null||s.abort(),(u=s==null?void 0:s.reset)==null||u.call(s);return}_||f==null||f.abort(),A(r,"introstart"),f=F(r,T(),s,1,()=>{A(r,"introend"),f==null||f.abort(),f=h=void 0,r.style.overflow=N})},out(u){if(!_){u==null||u(),h=void 0;return}r.inert=!0,A(r,"outrostart"),s=F(r,T(),f,0,()=>{A(r,"outroend"),u==null||u()})},stop:()=>{f==null||f.abort(),s==null||s.abort()}},c=x;if((c.transitions??(c.transitions=[])).push(e),v&&Z){var l=b;if(!l){for(var o=c.parent;o&&o.f&B;)for(;(o=o.parent)&&!(o.f&L););l=!o||(o.f&M)!==0}l&&U(()=>{j(()=>e.in())})}}function F(a,r,i,t,v){var _=t===1;if(z(r)){var d,b=!1;return G(()=>{if(!b){var c=r({direction:_?"in":"out"});d=F(a,c,i,t,v)}}),{abort:()=>{b=!0,d==null||d.abort()},deactivate:()=>d.deactivate(),reset:()=>d.reset(),t:()=>d.t()}}if(i==null||i.deactivate(),!(r!=null&&r.duration))return v(),{abort:w,deactivate:w,reset:w,t:()=>t};const{delay:y=0,css:h,tick:n,easing:N=D}=r;var f=[];if(_&&i===void 0&&(n&&n(0,1),h)){var s=S(h(0,1));f.push(s,s)}var T=()=>1-t,e=a.animate(f,{duration:y,fill:"forwards"});return e.onfinish=()=>{e.cancel();var c=(i==null?void 0:i.t())??1-t;i==null||i.abort();var l=t-c,o=r.duration*Math.abs(l),u=[];if(o>0){var I=!1;if(h)for(var O=Math.ceil(o/16.666666666666668),C=0;C<=O;C+=1){var R=c+l*N(C/O),m=S(h(R,1-R));u.push(m),I||(I=m.overflow==="hidden")}I&&(a.style.overflow="hidden"),T=()=>{var E=e.currentTime;return c+l*N(E/o)},n&&W(()=>{if(e.playState!=="running")return!1;var E=T();return n(E,1-E),!0})}e=a.animate(u,{duration:o,fill:"forwards"}),e.onfinish=()=>{T=()=>t,n==null||n(t,1-t),v()}},{abort:()=>{e&&(e.cancel(),e.effect=null,e.onfinish=w)},deactivate:()=>{v=w},reset:()=>{t===0&&(n==null||n(1,0))},t:()=>T()}}export{V as t};
