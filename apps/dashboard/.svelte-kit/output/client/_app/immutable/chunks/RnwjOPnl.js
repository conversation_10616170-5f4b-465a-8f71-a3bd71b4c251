import{J as a,u as g,K as n,L as v,M as f,N as b,O as _,P as y,h as c,Q as h,R as x,S as C,T as S,U as k,V as w,W as A}from"./DDiqt3uM.js";import{a as M,m as O,u as P}from"./DWulv87v.js";import{c as U}from"./DtGADYZa.js";function j(){var t;return f===null&&b(),((t=f).ac??(t.ac=new AbortController)).signal}function p(t){n===null&&a(),y&&n.l!==null?r(n).m.push(t):g(()=>{const e=c(t);if(typeof e=="function")return e})}function D(t){n===null&&a(),p(()=>()=>c(t))}function E(t,e,{bubbles:s=!1,cancelable:l=!1}={}){return new CustomEvent(t,{detail:e,bubbles:s,cancelable:l})}function R(){const t=n;return t===null&&a(),(e,s,l)=>{var u;const o=(u=t.s.$$events)==null?void 0:u[e];if(o){const m=v(o)?o.slice():[o],i=E(e,s,l);for(const d of m)d.call(t.x,i);return!i.defaultPrevented}return!0}}function T(t){n===null&&a(),n.l===null&&_(),r(n).b.push(t)}function $(t){n===null&&a(),n.l===null&&_(),r(n).a.push(t)}function r(t){var e=t.l;return e.u??(e.u={a:[],b:[],m:[]})}const L=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:$,beforeUpdate:T,createEventDispatcher:R,createRawSnippet:U,flushSync:h,getAbortSignal:j,getAllContexts:x,getContext:C,hasContext:S,hydrate:M,mount:O,onDestroy:D,onMount:p,setContext:k,settled:w,tick:A,unmount:P,untrack:c},Symbol.toStringTag,{value:"Module"}));export{D as a,R as c,p as o,L as s};
