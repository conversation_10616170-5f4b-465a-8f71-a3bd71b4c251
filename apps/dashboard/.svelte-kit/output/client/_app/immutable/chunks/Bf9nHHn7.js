import{a as d}from"./DE2v8SHj.js";import{t as x}from"./CVCfOWck.js";import{c as p}from"./CD_pvLCz.js";function b(...o){return x(d(o))}const A=(o,e={y:-8,x:0,start:.95,duration:150})=>{const c=getComputedStyle(o),a=c.transform==="none"?"":c.transform,u=(t,r,n)=>{const[s,l]=r,[i,f]=n;return(t-s)/(l-s)*(f-i)+i},m=t=>Object.keys(t).reduce((r,n)=>t[n]===void 0?r:r+`${n}:${t[n]};`,"");return{duration:e.duration??200,delay:0,css:t=>{const r=u(t,[0,1],[e.y??5,0]),n=u(t,[0,1],[e.x??0,0]),s=u(t,[0,1],[e.start??.95,1]);return m({transform:`${a} translate3d(${n}px, ${r}px, 0) scale(${s})`,opacity:t})},easing:p}};function S(o,e){let c;return(...a)=>{clearTimeout(c),c=setTimeout(()=>o(...a),e)}}export{b as c,S as d,A as f};
