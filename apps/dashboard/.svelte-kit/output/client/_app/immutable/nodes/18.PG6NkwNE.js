import"../chunks/CWj6FrbW.js";import{p as r,f as s,s as n,d as c,a as u,e as l,$ as i}from"../chunks/DDiqt3uM.js";import{h as d}from"../chunks/DWulv87v.js";import{S as m}from"../chunks/DF2MXVHf.js";import{d as p}from"../chunks/CCJOWbOV.js";var f=s('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function y(o,e){r(e,!0);var t=f();d(g=>{i.title="Delete Account"});var a=n(c(t),2);m(a,{get data(){return e.data.form},get schema(){return p},title:"Delete Account",editable:!0,dangerous:!0,message:"Deleting your account can not be undone.",saveButtonTitle:"Delete Account",successTitle:"Account queued for deletion",successBody:"Your account will be deleted shortly.",formTarget:"/api?/deleteAccount",fields:[{id:"currentPassword",label:"Current Password",initialValue:"",inputType:"password"}]}),u(o,t),l()}export{y as component};
