import"../chunks/CWj6FrbW.js";import{p as ft,b as q,d as l,a as t,e as ct,$ as pt,f as v,n as M,q as u,s as d,c as N,o as _t,h as y,i as ut,k as vt,r as O,t as $t}from"../chunks/DDiqt3uM.js";import{h as ht,s as gt}from"../chunks/DWulv87v.js";import{i as w}from"../chunks/2C89X9tI.js";import{c as s}from"../chunks/BCmD-YNt.js";import{a as xt}from"../chunks/D-ywOz1J.js";import{r as bt}from"../chunks/C36Ip9GY.js";import{b as yt}from"../chunks/DUGxtfU6.js";import{s as Ct}from"../chunks/C-ZVHnwW.js";import{a as Pt,b as Q,s as C}from"../chunks/B82PTGnX.js";import{s as Ft,z as kt,b as Dt,C as zt,F as At,a as Bt}from"../chunks/usILQaII.js";import{a as It,C as qt}from"../chunks/CfRLwaeF.js";import{C as wt}from"../chunks/Ckrcpd9Z.js";import"../chunks/DhRTwODG.js";import{C as Et,a as St}from"../chunks/Bls5ffmn.js";import"../chunks/DfS0riH5.js";import"../chunks/CioyQbQI.js";import{o as Vt}from"../chunks/CCJOWbOV.js";import{I as jt}from"../chunks/Cn9N4mio.js";import"../chunks/ChutyBgo.js";import{B as Gt}from"../chunks/BFbKPyUZ.js";var Ht=v("<!> <!>",1),Jt=v("<!> <!>",1),Kt=v("<!> <!>",1),Lt=v('<p class="text-destructive text-sm font-bold mt-1"> </p>'),Mt=v('<form method="post" class="grid gap-4"><input name="email" hidden/> <!> <!> <!></form>'),Nt=v("<!> <!>",1);function _r(R,P){ft(P,!0);const[g,T]=Pt(),f=()=>C(F,"$formData",g),U=()=>C(Y,"$constraints",g),E=()=>C(X,"$errors",g),S=()=>C(W,"$delayed",g),V=Ft(P.data.form,{validators:kt(Vt)}),{form:F,enhance:k,delayed:W,errors:X,constraints:Y}=V;var j=q();ht(G=>{pt.title="Confirmation"});var Z=l(j);s(Z,()=>qt,(G,tt)=>{tt(G,{class:"mt-6",children:(rt,Ot)=>{var H=Nt(),J=l(H);s(J,()=>Et,(D,z)=>{z(D,{children:(A,at)=>{var i=Ht(),c=l(i);s(c,()=>St,(p,$)=>{$(p,{class:"text-2xl font-bold text-center",children:(h,r)=>{M();var a=u("Enter your verification code");t(h,a)},$$slots:{default:!0}})});var x=d(c,2);s(x,()=>wt,(p,$)=>{$(p,{children:(h,r)=>{var a=q(),n=l(a);{var b=e=>{var o=u(`A 6-digit confirmation code has been sent to your email. Please verify
        your email to complete your account setup.`);t(e,o)},_=e=>{var o=u(`A 6-digit confirmation code has been sent to your email to confirm your
        email change.`);t(e,o)};w(n,e=>{P.data.type==="signup"?e(b):e(_,!1)})}t(h,a)},$$slots:{default:!0}})}),t(A,i)},$$slots:{default:!0}})});var ot=d(J,2);s(ot,()=>It,(D,z)=>{z(D,{children:(A,at)=>{var i=Mt(),c=N(i);bt(c);var x=d(c,2);s(x,()=>Dt,(r,a)=>{a(r,{get form(){return V},name:"code",children:(n,b)=>{var _=Kt(),e=l(_);s(e,()=>zt,(m,B)=>{B(m,{children:_t,$$slots:{default:(et,st)=>{const nt=vt(()=>st.attrs);var K=Jt(),L=l(K);s(L,()=>At,(I,mt)=>{mt(I,{children:(lt,Qt)=>{M();var dt=u("6-digit code");t(lt,dt)},$$slots:{default:!0}})});var it=d(L,2);jt(it,Ct(()=>ut(nt),()=>U().code,{get value(){return f().code},set value(I){Q(F,y(f).code=I,y(f))}})),t(et,K)}}})});var o=d(e,2);s(o,()=>Bt,(m,B)=>{B(m,{})}),t(n,_)},$$slots:{default:!0}})});var p=d(x,2);{var $=r=>{var a=Lt(),n=N(a,!0);O(a),$t(()=>gt(n,E()._errors[0])),t(r,a)};w(p,r=>{E()._errors&&r($)})}var h=d(p,2);Gt(h,{type:"submit",get disabled(){return S()},class:"w-full",children:(r,a)=>{var n=q(),b=l(n);{var _=o=>{var m=u("...");t(o,m)},e=o=>{var m=u("Verify");t(o,m)};w(b,o=>{S()?o(_):o(e,!1)})}t(r,n)},$$slots:{default:!0}}),O(i),xt(i,r=>k==null?void 0:k(r)),yt(c,()=>f().email,r=>Q(F,y(f).email=r,y(f))),t(A,i)},$$slots:{default:!0}})}),t(rt,H)},$$slots:{default:!0}})}),t(R,j),ct(),T()}export{_r as component};
