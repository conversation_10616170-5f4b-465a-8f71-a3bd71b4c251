import"../chunks/CWj6FrbW.js";import{b as Te,d as fe,a as i,p as Ne,f as u,c as t,r as e,s as o,I as ie,i as a,t as Y,k as Oe,e as Le,j as S,n as ke,u as qe,a0 as lt,al as Ue,q as Ge,$ as jt}from"../chunks/DDiqt3uM.js";import{d as Ye,s as G,r as It,h as Pt}from"../chunks/DWulv87v.js";import{G as Rt,T as vt,H as Ot,R as Nt,b as mt}from"../chunks/Di-aknNf.js";import{w as gt}from"../chunks/rjRVMZXi.js";import{o as Je}from"../chunks/RnwjOPnl.js";import{i as y}from"../chunks/2C89X9tI.js";import{s as ut}from"../chunks/DtGADYZa.js";import{s as ce,c as Lt}from"../chunks/DE2v8SHj.js";import{l as st,s as it,p}from"../chunks/C-ZVHnwW.js";import{e as Se,i as Re}from"../chunks/OiKQa7Wx.js";import{c as Qe}from"../chunks/BCmD-YNt.js";import{s as Be,b as Kt,r as ct,d as Ft}from"../chunks/C36Ip9GY.js";import{b as bt}from"../chunks/Dqu9JXqq.js";import{a as pt,s as _t}from"../chunks/B82PTGnX.js";import{p as Gt}from"../chunks/CD0KqtXh.js";import{M as xt}from"../chunks/Bxi4P_5L.js";import{X as ht}from"../chunks/rNGuVYtO.js";import{S as He}from"../chunks/QFpxkGuO.js";import{C as yt}from"../chunks/3OymkobC.js";import{S as rt,L as Ut}from"../chunks/Rh-W-Viu.js";import{s as Xe}from"../chunks/iCEqKm8o.js";import{t as at}from"../chunks/A4ulxp7Q.js";import{b as et}from"../chunks/DUGxtfU6.js";import{s as ot}from"../chunks/DSm1r-pw.js";import"../chunks/Bo96ghGo.js";import{C as Bt}from"../chunks/CXeeNO9R.js";import{C as Wt}from"../chunks/DR0c5pEj.js";import{L as wt}from"../chunks/DWMPsgkI.js";import{C as Vt}from"../chunks/C6jXKNMu.js";import{C as kt}from"../chunks/oaGF9CiI.js";import"../chunks/DhRTwODG.js";import{I as nt}from"../chunks/CkoRhfQ8.js";import{T as tt,C as dt,a as Ht,S as St}from"../chunks/BxkoWS-i.js";import{U as qt,B as Yt}from"../chunks/ng6E9JCw.js";import{C as Jt}from"../chunks/CgBAmp64.js";function Ct(x,r){const d=st(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}]];nt(x,it({name:"book-open"},()=>d,{get iconNode(){return C},children:(A,f)=>{var w=Te(),E=fe(w);Xe(E,r,"default",{},null),i(A,w)},$$slots:{default:!0}}))}function Qt(x,r){const d=st(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M12 18v-6"}],["path",{d:"m9 15 3 3 3-3"}]];nt(x,it({name:"file-down"},()=>d,{get iconNode(){return C},children:(A,f)=>{var w=Te(),E=fe(w);Xe(E,r,"default",{},null),i(A,w)},$$slots:{default:!0}}))}function Xt(x,r){const d=st(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"}]];nt(x,it({name:"filter"},()=>d,{get iconNode(){return C},children:(A,f)=>{var w=Te(),E=fe(w);Xe(E,r,"default",{},null),i(A,w)},$$slots:{default:!0}}))}function Zt(x,r){r(!r())}function $t(x,r){S(r,!a(r))}async function er(x,r){S(r,!1);try{window.location.href="/sign_out"}catch(d){console.error("Error signing out:",d)}}function tr(x,r,d){S(r,!1);const C=d().params.envSlug;window.location.href=`/dashboard/${C}/settings`}var rr=u('<span class="font-semibold text-lg text-sidebar-foreground"> </span>'),ar=u('<div class="relative"><!> <input type="text" class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"/> <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">⌘K</kbd></div>'),or=u('<div class="flex justify-center"><button class="p-2 hover:bg-sidebar-accent rounded-lg svelte-947kzy"><!></button></div>'),sr=(x,r,d)=>r(a(d)),ir=u('<span class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full"> </span>'),nr=u(" <!>",1),dr=u("<button><!> <!></button>"),lr=u('<div class="flex-1 min-w-0"><p class="text-sm font-medium text-sidebar-foreground truncate text-left"> </p> <p class="text-xs text-muted-foreground truncate text-left"> </p></div> <!>',1),vr=u('<div class="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg z-50 py-2 animate-slide-up svelte-947kzy" style="box-shadow: var(--shadow-lg);"><button class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors svelte-947kzy"><!> Settings</button> <button class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors svelte-947kzy"><!> Sign Out</button></div>'),ur=u('<aside><button><!></button> <header><div class="flex items-center space-x-2 mb-4"><div class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center"><!></div> <!></div> <!></header> <nav class="flex-1 p-4"><div class="space-y-1"></div></nav> <footer><div class="relative mt-4 pt-4 border-t border-sidebar-border"><button><div><span class="text-sm font-medium text-primary-foreground"> </span></div> <!></button> <!></div></footer></aside>');function cr(x,r){Ne(r,!0);const[d,C]=pt(),A=()=>_t(Gt,"$page",d);let f=p(r,"collapsed",15,!1),w=p(r,"isMobile",3,!1),E=p(r,"session",3,null),V=p(r,"profile",3,null);p(r,"agentType",3,"research");let Q=p(r,"navigationItems",19,()=>[]),ne=p(r,"brandName",3,"Robynn AI"),J=p(r,"searchPlaceholder",3,"Search"),M=ie(!1),g;function v(n){g&&!g.contains(n.target)&&S(M,!1)}let k=Oe(()=>{var n,c,s,l,h,z,K,j,b,I,F,re,O,W,q,ue;return{name:((n=V())==null?void 0:n.full_name)||((l=(s=(c=E())==null?void 0:c.user)==null?void 0:s.user_metadata)==null?void 0:l.full_name)||((K=(z=(h=E())==null?void 0:h.user)==null?void 0:z.email)==null?void 0:K.split("@")[0])||"User",email:((b=(j=E())==null?void 0:j.user)==null?void 0:b.email)||"<EMAIL>",initials:T(((I=V())==null?void 0:I.full_name)||((O=(re=(F=E())==null?void 0:F.user)==null?void 0:re.user_metadata)==null?void 0:O.full_name)||((ue=(q=(W=E())==null?void 0:W.user)==null?void 0:q.email)==null?void 0:ue.split("@")[0])||"User")}});function T(n){return n.split(" ").map(c=>c.charAt(0).toUpperCase()).slice(0,2).join("")}function R(n){n.onClick?n.onClick():n.href&&(window.location.href=n.href)}Je(()=>(document.addEventListener("click",v),()=>{document.removeEventListener("click",v)}));var _=ur();let D;var L=t(_);L.__click=[Zt,f];let U;var X=t(L);{var P=n=>{xt(n,{class:"w-3 h-3"})},te=n=>{ht(n,{class:"w-3 h-3"})};y(X,n=>{f()?n(P):n(te,!1)})}e(L);var Z=o(L,2);let H;var ae=t(Z),oe=t(ae),$=t(oe);Rt($,{class:"w-4 h-4 text-sidebar-primary-foreground"}),e(oe);var de=o(oe,2);{var m=n=>{var c=rr(),s=t(c,!0);e(c),Y(()=>G(s,ne())),i(n,c)};y(de,n=>{f()||n(m)})}e(ae);var N=o(ae,2);{var se=n=>{var c=ar(),s=t(c);He(s,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"});var l=o(s,2);ke(2),e(c),Y(()=>Be(l,"placeholder",J())),i(n,c)},le=n=>{var c=or(),s=t(c),l=t(s);He(l,{class:"w-4 h-4 text-muted-foreground"}),e(s),e(c),i(n,c)};y(N,n=>{f()?n(le,!1):n(se)})}e(Z);var me=o(Z,2),_e=t(me);Se(_e,21,Q,n=>n.id,(n,c)=>{var s=dr();s.__click=[sr,R,c];let l;var h=t(s);{let j=Oe(()=>f()?"":"mr-3");Qe(h,()=>a(c).icon,(b,I)=>{I(b,{get class(){return`w-4 h-4 ${a(j)??""}`}})})}var z=o(h,2);{var K=j=>{var b=nr(),I=fe(b),F=o(I);{var re=O=>{var W=ir(),q=t(W,!0);e(W),Y(()=>G(q,a(c).badge)),i(O,W)};y(F,O=>{a(c).badge&&O(re)})}Y(()=>G(I,`${a(c).label??""} `)),i(j,b)};y(z,j=>{f()||j(K)})}e(s),Y(j=>{l=ce(s,1,"flex items-center w-full px-3 py-2 text-sm rounded-lg transition-colors svelte-947kzy",null,l,j),Be(s,"title",f()?a(c).label:"")},[()=>({"text-sidebar-primary-foreground":a(c).active,"bg-sidebar-primary":a(c).active,"text-sidebar-foreground":!a(c).active,"hover:bg-sidebar-accent":!a(c).active,"justify-center":f()})]),i(n,s)}),e(_e),e(me);var xe=o(me,2);let De;var Ee=t(xe),he=t(Ee);he.__click=[$t,M];let Ie;var Me=t(he);let Pe;var B=t(Me),ge=t(B,!0);e(B),e(Me);var be=o(Me,2);{var ee=n=>{var c=lr(),s=fe(c),l=t(s),h=t(l,!0);e(l);var z=o(l,2),K=t(z,!0);e(z),e(s);var j=o(s,2);{let b=Oe(()=>a(M)?"rotate-180":"");yt(j,{get class(){return`w-4 h-4 text-muted-foreground transition-transform duration-200 ${a(b)??""}`}})}Y(()=>{G(h,a(k).name),G(K,a(k).email)}),i(n,c)};y(be,n=>{f()||n(ee)})}e(he);var ve=o(he,2);{var pe=n=>{var c=vr(),s=t(c);s.__click=[tr,M,A];var l=t(s);rt(l,{class:"w-4 h-4 mr-3"}),ke(),e(s);var h=o(s,2);h.__click=[er,M];var z=t(h);Ut(z,{class:"w-4 h-4 mr-3"}),ke(),e(h),e(c),i(n,c)};y(ve,n=>{a(M)&&!f()&&n(pe)})}e(Ee),bt(Ee,n=>g=n,()=>g),e(xe),e(_),Y((n,c,s,l,h,z)=>{D=ce(_,1,"bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative",null,D,n),U=ce(L,1,"absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors svelte-947kzy",null,U,c),H=ce(Z,1,"p-4 border-b border-sidebar-border",null,H,s),De=ce(xe,1,"p-4 border-t border-sidebar-border",null,De,l),Ie=ce(he,1,"flex items-center w-full p-2 hover:bg-sidebar-accent rounded-lg transition-colors group svelte-947kzy",null,Ie,h),Be(he,"title",f()?a(k).email:"Profile menu"),Pe=ce(Me,1,"w-8 h-8 bg-primary rounded-full flex items-center justify-center svelte-947kzy",null,Pe,z),G(ge,a(k).initials)},[()=>({"w-64":!f(),"w-16":f()&&!w(),"w-0":f()&&w(),"overflow-hidden":f()&&w()}),()=>({hidden:w()&&f()}),()=>({hidden:f()&&w()}),()=>({hidden:f()&&w()}),()=>({"justify-center":f()}),()=>({"mr-3":!f()})]),i(x,_),Le(),C()}Ye(["click"]);function ft(x,r,d,C,A,f){S(r,!1),d(null),S(C,""),S(A,""),f()&&f()()}function fr(){window.location.href="/"}var mr=u('<button class="ml-auto text-xs text-destructive hover:text-destructive/80 underline">Retry</button>'),gr=u('<div class="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg"><!> <span class="text-sm text-destructive">Something went wrong</span> <!></div>'),br=u('<pre class="text-xs text-muted-foreground mt-2 overflow-auto max-h-32"> </pre>'),pr=u('<details class="mb-6 w-full max-w-md"><summary class="cursor-pointer text-sm text-muted-foreground hover:text-foreground">Error Details</summary> <div class="mt-2 p-3 bg-muted rounded-lg text-left"><p class="text-xs font-mono text-destructive break-all"> </p> <!></div></details>'),_r=u('<button class="btn-primary px-4 py-2 flex items-center gap-2"><!> Try Again</button>'),xr=u('<div class="flex flex-col items-center justify-center min-h-[400px] p-8 text-center"><div class="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4"><!></div> <h2 class="text-xl font-semibold text-foreground mb-2">Oops! Something went wrong</h2> <p class="text-muted-foreground mb-6 max-w-md">We encountered an unexpected error. This has been logged and our team will investigate.</p> <!> <div class="flex gap-3"><!> <button class="btn-secondary px-4 py-2 flex items-center gap-2"><!> Go Home</button></div></div>');function hr(x,r){Ne(r,!0);let d=p(r,"fallback",3,"full"),C=p(r,"onRetry",3,null),A=p(r,"error",7,null),f=ie(!1),w=ie(""),E=ie("");qe(()=>{A()&&(S(f,!0),S(w,A().message,!0),S(E,A().stack||"",!0))}),Je(()=>{const M=g=>{var v,k;console.error("Unhandled promise rejection:",g.reason),S(f,!0),S(w,((v=g.reason)==null?void 0:v.message)||"An unexpected error occurred",!0),S(E,((k=g.reason)==null?void 0:k.stack)||"",!0)};return window.addEventListener("unhandledrejection",M),()=>{window.removeEventListener("unhandledrejection",M)}});var V=Te(),Q=fe(V);{var ne=M=>{var g=Te(),v=fe(g);{var k=R=>{var _=gr(),D=t(_);vt(D,{class:"w-4 h-4 text-destructive flex-shrink-0"});var L=o(D,4);{var U=X=>{var P=mr();P.__click=[ft,f,A,w,E,C],i(X,P)};y(L,X=>{C()&&X(U)})}e(_),i(R,_)},T=R=>{var _=xr(),D=t(_),L=t(D);vt(L,{class:"w-8 h-8 text-destructive"}),e(D);var U=o(D,6);{var X=oe=>{var $=pr(),de=o(t($),2),m=t(de),N=t(m,!0);e(m);var se=o(m,2);{var le=me=>{var _e=br(),xe=t(_e);e(_e),Y(()=>G(xe,`
                ${a(E)??""}
              `)),i(me,_e)};y(se,me=>{a(E)&&me(le)})}e(de),e($),Y(()=>G(N,a(w))),i(oe,$)};y(U,oe=>{a(w)&&oe(X)})}var P=o(U,2),te=t(P);{var Z=oe=>{var $=_r();$.__click=[ft,f,A,w,E,C];var de=t($);Nt(de,{class:"w-4 h-4"}),ke(),e($),i(oe,$)};y(te,oe=>{C()&&oe(Z)})}var H=o(te,2);H.__click=[fr];var ae=t(H);Ot(ae,{class:"w-4 h-4"}),ke(),e(H),e(P),e(_),i(R,_)};y(v,R=>{d()==="minimal"?R(k):R(T,!1)})}i(M,g)},J=M=>{var g=Te(),v=fe(g);Xe(v,r,"default",{},null),i(M,g)};y(Q,M=>{a(f)?M(ne):M(J,!1)})}i(x,V),Le()}Ye(["click"]);var yr=u("<aside><!></aside>"),wr=u('<div class="flex h-screen bg-background text-foreground svelte-bm75gl"><!> <main class="flex-1 flex flex-col min-w-0 svelte-bm75gl"><div class="flex-1 overflow-hidden"><!></div></main> <!></div>');function kr(x,r){Ne(r,!0);let d=p(r,"session",3,null),C=p(r,"profile",3,null),A=p(r,"agentType",3,"research"),f=p(r,"navigationItems",19,()=>[]),w=p(r,"brandName",3,"Robynn AI"),E=p(r,"searchPlaceholder",3,"Search"),V=p(r,"leftSidebarCollapsed",15,!1),Q=p(r,"rightSidebarCollapsed",15,!1),ne=p(r,"showRightSidebar",3,!0),J=p(r,"rightSidebarWidth",3,"w-80"),M=ie(0),g=Oe(()=>a(M)<768);qe(()=>{a(g)&&(V(!0),Q(!0))}),Je(()=>{S(M,window.innerWidth,!0)}),hr(x,{children:(v,k)=>{var T=wr(),R=t(T);cr(R,{get isMobile(){return a(g)},get session(){return d()},get profile(){return C()},get agentType(){return A()},get navigationItems(){return f()},get brandName(){return w()},get searchPlaceholder(){return E()},get collapsed(){return V()},set collapsed(P){V(P)}});var _=o(R,2),D=t(_),L=t(D);ut(L,()=>r.children??lt),e(D),e(_);var U=o(_,2);{var X=P=>{var te=yr();let Z;var H=t(te);ut(H,()=>r.rightSidebar??lt),e(te),Y(ae=>Z=ce(te,1,`bg-sidebar border-l border-sidebar-border flex flex-col transition-all duration-300 ease-in-out ${J()??""}`,"svelte-bm75gl",Z,ae),[()=>({"w-0":Q(),"overflow-hidden":Q()})]),i(P,te)};y(U,P=>{ne()&&P(X)})}e(T),i(v,T)},$$slots:{default:!0}}),mt("innerWidth",v=>S(M,v,!0)),Le()}var Sr=u('<div class="flex items-start space-x-4 animate-pulse svelte-5pvuvw"><div class="w-10 h-10 bg-muted rounded-full flex-shrink-0 svelte-5pvuvw"></div> <div class="flex-1 space-y-3 svelte-5pvuvw"><div class="flex items-center gap-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-16 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-20 svelte-5pvuvw"></div></div> <div class="space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-4/5 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div></div></div></div>'),Cr=u('<div class="flex items-center space-x-3 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-20 svelte-5pvuvw"></div></div>'),Ar=u('<div class="animate-pulse space-y-4 svelte-5pvuvw"><div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-6 h-6 bg-muted rounded svelte-5pvuvw"></div> <div class="h-5 bg-muted rounded w-24 svelte-5pvuvw"></div></div> <div class="h-10 bg-muted rounded-lg svelte-5pvuvw"></div> <!></div>'),Tr=u('<div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-40 svelte-5pvuvw"></div></div>'),Dr=u('<div class="animate-pulse space-y-3 svelte-5pvuvw"><div class="flex items-center justify-between svelte-5pvuvw"><div class="h-4 bg-muted rounded w-32 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-12 svelte-5pvuvw"></div></div> <div class="h-2 bg-muted rounded-full w-full svelte-5pvuvw"></div> <div class="space-y-2 svelte-5pvuvw"></div></div>'),Er=u('<div class="animate-pulse p-4 border border-border rounded-lg space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-2/3 svelte-5pvuvw"></div></div>'),Mr=u("<!> <!> <!> <!>",1);function zr(x,r){let d=p(r,"type",3,"message"),C=p(r,"count",3,1);var A=Mr(),f=fe(A);{var w=g=>{var v=Te(),k=fe(v);Se(k,17,()=>Array(C()),Re,(T,R)=>{var _=Sr();i(T,_)}),i(g,v)};y(f,g=>{d()==="message"&&g(w)})}var E=o(f,2);{var V=g=>{var v=Ar(),k=o(t(v),4);Se(k,16,()=>Array(5),Re,(T,R)=>{var _=Cr();i(T,_)}),e(v),i(g,v)};y(E,g=>{d()==="sidebar"&&g(V)})}var Q=o(E,2);{var ne=g=>{var v=Dr(),k=o(t(v),4);Se(k,20,()=>Array(3),Re,(T,R)=>{var _=Tr();i(T,_)}),e(k),e(v),i(g,v)};y(Q,g=>{d()==="progress"&&g(ne)})}var J=o(Q,2);{var M=g=>{var v=Te(),k=fe(v);Se(k,17,()=>Array(C()),Re,(T,R)=>{var _=Er();i(T,_)}),i(g,v)};y(J,g=>{d()==="project"&&g(M)})}i(x,A)}var jr=u('<div><!> <div class="flex-1 min-w-0"><p class="font-medium text-sm truncate"> </p> <p class="text-xs opacity-80 truncate"> </p></div></div>'),Ir=u('<div class="fixed top-4 right-4 w-80 bg-background border-2 border-border rounded-lg shadow-lg z-50 p-4 animate-slide-in svelte-lmyhl8" style="box-shadow: var(--shadow-lg);"><div class="flex items-center justify-between mb-4"><h3 class="font-semibold text-foreground flex items-center gap-2"><!> </h3> <span class="text-sm font-medium text-muted-foreground"> </span></div> <div class="mb-4"><div class="w-full bg-muted rounded-full h-2 overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"></div></div></div> <div class="space-y-2 max-h-64 overflow-y-auto"></div></div>');function Pr(x,r){Ne(r,!0);let d=p(r,"steps",19,()=>[]),C=p(r,"currentProgress",3,0),A=p(r,"isVisible",3,!1),f=p(r,"title",3,"Progress"),w,E=ie(0);qe(()=>{A()&&C()!==a(E)&&V(C())});function V(v){const k=a(E),T=800,R=Date.now();function _(){const D=Date.now()-R,L=Math.min(D/T,1),U=1-Math.pow(1-L,3);S(E,k+(v-k)*U),L<1?requestAnimationFrame(_):S(E,v,!0)}requestAnimationFrame(_)}function Q(v,k){return v.status==="completed"?Wt:v.status==="pending"&&k===d().findIndex(T=>T.status==="pending")?wt:Vt}function ne(v,k){const T="flex items-center space-x-3 p-3 rounded-lg transition-all duration-500";return v.status==="completed"?`${T} bg-green-50 border border-green-200 text-green-800`:v.status==="pending"&&k===d().findIndex(R=>R.status==="pending")?`${T} bg-blue-50 border border-blue-200 text-blue-800 animate-pulse`:`${T} bg-muted/30 border border-border text-muted-foreground`}var J=Te(),M=fe(J);{var g=v=>{var k=Ir(),T=t(k),R=t(T),_=t(R);Bt(_,{class:"w-4 h-4"});var D=o(_);e(R);var L=o(R,2),U=t(L);e(L),e(T);var X=o(T,2),P=t(X),te=t(P);e(P),e(X);var Z=o(X,2);Se(Z,23,d,H=>H.id,(H,ae,oe)=>{var $=jr(),de=t($);{let _e=Oe(()=>a(ae).status==="pending"&&a(oe)===d().findIndex(xe=>xe.status==="pending")?"animate-spin":"");Qe(de,()=>Q(a(ae),a(oe)),(xe,De)=>{De(xe,{get class(){return`w-4 h-4 flex-shrink-0 ${a(_e)??""}`}})})}var m=o(de,2),N=t(m),se=t(N,!0);e(N);var le=o(N,2),me=t(le,!0);e(le),e(m),e($),Y(_e=>{ce($,1,_e,"svelte-lmyhl8"),G(se,a(ae).title),G(me,a(ae).description)},[()=>Lt(ne(a(ae),a(oe)))]),i(H,$)}),e(Z),e(k),bt(k,H=>w=H,()=>w),Y(H=>{G(D,` ${f()??""}`),G(U,`${H??""}%`),Kt(te,`width: ${a(E)??""}%`)},[()=>Math.round(a(E))]),i(v,k)};y(M,v=>{A()&&d().length>0&&v(g)})}i(x,J),Le()}function Rr(x,r){x.key==="Enter"&&!x.shiftKey&&(x.preventDefault(),r())}var Or=(x,r,d)=>r(a(d)),Nr=u('<button class="group p-6 bg-card border border-border rounded-xl hover:border-primary/50 transition-all duration-300 text-left hover:shadow-lg"><div class="flex items-start gap-4"><div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><!></div> <div class="flex-1"><h3 class="font-semibold text-foreground mb-2 group-hover:text-primary transition-colors"> </h3> <p class="text-sm text-muted-foreground mb-3"> </p> <div class="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 font-mono"> </div></div></div></button>'),Lr=u(`<div class="max-w-4xl mx-auto"><div class="text-center mb-12"><div class="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6"><!></div> <h1 class="text-3xl font-bold text-foreground mb-4">SEO Strategy Assistant</h1> <p class="text-lg text-muted-foreground max-w-2xl mx-auto">Discover high-value keywords, analyze competitor gaps, and build
            content clusters that drive organic traffic to your website.</p></div> <div class="grid md:grid-cols-2 gap-6 mb-12"></div></div>`),Kr=(x,r,d)=>r(a(d).content),Fr=(x,r,d)=>r(a(d).content,`seo-analysis-${Date.now()}.txt`),Gr=u('<button class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"><!> Download</button>'),Ur=u('<div class="flex items-center gap-2 mt-3"><button class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"><!> Copy</button> <!></div>'),Br=u('<div class="flex gap-4"><div><!></div> <div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-2"><span class="font-medium text-sm text-foreground"> </span> <span class="text-xs text-muted-foreground"> </span></div> <div class="prose prose-sm max-w-none text-foreground"><div class="whitespace-pre-wrap"> </div></div> <!></div></div>'),Wr=u('<div class="max-w-4xl mx-auto space-y-6"><!> <!></div>'),Vr=u('<div class="bg-muted/30 rounded-lg p-4"><div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2 text-foreground">Target Audience</label> <input placeholder="e.g., Small business owners" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-bold mb-2 text-foreground">Region Focus</label> <input placeholder="e.g., United States" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-bold mb-2 text-foreground">Funnel Stage</label> <select class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"><option>Awareness</option><option>Consideration</option><option>Decision</option></select></div></div></div>'),Hr=(x,r)=>S(r,!a(r)),qr=u('<!> <main class="flex-1 flex flex-col bg-background"><header class="flex items-center justify-between p-6 border-b border-border bg-background"><div class="flex items-center"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-2"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">SEO Agent</span></nav></div></header> <div class="flex-1 overflow-y-auto p-6"><!></div> <div class="border-t border-border p-6"><div class="max-w-4xl mx-auto space-y-4"><!> <div class="flex gap-3"><button><!></button> <div class="flex-1 relative"><textarea class="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-primary/50" rows="2"></textarea></div> <button class="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"><!> Analyze</button></div></div></div></main>',1);function Yr(x,r){Ne(r,!0);const[d,C]=pt(),A=()=>_t(f(),"$messages",d);let f=p(r,"messages",19,()=>gt([])),w=p(r,"isLoading",3,!1),E=p(r,"onSendMessage",3,null),V=p(r,"progressSteps",19,()=>[]),Q=p(r,"currentProgress",3,0),ne=p(r,"envSlug",3,""),J=ie(""),M="summary",g=ie(0),v=ie(""),k=ie(!1),T=ie(""),R=ie(""),_=ie("awareness");const D=["Find long-tail keywords for organic skincare...","Analyze competitor keywords for project management tools...","Discover niche keywords for sustainable fashion brands...","Research local SEO keywords for coffee shops in Seattle..."],L=[{icon:He,title:"Niche Keyword Discovery",description:"Find untapped long-tail keywords in your specific niche",prompt:"Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"},{icon:dt,title:"Competitor Gap Analysis",description:"Identify keyword opportunities your competitors are missing",prompt:"Analyze keyword gaps between my website and [Competitor Domain] in the [Industry] space"},{icon:Ht,title:"Content Cluster Mapping",description:"Build topic clusters for better content organization",prompt:"Create a content cluster strategy for [Main Topic] with supporting subtopics and internal linking"},{icon:tt,title:"Local SEO Research",description:"Optimize for location-based search queries",prompt:"Find local SEO opportunities for a [Business Type] in [City, State]"}];Je(()=>{const s=setInterval(()=>{S(g,(a(g)+1)%D.length),S(v,D[a(g)],!0)},3e3);return S(v,D[0],!0),()=>clearInterval(s)});function U(){if(!a(J).trim()||w()||!E())return;const s=a(k)?{targetAudience:a(T),regionFocus:a(R),funnelStage:a(_),outputFormat:M}:void 0;E()(a(J).trim(),s),S(J,"")}function X(s){S(J,s.prompt,!0)}function P(s){return s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}function te(s){navigator.clipboard.writeText(s)}function Z(s,l){const h=new Blob([s],{type:"text/plain"}),z=URL.createObjectURL(h),K=document.createElement("a");K.href=z,K.download=l,K.click(),URL.revokeObjectURL(z)}var H=qr(),ae=fe(H);{var oe=s=>{Pr(s,{get steps(){return V()},get currentProgress(){return Q()},get isVisible(){return w()},title:"SEO Analysis Progress"})};y(ae,s=>{V().length>0&&s(oe)})}var $=o(ae,2),de=t($),m=t(de),N=t(m),se=t(N),le=o(se,2);kt(le,{class:"w-4 h-4"}),ke(2),e(N),e(m),e(de);var me=o(de,2),_e=t(me);{var xe=s=>{var l=Lr(),h=t(l),z=t(h),K=t(z);tt(K,{class:"w-8 h-8 text-primary"}),e(z),ke(4),e(h);var j=o(h,2);Se(j,21,()=>L,b=>b.title,(b,I)=>{var F=Nr();F.__click=[Or,X,I];var re=t(F),O=t(re),W=t(O);Qe(W,()=>a(I).icon,(Ke,Fe)=>{Fe(Ke,{class:"w-6 h-6 text-primary"})}),e(O);var q=o(O,2),ue=t(q),ye=t(ue,!0);e(ue);var Ce=o(ue,2),Ae=t(Ce,!0);e(Ce);var ze=o(Ce,2),je=t(ze,!0);e(ze),e(q),e(re),e(F),Y(()=>{G(ye,a(I).title),G(Ae,a(I).description),G(je,a(I).prompt)}),i(b,F)}),e(j),e(l),i(s,l)},De=s=>{var l=Wr(),h=t(l);Se(h,1,A,j=>j.id,(j,b)=>{var I=Br(),F=t(I),re=t(F);{var O=we=>{qt(we,{class:"w-4 h-4 text-primary-foreground"})},W=we=>{Yt(we,{class:"w-4 h-4 text-muted-foreground"})};y(re,we=>{a(b).role==="user"?we(O):we(W,!1)})}e(F);var q=o(F,2),ue=t(q),ye=t(ue),Ce=t(ye,!0);e(ye);var Ae=o(ye,2),ze=t(Ae,!0);e(Ae),e(ue);var je=o(ue,2),Ke=t(je),Fe=t(Ke,!0);e(Ke),e(je);var At=o(je,2);{var Tt=we=>{var Ze=Ur(),We=t(Ze);We.__click=[Kr,te,b];var Dt=t(We);Jt(Dt,{class:"w-3 h-3"}),ke(),e(We);var Et=o(We,2);{var Mt=$e=>{var Ve=Gr();Ve.__click=[Fr,Z,b];var zt=t(Ve);Qt(zt,{class:"w-3 h-3"}),ke(),e(Ve),i($e,Ve)};y(Et,$e=>{a(b).isReport&&$e(Mt)})}e(Ze),i(we,Ze)};y(At,we=>{a(b).role==="assistant"&&we(Tt)})}e(q),e(I),Y(we=>{ce(F,1,`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${a(b).role==="user"?"bg-primary":"bg-muted"}`),G(Ce,a(b).role==="user"?"You":"SEO Assistant"),G(ze,we),G(Fe,a(b).content)},[()=>P(a(b).timestamp)]),at(3,I,()=>ot,()=>({duration:300})),i(j,I)});var z=o(h,2);{var K=j=>{zr(j,{type:"message",count:1})};y(z,j=>{w()&&j(K)})}e(l),i(s,l)};y(_e,s=>{A().length===0?s(xe):s(De,!1)})}e(me);var Ee=o(me,2),he=t(Ee),Ie=t(he);{var Me=s=>{var l=Vr(),h=t(l),z=t(h),K=o(t(z),2);ct(K),e(z);var j=o(z,2),b=o(t(j),2);ct(b),e(j);var I=o(j,2),F=o(t(I),2),re=t(F);re.value=re.__value="awareness";var O=o(re);O.value=O.__value="consideration";var W=o(O);W.value=W.__value="decision",e(F),e(I),e(h),e(l),et(K,()=>a(T),q=>S(T,q)),et(b,()=>a(R),q=>S(R,q)),Ft(F,()=>a(_),q=>S(_,q)),at(3,l,()=>ot),i(s,l)};y(Ie,s=>{a(k)&&s(Me)})}var Pe=o(Ie,2),B=t(Pe);B.__click=[Hr,k];var ge=t(B);Xt(ge,{class:"w-4 h-4"}),e(B);var be=o(B,2),ee=t(be);It(ee),ee.__keydown=[Rr,U],e(be);var ve=o(be,2);ve.__click=U;var pe=t(ve);{var n=s=>{wt(s,{class:"w-4 h-4 animate-spin"})},c=s=>{tt(s,{class:"w-4 h-4"})};y(pe,s=>{w()?s(n):s(c,!1)})}ke(),e(ve),e(Pe),e(he),e(Ee),e($),Y(s=>{Be(se,"href",`/dashboard/${ne()??""}`),ce(B,1,`px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors ${a(k)?"bg-muted":""}`),Be(ee,"placeholder",a(v)),ee.disabled=w(),ve.disabled=s},[()=>!a(J).trim()||w()]),et(ee,()=>a(J),s=>S(J,s)),i(x,H),Le(),C()}Ye(["click","keydown"]);function Jr(x,r){r(!r())}function Qr(x,r){r()&&r()({seedKeywords:["example","keywords"],filters:{industry:"",location:"United States",volumeRange:{min:100,max:1e4},maxDifficulty:50}})}function Xr(x,r){r()&&r()({yourDomain:"",competitors:[""],location:"United States",minVolume:100,maxDifficulty:50,gapType:"missing"})}function Zr(x,r){r()&&r()({mainTopic:"",subtopics:[],contentTypes:["blog","landing-page"]})}var $r=u('<h2 class="text-lg font-semibold text-foreground">SEO Tools</h2> <button class="text-muted-foreground hover:text-foreground transition-colors"><!></button>',1),ea=u('<div class="flex flex-col items-center w-full"><button class="p-2 hover:bg-accent rounded-lg mb-2" title="SEO Tools"><!></button> <span class="text-xs text-muted-foreground">Tools</span></div>'),ta=(x,r)=>r("tools"),ra=(x,r,d)=>r(a(d).id),aa=u('<button><div class="flex items-start gap-3"><div><!></div> <div class="flex-1 min-w-0"><h3 class="font-medium text-sm text-foreground mb-1"> </h3> <p class="text-xs text-muted-foreground"> </p></div></div></button>'),oa=u('<div class="space-y-2"></div>'),sa=u('<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground"> </div> <div class="text-muted-foreground"> </div></div>'),ia=u('<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2"> </h4> <div class="space-y-2 max-h-40 overflow-y-auto"></div></div>'),na=u('<div class="space-y-4"><h3 class="font-medium text-foreground">Niche Discovery</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Seed Keywords</label> <textarea placeholder="organic skincare, natural beauty" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground resize-none" rows="2"></textarea></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Industry</label> <input type="text" placeholder="e.g., Beauty &amp; Cosmetics" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"><!></button></div> <!></div>'),da=u('<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground"> </div> <div class="text-muted-foreground"> </div></div>'),la=u('<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2"> </h4> <div class="space-y-2 max-h-40 overflow-y-auto"></div></div>'),va=u('<div class="space-y-4"><h3 class="font-medium text-sidebar-foreground">Gap Analysis</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Your Domain</label> <input type="text" placeholder="yourdomain.com" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Competitor Domain</label> <input type="text" placeholder="competitor.com" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"><!></button></div> <!></div>'),ua=u('<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground"> </div> <div class="text-muted-foreground"> </div></div>'),ca=u('<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2">Cluster Strategy</h4> <div class="space-y-2 max-h-40 overflow-y-auto"></div></div>'),fa=u('<div class="space-y-4"><h3 class="font-medium text-sidebar-foreground">Content Clusters</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Main Topic</label> <input type="text" placeholder="e.g., Digital Marketing" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Target Audience</label> <input type="text" placeholder="e.g., Small business owners" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"><!></button></div> <!></div>'),ma=u('<div class="space-y-4"><div><button class="flex items-center justify-between w-full text-left mb-3"><span class="font-medium text-foreground">Analysis Tools</span> <!></button> <!></div> <div class="border-t border-border pt-4"><!></div> <div class="border-t border-sidebar-border p-4"><h3 class="font-medium text-sidebar-foreground mb-3">Quick Actions</h3> <div class="space-y-2"><button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Export Keywords</button> <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Save Analysis</button> <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Share Report</button></div></div></div>'),ga=u('<aside><button><!></button> <div><div class="flex items-center justify-between mb-6"><!></div> <!></div></aside>');function ba(x,r){Ne(r,!0);let d=p(r,"collapsed",15,!1),C=p(r,"isMobile",3,!1),A=p(r,"activeTab",15,"niche"),f=p(r,"onNicheDiscovery",3,null),w=p(r,"onGapAnalysis",3,null),E=p(r,"onContentCluster",3,null),V=p(r,"isLoading",3,!1),Q=p(r,"nicheKeywords",19,()=>[]),ne=p(r,"gapKeywords",19,()=>[]),J=p(r,"clusterData",19,()=>[]),M=ie(Ue(new Set(["tools"])));const g=[{id:"niche",title:"Niche Discovery",description:"Find untapped long-tail keywords",icon:St,color:"text-blue-500",bgColor:"bg-blue-50"},{id:"gap",title:"Gap Analysis",description:"Identify competitor keyword gaps",icon:dt,color:"text-green-500",bgColor:"bg-green-50"},{id:"cluster",title:"Content Clusters",description:"Build topic cluster strategies",icon:Ct,color:"text-purple-500",bgColor:"bg-purple-50"}];function v(m){a(M).has(m)?a(M).delete(m):a(M).add(m),S(M,new Set(a(M)),!0)}function k(m){A(m)}var T=ga();let R;var _=t(T);_.__click=[Jr,d];let D;var L=t(_);{var U=m=>{xt(m,{class:"w-3 h-3"})},X=m=>{ht(m,{class:"w-3 h-3"})};y(L,m=>{d()?m(U):m(X,!1)})}e(_);var P=o(_,2);let te;var Z=t(P),H=t(Z);{var ae=m=>{var N=$r(),se=o(fe(N),2),le=t(se);rt(le,{class:"w-5 h-5"}),e(se),i(m,N)},oe=m=>{var N=ea(),se=t(N),le=t(se);rt(le,{class:"w-5 h-5"}),e(se),ke(2),e(N),i(m,N)};y(H,m=>{d()?m(oe,!1):m(ae)})}e(Z);var $=o(Z,2);{var de=m=>{var N=ma(),se=t(N),le=t(se);le.__click=[ta,v];var me=o(t(le),2);{var _e=B=>{yt(B,{class:"w-4 h-4 text-muted-foreground"})},xe=B=>{kt(B,{class:"w-4 h-4 text-muted-foreground"})};y(me,B=>{a(M).has("tools")?B(_e):B(xe,!1)})}e(le);var De=o(le,2);{var Ee=B=>{var ge=oa();Se(ge,21,()=>g,be=>be.id,(be,ee)=>{var ve=aa();ve.__click=[ra,k,ee];var pe=t(ve),n=t(pe),c=t(n);Qe(c,()=>a(ee).icon,(j,b)=>{b(j,{get class(){return`w-4 h-4 ${a(ee).color??""}`}})}),e(n);var s=o(n,2),l=t(s),h=t(l,!0);e(l);var z=o(l,2),K=t(z,!0);e(z),e(s),e(pe),e(ve),Y(()=>{ce(ve,1,`w-full p-3 rounded-lg border transition-all text-left ${A()===a(ee).id?"border-primary bg-primary/5":"border-border hover:border-primary/50 hover:bg-muted/50"}`),ce(n,1,`w-8 h-8 rounded-lg ${a(ee).bgColor??""} flex items-center justify-center`),G(h,a(ee).title),G(K,a(ee).description)}),i(be,ve)}),e(ge),at(3,ge,()=>ot),i(B,ge)};y(De,B=>{a(M).has("tools")&&B(Ee)})}e(se);var he=o(se,2),Ie=t(he);{var Me=B=>{var ge=na(),be=o(t(ge),2),ee=o(t(be),4);ee.__click=[Qr,f];var ve=t(ee);{var pe=l=>{var h=Ge("Analyzing...");i(l,h)},n=l=>{var h=Ge("Discover Keywords");i(l,h)};y(ve,l=>{V()?l(pe):l(n,!1)})}e(ee),e(be);var c=o(be,2);{var s=l=>{var h=ia(),z=t(h),K=t(z);e(z);var j=o(z,2);Se(j,21,()=>Q().slice(0,5),Re,(b,I)=>{var F=sa(),re=t(F),O=t(re,!0);e(re);var W=o(re,2),q=t(W);e(W),e(F),Y(()=>{G(O,a(I).keyword),G(q,`Vol: ${a(I).search_volume??""} | Diff: ${a(I).difficulty??""}`)}),i(b,F)}),e(j),e(h),Y(()=>G(K,`Found ${Q().length??""} Keywords`)),i(l,h)};y(c,l=>{Q().length>0&&l(s)})}e(ge),Y(()=>ee.disabled=V()),i(B,ge)},Pe=B=>{var ge=Te(),be=fe(ge);{var ee=pe=>{var n=va(),c=o(t(n),2),s=o(t(c),4);s.__click=[Xr,w];var l=t(s);{var h=b=>{var I=Ge("Analyzing...");i(b,I)},z=b=>{var I=Ge("Find Gaps");i(b,I)};y(l,b=>{V()?b(h):b(z,!1)})}e(s),e(c);var K=o(c,2);{var j=b=>{var I=la(),F=t(I),re=t(F);e(F);var O=o(F,2);Se(O,21,()=>ne().slice(0,5),Re,(W,q)=>{var ue=da(),ye=t(ue),Ce=t(ye,!0);e(ye);var Ae=o(ye,2),ze=t(Ae);e(Ae),e(ue),Y(()=>{G(Ce,a(q).keyword),G(ze,`Gap: ${a(q).gap_type??""} | Vol: ${a(q).search_volume??""}`)}),i(W,ue)}),e(O),e(I),Y(()=>G(re,`Found ${ne().length??""} Opportunities`)),i(b,I)};y(K,b=>{ne().length>0&&b(j)})}e(n),Y(()=>s.disabled=V()),i(pe,n)},ve=pe=>{var n=Te(),c=fe(n);{var s=l=>{var h=fa(),z=o(t(h),2),K=o(t(z),4);K.__click=[Zr,E];var j=t(K);{var b=O=>{var W=Ge("Creating...");i(O,W)},I=O=>{var W=Ge("Build Cluster");i(O,W)};y(j,O=>{V()?O(b):O(I,!1)})}e(K),e(z);var F=o(z,2);{var re=O=>{var W=ca(),q=o(t(W),2);Se(q,21,()=>J().slice(0,3),Re,(ue,ye)=>{var Ce=ua(),Ae=t(Ce),ze=t(Ae,!0);e(Ae);var je=o(Ae,2),Ke=t(je);e(je),e(Ce),Y(()=>{var Fe;G(ze,a(ye).topic),G(Ke,`${(((Fe=a(ye).subtopics)==null?void 0:Fe.length)||0)??""} subtopics`)}),i(ue,Ce)}),e(q),e(W),i(O,W)};y(F,O=>{J().length>0&&O(re)})}e(h),Y(()=>K.disabled=V()),i(l,h)};y(c,l=>{A()==="cluster"&&l(s)},!0)}i(pe,n)};y(be,pe=>{A()==="gap"?pe(ee):pe(ve,!1)},!0)}i(B,ge)};y(Ie,B=>{A()==="niche"?B(Me):B(Pe,!1)})}e(he),ke(2),e(N),i(m,N)};y($,m=>{d()||m(de)})}e(P),e(T),Y((m,N,se)=>{R=ce(T,1,"bg-background border-l border-border overflow-y-auto transition-all duration-300 ease-in-out relative",null,R,m),D=ce(_,1,"absolute -left-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors",null,D,N),te=ce(P,1,"p-6",null,te,se)},[()=>({"w-80":!d(),"w-16":d()&&!C(),"w-0":d()&&C(),"overflow-hidden":d()&&C()}),()=>({hidden:C()&&d()}),()=>({hidden:d()&&C()})]),i(x,T),Le()}Ye(["click"]);var pa=u('<meta name="description" content="AI-powered SEO strategy and keyword research assistant"/>');function eo(x,r){Ne(r,!0);const d=gt([]);let C=ie(!1),A=ie(Ue([])),f=ie(0),w=Ue([]),E=Ue([]),V=Ue([]),Q=ie(!1),ne=ie("niche"),J=ie(0),M=Oe(()=>a(J)<768);qe(()=>{a(M)&&S(Q,!0)});const g=[{id:"chat",label:"Chat",icon:He,active:!0},{id:"niche",label:"Niche Discovery",icon:St},{id:"gap",label:"Gap Analysis",icon:dt},{id:"cluster",label:"Content Clusters",icon:Ct}];function v(){return Math.random().toString(36).substr(2,9)}async function k(D,L){var U,X;if(!(!D.trim()||a(C))){S(C,!0),S(A,[{id:1,title:"Industry Research",description:"Analyzing your business niche...",status:"pending"},{id:2,title:"Keyword Discovery",description:"Finding relevant keywords...",status:"pending"},{id:3,title:"Volume Analysis",description:"Checking search volumes...",status:"pending"},{id:4,title:"Competition Analysis",description:"Analyzing keyword difficulty...",status:"pending"},{id:5,title:"Report Generation",description:"Creating your SEO strategy...",status:"pending"}],!0),S(f,0),d.update(P=>[...P,{id:v(),role:"user",content:D,timestamp:new Date}]);try{const P=await fetch(`/dashboard/${(U=r.data.environment)==null?void 0:U.slug}/agent-seo?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:D})});if(!P.ok)throw new Error(`HTTP error! status: ${P.status}`);const te=(X=P.body)==null?void 0:X.getReader();if(!te)throw new Error("No response body");let Z={id:v(),role:"assistant",content:"",timestamp:new Date,isReport:!0};for(d.update(H=>[...H,Z]);;){const{done:H,value:ae}=await te.read();if(H)break;const $=new TextDecoder().decode(ae).split(`
`);for(const de of $)if(de.startsWith("data: "))try{const m=JSON.parse(de.slice(6));m.content&&(Z.content+=m.content,d.update(N=>[...N.slice(0,-1),{...Z}])),m.progress&&(S(f,m.progress,!0),m.step&&S(A,a(A).map(N=>N.id===m.step?{...N,status:"active"}:N),!0))}catch(m){console.error("Error parsing SSE data:",m)}}}catch(P){console.error("Error sending message:",P),d.update(te=>[...te,{id:v(),role:"assistant",content:"I apologize, but I encountered an error while processing your request. Please try again.",timestamp:new Date}])}finally{S(C,!1),S(A,[],!0)}}}function T(D){console.log("Niche discovery:",D)}function R(D){console.log("Gap analysis:",D)}function _(D){console.log("Content cluster:",D)}Pt(D=>{var L=pa();jt.title="SEO Agent - Robynn AI",i(D,L)}),kr(x,{get session(){return r.data.session},get profile(){return r.data.profile},agentType:"seo",get navigationItems(){return g},brandName:"Robynn AI",searchPlaceholder:"Search SEO tools...",get rightSidebarCollapsed(){return a(Q)},showRightSidebar:!0,rightSidebarWidth:"320px",rightSidebar:L=>{ba(L,{get isMobile(){return a(M)},onNicheDiscovery:T,onGapAnalysis:R,onContentCluster:_,get isLoading(){return a(C)},get nicheKeywords(){return w},get gapKeywords(){return E},get clusterData(){return V},get collapsed(){return a(Q)},set collapsed(U){S(Q,U,!0)},get activeTab(){return a(ne)},set activeTab(U){S(ne,U,!0)}})},children:(L,U)=>{{let X=Oe(()=>{var P;return(P=r.data.environment)==null?void 0:P.slug});Yr(L,{get messages(){return d},get isLoading(){return a(C)},onSendMessage:k,get progressSteps(){return a(A)},get currentProgress(){return a(f)},get envSlug(){return a(X)}})}},$$slots:{rightSidebar:!0,default:!0}}),mt("innerWidth",D=>S(J,D,!0)),Le()}export{eo as component};
