import"../chunks/CWj6FrbW.js";import{W as Ve,S as or,U as ir,p as he,l as nt,j as Ue,m as Xe,aT as U,i as L,g as Qe,b as ct,d as rt,a as M,e as ve,f as ut,c as ht,r as vt,ac as ae,ak as Wn,h as Bn,t as nn,s as Nt,k as rn,o as sr}from"../chunks/DDiqt3uM.js";import{e as _t,s as ar}from"../chunks/DWulv87v.js";import{s as lr}from"../chunks/DtGADYZa.js";import{i as Pt}from"../chunks/2C89X9tI.js";import{c as oe}from"../chunks/BCmD-YNt.js";import{a as le,s as xn}from"../chunks/C36Ip9GY.js";import{s as Ae,a as We}from"../chunks/B82PTGnX.js";import"../chunks/ChutyBgo.js";import"../chunks/DhRTwODG.js";import{h as ce,i as _,w as wt,n as ue,u as Nn,e as Kt,m as Ct,g as Vt,a as H,F as On,k as Et,p as cr,s as De,f as ur,S as ln,q as Gt,r as _n,d as ie,j as on,o as Vn}from"../chunks/BGh_Dfnt.js";import{s as St}from"../chunks/iCEqKm8o.js";import{i as Ce}from"../chunks/B_FgA42l.js";import{l as Ht,p as k,s as Kn}from"../chunks/C-ZVHnwW.js";import{d as Oe,w as yt,g as dr}from"../chunks/rjRVMZXi.js";import{t as Fe,g as An,o as je,r as fr,a as gr}from"../chunks/CMLKS1ED.js";import{s as ge,w as mr,a as zn,c as hr,u as vr,g as Tn,h as pr,r as br,S as yr}from"../chunks/CsZUEjWq.js";import{c as wr,a as fn,d as xr}from"../chunks/DaUg0vjO.js";import{a as de}from"../chunks/D-ywOz1J.js";import{b as fe}from"../chunks/Dqu9JXqq.js";import{g as Or}from"../chunks/CaxpRkM3.js";import{p as _r}from"../chunks/BwDA7qM4.js";import{W as Ar}from"../chunks/QNe64B_9.js";import{B as Tr}from"../chunks/BFbKPyUZ.js";import{M as Cr}from"../chunks/Bxi4P_5L.js";import{b as Jt}from"../chunks/B9BVeOQN.js";import{t as Ie}from"../chunks/A4ulxp7Q.js";import{c as Ye,f as Er}from"../chunks/Bf9nHHn7.js";import{e as kr}from"../chunks/CzTs9bhI.js";function Pr(e){e.setAttribute("data-highlighted","")}function xe(e){e.removeAttribute("data-highlighted")}function Sr(e,t=500){let r=null;return function(...n){const o=()=>{r=null,e(...n)};r&&clearTimeout(r),r=setTimeout(o,t)}}function Cn(e){const{open:t,forceVisible:r,activeTrigger:n}=e;return Oe([t,r,n],([o,s,i])=>(o||s)&&i!==null)}function bt(e){ce&&ge(1).then(()=>{const t=document.activeElement;!_(t)||t===e||(t.tabIndex=-1,e&&(e.tabIndex=0,e.focus()))})}function Hn(){return Array.from(document.querySelectorAll('a[href]:not([tabindex="-1"]), button:not([disabled]):not([tabindex="-1"]), input:not([disabled]):not([tabindex="-1"]), select:not([disabled]):not([tabindex="-1"]), textarea:not([disabled]):not([tabindex="-1"]), [tabindex]:not([tabindex="-1"])'))}function Dr(e){const t=Hn(),n=t.indexOf(e)+1,o=t[n];return n<t.length&&_(o)?o:null}function Ir(e){const t=Hn(),n=t.indexOf(e)-1,o=t[n];return n>=0&&_(o)?o:null}const Rr=new Set(["Shift","Control","Alt","Meta","CapsLock","NumLock"]),Mr={onMatch:bt,getCurrentItem:()=>document.activeElement};function Fr(e={}){const t={...Mr,...e},r=wt(yt([])),n=Sr(()=>{r.update(()=>[])});return{typed:r,resetTyped:n,handleTypeaheadSearch:(s,i)=>{if(Rr.has(s))return;const a=t.getCurrentItem(),l=dr(r);if(!Array.isArray(l))return;l.push(s.toLowerCase()),r.set(l);const u=i.filter(f=>!(f.getAttribute("disabled")==="true"||f.getAttribute("aria-disabled")==="true"||f.hasAttribute("data-disabled"))),g=l.length>1&&l.every(f=>f===l[0])?l[0]:l.join(""),b=_(a)?u.indexOf(a):-1;let m=mr(u,Math.max(b,0));g.length===1&&(m=m.filter(f=>f!==a));const w=m.find(f=>(f==null?void 0:f.innerText)&&f.innerText.toLowerCase().startsWith(g.toLowerCase()));_(w)&&w!==a&&t.onMatch(w),n()}}}const ee=Math.min,Dt=Math.max,qe=Math.round,Ke=Math.floor,zt=e=>({x:e,y:e}),Lr={left:"right",right:"left",bottom:"top",top:"bottom"},Wr={start:"end",end:"start"};function cn(e,t,r){return Dt(e,ee(t,r))}function Ee(e,t){return typeof e=="function"?e(t):e}function ne(e){return e.split("-")[0]}function ke(e){return e.split("-")[1]}function jn(e){return e==="x"?"y":"x"}function gn(e){return e==="y"?"height":"width"}const Br=new Set(["top","bottom"]);function Qt(e){return Br.has(ne(e))?"y":"x"}function mn(e){return jn(Qt(e))}function Nr(e,t,r){r===void 0&&(r=!1);const n=ke(e),o=mn(e),s=gn(o);let i=o==="x"?n===(r?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Ge(i)),[i,Ge(i)]}function Vr(e){const t=Ge(e);return[un(e),t,un(t)]}function un(e){return e.replace(/start|end/g,t=>Wr[t])}const En=["left","right"],kn=["right","left"],Kr=["top","bottom"],zr=["bottom","top"];function Hr(e,t,r){switch(e){case"top":case"bottom":return r?t?kn:En:t?En:kn;case"left":case"right":return t?Kr:zr;default:return[]}}function jr(e,t,r,n){const o=ke(e);let s=Hr(ne(e),r==="start",n);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(un)))),s}function Ge(e){return e.replace(/left|right|bottom|top/g,t=>Lr[t])}function Ur(e){return{top:0,right:0,bottom:0,left:0,...e}}function Un(e){return typeof e!="number"?Ur(e):{top:e,right:e,bottom:e,left:e}}function Je(e){const{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function Pn(e,t,r){let{reference:n,floating:o}=e;const s=Qt(t),i=mn(t),a=gn(i),l=ne(t),u=s==="y",d=n.x+n.width/2-o.width/2,g=n.y+n.height/2-o.height/2,b=n[a]/2-o[a]/2;let m;switch(l){case"top":m={x:d,y:n.y-o.height};break;case"bottom":m={x:d,y:n.y+n.height};break;case"right":m={x:n.x+n.width,y:g};break;case"left":m={x:n.x-o.width,y:g};break;default:m={x:n.x,y:n.y}}switch(ke(t)){case"start":m[i]-=b*(r&&u?-1:1);break;case"end":m[i]+=b*(r&&u?-1:1);break}return m}const Xr=async(e,t,r)=>{const{placement:n="bottom",strategy:o="absolute",middleware:s=[],platform:i}=r,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:g}=Pn(u,n,l),b=n,m={},y=0;for(let w=0;w<a.length;w++){const{name:f,fn:x}=a[w],{x:P,y:A,data:T,reset:O}=await x({x:d,y:g,initialPlacement:n,placement:b,strategy:o,middlewareData:m,rects:u,platform:i,elements:{reference:e,floating:t}});d=P??d,g=A??g,m={...m,[f]:{...m[f],...T}},O&&y<=50&&(y++,typeof O=="object"&&(O.placement&&(b=O.placement),O.rects&&(u=O.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):O.rects),{x:d,y:g}=Pn(u,b,l)),w=-1)}return{x:d,y:g,placement:b,strategy:o,middlewareData:m}};async function hn(e,t){var r;t===void 0&&(t={});const{x:n,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:g="floating",altBoundary:b=!1,padding:m=0}=Ee(t,e),y=Un(m),f=a[b?g==="floating"?"reference":"floating":g],x=Je(await s.getClippingRect({element:(r=await(s.isElement==null?void 0:s.isElement(f)))==null||r?f:f.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),P=g==="floating"?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,A=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),T=await(s.isElement==null?void 0:s.isElement(A))?await(s.getScale==null?void 0:s.getScale(A))||{x:1,y:1}:{x:1,y:1},O=Je(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:P,offsetParent:A,strategy:l}):P);return{top:(x.top-O.top+y.top)/T.y,bottom:(O.bottom-x.bottom+y.bottom)/T.y,left:(x.left-O.left+y.left)/T.x,right:(O.right-x.right+y.right)/T.x}}const Yr=e=>({name:"arrow",options:e,async fn(t){const{x:r,y:n,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=Ee(e,t)||{};if(u==null)return{};const g=Un(d),b={x:r,y:n},m=mn(o),y=gn(m),w=await i.getDimensions(u),f=m==="y",x=f?"top":"left",P=f?"bottom":"right",A=f?"clientHeight":"clientWidth",T=s.reference[y]+s.reference[m]-b[m]-s.floating[y],O=b[m]-s.reference[m],W=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let S=W?W[A]:0;(!S||!await(i.isElement==null?void 0:i.isElement(W)))&&(S=a.floating[A]||s.floating[y]);const j=T/2-O/2,J=S/2-w[y]/2-1,Q=ee(g[x],J),Z=ee(g[P],J),N=Q,at=S-w[y]-Z,$=S/2-w[y]/2+j,kt=cn(N,$,at),X=!l.arrow&&ke(o)!=null&&$!==kt&&s.reference[y]/2-($<N?Q:Z)-w[y]/2<0,K=X?$<N?$-N:$-at:0;return{[m]:b[m]+K,data:{[m]:kt,centerOffset:$-kt-K,...X&&{alignmentOffset:K}},reset:X}}}),qr=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var r,n;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:g=!0,fallbackPlacements:b,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:w=!0,...f}=Ee(e,t);if((r=s.arrow)!=null&&r.alignmentOffset)return{};const x=ne(o),P=Qt(a),A=ne(a)===a,T=await(l.isRTL==null?void 0:l.isRTL(u.floating)),O=b||(A||!w?[Ge(a)]:Vr(a)),W=y!=="none";!b&&W&&O.push(...jr(a,w,y,T));const S=[a,...O],j=await hn(t,f),J=[];let Q=((n=s.flip)==null?void 0:n.overflows)||[];if(d&&J.push(j[x]),g){const $=Nr(o,i,T);J.push(j[$[0]],j[$[1]])}if(Q=[...Q,{placement:o,overflows:J}],!J.every($=>$<=0)){var Z,N;const $=(((Z=s.flip)==null?void 0:Z.index)||0)+1,kt=S[$];if(kt&&(!(g==="alignment"?P!==Qt(kt):!1)||Q.every(ft=>ft.overflows[0]>0&&Qt(ft.placement)===P)))return{data:{index:$,overflows:Q},reset:{placement:kt}};let X=(N=Q.filter(K=>K.overflows[0]<=0).sort((K,ft)=>K.overflows[1]-ft.overflows[1])[0])==null?void 0:N.placement;if(!X)switch(m){case"bestFit":{var at;const K=(at=Q.filter(ft=>{if(W){const At=Qt(ft.placement);return At===P||At==="y"}return!0}).map(ft=>[ft.placement,ft.overflows.filter(At=>At>0).reduce((At,Zt)=>At+Zt,0)]).sort((ft,At)=>ft[1]-At[1])[0])==null?void 0:at[0];K&&(X=K);break}case"initialPlacement":X=a;break}if(o!==X)return{reset:{placement:X}}}return{}}}},Gr=new Set(["left","top"]);async function Jr(e,t){const{placement:r,platform:n,elements:o}=e,s=await(n.isRTL==null?void 0:n.isRTL(o.floating)),i=ne(r),a=ke(r),l=Qt(r)==="y",u=Gr.has(i)?-1:1,d=s&&l?-1:1,g=Ee(t,e);let{mainAxis:b,crossAxis:m,alignmentAxis:y}=typeof g=="number"?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return a&&typeof y=="number"&&(m=a==="end"?y*-1:y),l?{x:m*d,y:b*u}:{x:b*u,y:m*d}}const Qr=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var r,n;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await Jr(t,e);return i===((r=a.offset)==null?void 0:r.placement)&&(n=a.arrow)!=null&&n.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},Zr=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:r,y:n,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:f=>{let{x,y:P}=f;return{x,y:P}}},...l}=Ee(e,t),u={x:r,y:n},d=await hn(t,l),g=Qt(ne(o)),b=jn(g);let m=u[b],y=u[g];if(s){const f=b==="y"?"top":"left",x=b==="y"?"bottom":"right",P=m+d[f],A=m-d[x];m=cn(P,m,A)}if(i){const f=g==="y"?"top":"left",x=g==="y"?"bottom":"right",P=y+d[f],A=y-d[x];y=cn(P,y,A)}const w=a.fn({...t,[b]:m,[g]:y});return{...w,data:{x:w.x-r,y:w.y-n,enabled:{[b]:s,[g]:i}}}}}},$r=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var r,n;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=Ee(e,t),d=await hn(t,u),g=ne(o),b=ke(o),m=Qt(o)==="y",{width:y,height:w}=s.floating;let f,x;g==="top"||g==="bottom"?(f=g,x=b===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=g,f=b==="end"?"top":"bottom");const P=w-d.top-d.bottom,A=y-d.left-d.right,T=ee(w-d[f],P),O=ee(y-d[x],A),W=!t.middlewareData.shift;let S=T,j=O;if((r=t.middlewareData.shift)!=null&&r.enabled.x&&(j=A),(n=t.middlewareData.shift)!=null&&n.enabled.y&&(S=P),W&&!b){const Q=Dt(d.left,0),Z=Dt(d.right,0),N=Dt(d.top,0),at=Dt(d.bottom,0);m?j=y-2*(Q!==0||Z!==0?Q+Z:Dt(d.left,d.right)):S=w-2*(N!==0||at!==0?N+at:Dt(d.top,d.bottom))}await l({...t,availableWidth:j,availableHeight:S});const J=await i.getDimensions(a.floating);return y!==J.width||w!==J.height?{reset:{rects:!0}}:{}}}};function Ze(){return typeof window<"u"}function Pe(e){return Xn(e)?(e.nodeName||"").toLowerCase():"#document"}function It(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Ut(e){var t;return(t=(Xn(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Xn(e){return Ze()?e instanceof Node||e instanceof It(e).Node:!1}function Wt(e){return Ze()?e instanceof Element||e instanceof It(e).Element:!1}function jt(e){return Ze()?e instanceof HTMLElement||e instanceof It(e).HTMLElement:!1}function Sn(e){return!Ze()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof It(e).ShadowRoot}const to=new Set(["inline","contents"]);function Be(e){const{overflow:t,overflowX:r,overflowY:n,display:o}=Bt(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!to.has(o)}const eo=new Set(["table","td","th"]);function no(e){return eo.has(Pe(e))}const ro=[":popover-open",":modal"];function $e(e){return ro.some(t=>{try{return e.matches(t)}catch{return!1}})}const oo=["transform","translate","scale","rotate","perspective"],io=["transform","translate","scale","rotate","perspective","filter"],so=["paint","layout","strict","content"];function vn(e){const t=pn(),r=Wt(e)?Bt(e):e;return oo.some(n=>r[n]?r[n]!=="none":!1)||(r.containerType?r.containerType!=="normal":!1)||!t&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!t&&(r.filter?r.filter!=="none":!1)||io.some(n=>(r.willChange||"").includes(n))||so.some(n=>(r.contain||"").includes(n))}function ao(e){let t=re(e);for(;jt(t)&&!Te(t);){if(vn(t))return t;if($e(t))return null;t=re(t)}return null}function pn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const lo=new Set(["html","body","#document"]);function Te(e){return lo.has(Pe(e))}function Bt(e){return It(e).getComputedStyle(e)}function tn(e){return Wt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function re(e){if(Pe(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Sn(e)&&e.host||Ut(e);return Sn(t)?t.host:t}function Yn(e){const t=re(e);return Te(t)?e.ownerDocument?e.ownerDocument.body:e.body:jt(t)&&Be(t)?t:Yn(t)}function Le(e,t,r){var n;t===void 0&&(t=[]),r===void 0&&(r=!0);const o=Yn(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),i=It(o);if(s){const a=dn(i);return t.concat(i,i.visualViewport||[],Be(o)?o:[],a&&r?Le(a):[])}return t.concat(o,Le(o,[],r))}function dn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function qn(e){const t=Bt(e);let r=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const o=jt(e),s=o?e.offsetWidth:r,i=o?e.offsetHeight:n,a=qe(r)!==s||qe(n)!==i;return a&&(r=s,n=i),{width:r,height:n,$:a}}function bn(e){return Wt(e)?e:e.contextElement}function _e(e){const t=bn(e);if(!jt(t))return zt(1);const r=t.getBoundingClientRect(),{width:n,height:o,$:s}=qn(t);let i=(s?qe(r.width):r.width)/n,a=(s?qe(r.height):r.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const co=zt(0);function Gn(e){const t=It(e);return!pn()||!t.visualViewport?co:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function uo(e,t,r){return t===void 0&&(t=!1),!r||t&&r!==It(e)?!1:t}function me(e,t,r,n){t===void 0&&(t=!1),r===void 0&&(r=!1);const o=e.getBoundingClientRect(),s=bn(e);let i=zt(1);t&&(n?Wt(n)&&(i=_e(n)):i=_e(e));const a=uo(s,r,n)?Gn(s):zt(0);let l=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,d=o.width/i.x,g=o.height/i.y;if(s){const b=It(s),m=n&&Wt(n)?It(n):n;let y=b,w=dn(y);for(;w&&n&&m!==y;){const f=_e(w),x=w.getBoundingClientRect(),P=Bt(w),A=x.left+(w.clientLeft+parseFloat(P.paddingLeft))*f.x,T=x.top+(w.clientTop+parseFloat(P.paddingTop))*f.y;l*=f.x,u*=f.y,d*=f.x,g*=f.y,l+=A,u+=T,y=It(w),w=dn(y)}}return Je({width:d,height:g,x:l,y:u})}function yn(e,t){const r=tn(e).scrollLeft;return t?t.left+r:me(Ut(e)).left+r}function Jn(e,t,r){r===void 0&&(r=!1);const n=e.getBoundingClientRect(),o=n.left+t.scrollLeft-(r?0:yn(e,n)),s=n.top+t.scrollTop;return{x:o,y:s}}function fo(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e;const s=o==="fixed",i=Ut(n),a=t?$e(t.floating):!1;if(n===i||a&&s)return r;let l={scrollLeft:0,scrollTop:0},u=zt(1);const d=zt(0),g=jt(n);if((g||!g&&!s)&&((Pe(n)!=="body"||Be(i))&&(l=tn(n)),jt(n))){const m=me(n);u=_e(n),d.x=m.x+n.clientLeft,d.y=m.y+n.clientTop}const b=i&&!g&&!s?Jn(i,l,!0):zt(0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-l.scrollLeft*u.x+d.x+b.x,y:r.y*u.y-l.scrollTop*u.y+d.y+b.y}}function go(e){return Array.from(e.getClientRects())}function mo(e){const t=Ut(e),r=tn(e),n=e.ownerDocument.body,o=Dt(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),s=Dt(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let i=-r.scrollLeft+yn(e);const a=-r.scrollTop;return Bt(n).direction==="rtl"&&(i+=Dt(t.clientWidth,n.clientWidth)-o),{width:o,height:s,x:i,y:a}}function ho(e,t){const r=It(e),n=Ut(e),o=r.visualViewport;let s=n.clientWidth,i=n.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const u=pn();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}const vo=new Set(["absolute","fixed"]);function po(e,t){const r=me(e,!0,t==="fixed"),n=r.top+e.clientTop,o=r.left+e.clientLeft,s=jt(e)?_e(e):zt(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,u=n*s.y;return{width:i,height:a,x:l,y:u}}function Dn(e,t,r){let n;if(t==="viewport")n=ho(e,r);else if(t==="document")n=mo(Ut(e));else if(Wt(t))n=po(t,r);else{const o=Gn(e);n={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Je(n)}function Qn(e,t){const r=re(e);return r===t||!Wt(r)||Te(r)?!1:Bt(r).position==="fixed"||Qn(r,t)}function bo(e,t){const r=t.get(e);if(r)return r;let n=Le(e,[],!1).filter(a=>Wt(a)&&Pe(a)!=="body"),o=null;const s=Bt(e).position==="fixed";let i=s?re(e):e;for(;Wt(i)&&!Te(i);){const a=Bt(i),l=vn(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&vo.has(o.position)||Be(i)&&!l&&Qn(e,i))?n=n.filter(d=>d!==i):o=a,i=re(i)}return t.set(e,n),n}function yo(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e;const i=[...r==="clippingAncestors"?$e(t)?[]:bo(t,this._c):[].concat(r),n],a=i[0],l=i.reduce((u,d)=>{const g=Dn(t,d,o);return u.top=Dt(g.top,u.top),u.right=ee(g.right,u.right),u.bottom=ee(g.bottom,u.bottom),u.left=Dt(g.left,u.left),u},Dn(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function wo(e){const{width:t,height:r}=qn(e);return{width:t,height:r}}function xo(e,t,r){const n=jt(t),o=Ut(t),s=r==="fixed",i=me(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=zt(0);function u(){l.x=yn(o)}if(n||!n&&!s)if((Pe(t)!=="body"||Be(o))&&(a=tn(t)),n){const m=me(t,!0,s,t);l.x=m.x+t.clientLeft,l.y=m.y+t.clientTop}else o&&u();s&&!n&&o&&u();const d=o&&!n&&!s?Jn(o,a):zt(0),g=i.left+a.scrollLeft-l.x-d.x,b=i.top+a.scrollTop-l.y-d.y;return{x:g,y:b,width:i.width,height:i.height}}function sn(e){return Bt(e).position==="static"}function In(e,t){if(!jt(e)||Bt(e).position==="fixed")return null;if(t)return t(e);let r=e.offsetParent;return Ut(e)===r&&(r=r.ownerDocument.body),r}function Zn(e,t){const r=It(e);if($e(e))return r;if(!jt(e)){let o=re(e);for(;o&&!Te(o);){if(Wt(o)&&!sn(o))return o;o=re(o)}return r}let n=In(e,t);for(;n&&no(n)&&sn(n);)n=In(n,t);return n&&Te(n)&&sn(n)&&!vn(n)?r:n||ao(e)||r}const Oo=async function(e){const t=this.getOffsetParent||Zn,r=this.getDimensions,n=await r(e.floating);return{reference:xo(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function _o(e){return Bt(e).direction==="rtl"}const Ao={convertOffsetParentRelativeRectToViewportRelativeRect:fo,getDocumentElement:Ut,getClippingRect:yo,getOffsetParent:Zn,getElementRects:Oo,getClientRects:go,getDimensions:wo,getScale:_e,isElement:Wt,isRTL:_o};function $n(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function To(e,t){let r=null,n;const o=Ut(e);function s(){var a;clearTimeout(n),(a=r)==null||a.disconnect(),r=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:d,top:g,width:b,height:m}=u;if(a||t(),!b||!m)return;const y=Ke(g),w=Ke(o.clientWidth-(d+b)),f=Ke(o.clientHeight-(g+m)),x=Ke(d),A={rootMargin:-y+"px "+-w+"px "+-f+"px "+-x+"px",threshold:Dt(0,ee(1,l))||1};let T=!0;function O(W){const S=W[0].intersectionRatio;if(S!==l){if(!T)return i();S?i(!1,S):n=setTimeout(()=>{i(!1,1e-7)},1e3)}S===1&&!$n(u,e.getBoundingClientRect())&&i(),T=!1}try{r=new IntersectionObserver(O,{...A,root:o.ownerDocument})}catch{r=new IntersectionObserver(O,A)}r.observe(e)}return i(!0),s}function Co(e,t,r,n){n===void 0&&(n={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,u=bn(e),d=o||s?[...u?Le(u):[],...Le(t)]:[];d.forEach(x=>{o&&x.addEventListener("scroll",r,{passive:!0}),s&&x.addEventListener("resize",r)});const g=u&&a?To(u,r):null;let b=-1,m=null;i&&(m=new ResizeObserver(x=>{let[P]=x;P&&P.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var A;(A=m)==null||A.observe(t)})),r()}),u&&!l&&m.observe(u),m.observe(t));let y,w=l?me(e):null;l&&f();function f(){const x=me(e);w&&!$n(w,x)&&r(),w=x,y=requestAnimationFrame(f)}return r(),()=>{var x;d.forEach(P=>{o&&P.removeEventListener("scroll",r),s&&P.removeEventListener("resize",r)}),g==null||g(),(x=m)==null||x.disconnect(),m=null,l&&cancelAnimationFrame(y)}}const Eo=Qr,ko=Zr,Po=qr,So=$r,Do=Yr,Io=(e,t,r)=>{const n=new Map,o={platform:Ao,...r},s={...o.platform,_c:n};return Xr(e,t,{...o,platform:s})},Ro={strategy:"absolute",placement:"top",gutter:5,flip:!0,sameWidth:!1,overflowPadding:8},Mo={bottom:"rotate(45deg)",left:"rotate(135deg)",top:"rotate(225deg)",right:"rotate(315deg)"};function Fo(e,t,r={}){if(!t||!e||r===null)return{destroy:ue};const n={...Ro,...r},o=t.querySelector("[data-arrow=true]"),s=[];n.flip&&s.push(Po({boundary:n.boundary,padding:n.overflowPadding}));const i=_(o)?o.offsetHeight/2:0;if(n.gutter||n.offset){const l=n.gutter?{mainAxis:n.gutter}:n.offset;(l==null?void 0:l.mainAxis)!=null&&(l.mainAxis+=i),s.push(Eo(l))}s.push(ko({boundary:n.boundary,crossAxis:n.overlap,padding:n.overflowPadding})),o&&s.push(Do({element:o,padding:8})),s.push(So({padding:n.overflowPadding,apply({rects:l,availableHeight:u,availableWidth:d}){n.sameWidth&&Object.assign(t.style,{width:`${Math.round(l.reference.width)}px`,minWidth:"unset"}),n.fitViewport&&Object.assign(t.style,{maxWidth:`${d}px`,maxHeight:`${u}px`})}}));function a(){if(!e||!t||_(e)&&!e.ownerDocument.documentElement.contains(e))return;const{placement:l,strategy:u}=n;Io(e,t,{placement:l,middleware:s,strategy:u}).then(d=>{const g=Math.round(d.x),b=Math.round(d.y),[m,y]=Lo(d.placement);if(t.setAttribute("data-side",m),t.setAttribute("data-align",y),Object.assign(t.style,{position:n.strategy,top:`${b}px`,left:`${g}px`}),_(o)&&d.middlewareData.arrow){const{x:w,y:f}=d.middlewareData.arrow,x=d.placement.split("-")[0];o.setAttribute("data-side",x),Object.assign(o.style,{position:"absolute",left:w!=null?`${w}px`:"",top:f!=null?`${f}px`:"",[x]:`calc(100% - ${i}px)`,transform:Mo[x],backgroundColor:"inherit",zIndex:"inherit"})}return d})}return Object.assign(t.style,{position:n.strategy}),{destroy:Co(e,t,a)}}function Lo(e){const[t,r="center"]=e.split("-");return[t,r]}const Wo={floating:{},focusTrap:{},modal:{},escapeKeydown:{},portal:"body"},Rn=(e,t)=>{e.dataset.escapee="";const{anchorElement:r,open:n,options:o}=t;if(!r||!n||!o)return{destroy:ue};const s={...Wo,...o},i=[];if(s.portal!==null&&i.push(zn(e,s.portal).destroy),i.push(Fo(r,e,s.floating).destroy),s.focusTrap!==null){const{useFocusTrap:l}=hr({immediate:!0,escapeDeactivates:!1,allowOutsideClick:!0,returnFocusOnDeactivate:!1,fallbackFocus:e,...s.focusTrap});i.push(l(e).destroy)}s.modal!==null&&i.push(vr(e,{onClose:()=>{_(r)&&(n.set(!1),r.focus())},shouldCloseOnInteractOutside:l=>!(l.defaultPrevented||_(r)&&r.contains(l.target)),...s.modal}).destroy),s.escapeKeydown!==null&&i.push(Nn(e,{enabled:n,handler:()=>{n.set(!1)},...s.escapeKeydown}).destroy);const a=Kt(...i);return{destroy(){a()}}},Bo={ltr:[...ln,Et.ARROW_RIGHT]},No={ltr:[Et.ARROW_LEFT]},Mn=["menu","trigger"],Vo={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function Ko(e){const{name:t,selector:r}=ur(e.selector),{preventScroll:n,arrowSize:o,positioning:s,closeOnEscape:i,closeOnOutsideClick:a,portal:l,forceVisible:u,typeahead:d,loop:g,closeFocus:b,disableFocusFirstItem:m,closeOnItemClick:y,onOutsideClick:w}=e.rootOptions,f=e.rootOpen,x=e.rootActiveTrigger,P=e.nextFocusable,A=e.prevFocusable,T=wt.writable(!1),O=wt(yt(0)),W=wt(yt(null)),S=wt(yt("right")),j=wt(yt(null)),J=wt(Oe([S,W],([c,h])=>v=>c===(h==null?void 0:h.side)&&zo(v,h==null?void 0:h.area))),{typed:Q,handleTypeaheadSearch:Z}=Fr(),N=Fe({...An(Mn),...e.ids}),at=Cn({open:f,forceVisible:u,activeTrigger:x}),$=Ct(t(),{stores:[at,l,N.menu,N.trigger],returned:([c,h,v,p])=>({role:"menu",hidden:c?void 0:!0,style:De({display:c?void 0:"none"}),id:v,"aria-labelledby":p,"data-state":c?"open":"closed","data-portal":cr(h),tabindex:-1}),action:c=>{let h=ue;const v=Vt([at,x,s,a,l,i],([E,q,ot,B,R,F])=>{h(),!(!E||!q)&&Ve().then(()=>{h(),Me(c,r),h=Rn(c,{anchorElement:q,open:f,options:{floating:ot,modal:{closeOnInteractOutside:B,shouldCloseOnInteractOutside:D=>{var I;return(I=w.get())==null||I(D),!(D.defaultPrevented||_(q)&&q.contains(D.target))},onClose:()=>{f.set(!1),q.focus()},open:E},portal:Tn(c,R),escapeKeydown:F?void 0:null}}).destroy})}),p=Kt(H(c,"keydown",E=>{const q=E.target,ot=E.currentTarget;if(!_(q)||!_(ot)||!(q.closest('[role="menu"]')===ot))return;if(On.includes(E.key)&&Ln(E,g.get()??!1),E.key===Et.TAB){E.preventDefault(),f.set(!1),Fn(E,P,A);return}const R=E.key.length===1;!(E.ctrlKey||E.altKey||E.metaKey)&&R&&d.get()===!0&&Z(E.key,se(ot))}));return{destroy(){v(),p(),h()}}}}),kt=Ct(t("trigger"),{stores:[f,N.menu,N.trigger],returned:([c,h,v])=>({"aria-controls":h,"aria-expanded":c,"data-state":c?"open":"closed",id:v,tabindex:0}),action:c=>(ze(c),x.update(v=>v||c),{destroy:Kt(H(c,"click",v=>{const p=f.get(),E=v.currentTarget;_(E)&&(Yt(E),p||v.preventDefault())}),H(c,"keydown",v=>{const p=v.currentTarget;if(!_(p)||!(ln.includes(v.key)||v.key===Et.ARROW_DOWN))return;v.preventDefault(),Yt(p);const E=p.getAttribute("aria-controls");if(!E)return;const q=document.getElementById(E);if(!q)return;const ot=se(q);ot.length&&bt(ot[0])}))})}),X=Ct(t("arrow"),{stores:o,returned:c=>({"data-arrow":!0,style:De({position:"absolute",width:`var(--arrow-size, ${c}px)`,height:`var(--arrow-size, ${c}px)`})})}),K=Ct(t("overlay"),{stores:[at],returned:([c])=>({hidden:c?void 0:!0,tabindex:-1,style:De({display:c?void 0:"none"}),"aria-hidden":"true","data-state":Uo(c)}),action:c=>{let h=ue;if(i.get()){const p=Nn(c,{handler:()=>{f.set(!1);const E=x.get();E&&E.focus()}});p&&p.destroy&&(h=p.destroy)}const v=Vt([l],([p])=>{if(p===null)return ue;const E=Tn(c,p);return E===null?ue:zn(c,E).destroy});return{destroy(){h(),v()}}}}),ft=Ct(t("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:c=>(Me(c,r),ze(c),{destroy:Kt(H(c,"pointerdown",v=>{const p=v.currentTarget;if(_(p)&&Gt(p)){v.preventDefault();return}}),H(c,"click",v=>{const p=v.currentTarget;if(_(p)){if(Gt(p)){v.preventDefault();return}if(v.defaultPrevented){bt(p);return}y.get()&&ge(1).then(()=>{f.set(!1)})}}),H(c,"keydown",v=>{Ot(v)}),H(c,"pointermove",v=>{Y(v)}),H(c,"pointerleave",v=>{xt(v)}),H(c,"focusin",v=>{pt(v)}),H(c,"focusout",v=>{it(v)}))})}),At=Ct(t("group"),{returned:()=>c=>({role:"group","aria-labelledby":c})}),Zt=Ct(t("group-label"),{returned:()=>c=>({id:c})}),pe={defaultChecked:!1,disabled:!1},Se=c=>{const h={...pe,...c},v=h.checked??yt(h.defaultChecked??null),p=je(v,h.onCheckedChange),E=yt(h.disabled),q=Ct(t("checkbox-item"),{stores:[p,E],returned:([R,F])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":gt(R)?"mixed":R?"true":"false","data-disabled":on(F),"data-state":mt(R)}),action:R=>(Me(R,r),ze(R),{destroy:Kt(H(R,"pointerdown",D=>{const I=D.currentTarget;if(_(I)&&Gt(I)){D.preventDefault();return}}),H(R,"click",D=>{const I=D.currentTarget;if(_(I)){if(Gt(I)){D.preventDefault();return}if(D.defaultPrevented){bt(I);return}p.update(te=>gt(te)?!0:!te),y.get()&&Ve().then(()=>{f.set(!1)})}}),H(R,"keydown",D=>{Ot(D)}),H(R,"pointermove",D=>{const I=D.currentTarget;if(_(I)){if(Gt(I)){Lt(D);return}Y(D,I)}}),H(R,"pointerleave",D=>{xt(D)}),H(R,"focusin",D=>{pt(D)}),H(R,"focusout",D=>{it(D)}))})}),ot=Oe(p,R=>R===!0),B=Oe(p,R=>R==="indeterminate");return{elements:{checkboxItem:q},states:{checked:p},helpers:{isChecked:ot,isIndeterminate:B},options:{disabled:E}}},Rt=(c={})=>{const h=c.value??yt(c.defaultValue??null),v=je(h,c.onValueChange),p=Ct(t("radio-group"),{returned:()=>({role:"group"})}),E={disabled:!1},q=Ct(t("radio-item"),{stores:[v],returned:([B])=>R=>{const{value:F,disabled:D}={...E,...R},I=B===F;return{disabled:D,role:"menuitemradio","data-state":I?"checked":"unchecked","aria-checked":I,"data-disabled":on(D),"data-value":F,"data-orientation":"vertical",tabindex:-1}},action:B=>(Me(B,r),{destroy:Kt(H(B,"pointerdown",F=>{const D=F.currentTarget;if(!_(D))return;const I=B.dataset.value;if(B.dataset.disabled||I===void 0){F.preventDefault();return}}),H(B,"click",F=>{const D=F.currentTarget;if(!_(D))return;const I=B.dataset.value;if(B.dataset.disabled||I===void 0){F.preventDefault();return}if(F.defaultPrevented){if(!_(D))return;bt(D);return}v.set(I),y.get()&&Ve().then(()=>{f.set(!1)})}),H(B,"keydown",F=>{Ot(F)}),H(B,"pointermove",F=>{const D=F.currentTarget;if(!_(D))return;const I=B.dataset.value;if(B.dataset.disabled||I===void 0){Lt(F);return}Y(F,D)}),H(B,"pointerleave",F=>{xt(F)}),H(B,"focusin",F=>{pt(F)}),H(B,"focusout",F=>{it(F)}))})}),ot=Oe(v,B=>R=>B===R);return{elements:{radioGroup:p,radioItem:q},states:{value:v},helpers:{isChecked:ot}}},{elements:{root:Ft}}=Go({orientation:"horizontal"}),Xt={...Vo,disabled:!1,positioning:{placement:"right-start",gutter:8}},be=c=>{const h={...Xt,...c},v=h.open??yt(!1),p=je(v,h==null?void 0:h.onOpenChange),E=Fe(Vn(h,"ids")),{positioning:q,arrowSize:ot,disabled:B}=E,R=wt(yt(null)),F=wt(yt(null)),D=wt(yt(0)),I=Fe({...An(Mn),...h.ids});_n(()=>{const V=document.getElementById(I.trigger.get());V&&R.set(V)});const te=Cn({open:p,forceVisible:u,activeTrigger:R}),er=Ct(t("submenu"),{stores:[te,I.menu,I.trigger],returned:([V,dt,Tt])=>({role:"menu",hidden:V?void 0:!0,style:De({display:V?void 0:"none"}),id:dt,"aria-labelledby":Tt,"data-state":V?"open":"closed","data-id":dt,tabindex:-1}),action:V=>{let dt=ue;const Tt=Vt([te,q],([C,G])=>{if(dt(),!C)return;const et=R.get();et&&Ve().then(()=>{dt();const st=Mt(et);dt=Rn(V,{anchorElement:et,open:p,options:{floating:G,portal:_(st)?st:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy})}),z=Kt(H(V,"keydown",C=>{if(C.key===Et.ESCAPE)return;const G=C.target,et=C.currentTarget;if(!_(G)||!_(et)||!(G.closest('[role="menu"]')===et))return;if(On.includes(C.key)){C.stopImmediatePropagation(),Ln(C,g.get()??!1);return}const ye=No.ltr.includes(C.key),we=C.ctrlKey||C.altKey||C.metaKey,Ne=C.key.length===1;if(ye){const wn=R.get();C.preventDefault(),p.update(()=>(wn&&bt(wn),!1));return}if(C.key===Et.TAB){C.preventDefault(),f.set(!1),Fn(C,P,A);return}!we&&Ne&&d.get()===!0&&Z(C.key,se(et))}),H(V,"pointermove",C=>{tt(C)}),H(V,"focusout",C=>{const G=R.get();if(T.get()){const et=C.target,st=document.getElementById(I.menu.get());if(!_(st)||!_(et))return;!st.contains(et)&&et!==G&&p.set(!1)}else{const et=C.currentTarget,st=C.relatedTarget;if(!_(st)||!_(et))return;!et.contains(st)&&st!==G&&p.set(!1)}}));return{destroy(){Tt(),dt(),z()}}}}),nr=Ct(t("subtrigger"),{stores:[p,B,I.menu,I.trigger],returned:([V,dt,Tt,z])=>({role:"menuitem",id:z,tabindex:-1,"aria-controls":Tt,"aria-expanded":V,"data-state":V?"open":"closed","data-disabled":on(dt),"aria-haspopop":"menu"}),action:V=>{Me(V,r),ze(V),R.update(z=>z||V);const dt=()=>{an(F),window.clearTimeout(D.get()),W.set(null)},Tt=Kt(H(V,"click",z=>{if(z.defaultPrevented)return;const C=z.currentTarget;!_(C)||Gt(C)||(bt(C),p.get()||p.update(G=>G||(R.set(C),!G)))}),H(V,"keydown",z=>{const C=Q.get(),G=z.currentTarget;if(!(!_(G)||Gt(G)||C.length>0&&z.key===Et.SPACE)&&Bo.ltr.includes(z.key)){if(!p.get()){G.click(),z.preventDefault();return}const st=G.getAttribute("aria-controls");if(!st)return;const ye=document.getElementById(st);if(!_(ye))return;const we=se(ye)[0];bt(we)}}),H(V,"pointermove",z=>{if(!Re(z)||(qt(z),z.defaultPrevented))return;const C=z.currentTarget;if(!_(C))return;jo(I.menu.get())||bt(C);const G=F.get();!p.get()&&!G&&!Gt(C)&&F.set(window.setTimeout(()=>{p.update(()=>(R.set(C),!0)),an(F)},100))}),H(V,"pointerleave",z=>{if(!Re(z))return;an(F);const C=document.getElementById(I.menu.get()),G=C==null?void 0:C.getBoundingClientRect();if(G){const et=C==null?void 0:C.dataset.side,st=et==="right",ye=st?-5:5,we=G[st?"left":"right"],Ne=G[st?"right":"left"];W.set({area:[{x:z.clientX+ye,y:z.clientY},{x:we,y:G.top},{x:Ne,y:G.top},{x:Ne,y:G.bottom},{x:we,y:G.bottom}],side:et}),window.clearTimeout(D.get()),D.set(window.setTimeout(()=>{W.set(null)},300))}else{if($t(z),z.defaultPrevented)return;W.set(null)}}),H(V,"focusout",z=>{const C=z.currentTarget;if(!_(C))return;xe(C);const G=z.relatedTarget;if(!_(G))return;const et=C.getAttribute("aria-controls");if(!et)return;const st=document.getElementById(et);st&&!st.contains(G)&&p.set(!1)}),H(V,"focusin",z=>{pt(z)}));return{destroy(){dt(),Tt()}}}}),rr=Ct(t("subarrow"),{stores:ot,returned:V=>({"data-arrow":!0,style:De({position:"absolute",width:`var(--arrow-size, ${V}px)`,height:`var(--arrow-size, ${V}px)`})})});return Vt([f],([V])=>{V||(R.set(null),p.set(!1))}),Vt([W],([V])=>{!ce||V||window.clearTimeout(D.get())}),Vt([p],([V])=>{if(ce&&(V&&T.get()&&ge(1).then(()=>{const dt=document.getElementById(I.menu.get());if(!dt)return;const Tt=se(dt);Tt.length&&bt(Tt[0])}),!V)){const dt=j.get(),Tt=document.getElementById(I.trigger.get());if(dt&&ge(1).then(()=>{const z=document.getElementById(I.menu.get());z&&z.contains(dt)&&xe(dt)}),!Tt||document.activeElement===Tt)return;xe(Tt)}}),{ids:I,elements:{subTrigger:nr,subMenu:er,subArrow:rr},states:{subOpen:p},options:E}};_n(()=>{const c=document.getElementById(N.trigger.get());_(c)&&f.get()&&x.set(c);const h=[],v=()=>T.set(!1),p=()=>{T.set(!0),h.push(Kt(ie(document,"pointerdown",v,{capture:!0,once:!0}),ie(document,"pointermove",v,{capture:!0,once:!0})))},E=q=>{if(q.key===Et.ESCAPE&&i.get()){f.set(!1);return}};return h.push(ie(document,"keydown",p,{capture:!0})),h.push(ie(document,"keydown",E)),()=>{h.forEach(q=>q())}}),Vt([f,j],([c,h])=>{!c&&h&&xe(h)}),Vt([f],([c])=>{if(ce&&!c){const h=x.get();if(!h)return;const v=b.get();!c&&h&&pr({prop:v,defaultEl:h})}}),Vt([f,n],([c,h])=>{if(!ce)return;const v=[];return c&&h&&v.push(br()),ge(1).then(()=>{const p=document.getElementById(N.menu.get());if(p&&c&&T.get()){if(m.get()){bt(p);return}const E=se(p);if(!E.length)return;bt(E[0])}}),()=>{v.forEach(p=>p())}}),Vt(f,c=>{if(!ce)return;const h=()=>T.set(!1),v=p=>{if(T.set(!0),p.key===Et.ESCAPE&&c&&i.get()){f.set(!1);return}};return Kt(ie(document,"pointerdown",h,{capture:!0,once:!0}),ie(document,"pointermove",h,{capture:!0,once:!0}),ie(document,"keydown",v,{capture:!0}))});function Yt(c){f.update(h=>{const v=!h;return v&&(P.set(Dr(c)),A.set(Ir(c)),x.set(c)),v})}function pt(c){const h=c.currentTarget;if(!_(h))return;const v=j.get();v&&xe(v),Pr(h),j.set(h)}function it(c){const h=c.currentTarget;_(h)&&xe(h)}function qt(c){lt(c)&&c.preventDefault()}function Lt(c){if(lt(c))return;const h=c.target;if(!_(h))return;const v=Mt(h);v&&bt(v)}function $t(c){lt(c)&&c.preventDefault()}function tt(c){if(!Re(c))return;const h=c.target,v=c.currentTarget;if(!_(v)||!_(h))return;const p=O.get(),E=p!==c.clientX;if(v.contains(h)&&E){const q=c.clientX>p?"right":"left";S.set(q),O.set(c.clientX)}}function Y(c,h=null){if(!Re(c)||(qt(c),c.defaultPrevented))return;if(h){bt(h);return}const v=c.currentTarget;_(v)&&bt(v)}function xt(c){Re(c)&&Lt(c)}function Ot(c){if(Q.get().length>0&&c.key===Et.SPACE){c.preventDefault();return}if(ln.includes(c.key)){c.preventDefault();const p=c.currentTarget;if(!_(p))return;p.click()}}function gt(c){return c==="indeterminate"}function mt(c){return gt(c)?"indeterminate":c?"checked":"unchecked"}function lt(c){return J.get()(c)}function Mt(c){const h=c.closest('[role="menu"]');return _(h)?h:null}return{elements:{trigger:kt,menu:$,overlay:K,item:ft,group:At,groupLabel:Zt,arrow:X,separator:Ft},builders:{createCheckboxItem:Se,createSubmenu:be,createMenuRadioGroup:Rt},states:{open:f},helpers:{handleTypeaheadSearch:Z},ids:N,options:e.rootOptions}}function Fn(e,t,r){if(e.shiftKey){const n=r.get();n&&(e.preventDefault(),ge(1).then(()=>n.focus()),r.set(null))}else{const n=t.get();n&&(e.preventDefault(),ge(1).then(()=>n.focus()),t.set(null))}}function se(e){return Array.from(e.querySelectorAll(`[data-melt-menu-id="${e.id}"]`)).filter(t=>_(t))}function ze(e){!e||!Gt(e)||(e.setAttribute("data-disabled",""),e.setAttribute("aria-disabled","true"))}function an(e){if(!ce)return;const t=e.get();t&&(window.clearTimeout(t),e.set(null))}function Re(e){return e.pointerType==="mouse"}function Me(e,t){if(!e)return;const r=e.closest(`${t()}, ${t("submenu")}`);_(r)&&e.setAttribute("data-melt-menu-id",r.id)}function Ln(e,t){e.preventDefault();const r=document.activeElement,n=e.currentTarget;if(!_(r)||!_(n))return;const o=se(n);if(!o.length)return;const s=o.filter(l=>!(l.hasAttribute("data-disabled")||l.getAttribute("disabled")==="true")),i=s.indexOf(r);let a;switch(e.key){case Et.ARROW_DOWN:t?a=i<s.length-1?i+1:0:a=i<s.length-1?i+1:i;break;case Et.ARROW_UP:t?a=i>0?i-1:s.length-1:a=i<0?s.length-1:i>0?i-1:0;break;case Et.HOME:a=0;break;case Et.END:a=s.length-1;break;default:return}bt(s[a])}function zo(e,t){if(!t)return!1;const r={x:e.clientX,y:e.clientY};return Ho(r,t)}function Ho(e,t){const{x:r,y:n}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>n!=d>n&&r<(u-a)*(n-l)/(d-l)+a&&(o=!o)}return o}function jo(e){const t=document.activeElement;if(!_(t))return!1;const r=t.closest(`[data-id="${e}"]`);return _(r)}function Uo(e){return e?"open":"closed"}const Xo={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function Yo(e){const t={...Xo,...e},r=Fe(Vn(t,"ids")),n=t.open??yt(t.defaultOpen),o=je(n,t==null?void 0:t.onOpenChange),s=wt(yt(null)),i=wt(yt(null)),a=wt(yt(null)),{elements:l,builders:u,ids:d,states:g,options:b}=Ko({rootOptions:r,rootOpen:o,rootActiveTrigger:wt(s),nextFocusable:wt(i),prevFocusable:wt(a),selector:"dropdown-menu",ids:t.ids});return{ids:d,elements:l,states:g,builders:u,options:b}}const qo={orientation:"horizontal",decorative:!1},Go=e=>{const t={...qo,...e},r=Fe(t),{orientation:n,decorative:o}=r;return{elements:{root:Ct("separator",{stores:[n,o],returned:([i,a])=>({role:a?"none":"separator","aria-orientation":i==="vertical"?i:void 0,"aria-hidden":a,"data-orientation":i})})},options:r}};function Jo(e){return(t={})=>Qo(e,t)}function Qo(e,t){const n={...{side:"bottom",align:"center",sideOffset:0,alignOffset:0,sameWidth:!1,avoidCollisions:!0,collisionPadding:8,fitViewport:!1,strategy:"absolute",overlap:!1},...t};e.update(o=>({...o,placement:Zo(n.side,n.align),offset:{...o.offset,mainAxis:n.sideOffset,crossAxis:n.alignOffset},gutter:0,sameWidth:n.sameWidth,flip:n.avoidCollisions,overflowPadding:n.collisionPadding,boundary:n.collisionBoundary,fitViewport:n.fitViewport,strategy:n.strategy,overlap:n.overlap}))}function Zo(e,t){return t==="center"?e:`${e}-${t}`}function tr(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function en(){const{NAME:e}=tr();return or(e)}function $o(e){const{NAME:t,PARTS:r}=tr(),n=wr("menu",r),o={...Yo({...fr(e),forceVisible:!0}),getAttrs:n};return ir(t,o),{...o,updateOption:gr(o.options)}}function ti(e){const r={...{side:"bottom",align:"center"},...e},{options:{positioning:n}}=en();Jo(n)(r)}function ei(e,t){const r=Ht(t,["children","$$slots","$$events","$$legacy"]),n=Ht(r,["href","asChild","disabled","el"]);he(t,!1);const[o,s]=We(),i=()=>Ae(m,"$item",o),a=Xe(),l=Xe();let u=k(t,"href",24,()=>{}),d=k(t,"asChild",8,!1),g=k(t,"disabled",8,!1),b=k(t,"el",28,()=>{});const{elements:{item:m},getAttrs:y}=en(),w=fn();nt(()=>i(),()=>{Ue(a,i())}),nt(()=>U(g()),()=>{Ue(l,{...y("item"),...xr(g())})}),nt(()=>(L(a),L(l)),()=>{Object.assign(L(a),L(l))}),Qe(),Ce();var f=ct(),x=rt(f);{var P=T=>{var O=ct(),W=rt(O);St(W,t,"default",{get builder(){return L(a)}},null),M(T,O)},A=T=>{var O=ct(),W=rt(O);kr(W,()=>u()?"a":"div",!1,(S,j)=>{fe(S,Z=>b(Z),()=>b()),de(S,Z=>{var N,at;return(at=(N=L(a)).action)==null?void 0:at.call(N,Z)}),le(S,()=>({href:u(),...L(a),...n})),_t("m-click",S,w),_t("m-focusin",S,w),_t("m-focusout",S,w),_t("m-keydown",S,w),_t("m-pointerdown",S,w),_t("m-pointerleave",S,w),_t("m-pointermove",S,w),_t("pointerenter",S,function(Z){Jt.call(this,t,Z)});var J=ct(),Q=rt(J);St(Q,t,"default",{get builder(){return L(a)}},null),M(j,J)}),M(T,O)};Pt(x,T=>{d()?T(P):T(A,!1)})}M(e,f),ve(),s()}function ni(e,t){he(t,!1);const[r,n]=We(),o=()=>Ae(O,"$idValues",r);let s=k(t,"closeOnOutsideClick",24,()=>{}),i=k(t,"closeOnEscape",24,()=>{}),a=k(t,"portal",24,()=>{}),l=k(t,"open",28,()=>{}),u=k(t,"onOpenChange",24,()=>{}),d=k(t,"preventScroll",24,()=>{}),g=k(t,"loop",24,()=>{}),b=k(t,"dir",24,()=>{}),m=k(t,"typeahead",24,()=>{}),y=k(t,"closeFocus",24,()=>{}),w=k(t,"disableFocusFirstItem",24,()=>{}),f=k(t,"closeOnItemClick",24,()=>{}),x=k(t,"onOutsideClick",24,()=>{});const{states:{open:P},updateOption:A,ids:T}=$o({closeOnOutsideClick:s(),closeOnEscape:i(),portal:a(),forceVisible:!0,defaultOpen:l(),preventScroll:d(),loop:g(),dir:b(),typeahead:m(),closeFocus:y(),disableFocusFirstItem:w(),closeOnItemClick:f(),onOutsideClick:x(),onOpenChange:({next:j})=>{var J;return l()!==j&&((J=u())==null||J(j),l(j)),j}}),O=Oe([T.menu,T.trigger],([j,J])=>({menu:j,trigger:J}));nt(()=>U(l()),()=>{l()!==void 0&&P.set(l())}),nt(()=>U(s()),()=>{A("closeOnOutsideClick",s())}),nt(()=>U(i()),()=>{A("closeOnEscape",i())}),nt(()=>U(a()),()=>{A("portal",a())}),nt(()=>U(d()),()=>{A("preventScroll",d())}),nt(()=>U(g()),()=>{A("loop",g())}),nt(()=>U(b()),()=>{A("dir",b())}),nt(()=>U(y()),()=>{A("closeFocus",y())}),nt(()=>U(w()),()=>{A("disableFocusFirstItem",w())}),nt(()=>U(m()),()=>{A("typeahead",m())}),nt(()=>U(f()),()=>{A("closeOnItemClick",f())}),nt(()=>U(x()),()=>{A("onOutsideClick",x())}),Qe(),Ce();var W=ct(),S=rt(W);St(S,t,"default",{get ids(){return o()}},null),M(e,W),ve(),n()}var ri=ut("<div><!></div>"),oi=ut("<div><!></div>"),ii=ut("<div><!></div>"),si=ut("<div><!></div>"),ai=ut("<div><!></div>");function li(e,t){const r=Ht(t,["children","$$slots","$$events","$$legacy"]),n=Ht(r,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]);he(t,!1);const[o,s]=We(),i=()=>Ae(at,"$menu",o),a=()=>Ae($,"$open",o),l=Xe();let u=k(t,"transition",24,()=>{}),d=k(t,"transitionConfig",24,()=>{}),g=k(t,"inTransition",24,()=>{}),b=k(t,"inTransitionConfig",24,()=>{}),m=k(t,"outTransition",24,()=>{}),y=k(t,"outTransitionConfig",24,()=>{}),w=k(t,"asChild",8,!1),f=k(t,"id",24,()=>{}),x=k(t,"side",8,"bottom"),P=k(t,"align",8,"center"),A=k(t,"sideOffset",8,0),T=k(t,"alignOffset",8,0),O=k(t,"collisionPadding",8,8),W=k(t,"avoidCollisions",8,!0),S=k(t,"collisionBoundary",24,()=>{}),j=k(t,"sameWidth",8,!1),J=k(t,"fitViewport",8,!1),Q=k(t,"strategy",8,"absolute"),Z=k(t,"overlap",8,!1),N=k(t,"el",28,()=>{});const{elements:{menu:at},states:{open:$},ids:kt,getAttrs:X}=en(),K=fn(),ft=X("content");nt(()=>U(f()),()=>{f()&&kt.menu.set(f())}),nt(()=>i(),()=>{Ue(l,i())}),nt(()=>L(l),()=>{Object.assign(L(l),ft)}),nt(()=>(a(),U(x()),U(P()),U(A()),U(T()),U(O()),U(W()),U(S()),U(j()),U(J()),U(Q()),U(Z())),()=>{a()&&ti({side:x(),align:P(),sideOffset:A(),alignOffset:T(),collisionPadding:O(),avoidCollisions:W(),collisionBoundary:S(),sameWidth:j(),fitViewport:J(),strategy:Q(),overlap:Z()})}),Qe(),Ce();var At=ct(),Zt=rt(At);{var pe=Rt=>{var Ft=ct(),Xt=rt(Ft);St(Xt,t,"default",{get builder(){return L(l)}},null),M(Rt,Ft)},Se=Rt=>{var Ft=ct(),Xt=rt(Ft);{var be=pt=>{var it=ri();le(it,()=>({...L(l),...n}));var qt=ht(it);St(qt,t,"default",{get builder(){return L(l)}},null),vt(it),fe(it,Lt=>N(Lt),()=>N()),de(it,Lt=>{var $t,tt;return(tt=($t=L(l)).action)==null?void 0:tt.call($t,Lt)}),ae(()=>_t("m-keydown",it,K)),Ie(3,it,u,d),M(pt,it)},Yt=pt=>{var it=ct(),qt=rt(it);{var Lt=tt=>{var Y=oi();le(Y,()=>({...L(l),...n}));var xt=ht(Y);St(xt,t,"default",{get builder(){return L(l)}},null),vt(Y),fe(Y,Ot=>N(Ot),()=>N()),de(Y,Ot=>{var gt,mt;return(mt=(gt=L(l)).action)==null?void 0:mt.call(gt,Ot)}),ae(()=>_t("m-keydown",Y,K)),Ie(1,Y,g,b),Ie(2,Y,m,y),M(tt,Y)},$t=tt=>{var Y=ct(),xt=rt(Y);{var Ot=mt=>{var lt=ii();le(lt,()=>({...L(l),...n}));var Mt=ht(lt);St(Mt,t,"default",{get builder(){return L(l)}},null),vt(lt),fe(lt,c=>N(c),()=>N()),de(lt,c=>{var h,v;return(v=(h=L(l)).action)==null?void 0:v.call(h,c)}),ae(()=>_t("m-keydown",lt,K)),Ie(1,lt,g,b),M(mt,lt)},gt=mt=>{var lt=ct(),Mt=rt(lt);{var c=v=>{var p=si();le(p,()=>({...L(l),...n}));var E=ht(p);St(E,t,"default",{get builder(){return L(l)}},null),vt(p),fe(p,q=>N(q),()=>N()),de(p,q=>{var ot,B;return(B=(ot=L(l)).action)==null?void 0:B.call(ot,q)}),ae(()=>_t("m-keydown",p,K)),Ie(2,p,m,y),M(v,p)},h=v=>{var p=ct(),E=rt(p);{var q=ot=>{var B=ai();le(B,()=>({...L(l),...n}));var R=ht(B);St(R,t,"default",{get builder(){return L(l)}},null),vt(B),fe(B,F=>N(F),()=>N()),de(B,F=>{var D,I;return(I=(D=L(l)).action)==null?void 0:I.call(D,F)}),ae(()=>_t("m-keydown",B,K)),M(ot,B)};Pt(E,ot=>{a()&&ot(q)},!0)}M(v,p)};Pt(Mt,v=>{m()&&a()?v(c):v(h,!1)},!0)}M(mt,lt)};Pt(xt,mt=>{g()&&a()?mt(Ot):mt(gt,!1)},!0)}M(tt,Y)};Pt(qt,tt=>{g()&&m()&&a()?tt(Lt):tt($t,!1)},!0)}M(pt,it)};Pt(Xt,pt=>{u()&&a()?pt(be):pt(Yt,!1)},!0)}M(Rt,Ft)};Pt(Zt,Rt=>{w()&&a()?Rt(pe):Rt(Se,!1)})}M(e,At),ve(),s()}var ci=ut("<button><!></button>");function ui(e,t){const r=Ht(t,["children","$$slots","$$events","$$legacy"]),n=Ht(r,["asChild","id","el"]);he(t,!1);const[o,s]=We(),i=()=>Ae(g,"$trigger",o),a=Xe();let l=k(t,"asChild",8,!1),u=k(t,"id",24,()=>{}),d=k(t,"el",28,()=>{});const{elements:{trigger:g},ids:b,getAttrs:m}=en(),y=fn(),w=m("trigger");nt(()=>U(u()),()=>{u()&&b.trigger.set(u())}),nt(()=>i(),()=>{Ue(a,i())}),nt(()=>L(a),()=>{Object.assign(L(a),w)}),Qe(),Ce();var f=ct(),x=rt(f);{var P=T=>{var O=ct(),W=rt(O);St(W,t,"default",{get builder(){return L(a)}},null),M(T,O)},A=T=>{var O=ci();le(O,()=>({...L(a),type:"button",...n}));var W=ht(O);St(W,t,"default",{get builder(){return L(a)}},null),vt(O),fe(O,S=>d(S),()=>d()),de(O,S=>{var j,J;return(J=(j=L(a)).action)==null?void 0:J.call(j,S)}),ae(()=>_t("m-keydown",O,y)),ae(()=>_t("m-pointerdown",O,y)),M(T,O)};Pt(x,T=>{l()?T(P):T(A,!1)})}M(e,f),ve(),s()}function He(e,t){const r=Ht(t,["children","$$slots","$$events","$$legacy"]),n=Ht(r,["class","inset"]);he(t,!1);let o=k(t,"class",8,void 0),s=k(t,"inset",8,void 0);Ce();{let i=Wn(()=>(U(Ye),U(s()),U(o()),Bn(()=>Ye("data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s()&&"pl-8",o()))));ei(e,Kn({get class(){return L(i)}},()=>n,{$$events:{click(a){Jt.call(this,t,a)},keydown(a){Jt.call(this,t,a)},focusin(a){Jt.call(this,t,a)},focusout(a){Jt.call(this,t,a)},pointerdown(a){Jt.call(this,t,a)},pointerleave(a){Jt.call(this,t,a)},pointermove(a){Jt.call(this,t,a)}},children:(a,l)=>{var u=ct(),d=rt(u);St(d,t,"default",{},null),M(a,u)},$$slots:{default:!0}}))}ve()}function di(e,t){const r=Ht(t,["children","$$slots","$$events","$$legacy"]),n=Ht(r,["class","sideOffset","transition","transitionConfig"]);he(t,!1);let o=k(t,"class",8,void 0),s=k(t,"sideOffset",8,4),i=k(t,"transition",8,Er),a=k(t,"transitionConfig",8,void 0);Ce();{let l=Wn(()=>(U(Ye),U(o()),Bn(()=>Ye("bg-popover text-popover-foreground z-50 min-w-[8rem] rounded-md border p-1 shadow-md focus:outline-none",o()))));li(e,Kn({get transition(){return i()},get transitionConfig(){return a()},get sideOffset(){return s()},get class(){return L(l)}},()=>n,{$$events:{keydown(u){Jt.call(this,t,u)}},children:(u,d)=>{var g=ct(),b=rt(g);St(b,t,"default",{},null),M(u,g)},$$slots:{default:!0}}))}ve()}const fi=ni,gi=ui;var mi=ut('<li><a href="/login" class="text-muted-foreground hover:text-foreground transition-colors">Sign In</a></li>'),hi=ut('<li><a href="/sign_out" class="text-muted-foreground hover:text-foreground transition-colors">Sign Out</a></li>'),vi=ut('<a href="/onboarding" class="btn-primary px-6 py-2">Get Started</a>'),pi=ut('<a class="btn-primary px-6 py-2">Dashboard</a>'),bi=ut('<a href="/pricing" class="w-full">Pricing</a>'),yi=ut('<a href="/login" class="w-full">Sign In</a>'),wi=ut('<a href="/sign_out" class="w-full">Sign Out</a>'),xi=ut('<a href="/onboarding" class="w-full">Get Started</a>'),Oi=ut('<a class="w-full">Dashboard</a>'),_i=ut("<!> <!> <!>",1),Ai=ut("<!> <!>",1),Ti=ut('<div class="flex-none"><ul class="hidden sm:flex items-center gap-8 font-bold"><li><a href="/pricing" class="text-muted-foreground hover:text-foreground transition-colors">Pricing</a></li> <!> <li><!></li></ul> <div class="sm:hidden"><!></div></div>'),Ci=ut('<div class="bg-background border-b-2 border-border sticky top-0 z-50 nav-blur"><div class="max-w-6xl mx-auto px-6 py-4"><div class="flex items-center justify-between"><div class="flex-1 flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-xl font-bold text-foreground hover:opacity-70 transition-colors"> </a></div> <!></div></div></div> <div class="flex flex-col min-h-screen"><main class="flex-1"><!></main> <!></div>',1);function ns(e,t){he(t,!0);const[r,n]=We(),o=()=>Ae(_r,"$page",r),s=Or(),i=rn(()=>o().url.pathname.startsWith("/login")||o().url.pathname.startsWith("/sign_up")||o().url.pathname.startsWith("/forgot_password")||o().url.pathname.startsWith("/check_email")||o().url.pathname.startsWith("/onboarding"));var a=Ci(),l=rt(a),u=ht(l),d=ht(u),g=ht(d),b=Nt(ht(g),2),m=ht(b,!0);vt(b),vt(g);var y=Nt(g,2);{var w=T=>{var O=Ti(),W=ht(O),S=Nt(ht(W),2);{var j=X=>{var K=mi();M(X,K)},J=X=>{var K=hi();M(X,K)};Pt(S,X=>{var K;!t.data.auth.user||(K=t.data.auth.user)!=null&&K.is_anonymous?X(j):X(J,!1)})}var Q=Nt(S,2),Z=ht(Q);{var N=X=>{var K=vi();M(X,K)},at=X=>{var K=pi();nn(()=>xn(K,"href",`/dashboard/${s.value.slug??""}`)),M(X,K)};Pt(Z,X=>{s.value?X(at,!1):X(N)})}vt(Q),vt(W);var $=Nt(W,2),kt=ht($);oe(kt,()=>fi,(X,K)=>{K(X,{children:(ft,At)=>{var Zt=Ai(),pe=rt(Zt);oe(pe,()=>gi,(Rt,Ft)=>{Ft(Rt,{asChild:!0,children:sr,$$slots:{default:(Xt,be)=>{const Yt=rn(()=>be.builder);{let pt=rn(()=>[L(Yt)]);Tr(Xt,{get builders(){return L(pt)},variant:"ghost",size:"sm",class:"text-muted-foreground",children:(it,qt)=>{Cr(it,{class:"h-5 w-5"})},$$slots:{default:!0}})}}}})});var Se=Nt(pe,2);oe(Se,()=>di,(Rt,Ft)=>{Ft(Rt,{class:"w-56 sm:hidden",children:(Xt,be)=>{var Yt=_i(),pt=rt(Yt);oe(pt,()=>He,(tt,Y)=>{Y(tt,{children:(xt,Ot)=>{var gt=bi();M(xt,gt)},$$slots:{default:!0}})});var it=Nt(pt,2);{var qt=tt=>{var Y=ct(),xt=rt(Y);oe(xt,()=>He,(Ot,gt)=>{gt(Ot,{children:(mt,lt)=>{var Mt=yi();M(mt,Mt)},$$slots:{default:!0}})}),M(tt,Y)},Lt=tt=>{var Y=ct(),xt=rt(Y);oe(xt,()=>He,(Ot,gt)=>{gt(Ot,{children:(mt,lt)=>{var Mt=wi();M(mt,Mt)},$$slots:{default:!0}})}),M(tt,Y)};Pt(it,tt=>{var Y;!t.data.auth.user||(Y=t.data.auth.user)!=null&&Y.is_anonymous?tt(qt):tt(Lt,!1)})}var $t=Nt(it,2);oe($t,()=>He,(tt,Y)=>{Y(tt,{children:(xt,Ot)=>{var gt=ct(),mt=rt(gt);{var lt=c=>{var h=xi();M(c,h)},Mt=c=>{var h=Oi();nn(()=>xn(h,"href",`/dashboard/${s.value.slug??""}`)),M(c,h)};Pt(mt,c=>{s.value?c(Mt,!1):c(lt)})}M(xt,gt)},$$slots:{default:!0}})}),M(Xt,Yt)},$$slots:{default:!0}})}),M(ft,Zt)},$$slots:{default:!0}})}),vt($),vt(O),M(T,O)};Pt(y,T=>{L(i)||T(w)})}vt(d),vt(u),vt(l);var f=Nt(l,2),x=ht(f),P=ht(x);lr(P,()=>t.children),vt(x);var A=Nt(x,2);yr(A,{}),vt(f),nn(()=>ar(m,Ar)),M(e,a),ve(),n()}export{ns as component};
