import"../chunks/CWj6FrbW.js";import{o as At}from"../chunks/RnwjOPnl.js";import{b as ye,d as me,a as n,p as et,f as v,t as Y,e as tt,s as a,c as r,i as e,k as Le,r as t,I as Te,j as f,n as ve,q as dt,h as _,aT as re,l as Xe,g as zt,m as pe,ak as Ut,W as Vt,bf as xr,be as _r,al as yr,u as Zt,$ as kr}from"../chunks/DDiqt3uM.js";import{e as he,s as J,r as $r,h as Mr}from"../chunks/DWulv87v.js";import{G as Ye,T as Qt,H as Cr,R as Sr,b as jr}from"../chunks/Di-aknNf.js";import{a as Ht,s as Pt}from"../chunks/B82PTGnX.js";import{w as er}from"../chunks/rjRVMZXi.js";import"../chunks/DfS0riH5.js";import{i as k}from"../chunks/2C89X9tI.js";import{s as Ce,b as Wt,d as Pr,r as Rr}from"../chunks/C36Ip9GY.js";import{s as ce,c as Ar}from"../chunks/DE2v8SHj.js";import{b as Rt}from"../chunks/Dqu9JXqq.js";import{l as ct,s as vt,p as se}from"../chunks/C-ZVHnwW.js";import{p as tr}from"../chunks/BwDA7qM4.js";import"../chunks/DhRTwODG.js";import{s as rt}from"../chunks/iCEqKm8o.js";import{I as ut}from"../chunks/CkoRhfQ8.js";import{U as rr,B as zr}from"../chunks/ng6E9JCw.js";import{C as Er,D as Ir}from"../chunks/ZorUncli.js";import{M as sr}from"../chunks/Bxi4P_5L.js";import{X as ar}from"../chunks/rNGuVYtO.js";import{S as Je}from"../chunks/QFpxkGuO.js";import{C as Lr}from"../chunks/3OymkobC.js";import{S as Tr,L as Dr}from"../chunks/Rh-W-Viu.js";import{e as Se,i as Oe}from"../chunks/OiKQa7Wx.js";import{h as Yt}from"../chunks/CYhDwGx0.js";import{b as Jt}from"../chunks/DUGxtfU6.js";import{i as Et}from"../chunks/B_FgA42l.js";import{c as Nr}from"../chunks/BCmD-YNt.js";import{C as or}from"../chunks/CXeeNO9R.js";import{C as Fr}from"../chunks/DR0c5pEj.js";import{L as qr}from"../chunks/DWMPsgkI.js";import{C as Ur}from"../chunks/C6jXKNMu.js";import{C as Wr}from"../chunks/oaGF9CiI.js";import{Z as Xt}from"../chunks/BOkOaIl2.js";import{E as Hr}from"../chunks/Dxemv0ia.js";import{P as Br}from"../chunks/BBWSBDCg.js";function Or(B,u){const g=ct(u,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"}],["path",{d:"M10 6h4"}],["path",{d:"M10 10h4"}],["path",{d:"M10 14h4"}],["path",{d:"M10 18h4"}]];ut(B,vt({name:"building-2"},()=>g,{get iconNode(){return h},children:(M,l)=>{var m=ye(),$=me(m);rt($,u,"default",{},null),n(M,m)},$$slots:{default:!0}}))}function Kr(B,u){const g=ct(u,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M7 16h8"}],["path",{d:"M7 11h12"}],["path",{d:"M7 6h3"}]];ut(B,vt({name:"chart-bar"},()=>g,{get iconNode(){return h},children:(M,l)=>{var m=ye(),$=me(m);rt($,u,"default",{},null),n(M,m)},$$slots:{default:!0}}))}function Gr(B,u){const g=ct(u,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"}],["path",{d:"M3 3v5h5"}],["path",{d:"M12 7v5l4 2"}]];ut(B,vt({name:"history"},()=>g,{get iconNode(){return h},children:(M,l)=>{var m=ye(),$=me(m);rt($,u,"default",{},null),n(M,m)},$$slots:{default:!0}}))}function Vr(B,u){const g=ct(u,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["path",{d:"M10 8h.01"}],["path",{d:"M12 12h.01"}],["path",{d:"M14 8h.01"}],["path",{d:"M16 12h.01"}],["path",{d:"M18 8h.01"}],["path",{d:"M6 8h.01"}],["path",{d:"M7 16h10"}],["path",{d:"M8 12h.01"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2"}]];ut(B,vt({name:"keyboard"},()=>g,{get iconNode(){return h},children:(M,l)=>{var m=ye(),$=me(m);rt($,u,"default",{},null),n(M,m)},$$slots:{default:!0}}))}function nr(B,u){const g=ct(u,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}]];ut(B,vt({name:"message-square"},()=>g,{get iconNode(){return h},children:(M,l)=>{var m=ye(),$=me(m);rt($,u,"default",{},null),n(M,m)},$$slots:{default:!0}}))}var Zr=v('<span class="font-semibold text-lg text-sidebar-foreground">Robynn AI</span>'),Qr=v('<div class="relative"><!> <input type="text" placeholder="Search" class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"/> <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">⌘K</kbd></div>'),Yr=v('<div class="flex justify-center"><button class="p-2 hover:bg-sidebar-accent rounded-lg svelte-947kzy"><!></button></div>'),Jr=v('Community <span class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">NEW</span>',1),Xr=v('<div class="flex-1 min-w-0"><p class="text-sm font-medium text-sidebar-foreground truncate text-left"> </p> <p class="text-xs text-muted-foreground truncate text-left"> </p></div> <!>',1),es=v('<div class="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg z-50 py-2 animate-slide-up svelte-947kzy" style="box-shadow: var(--shadow-lg);"><button class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors svelte-947kzy"><!> Settings</button> <button class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors svelte-947kzy"><!> Sign Out</button></div>'),ts=v('<aside><button><!></button> <header><div class="flex items-center space-x-2 mb-4"><div class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center"><!></div> <!></div> <!></header> <nav class="flex-1 p-4"><div class="space-y-1"><button><!> <!></button> <button><!> <!></button> <button><!> <!></button> <button><!> <!></button> <button><!> <!></button> <button><!> <!></button></div></nav> <footer><div class="relative mt-4 pt-4 border-t border-sidebar-border"><button><div><span class="text-sm font-medium text-primary-foreground"> </span></div> <!></button> <!></div></footer></aside>');function rs(B,u){et(u,!0);const[g,h]=Ht(),M=()=>Pt(tr,"$page",g);let l=se(u,"collapsed",15,!1),m=se(u,"isMobile",3,!1),$=se(u,"session",3,null),D=se(u,"profile",3,null),C=Te(!1),R;function X(){l(!l())}function oe(){f(C,!e(C))}function c(s){R&&!R.contains(s.target)&&f(C,!1)}let i=Le(()=>{var s,b,S,x,G,E,te,V,q,ae,ue,ie,$e,Ie,Ne,We;return{name:((s=D())==null?void 0:s.full_name)||((x=(S=(b=$())==null?void 0:b.user)==null?void 0:S.user_metadata)==null?void 0:x.full_name)||((te=(E=(G=$())==null?void 0:G.user)==null?void 0:E.email)==null?void 0:te.split("@")[0])||"User",email:((q=(V=$())==null?void 0:V.user)==null?void 0:q.email)||"<EMAIL>",initials:d(((ae=D())==null?void 0:ae.full_name)||(($e=(ie=(ue=$())==null?void 0:ue.user)==null?void 0:ie.user_metadata)==null?void 0:$e.full_name)||((We=(Ne=(Ie=$())==null?void 0:Ie.user)==null?void 0:Ne.email)==null?void 0:We.split("@")[0])||"User")}});function d(s){return s.split(" ").map(b=>b.charAt(0).toUpperCase()).slice(0,2).join("")}async function y(){f(C,!1);try{window.location.href="/sign_out"}catch(s){console.error("Error signing out:",s)}}function U(){f(C,!1);const s=M().params.envSlug;window.location.href=`/dashboard/${s}/settings`}At(()=>(document.addEventListener("click",c),()=>{document.removeEventListener("click",c)}));var A=ts();let O;var W=r(A);let Z;var I=r(W);{var fe=s=>{sr(s,{class:"w-3 h-3"})},Q=s=>{ar(s,{class:"w-3 h-3"})};k(I,s=>{l()?s(fe):s(Q,!1)})}t(W);var N=a(W,2);let ge;var be=r(N),w=r(be),z=r(w);Ye(z,{class:"w-4 h-4 text-sidebar-primary-foreground"}),t(w);var j=a(w,2);{var F=s=>{var b=Zr();n(s,b)};k(j,s=>{l()||s(F)})}t(be);var K=a(be,2);{var H=s=>{var b=Qr(),S=r(b);Je(S,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"}),ve(4),t(b),n(s,b)},ne=s=>{var b=Yr(),S=r(b),x=r(S);Je(x,{class:"w-4 h-4 text-muted-foreground"}),t(S),t(b),n(s,b)};k(K,s=>{l()?s(ne,!1):s(H)})}t(N);var ee=a(N,2),ke=r(ee),we=r(ke);let Ae;var Fe=r(we);{let s=Le(()=>l()?"":"mr-3");nr(Fe,{get class(){return`w-4 h-4 ${e(s)??""}`}})}var It=a(Fe,2);{var mt=s=>{var b=dt("AI Chat");n(s,b)};k(It,s=>{l()||s(mt)})}t(we);var je=a(we,2);let pt;var Ke=r(je);{let s=Le(()=>l()?"":"mr-3");Ye(Ke,{get class(){return`w-4 h-4 ${e(s)??""}`}})}var Lt=a(Ke,2);{var ft=s=>{var b=dt("Projects");n(s,b)};k(Lt,s=>{l()||s(ft)})}t(je);var qe=a(je,2);let st;var gt=r(qe);{let s=Le(()=>l()?"":"mr-3");Ye(gt,{get class(){return`w-4 h-4 ${e(s)??""}`}})}var at=a(gt,2);{var bt=s=>{var b=dt("Templates");n(s,b)};k(at,s=>{l()||s(bt)})}t(qe);var ze=a(qe,2);let ht;var ot=r(ze);{let s=Le(()=>l()?"":"mr-3");Ye(ot,{get class(){return`w-4 h-4 ${e(s)??""}`}})}var wt=a(ot,2);{var Tt=s=>{var b=dt("Documents");n(s,b)};k(wt,s=>{l()||s(Tt)})}t(ze);var Pe=a(ze,2);let nt;var xt=r(Pe);{let s=Le(()=>l()?"":"mr-3");rr(xt,{get class(){return`w-4 h-4 ${e(s)??""}`}})}var _t=a(xt,2);{var yt=s=>{var b=Jr();ve(),n(s,b)};k(_t,s=>{l()||s(yt)})}t(Pe);var Ue=a(Pe,2);let Ge;var Ve=r(Ue);{let s=Le(()=>l()?"":"mr-3");Er(Ve,{get class(){return`w-4 h-4 ${e(s)??""}`}})}var kt=a(Ve,2);{var Re=s=>{var b=dt("History");n(s,b)};k(kt,s=>{l()||s(Re)})}t(Ue),t(ke),t(ee);var Ze=a(ee,2);let lt;var Ee=r(Ze),De=r(Ee);let $t;var Qe=r(De);let o;var p=r(Qe),P=r(p,!0);t(p),t(Qe);var L=a(Qe,2);{var T=s=>{var b=Xr(),S=me(b),x=r(S),G=r(x,!0);t(x);var E=a(x,2),te=r(E,!0);t(E),t(S);var V=a(S,2);{let q=Le(()=>e(C)?"rotate-180":"");Lr(V,{get class(){return`w-4 h-4 text-muted-foreground transition-transform duration-200 ${e(q)??""}`}})}Y(()=>{J(G,e(i).name),J(te,e(i).email)}),n(s,b)};k(L,s=>{l()||s(T)})}t(De);var le=a(De,2);{var xe=s=>{var b=es(),S=r(b),x=r(S);Tr(x,{class:"w-4 h-4 mr-3"}),ve(),t(S);var G=a(S,2),E=r(G);Dr(E,{class:"w-4 h-4 mr-3"}),ve(),t(G),t(b),he("click",S,U),he("click",G,y),n(s,b)};k(le,s=>{e(C)&&!l()&&s(xe)})}t(Ee),Rt(Ee,s=>R=s,()=>R),t(Ze),t(A),Y((s,b,S,x,G,E,te,V,q,ae,ue,ie)=>{O=ce(A,1,"bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative",null,O,s),Z=ce(W,1,"absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors svelte-947kzy",null,Z,b),ge=ce(N,1,"p-4 border-b border-sidebar-border",null,ge,S),Ae=ce(we,1,"flex items-center w-full px-3 py-2 text-sm text-sidebar-primary-foreground bg-sidebar-primary rounded-lg svelte-947kzy",null,Ae,x),Ce(we,"title",l()?"AI Chat":""),pt=ce(je,1,"flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy",null,pt,G),Ce(je,"title",l()?"Projects":""),st=ce(qe,1,"flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy",null,st,E),Ce(qe,"title",l()?"Templates":""),ht=ce(ze,1,"flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy",null,ht,te),Ce(ze,"title",l()?"Documents":""),nt=ce(Pe,1,"flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy",null,nt,V),Ce(Pe,"title",l()?"Community":""),Ge=ce(Ue,1,"flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors svelte-947kzy",null,Ge,q),Ce(Ue,"title",l()?"History":""),lt=ce(Ze,1,"p-4 border-t border-sidebar-border",null,lt,ae),$t=ce(De,1,"flex items-center w-full p-2 hover:bg-sidebar-accent rounded-lg transition-colors group svelte-947kzy",null,$t,ue),Ce(De,"title",l()?e(i).email:"Profile menu"),o=ce(Qe,1,"w-8 h-8 bg-primary rounded-full flex items-center justify-center svelte-947kzy",null,o,ie),J(P,e(i).initials)},[()=>({"w-64":!l(),"w-16":l()&&!m(),"w-0":l()&&m(),"overflow-hidden":l()&&m()}),()=>({hidden:m()&&l()}),()=>({hidden:l()&&m()}),()=>({"justify-center":l()}),()=>({"justify-center":l()}),()=>({"justify-center":l()}),()=>({"justify-center":l()}),()=>({"justify-center":l()}),()=>({"justify-center":l()}),()=>({hidden:l()&&m()}),()=>({"justify-center":l()}),()=>({"mr-3":!l()})]),he("click",W,X),he("click",De,oe),n(B,A),tt(),h()}var ss=v('<div class="flex items-start space-x-4 animate-pulse svelte-5pvuvw"><div class="w-10 h-10 bg-muted rounded-full flex-shrink-0 svelte-5pvuvw"></div> <div class="flex-1 space-y-3 svelte-5pvuvw"><div class="flex items-center gap-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-16 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-20 svelte-5pvuvw"></div></div> <div class="space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-4/5 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div></div></div></div>'),as=v('<div class="flex items-center space-x-3 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-20 svelte-5pvuvw"></div></div>'),os=v('<div class="animate-pulse space-y-4 svelte-5pvuvw"><div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-6 h-6 bg-muted rounded svelte-5pvuvw"></div> <div class="h-5 bg-muted rounded w-24 svelte-5pvuvw"></div></div> <div class="h-10 bg-muted rounded-lg svelte-5pvuvw"></div> <!></div>'),ns=v('<div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-40 svelte-5pvuvw"></div></div>'),ls=v('<div class="animate-pulse space-y-3 svelte-5pvuvw"><div class="flex items-center justify-between svelte-5pvuvw"><div class="h-4 bg-muted rounded w-32 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-12 svelte-5pvuvw"></div></div> <div class="h-2 bg-muted rounded-full w-full svelte-5pvuvw"></div> <div class="space-y-2 svelte-5pvuvw"></div></div>'),is=v('<div class="animate-pulse p-4 border border-border rounded-lg space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-2/3 svelte-5pvuvw"></div></div>'),ds=v("<!> <!> <!> <!>",1);function cs(B,u){let g=se(u,"type",8,"message"),h=se(u,"count",8,1);var M=ds(),l=me(M);{var m=c=>{var i=ye(),d=me(i);Se(d,1,()=>(re(h()),_(()=>Array(h()))),Oe,(y,U)=>{var A=ss();n(y,A)}),n(c,i)};k(l,c=>{g()==="message"&&c(m)})}var $=a(l,2);{var D=c=>{var i=os(),d=a(r(i),4);Se(d,0,()=>Array(5),Oe,(y,U)=>{var A=as();n(y,A)}),t(i),n(c,i)};k($,c=>{g()==="sidebar"&&c(D)})}var C=a($,2);{var R=c=>{var i=ls(),d=a(r(i),4);Se(d,4,()=>Array(3),Oe,(y,U)=>{var A=ns();n(y,A)}),t(d),t(i),n(c,i)};k(C,c=>{g()==="progress"&&c(R)})}var X=a(C,2);{var oe=c=>{var i=ye(),d=me(i);Se(d,1,()=>(re(h()),_(()=>Array(h()))),Oe,(y,U)=>{var A=is();n(y,A)}),n(c,i)};k(X,c=>{g()==="project"&&c(oe)})}n(B,M)}var vs=v('<div><!> <div class="flex-1 min-w-0"><p class="font-medium text-sm truncate"> </p> <p class="text-xs opacity-80 truncate"> </p></div></div>'),us=v('<div class="fixed top-4 right-4 w-80 bg-background border-2 border-border rounded-lg shadow-lg z-50 p-4 animate-slide-in svelte-lmyhl8" style="box-shadow: var(--shadow-lg);"><div class="flex items-center justify-between mb-4"><h3 class="font-semibold text-foreground flex items-center gap-2"><!> Research Progress</h3> <span class="text-sm font-medium text-muted-foreground"> </span></div> <div class="mb-4"><div class="w-full bg-muted rounded-full h-2 overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"></div></div></div> <div class="space-y-2 max-h-64 overflow-y-auto"></div></div>');function ms(B,u){et(u,!1);let g=se(u,"steps",24,()=>[]),h=se(u,"currentProgress",8,0),M=se(u,"isVisible",8,!1),l=pe(),m=pe(0);function $(c){const i=e(m),d=800,y=Date.now();function U(){const A=Date.now()-y,O=Math.min(A/d,1),W=1-Math.pow(1-O,3);f(m,i+(c-i)*W),O<1?requestAnimationFrame(U):f(m,c)}requestAnimationFrame(U)}function D(c,i){return c.status==="completed"?Fr:c.status==="pending"&&i===g().findIndex(d=>d.status==="pending")?qr:Ur}function C(c,i){const d="flex items-center space-x-3 p-3 rounded-lg transition-all duration-500";return c.status==="completed"?`${d} bg-green-50 border border-green-200 text-green-800`:c.status==="pending"&&i===g().findIndex(y=>y.status==="pending")?`${d} bg-blue-50 border border-blue-200 text-blue-800 animate-pulse`:`${d} bg-muted/30 border border-border text-muted-foreground`}Xe(()=>(re(M()),re(h()),e(m)),()=>{M()&&h()!==e(m)&&$(h())}),zt(),Et();var R=ye(),X=me(R);{var oe=c=>{var i=us(),d=r(i),y=r(d),U=r(y);or(U,{class:"w-4 h-4"}),ve(),t(y);var A=a(y,2),O=r(A);t(A),t(d);var W=a(d,2),Z=r(W),I=r(Z);t(Z),t(W);var fe=a(W,2);Se(fe,7,g,Q=>Q.id,(Q,N,ge)=>{var be=vs(),w=r(be);{let ne=Ut(()=>(e(N),re(e(ge)),re(g()),_(()=>e(N).status==="pending"&&e(ge)===g().findIndex(ee=>ee.status==="pending")?"animate-spin":"")));Nr(w,()=>D(e(N),e(ge)),(ee,ke)=>{ke(ee,{get class(){return`w-4 h-4 flex-shrink-0 ${e(ne)??""}`}})})}var z=a(w,2),j=r(z),F=r(j,!0);t(j);var K=a(j,2),H=r(K,!0);t(K),t(z),t(be),Y(ne=>{ce(be,1,ne,"svelte-lmyhl8"),J(F,(e(N),_(()=>e(N).title))),J(H,(e(N),_(()=>e(N).description)))},[()=>Ar((e(N),re(e(ge)),_(()=>C(e(N),e(ge)))))]),n(Q,be)}),t(fe),t(i),Rt(i,Q=>f(l,Q),()=>e(l)),Y(Q=>{J(O,`${Q??""}%`),Wt(I,`width: ${e(m)??""}%`)},[()=>(e(m),_(()=>Math.round(e(m))))]),n(c,i)};k(X,c=>{re(M()),re(g()),_(()=>M()&&g().length>0)&&c(oe)})}n(B,R),tt()}var ps=v('<button class="ml-auto text-xs text-destructive hover:text-destructive/80 underline">Retry</button>'),fs=v('<div class="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg"><!> <span class="text-sm text-destructive">Something went wrong</span> <!></div>'),gs=v('<pre class="text-xs text-muted-foreground mt-2 overflow-auto max-h-32"> </pre>'),bs=v('<details class="mb-6 w-full max-w-md"><summary class="cursor-pointer text-sm text-muted-foreground hover:text-foreground">Error Details</summary> <div class="mt-2 p-3 bg-muted rounded-lg text-left"><p class="text-xs font-mono text-destructive break-all"> </p> <!></div></details>'),hs=v('<button class="btn-primary px-4 py-2 flex items-center gap-2"><!> Try Again</button>'),ws=v('<div class="flex flex-col items-center justify-center min-h-[400px] p-8 text-center"><div class="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4"><!></div> <h2 class="text-xl font-semibold text-foreground mb-2">Oops! Something went wrong</h2> <p class="text-muted-foreground mb-6 max-w-md">We encountered an unexpected error. This has been logged and our team will investigate.</p> <!> <div class="flex gap-3"><!> <button class="btn-secondary px-4 py-2 flex items-center gap-2"><!> Go Home</button></div></div>');function xs(B,u){et(u,!1);let g=se(u,"fallback",8,"full"),h=se(u,"onRetry",8,null),M=se(u,"error",12,null),l=pe(!1),m=pe(""),$=pe("");function D(){f(l,!1),M(null),f(m,""),f($,""),h()&&h()()}function C(){window.location.href="/"}At(()=>{const i=d=>{var y,U;console.error("Unhandled promise rejection:",d.reason),f(l,!0),f(m,((y=d.reason)==null?void 0:y.message)||"An unexpected error occurred"),f($,((U=d.reason)==null?void 0:U.stack)||"")};return window.addEventListener("unhandledrejection",i),()=>{window.removeEventListener("unhandledrejection",i)}}),Xe(()=>re(M()),()=>{M()&&(f(l,!0),f(m,M().message),f($,M().stack||""))}),zt(),Et();var R=ye(),X=me(R);{var oe=i=>{var d=ye(),y=me(d);{var U=O=>{var W=fs(),Z=r(W);Qt(Z,{class:"w-4 h-4 text-destructive flex-shrink-0"});var I=a(Z,4);{var fe=Q=>{var N=ps();he("click",N,D),n(Q,N)};k(I,Q=>{h()&&Q(fe)})}t(W),n(O,W)},A=O=>{var W=ws(),Z=r(W),I=r(Z);Qt(I,{class:"w-8 h-8 text-destructive"}),t(Z);var fe=a(Z,6);{var Q=j=>{var F=bs(),K=a(r(F),2),H=r(K),ne=r(H,!0);t(H);var ee=a(H,2);{var ke=we=>{var Ae=gs(),Fe=r(Ae);t(Ae),Y(()=>J(Fe,`
                ${e($)??""}
              `)),n(we,Ae)};k(ee,we=>{e($)&&we(ke)})}t(K),t(F),Y(()=>J(ne,e(m))),n(j,F)};k(fe,j=>{e(m)&&j(Q)})}var N=a(fe,2),ge=r(N);{var be=j=>{var F=hs(),K=r(F);Sr(K,{class:"w-4 h-4"}),ve(),t(F),he("click",F,D),n(j,F)};k(ge,j=>{h()&&j(be)})}var w=a(ge,2),z=r(w);Cr(z,{class:"w-4 h-4"}),ve(),t(w),t(N),t(W),he("click",w,C),n(O,W)};k(y,O=>{g()==="minimal"?O(U):O(A,!1)})}n(i,d)},c=i=>{var d=ye(),y=me(d);rt(y,u,"default",{},null),n(i,d)};k(X,i=>{e(l)?i(oe):i(c,!1)})}n(B,R),tt()}var _s=v('<span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground svelte-m6peww"> </span>'),ys=v('<div class="px-6 py-3 border-b border-border bg-muted/30 svelte-m6peww"><div class="max-w-4xl mx-auto relative svelte-m6peww"><input id="search-input" placeholder="Search messages..." class="w-full px-4 py-2 pl-10 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground svelte-m6peww"/> <!> <!></div></div>'),ks=v(`<div class="text-center py-8 svelte-m6peww"><div class="flex items-center justify-center gap-2 mb-6 svelte-m6peww"><!> <h3 class="text-2xl font-bold svelte-m6peww" style="color: var(--foreground);">Ready to Research</h3></div> <p class="font-medium mb-8 max-w-2xl mx-auto svelte-m6peww" style="color: var(--muted-foreground);">Get marketing intelligence on any company. Ask your research
              question below.</p> <div class="text-xs text-muted-foreground space-y-1 svelte-m6peww"><p class="svelte-m6peww"><kbd class="px-2 py-1 bg-muted rounded svelte-m6peww">Ctrl+K</kbd> to search</p> <p class="svelte-m6peww"><kbd class="px-2 py-1 bg-muted rounded svelte-m6peww">/</kbd> to focus input</p> <p class="svelte-m6peww"><kbd class="px-2 py-1 bg-muted rounded svelte-m6peww">Ctrl+Enter</kbd> to send</p></div></div>`),$s=v('<div class="text-center py-8 svelte-m6peww"><!> <h3 class="text-lg font-semibold text-foreground mb-2 svelte-m6peww">No results found</h3> <p class="text-muted-foreground svelte-m6peww"> </p></div>'),Ms=v('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1 svelte-m6peww" title="Download as Markdown"><!> Download</button>'),Cs=v('<div class="p-4 border-2 svelte-m6peww" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium svelte-m6peww" style="color: var(--primary-foreground);"> </p></div>'),Ss=v('<div class="p-4 border-b-2 border-border bg-muted/50 svelte-m6peww"><div class="flex items-start gap-2 svelte-m6peww"><span class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded svelte-m6peww">TL;DR</span> <div class="text-sm font-medium formatted-summary svelte-m6peww" style="color: var(--foreground);"><!></div></div></div>'),js=v('<span class="text-xs font-bold px-2 py-1 border border-border rounded svelte-m6peww" style="background: var(--accent); color: var(--accent-foreground);"> </span>'),Ps=v('<div class="px-6 pt-4 pb-2 svelte-m6peww"><div class="flex flex-wrap gap-2 svelte-m6peww"></div></div>'),Rs=v('<div class="border-2 svelte-m6peww" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"><!> <!> <div class="p-6 svelte-m6peww"><div class="formatted-content max-w-none svelte-m6peww"><!></div></div> <div class="px-6 pb-4 border-t border-border svelte-m6peww"><div class="flex flex-wrap gap-2 mt-4 svelte-m6peww"><button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1 svelte-m6peww"><!> Compare with competitor</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1 svelte-m6peww"><!> Add visuals</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1 svelte-m6peww"><!> Turn into slides</button></div></div></div>'),As=v('<div class="flex items-start space-x-4 svelte-m6peww"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-m6peww"><!></div> <div class="flex-1 max-w-4xl svelte-m6peww"><div class="flex items-center gap-2 mb-2 svelte-m6peww"><span class="text-sm font-bold svelte-m6peww" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1 svelte-m6peww"><!> <span class="text-xs svelte-m6peww" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),zs=v('<div class="flex items-center space-x-3 svelte-m6peww"><div></div> <span> </span></div>'),Es=v('<div class="mt-3 svelte-m6peww"><div class="w-full bg-muted rounded-full h-2 svelte-m6peww"><div class="bg-primary h-2 rounded-full transition-all duration-300 svelte-m6peww"></div></div> <p class="text-xs text-muted-foreground mt-1 svelte-m6peww"> </p></div>'),Is=v('<div class="border-2 border-border bg-background p-4 rounded-lg svelte-m6peww"><h3 class="font-semibold text-foreground mb-3 svelte-m6peww">Research Progress</h3> <div class="space-y-2 svelte-m6peww"></div> <!></div>'),Ls=v("<!> <!> <!>",1),Ts=v('<option class="svelte-m6peww"> </option>'),Ds=v('<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin svelte-m6peww"></div>'),Ns=v(`<main class="flex-1 flex flex-col bg-background svelte-m6peww"><header class="flex items-center justify-between p-6 border-b border-border bg-background svelte-m6peww"><div class="flex items-center svelte-m6peww"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-2 svelte-m6peww"><a class="hover:text-foreground transition-colors svelte-m6peww">Dashboard</a> <!> <span class="text-foreground font-medium svelte-m6peww">Research Agent</span></nav></div> <div class="flex items-center space-x-2 svelte-m6peww"><button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww" title="Search messages (Ctrl+K)"><!></button> <button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww" title="Keyboard shortcuts: Ctrl+K (search), / (focus input), Ctrl+Enter (send)"><!></button> <button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww" title="Chat history"><!></button> <span class="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full svelte-m6peww">Online</span></div></header> <!> <div class="px-6 py-4 border-b border-border svelte-m6peww"><div class="flex items-center space-x-4 svelte-m6peww"><div class="w-12 h-12 flex items-center justify-center border-2 border-border bg-primary svelte-m6peww" style="box-shadow: var(--shadow-sm);"><!></div> <div class="svelte-m6peww"><h1 class="text-3xl font-black flex items-center gap-3 text-foreground svelte-m6peww"><!> Compass</h1> <p class="text-muted-foreground svelte-m6peww">AI-powered research assistant for competitive analysis and market
          insights</p></div></div></div> <div class="flex-1 overflow-y-auto p-6 svelte-m6peww"><div class="max-w-4xl mx-auto space-y-6 svelte-m6peww"><!></div></div> <!> <div class="p-6 border-t border-border bg-background svelte-m6peww"><div class="max-w-4xl mx-auto svelte-m6peww"><div class="flex items-center gap-2 mb-4 svelte-m6peww"><span class="text-sm font-bold svelte-m6peww" style="color: var(--muted-foreground);">Format:</span> <select class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded svelte-m6peww" style="border-radius: 0.375rem;"></select></div> <div class="relative svelte-m6peww"><textarea rows="1" style="min-height: 48px; max-height: 120px;"></textarea> <div class="absolute right-2 bottom-2 flex items-center space-x-2 svelte-m6peww"><button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww"><svg class="w-4 h-4 text-muted-foreground svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" class="svelte-m6peww"></path></svg></button> <button class="p-2 hover:bg-accent rounded-lg transition-colors svelte-m6peww"><svg class="w-4 h-4 text-muted-foreground svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" class="svelte-m6peww"></path></svg></button> <button class="p-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 svelte-m6peww"><!></button></div></div> <div class="flex items-center space-x-4 mt-3 svelte-m6peww"><button class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors svelte-m6peww"><svg class="w-4 h-4 mr-1 svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" class="svelte-m6peww"></path></svg> Attach</button> <button class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors svelte-m6peww"><svg class="w-4 h-4 mr-1 svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" class="svelte-m6peww"></path></svg> Voice Message</button> <button class="flex items-center px-3 py-1.5 text-sm text-primary-foreground bg-primary hover:bg-primary/90 rounded-lg border border-primary transition-colors svelte-m6peww"><svg class="w-4 h-4 mr-1 svelte-m6peww" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" class="svelte-m6peww"></path></svg> Browse Prompts</button> <div class="ml-auto text-xs text-muted-foreground svelte-m6peww">0 / 3,000</div></div> <p class="text-xs text-muted-foreground text-center mt-4 svelte-m6peww">Research may generate inaccurate information about companies, markets,
        or facts. Model: Compass AI v1.3</p></div></div></main>`);function Fs(B,u){et(u,!1);const[g,h]=Ht(),M=()=>Pt(oe,"$messagesStore",g),l=()=>Pt(tr,"$page",g),m=pe(),$=pe();let D=se(u,"messages",28,()=>[]),C=se(u,"isLoading",12,!1),R=se(u,"progressSteps",28,()=>[]),X=se(u,"currentProgress",12,0);se(u,"leftSidebarCollapsed",8,!1),se(u,"rightSidebarCollapsed",8,!1);const oe=er(D());let c=pe(""),i=pe("comprehensive"),d=pe(""),y=pe(!1),U=pe(!0),A=pe(),O=pe();const W=[{value:"comprehensive",label:"Comprehensive",description:"Full detailed analysis"},{value:"executive",label:"Executive Summary",description:"High-level overview"},{value:"slide-ready",label:"Slide-ready",description:"Sections + headers for export"},{value:"battlecard",label:"Competitive Battlecard",description:"Strategic comparison format"}],Z=["Research Stripe's competitive positioning and market strategy...","Analyze OpenAI's business model and recent developments...","Compare Notion vs. Obsidian feature sets and pricing...","Investigate Figma's growth strategy and market expansion...","Study Shopify's competitive advantages in e-commerce..."];let I=pe(Z[0]),fe=0;function Q(){return Math.random().toString(36).substring(2,11)}async function N(){var L;if(!e(c).trim()||C())return;let o="";e(i)==="executive"?o="[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. ":e(i)==="slide-ready"?o="[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. ":e(i)==="battlecard"&&(o="[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. ");const p=o+e(c).trim();f(c,""),C(!0),R([{id:1,title:"Initial Analysis",description:"Analyzing research request...",status:"pending"},{id:2,title:"Web Search",description:"Conducting comprehensive search...",status:"pending"},{id:3,title:"Financial Analysis",description:"Gathering financial data...",status:"pending"},{id:4,title:"Market Research",description:"Analyzing market position...",status:"pending"},{id:5,title:"Report Generation",description:"Creating research report...",status:"pending"}]),X(0);const P={id:Q(),role:"user",content:p,timestamp:new Date};oe.update(T=>[...T,P]),D([...D(),P]);try{const T=await fetch(`/dashboard/${l().params.envSlug}/researcher?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:p})});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const le=(L=T.body)==null?void 0:L.getReader(),xe=new TextDecoder;if(le)for(;;){const{done:s,value:b}=await le.read();if(s)break;const x=xe.decode(b).split(`
`);for(const G of x)if(G.startsWith("data: "))try{const E=JSON.parse(G.slice(6));if(E.type==="final_response"){const te={id:Q(),role:"assistant",content:E.response,timestamp:new Date,isReport:!0};oe.update(V=>[...V,te]),D([...D(),te])}else E.step&&(X(E.progress),R(R().map(te=>te.id===E.step?{...te,status:E.status,description:E.action}:te.id<E.step?{...te,status:"completed"}:te)))}catch(E){console.error("Error parsing SSE data:",E)}}}catch(T){console.error("Error sending message:",T);const le={id:Q(),role:"assistant",content:"I apologize, but Compass encountered an error while processing your request. Please try again.",timestamp:new Date};oe.update(xe=>[...xe,le]),D([...D(),le])}finally{C(!1),R([]),X(0)}}function ge(o){o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),N())}function be(o){var p,P,L;(o.ctrlKey||o.metaKey)&&o.key==="k"&&(o.preventDefault(),f(y,!e(y)),e(y)&&Vt().then(()=>{const T=document.querySelector("#search-input");T==null||T.focus()})),o.key==="Escape"&&(e(y)?(f(y,!1),f(d,"")):C()&&console.log("Cancel request requested")),(o.ctrlKey||o.metaKey)&&o.key==="Enter"&&!C()&&N(),o.key==="/"&&!e(y)&&((p=document.activeElement)==null?void 0:p.tagName)!=="INPUT"&&((P=document.activeElement)==null?void 0:P.tagName)!=="TEXTAREA"&&(o.preventDefault(),(L=e(O))==null||L.focus())}function w(){e(A)&&_r(A,e(A).scrollTop=e(A).scrollHeight)}function z(){N()}function j(o){if(!o.trim())return e(m);const p=o.toLowerCase();return e(m).filter(P=>P.content.toLowerCase().includes(p))}function F(o){const p=o.split(`
`).filter(le=>le.trim()),P=p.slice(0,2).join(" ").substring(0,200)+"...",L=p.filter(le=>le.includes("key")||le.includes("important")||le.includes("significant")).slice(0,3),T=[];return(o.includes("growth")||o.includes("increase"))&&T.push("↑ Trending"),(o.includes("insight")||o.includes("analysis"))&&T.push("💡 Insight"),(o.includes("challenge")||o.includes("weakness"))&&T.push("⚠ Weakness"),{summary:P,insights:L,badges:T}}function K(o){const p=H(o.content),P=o.timestamp.toISOString().split("T")[0],L=`${p||"research-report"}-${P}.md`,T=`# Marketing Research Report by Compass
**Generated on:** ${o.timestamp.toLocaleDateString()}
**Time:** ${o.timestamp.toLocaleTimeString()}
**Format:** ${e(i).charAt(0).toUpperCase()+e(i).slice(1)}

---

${o.content}

---

*Report generated by Compass - Your AI Market Researcher*
`,le=new Blob([T],{type:"text/markdown"}),xe=URL.createObjectURL(le),s=document.createElement("a");s.href=xe,s.download=L,s.click(),URL.revokeObjectURL(xe)}function H(o){var P;const p=o.split(`
`);for(const L of p.slice(0,5)){if(L.includes("Company:")||L.includes("Company Name:"))return((P=L.split(":")[1])==null?void 0:P.trim().replace(/[^a-zA-Z0-9-]/g,""))||"";if(L.startsWith("# ")&&!L.includes("Research")&&!L.includes("Report"))return L.replace("# ","").trim().replace(/[^a-zA-Z0-9-]/g,"")||""}return""}function ne(o){let p=o.replace(/^# (.*$)/gim,'<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>').replace(/^#### (.*$)/gim,'<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/\*(.*?)\*/g,'<em class="italic" style="color: var(--muted-foreground);">$1</em>').replace(/```([\s\S]*?)```/g,'<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>').replace(/^[\s]*[-*+] (.+)$/gim,'<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>').replace(/^[\s]*(\d+)\.\s+(.+)$/gim,'<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$2</li>').replace(/^> (.+)$/gim,'<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>').replace(/\[(\d+)\]/g,'<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-bold ml-1">[$1]</sup>').replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>').replace(/\n\n/g,'</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">').replace(/\n/g,"<br>");return!p.startsWith("<h")&&!p.startsWith("<p")&&!p.startsWith("<ul")&&!p.startsWith("<ol")&&!p.startsWith("<blockquote")&&(p='<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">'+p+"</p>"),p=p.replace(/(<li[^>]*>.*?<\/li>)/gs,P=>P.includes("list-style-type: disc")?'<ul class="mb-4">'+P+"</ul>":P.includes("list-style-type: decimal")?'<ol class="mb-4">'+P+"</ol>":P),p}At(()=>{f(I,Z[0]),f(U,!1);const o=setInterval(()=>{fe=(fe+1)%Z.length,f(I,Z[fe])},4e3);document.addEventListener("keydown",be);const p=oe.subscribe(()=>{Vt().then(w)});return()=>{clearInterval(o),document.removeEventListener("keydown",be),p()}}),Xe(()=>re(D()),()=>{oe.set(D())}),Xe(()=>M(),()=>{f(m,M().length>0?M():[])}),Xe(()=>(e(d),e(m)),()=>{f($,e(d)?j(e(d)):e(m))}),zt(),Et();var ee=Ns(),ke=r(ee),we=r(ke),Ae=r(we),Fe=r(Ae),It=a(Fe,2);Wr(It,{class:"w-4 h-4"}),ve(2),t(Ae),t(we);var mt=a(we,2),je=r(mt),pt=r(je);Je(pt,{class:"w-4 h-4"}),t(je);var Ke=a(je,2),Lt=r(Ke);Vr(Lt,{class:"w-4 h-4"}),t(Ke);var ft=a(Ke,2),qe=r(ft);Gr(qe,{class:"w-4 h-4"}),t(ft),ve(2),t(mt),t(ke);var st=a(ke,2);{var gt=o=>{var p=ys(),P=r(p),L=r(P);Rr(L);var T=a(L,2);Je(T,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"});var le=a(T,2);{var xe=s=>{var b=_s(),S=r(b);t(b),Y(()=>J(S,`${e($),_(()=>e($).length)??""} results`)),n(s,b)};k(le,s=>{e(d)&&s(xe)})}t(P),t(p),Jt(L,()=>e(d),s=>f(d,s)),n(o,p)};k(st,o=>{e(y)&&o(gt)})}var at=a(st,2),bt=r(at),ze=r(bt),ht=r(ze);Or(ht,{class:"w-6 h-6 text-primary-foreground"}),t(ze);var ot=a(ze,2),wt=r(ot),Tt=r(wt);Xt(Tt,{class:"w-8 h-8 text-primary"}),ve(),t(wt),ve(2),t(ot),t(bt),t(at);var Pe=a(at,2),nt=r(Pe),xt=r(nt);xs(xt,{onRetry:z,children:(o,p)=>{var P=Ls(),L=me(P);{var T=S=>{cs(S,{type:"message",count:2})},le=S=>{var x=ye(),G=me(x);{var E=V=>{var q=ks(),ae=r(q),ue=r(ae);Xt(ue,{class:"w-6 h-6",style:"color: var(--primary);"}),ve(2),t(ae),ve(4),t(q),n(V,q)},te=V=>{var q=ye(),ae=me(q);{var ue=ie=>{var $e=$s(),Ie=r($e);Je(Ie,{class:"w-12 h-12 text-muted-foreground mx-auto mb-4"});var Ne=a(Ie,4),We=r(Ne);t(Ne),t($e),Y(()=>J(We,`No messages match your search for "${e(d)??""}"`)),n(ie,$e)};k(ae,ie=>{e($),e(d),_(()=>e($).length===0&&e(d))&&ie(ue)},!0)}n(V,q)};k(G,V=>{e($),e(d),_(()=>e($).length===0&&!e(d))?V(E):V(te,!1)},!0)}n(S,x)};k(L,S=>{e(U)?S(T):S(le,!1)})}var xe=a(L,2);Se(xe,1,()=>e($),S=>S.id,(S,x)=>{var G=As(),E=r(G),te=r(E);{var V=de=>{rr(de,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},q=de=>{zr(de,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};k(te,de=>{e(x),_(()=>e(x).role==="user")?de(V):de(q,!1)})}t(E);var ae=a(E,2),ue=r(ae),ie=r(ue),$e=r(ie,!0);t(ie);var Ie=a(ie,2),Ne=r(Ie);or(Ne,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var We=a(Ne,2),lr=r(We,!0);t(We),t(Ie);var ir=a(Ie,2);{var dr=de=>{var Me=Ms(),_e=r(Me);Ir(_e,{class:"w-3 h-3"}),ve(),t(Me),he("click",Me,()=>K(e(x))),n(de,Me)};k(ir,de=>{e(x),_(()=>e(x).role==="assistant"&&e(x).isReport)&&de(dr)})}t(ue);var cr=a(ue,2);{var vr=de=>{var Me=Cs(),_e=r(Me),Mt=r(_e,!0);t(_e),t(Me),Y(()=>J(Mt,(e(x),_(()=>e(x).content)))),n(de,Me)},ur=de=>{var Me=Rs();const _e=Ut(()=>(e(x),_(()=>F(e(x).content))));var Mt=r(Me);{var mr=He=>{var Be=Ss(),it=r(Be),jt=a(r(it),2),Ft=r(jt);Yt(Ft,()=>(re(e(_e)),_(()=>ne(e(_e).summary)))),t(jt),t(it),t(Be),n(He,Be)};k(Mt,He=>{re(e(_e)),_(()=>e(_e).summary)&&He(mr)})}var Bt=a(Mt,2);{var pr=He=>{var Be=Ps(),it=r(Be);Se(it,5,()=>(re(e(_e)),_(()=>e(_e).badges)),Oe,(jt,Ft)=>{var qt=js(),wr=r(qt,!0);t(qt),Y(()=>J(wr,e(Ft))),n(jt,qt)}),t(it),t(Be),n(He,Be)};k(Bt,He=>{re(e(_e)),_(()=>e(_e).badges.length>0)&&He(pr)})}var Dt=a(Bt,2),Ot=r(Dt),fr=r(Ot);Yt(fr,()=>(e(x),_(()=>ne(e(x).content)))),t(Ot),t(Dt);var Kt=a(Dt,2),Gt=r(Kt),Ct=r(Gt),gr=r(Ct);Hr(gr,{class:"w-3 h-3"}),ve(),t(Ct);var St=a(Ct,2),br=r(St);Kr(br,{class:"w-3 h-3"}),ve(),t(St);var Nt=a(St,2),hr=r(Nt);nr(hr,{class:"w-3 h-3"}),ve(),t(Nt),t(Gt),t(Kt),t(Me),he("click",Ct,()=>f(c,"Compare this analysis with their main competitor")),he("click",St,()=>f(c,"Add visual charts and graphs to this analysis")),he("click",Nt,()=>f(c,"Turn this analysis into presentation slides")),n(de,Me)};k(cr,de=>{e(x),_(()=>e(x).role==="user")?de(vr):de(ur,!1)})}t(ae),t(G),Y(de=>{Wt(E,`background: var(--${e(x),_(()=>e(x).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),J($e,(e(x),_(()=>e(x).role==="user"?"You":"Compass"))),J(lr,de)},[()=>(e(x),_(()=>e(x).timestamp.toLocaleTimeString()))]),n(S,G)});var s=a(xe,2);{var b=S=>{var x=Is(),G=a(r(x),2);Se(G,5,R,Oe,(V,q)=>{var ae=zs(),ue=r(ae),ie=a(ue,2),$e=r(ie,!0);t(ie),t(ae),Y(()=>{ce(ue,1,`w-4 h-4 rounded-full ${e(q),_(()=>e(q).status==="completed"?"bg-green-500":e(q).status==="active"?"bg-primary":"bg-muted")??""}`,"svelte-m6peww"),ce(ie,1,`text-sm ${e(q),_(()=>e(q).status==="completed"?"text-green-600":"text-foreground")??""}`,"svelte-m6peww"),J($e,(e(q),_(()=>e(q).title)))}),n(V,ae)}),t(G);var E=a(G,2);{var te=V=>{var q=Es(),ae=r(q),ue=r(ae);t(ae);var ie=a(ae,2),$e=r(ie);t(ie),t(q),Y(()=>{Wt(ue,`width: ${X()??""}%`),J($e,`${X()??""}% complete`)}),n(V,q)};k(E,V=>{X()>0&&V(te)})}t(x),n(S,x)};k(s,S=>{re(C()),re(R()),_(()=>C()&&R().length>0)&&S(b)})}n(o,P)},$$slots:{default:!0}}),t(nt),t(Pe),Rt(Pe,o=>f(A,o),()=>e(A));var _t=a(Pe,2);{let o=Ut(()=>(re(C()),re(R()),_(()=>C()&&R().length>0)));ms(_t,{get steps(){return R()},get currentProgress(){return X()},get isVisible(){return e(o)}})}var yt=a(_t,2),Ue=r(yt),Ge=r(Ue),Ve=a(r(Ge),2);Y(()=>{e(i),xr(()=>{})}),Se(Ve,5,()=>W,Oe,(o,p)=>{var P=Ts(),L=r(P,!0);t(P);var T={};Y(()=>{J(L,(e(p),_(()=>e(p).label))),T!==(T=(e(p),_(()=>e(p).value)))&&(P.value=(P.__value=(e(p),_(()=>e(p).value)))??"")}),n(o,P)}),t(Ve),t(Ge);var kt=a(Ge,2),Re=r(kt);$r(Re);let Ze;Rt(Re,o=>f(O,o),()=>e(O));var lt=a(Re,2),Ee=a(r(lt),4),De=r(Ee);{var $t=o=>{var p=Ds();n(o,p)},Qe=o=>{Je(o,{class:"w-4 h-4"})};k(De,o=>{C()?o($t):o(Qe,!1)})}t(Ee),t(lt),t(kt),ve(4),t(Ue),t(yt),t(ee),Y((o,p)=>{Ce(Fe,"href",`/dashboard/${l(),_(()=>l().params.envSlug)??""}`),Ce(Re,"placeholder",C()?"Researching...":e(I)),Ze=ce(Re,1,"w-full px-4 py-3 pr-32 border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-foreground bg-background placeholder:text-muted-foreground transition-all duration-200 svelte-m6peww",null,Ze,o),Re.disabled=C(),Ee.disabled=p,Ce(Ee,"title",C()?"Researching...":"Send message (Ctrl+Enter)")},[()=>({"opacity-50":C()}),()=>(e(c),re(C()),_(()=>!e(c).trim()||C()))]),he("click",je,()=>f(y,!e(y))),Pr(Ve,()=>e(i),o=>f(i,o)),Jt(Re,()=>e(c),o=>f(c,o)),he("keydown",Re,ge),he("click",Ee,N),n(B,ee),tt(),h()}var qs=v('<h2 class="text-lg font-semibold text-foreground"> </h2> <button class="text-muted-foreground hover:text-foreground transition-colors"><!></button>',1),Us=v('<div class="flex flex-col items-center w-full"><button class="p-2 hover:bg-accent rounded-lg mb-2" title="Projects"><!></button> <span class="text-xs text-muted-foreground"> </span></div>'),Ws=v('<div class="p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors group"><h3 class="font-medium text-sm mb-2 text-foreground group-hover:text-accent-foreground"> </h3> <p class="text-xs text-muted-foreground group-hover:text-accent-foreground/80"> </p></div>'),Hs=v('<div class="space-y-4"></div>'),Bs=v('<button class="w-2 h-2 bg-muted-foreground rounded-full hover:bg-foreground transition-colors"></button>'),Os=v('<span class="text-xs text-muted-foreground"> </span>'),Ks=v('<div class="flex flex-col items-center space-y-2"><!> <!></div>'),Gs=v(`<h3 class="text-md font-semibold text-foreground mb-4">Quick Start Templates</h3> <div class="space-y-3"><button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Competitive Analysis</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Analyze competitors' strategies and positioning</div></button> <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Market Research</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Research market trends and opportunities</div></button> <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Industry Analysis</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Deep dive into industry dynamics</div></button> <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"><div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Customer Research</div> <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Understand customer needs and behavior</div></button></div>`,1),Vs=v('<div class="flex justify-center"><button class="p-2 hover:bg-accent rounded-lg" title="Quick Start Templates"><!></button></div>'),Zs=v('<aside><button><!></button> <div><div class="flex items-center justify-between mb-6"><!></div> <!> <div class="mt-8"><!></div></div></aside>');function Qs(B,u){et(u,!1);const g=pe();let h=se(u,"collapsed",12,!1),M=se(u,"isMobile",8,!1),l=se(u,"projects",24,()=>[]);function m(){h(!h())}const $=[{id:"1",title:"Generate 5 attention-grab...",description:'"Revolutionize Customer Enga...'},{id:"2",title:"Learning From 100 Years o...",description:"For athletes, high altitude prod..."},{id:"3",title:"Research officiants",description:"Maxwell's equations—the foun..."},{id:"4",title:"What does a senior lead de...",description:"Physiological respiration involv..."},{id:"5",title:"Write a sweet note to your...",description:"In the eighteenth century the G..."},{id:"6",title:"Meet with cake bakers",description:"Physical space is often conceiv..."},{id:"7",title:"Meet with cake bakers",description:"Physical space is often conceiv..."}];Xe(()=>re(l()),()=>{f(g,l().length>0?l():$)}),zt(),Et();var D=Zs();let C;var R=r(D);let X;var oe=r(R);{var c=w=>{sr(w,{class:"w-3 h-3"})},i=w=>{ar(w,{class:"w-3 h-3"})};k(oe,w=>{h()?w(c):w(i,!1)})}t(R);var d=a(R,2);let y;var U=r(d),A=r(U);{var O=w=>{var z=qs(),j=me(z),F=r(j);t(j);var K=a(j,2),H=r(K);Ye(H,{class:"w-5 h-5"}),t(K),Y(()=>J(F,`Projects (${e(g),_(()=>e(g).length)??""})`)),n(w,z)},W=w=>{var z=Us(),j=r(z),F=r(j);Ye(F,{class:"w-5 h-5"}),t(j);var K=a(j,2),H=r(K,!0);t(K),t(z),Y(()=>J(H,(e(g),_(()=>e(g).length)))),n(w,z)};k(A,w=>{h()?w(W,!1):w(O)})}t(U);var Z=a(U,2);{var I=w=>{var z=Hs();Se(z,5,()=>e(g),j=>j.id,(j,F)=>{var K=Ws(),H=r(K),ne=r(H,!0);t(H);var ee=a(H,2),ke=r(ee,!0);t(ee),t(K),Y(()=>{J(ne,(e(F),_(()=>e(F).title))),J(ke,(e(F),_(()=>e(F).description)))}),n(j,K)}),t(z),n(w,z)},fe=w=>{var z=Ks(),j=r(z);Se(j,1,()=>(e(g),_(()=>e(g).slice(0,5))),H=>H.id,(H,ne)=>{var ee=Bs();Y(()=>Ce(ee,"title",(e(ne),_(()=>e(ne).title)))),n(H,ee)});var F=a(j,2);{var K=H=>{var ne=Os(),ee=r(ne);t(ne),Y(()=>J(ee,`+${e(g),_(()=>e(g).length-5)??""}`)),n(H,ne)};k(F,H=>{e(g),_(()=>e(g).length>5)&&H(K)})}t(z),n(w,z)};k(Z,w=>{h()?w(fe,!1):w(I)})}var Q=a(Z,2),N=r(Q);{var ge=w=>{var z=Gs();ve(2),n(w,z)},be=w=>{var z=Vs(),j=r(z),F=r(j);Br(F,{class:"w-4 h-4"}),t(j),t(z),n(w,z)};k(N,w=>{h()?w(be,!1):w(ge)})}t(Q),t(d),t(D),Y((w,z,j)=>{C=ce(D,1,"bg-background border-l border-border overflow-y-auto transition-all duration-300 ease-in-out relative",null,C,w),X=ce(R,1,"absolute -left-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors",null,X,z),y=ce(d,1,"p-6",null,y,j)},[()=>({"w-80":!h(),"w-16":h()&&!M(),"w-0":h()&&M(),"overflow-hidden":h()&&M()}),()=>({hidden:M()&&h()}),()=>({hidden:h()&&M()})]),he("click",R,m),n(B,D),tt()}var Ys=v('<div class="flex h-screen bg-background text-foreground"><!> <!> <!></div>');function La(B,u){et(u,!0);const[g,h]=Ht(),M=()=>Pt($,"$messages",g);let{session:l,profile:m}=u.data;const $=er([]);let D=Te(!1),C=Te(0),R=Te(""),X=Te(yr([])),oe=Te(0),c=Te(!1),i=Te(!1),d=Te(0),y=Le(()=>e(d)<768);Zt(()=>{e(y)&&(f(c,!0),f(i,!0))});const U=["Compare Drift and Intercom's messaging and channel mix based on the last 30 days...","How is Adobe marketing Firefly across its channels based on recent data...","What influencer or social campaigns has Notion run recently...","Map Figma's demand-gen strategy from 2022 to now...","Analyze Stripe's developer marketing evolution over the past quarter..."];At(()=>{f(R,U[0],!0);const I=setInterval(()=>{f(C,(e(C)+1)%U.length),f(R,U[e(C)],!0)},4e3);return()=>clearInterval(I)}),Zt(()=>{M().length>0&&setTimeout(()=>{window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})},100)});var A=Ys();Mr(I=>{kr.title="Compass - AI Market Researcher"});var O=r(A);rs(O,{get isMobile(){return e(y)},get session(){return l},get profile(){return m},get collapsed(){return e(c)},set collapsed(I){f(c,I,!0)}});var W=a(O,2);Fs(W,{get messages(){return M()},get leftSidebarCollapsed(){return e(c)},get rightSidebarCollapsed(){return e(i)},get isLoading(){return e(D)},set isLoading(I){f(D,I,!0)},get progressSteps(){return e(X)},set progressSteps(I){f(X,I,!0)},get currentProgress(){return e(oe)},set currentProgress(I){f(oe,I,!0)}});var Z=a(W,2);Qs(Z,{get isMobile(){return e(y)},get collapsed(){return e(i)},set collapsed(I){f(i,I,!0)}}),t(A),jr("innerWidth",I=>f(d,I,!0)),n(B,A),tt(),h()}export{La as component};
