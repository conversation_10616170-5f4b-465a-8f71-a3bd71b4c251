import"../chunks/CWj6FrbW.js";import{o as gr}from"../chunks/RnwjOPnl.js";import{p as hr,u as xr,f as l,a as r,e as br,j as Cr,I as yr,c as _,i as X,r as c,b,d,n as E,q as M,s as p,o as kr,h as Y,k as Pr,t as Fr}from"../chunks/DDiqt3uM.js";import{s as wr}from"../chunks/DWulv87v.js";import{i as N}from"../chunks/2C89X9tI.js";import{c as o}from"../chunks/BCmD-YNt.js";import{a as zr}from"../chunks/D-ywOz1J.js";import{t as jr}from"../chunks/A4ulxp7Q.js";import{s as Dr}from"../chunks/C-ZVHnwW.js";import{a as Ir,b as Sr,s as W}from"../chunks/B82PTGnX.js";import{s as Br,c as Lr,b as qr,C as Er,F as Mr,a as Nr,d as Wr}from"../chunks/BjYfYTud.js";import"../chunks/DSS8CPXb.js";import{a as Ar}from"../chunks/CCJOWbOV.js";import{I as Gr}from"../chunks/Cn9N4mio.js";import"../chunks/ChutyBgo.js";import{a as Hr,C as Jr}from"../chunks/CfRLwaeF.js";import"../chunks/DhRTwODG.js";import{C as Kr,a as Or}from"../chunks/Bls5ffmn.js";import{g as Z}from"../chunks/Bo96ghGo.js";import{g as Qr}from"../chunks/CaxpRkM3.js";import{f as Rr}from"../chunks/DSm1r-pw.js";import{B as Tr}from"../chunks/BFbKPyUZ.js";import{L as rr}from"../chunks/DWMPsgkI.js";var Ur=l("<!> <!> <!>",1),Vr=l('<div class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"><!> <span> </span></div>'),Xr=l("<!> Creating...",1),Yr=l('<form method="post" class="space-y-4"><!> <!> <!></form>'),Zr=l("<!> <!>",1),ra=l('<div class="flex items-center justify-center"><!></div>'),aa=l('<div class="min-h-screen flex items-center justify-center px-4"><div class="w-full max-w-[500px]"><!></div></div>');function wa(ar,u){hr(u,!0);const[C,er]=Ir(),y=()=>W(K,"$formData",C),A=()=>W(tr,"$errors",C),G=()=>W(sr,"$delayed",C);let k=Qr(),H=yr(!0);const J=Br(u.data.form,{validators:Lr(Ar)});gr(()=>{k.value?Z(`/dashboard/${k.value.slug}`):Cr(H,!1)}),xr(()=>{var e;(e=u.form)!=null&&e.env&&(k.value=u.form.env,Z(`/dashboard/${u.form.env.slug}`))});const{form:K,enhance:P,errors:tr,delayed:sr}=J;var F=aa(),O=_(F),or=_(O);{var nr=e=>{var i=b(),w=d(i);o(w,()=>Jr,(dr,lr)=>{lr(dr,{class:"card-brutal",children:(ir,ea)=>{var Q=Zr(),R=d(Q);o(R,()=>Kr,(z,j)=>{j(z,{children:(D,vr)=>{var n=b(),h=d(n);o(h,()=>Or,(x,I)=>{I(x,{class:"text-2xl font-bold text-center",children:(S,a)=>{E();var t=M("Create your company workspace");r(S,t)},$$slots:{default:!0}})}),r(D,n)},$$slots:{default:!0}})});var fr=p(R,2);o(fr,()=>Hr,(z,j)=>{j(z,{children:(D,vr)=>{var n=Yr(),h=_(n);o(h,()=>qr,(a,t)=>{t(a,{get form(){return J},name:"name",children:(m,$)=>{var f=b(),B=d(f);o(B,()=>Er,(s,v)=>{v(s,{children:kr,$$slots:{default:(L,_r)=>{const cr=Pr(()=>_r.attrs);var T=Ur(),U=d(T);o(U,()=>Mr,(g,q)=>{q(g,{children:(ur,ta)=>{E();var $r=M("Company Workspace Name");r(ur,$r)},$$slots:{default:!0}})});var V=p(U,2);Gr(V,Dr(()=>X(cr),{get value(){return y().name},set value(g){Sr(K,Y(y).name=g,Y(y))}}));var pr=p(V,2);o(pr,()=>Nr,(g,q)=>{q(g,{})}),r(L,T)}}})}),r(m,f)},$$slots:{default:!0}})});var x=p(h,2);{var I=a=>{var t=Vr(),m=_(t);Wr(m,{size:16,class:"flex-shrink-0"});var $=p(m,2),f=_($,!0);c($),c(t),Fr(()=>wr(f,A()._errors[0])),jr(3,t,()=>Rr,()=>({y:-10,duration:300})),r(a,t)};N(x,a=>{A()._errors&&a(I)})}var S=p(x,2);Tr(S,{get disabled(){return G()},type:"submit",class:"w-full",children:(a,t)=>{var m=b(),$=d(m);{var f=s=>{var v=Xr(),L=d(v);rr(L,{class:"animate-spin mr-2",size:16}),E(),r(s,v)},B=s=>{var v=M("Create");r(s,v)};N($,s=>{G()?s(f):s(B,!1)})}r(a,m)},$$slots:{default:!0}}),c(n),zr(n,a=>P==null?void 0:P(a)),r(D,n)},$$slots:{default:!0}})}),r(ir,Q)},$$slots:{default:!0}})}),r(e,i)},mr=e=>{var i=ra(),w=_(i);rr(w,{class:"animate-spin",size:32}),c(i),r(e,i)};N(or,e=>{X(H)?e(mr,!1):e(nr)})}c(O),c(F),r(ar,F),br(),er()}export{wa as component};
