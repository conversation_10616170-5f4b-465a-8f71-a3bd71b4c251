import"../chunks/CWj6FrbW.js";import{o as F}from"../chunks/RnwjOPnl.js";import{p as G,f as p,d as c,a as e,e as $,$ as H,s as g,i as J,k as K,n as O,c as x,r as k,q as v,b}from"../chunks/DDiqt3uM.js";import{h as Q}from"../chunks/DWulv87v.js";import{i as n}from"../chunks/2C89X9tI.js";import{a as R,s as U}from"../chunks/B82PTGnX.js";import{A as V,s as W,o as X}from"../chunks/DlanGm9a.js";import"../chunks/DhRTwODG.js";import{i as Y,g as Z}from"../chunks/MyTzUPSZ.js";import{p as aa}from"../chunks/BVb9gh60.js";var ra=p('<div role="alert" class="alert alert-success mb-5"><svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span>Email verified! Please sign in.</span></div>'),ea=p('<div role="alert" class="alert alert-error mb-5"><svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span><!></span></div>'),ta=p(`<!> <!> <h1 class="text-2xl font-bold mb-6">Sign In</h1> <!> <div class="text-l text-primary mt-4"><a class="underline" href="/login/forgot_password">Forgot password?</a></div> <div class="text-l text-primary mt-3">Don't have an account? <a class="underline" href="/login/sign_up">Sign up</a>.</div>`,1);function ga(y,l){G(l,!0);const[P,A]=R(),o=()=>U(aa,"$page",P);let{supabase:S}=l.data;F(()=>{S.auth.onAuthStateChange(async(a,r)=>{if(a=="SIGNED_IN"){if(await Y("data:init"),r!=null&&r.user.is_anonymous)return;setTimeout(async()=>{Z("/find-env")},1)}})});var f=ta();Q(a=>{H.title="Sign in"});var h=c(f);{var z=a=>{var r=ra();e(a,r)};n(h,a=>{o().url.searchParams.get("verified")=="true"&&a(z)})}var _=g(h,2);{var C=a=>{var r=ea(),w=g(x(r),2),I=x(w);{var M=t=>{var d=v("Authentication failed. Please try again.");e(t,d)},j=t=>{var d=b(),B=c(d);{var E=s=>{var m=v("Session creation failed. Please try signing in again.");e(s,m)},L=s=>{var m=b(),N=c(m);{var T=i=>{var u=v(`Please confirm your email before signing in. Check your inbox for the
        confirmation link.`);e(i,u)},q=i=>{var u=v("An unexpected error occurred. Please try again.");e(i,u)};n(N,i=>{o().url.searchParams.get("error")==="email_not_confirmed"?i(T):i(q,!1)},!0)}e(s,m)};n(B,s=>{o().url.searchParams.get("error")==="session_failed"?s(E):s(L,!1)},!0)}e(t,d)};n(I,t=>{o().url.searchParams.get("error")==="auth_failed"?t(M):t(j,!1)})}k(w),k(r),e(a,r)};n(_,a=>{o().url.searchParams.get("error")&&a(C)})}var D=g(_,4);{let a=K(()=>`${l.data.url}/auth/callback`);V(D,{get supabaseClient(){return l.data.supabase},view:"sign_in",get redirectTo(){return J(a)},get providers(){return X},socialLayout:"horizontal",showLinks:!1,get appearance(){return W},additionalData:void 0})}O(4),e(y,f),$(),A()}export{ga as component};
