import"../chunks/CWj6FrbW.js";import{p as L,f as c,s as o,d as S,a as b,e as T,$ as x,i as l,k as n,c as C,n as E,r as N,t as P}from"../chunks/DDiqt3uM.js";import{h as D}from"../chunks/DWulv87v.js";import{i as z}from"../chunks/2C89X9tI.js";import{c as A,s as M}from"../chunks/DE2v8SHj.js";import{S as u}from"../chunks/rqFcJcsx.js";import{g as U}from"../chunks/CaxpRkM3.js";import{b as W}from"../chunks/ChutyBgo.js";var Y=c("<!> <!> <!> <!> <!> <!>",1),Z=c('<p>You need to <a href="/login/sign_up">sign up</a> to update your settings</p>'),j=c('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function Q(k,f){L(f,!0);let{profile:t,auth:_}=f.data;const d=U();var p=j();D(r=>{x.title="Settings"});var B=o(S(p),2);{var V=r=>{var s=Y(),g=S(s);{let a=n(()=>[{id:"fullName",label:"Name",initialValue:(t==null?void 0:t.full_name)??""},{id:"companyName",label:"Company Name",initialValue:(t==null?void 0:t.company_name)??""},{id:"website",label:"Company Website",initialValue:(t==null?void 0:t.website)??""}]),e=n(()=>{var i;return(i=d.value)==null?void 0:i.slug});u(g,{title:"Profile",editable:!1,get fields(){return l(a)},editButtonTitle:"Edit Profile",get editLink(){return`/dashboard/${l(e)??""}/settings/edit_profile`}})}var m=o(g,2);{let a=n(()=>{var i;return[{id:"email",initialValue:((i=_.user)==null?void 0:i.email)||""}]}),e=n(()=>{var i;return(i=d.value)==null?void 0:i.slug});u(m,{title:"Email",editable:!1,get fields(){return l(a)},editButtonTitle:"Change Email",get editLink(){return`/dashboard/${l(e)??""}/settings/change_email`}})}var v=o(m,2);{let a=n(()=>{var e;return(e=d.value)==null?void 0:e.slug});u(v,{title:"Password",editable:!1,fields:[{id:"password",initialValue:"••••••••••••••••"}],editButtonTitle:"Change Password",get editLink(){return`/dashboard/${l(a)??""}/settings/change_password`}})}var h=o(v,2);{let a=n(()=>[{id:"subscriptionStatus",initialValue:t!=null&&t.unsubscribed?"Unsubscribed":"Subscribed"}]),e=n(()=>{var i;return(i=d.value)==null?void 0:i.slug});u(h,{title:"Email Subscription",editable:!1,get fields(){return l(a)},editButtonTitle:"Change Subscription",get editLink(){return`/dashboard/${l(e)??""}/settings/change_email_subscription`}})}var $=o(h,2);{let a=n(()=>{var e;return(e=d.value)==null?void 0:e.slug});u($,{title:"Billing",editable:!1,fields:[],editButtonTitle:"Manage Billing",get editLink(){return`/dashboard/${l(a)??""}/billing`}})}var y=o($,2);{let a=n(()=>{var e;return(e=d.value)==null?void 0:e.slug});u(y,{title:"Danger Zone",editable:!1,dangerous:!0,fields:[],editButtonTitle:"Delete Account",get editLink(){return`/dashboard/${l(a)??""}/settings/delete_account`}})}b(r,s)},w=r=>{var s=Z(),g=o(C(s));E(),N(s),P(m=>M(g,1,m),[()=>A(W({size:"sm"}))]),b(r,s)};z(B,r=>{var s;(s=_.user)!=null&&s.is_anonymous?r(w,!1):r(V)})}b(k,p),T()}export{Q as component};
