import"../chunks/CWj6FrbW.js";import{b as Te,d as fe,a as i,p as Ne,f as c,c as t,r as e,s as o,I as ie,i as a,t as J,k as Oe,e as Le,j as S,n as ke,u as qe,a0 as lt,al as Ue,q as Ge,$ as zt}from"../chunks/DDiqt3uM.js";import{d as Ye,s as G,r as Pt,h as jt}from"../chunks/DWulv87v.js";import{G as Rt,T as vt,H as Ot,R as Nt,b as mt}from"../chunks/Di-aknNf.js";import{w as gt}from"../chunks/rjRVMZXi.js";import{o as Je}from"../chunks/RnwjOPnl.js";import{i as y}from"../chunks/2C89X9tI.js";import{s as ut}from"../chunks/DtGADYZa.js";import{s as ce,c as Lt}from"../chunks/DE2v8SHj.js";import{l as st,s as it,p}from"../chunks/C-ZVHnwW.js";import{e as Se,i as Re}from"../chunks/OiKQa7Wx.js";import{c as Qe}from"../chunks/BCmD-YNt.js";import{s as Be,b as Kt,r as ct,d as Ft}from"../chunks/C36Ip9GY.js";import{b as bt}from"../chunks/Dqu9JXqq.js";import{a as pt,s as _t}from"../chunks/B82PTGnX.js";import{p as Gt}from"../chunks/4KkXnDJG.js";import{M as xt}from"../chunks/Bxi4P_5L.js";import{X as ht}from"../chunks/rNGuVYtO.js";import{S as He}from"../chunks/QFpxkGuO.js";import{C as yt}from"../chunks/3OymkobC.js";import{S as rt,L as Ut}from"../chunks/Rh-W-Viu.js";import{s as Xe}from"../chunks/iCEqKm8o.js";import{t as at}from"../chunks/A4ulxp7Q.js";import{b as et}from"../chunks/DUGxtfU6.js";import{s as ot}from"../chunks/DSm1r-pw.js";import"../chunks/Bm1TgOhB.js";import{C as Bt}from"../chunks/CXeeNO9R.js";import{C as Wt}from"../chunks/DR0c5pEj.js";import{L as wt}from"../chunks/DWMPsgkI.js";import{C as Vt}from"../chunks/C6jXKNMu.js";import{C as kt}from"../chunks/oaGF9CiI.js";import"../chunks/DhRTwODG.js";import{I as nt}from"../chunks/CkoRhfQ8.js";import{T as tt,C as dt,a as Ht,S as St}from"../chunks/BxkoWS-i.js";import{U as qt,B as Yt}from"../chunks/ng6E9JCw.js";import{C as Jt}from"../chunks/CgBAmp64.js";function Ct(x,r){const d=st(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}]];nt(x,it({name:"book-open"},()=>d,{get iconNode(){return C},children:(A,m)=>{var w=Te(),E=fe(w);Xe(E,r,"default",{},null),i(A,w)},$$slots:{default:!0}}))}function Qt(x,r){const d=st(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M12 18v-6"}],["path",{d:"m9 15 3 3 3-3"}]];nt(x,it({name:"file-down"},()=>d,{get iconNode(){return C},children:(A,m)=>{var w=Te(),E=fe(w);Xe(E,r,"default",{},null),i(A,w)},$$slots:{default:!0}}))}function Xt(x,r){const d=st(r,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"}]];nt(x,it({name:"filter"},()=>d,{get iconNode(){return C},children:(A,m)=>{var w=Te(),E=fe(w);Xe(E,r,"default",{},null),i(A,w)},$$slots:{default:!0}}))}function Zt(x,r){r(!r())}function $t(x,r){S(r,!a(r))}async function er(x,r){S(r,!1);try{window.location.href="/sign_out"}catch(d){console.error("Error signing out:",d)}}function tr(x,r,d){S(r,!1);const C=d().params.envSlug;window.location.href=`/dashboard/${C}/settings`}var rr=c('<span class="font-semibold text-lg text-sidebar-foreground"> </span>'),ar=c('<div class="relative"><!> <input type="text" class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"/> <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">⌘K</kbd></div>'),or=c('<div class="flex justify-center"><button class="p-2 hover:bg-sidebar-accent rounded-lg svelte-947kzy"><!></button></div>'),sr=(x,r,d)=>r(a(d)),ir=c('<span class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full"> </span>'),nr=c(" <!>",1),dr=c("<button><!> <!></button>"),lr=c('<div class="flex-1 min-w-0"><p class="text-sm font-medium text-sidebar-foreground truncate text-left"> </p> <p class="text-xs text-muted-foreground truncate text-left"> </p></div> <!>',1),vr=c('<div class="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg z-50 py-2 animate-slide-up svelte-947kzy" style="box-shadow: var(--shadow-lg);"><button class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors svelte-947kzy"><!> Settings</button> <button class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors svelte-947kzy"><!> Sign Out</button></div>'),ur=c('<aside><button><!></button> <header><div class="flex items-center space-x-2 mb-4"><div class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center"><!></div> <!></div> <!></header> <nav class="flex-1 p-4"><div class="space-y-1"></div></nav> <footer><div class="relative mt-4 pt-4 border-t border-sidebar-border"><button><div><span class="text-sm font-medium text-primary-foreground"> </span></div> <!></button> <!></div></footer></aside>');function cr(x,r){Ne(r,!0);const[d,C]=pt(),A=()=>_t(Gt,"$page",d);let m=p(r,"collapsed",15,!1),w=p(r,"isMobile",3,!1),E=p(r,"session",3,null),H=p(r,"profile",3,null);p(r,"agentType",3,"research");let X=p(r,"navigationItems",19,()=>[]),ne=p(r,"brandName",3,"Robynn AI"),Q=p(r,"searchPlaceholder",3,"Search"),I=ie(!1),g;function u(n){g&&!g.contains(n.target)&&S(I,!1)}let k=Oe(()=>{var n,f,s,l,h,M,K,z,b,P,F,re,N,V,Y,ue;return{name:((n=H())==null?void 0:n.full_name)||((l=(s=(f=E())==null?void 0:f.user)==null?void 0:s.user_metadata)==null?void 0:l.full_name)||((K=(M=(h=E())==null?void 0:h.user)==null?void 0:M.email)==null?void 0:K.split("@")[0])||"User",email:((b=(z=E())==null?void 0:z.user)==null?void 0:b.email)||"<EMAIL>",initials:T(((P=H())==null?void 0:P.full_name)||((N=(re=(F=E())==null?void 0:F.user)==null?void 0:re.user_metadata)==null?void 0:N.full_name)||((ue=(Y=(V=E())==null?void 0:V.user)==null?void 0:Y.email)==null?void 0:ue.split("@")[0])||"User")}});function T(n){return n.split(" ").map(f=>f.charAt(0).toUpperCase()).slice(0,2).join("")}function O(n){n.onClick?n.onClick():n.href&&(window.location.href=n.href)}Je(()=>(document.addEventListener("click",u),()=>{document.removeEventListener("click",u)}));var _=ur();let D;var L=t(_);L.__click=[Zt,m];let U;var Z=t(L);{var j=n=>{xt(n,{class:"w-3 h-3"})},te=n=>{ht(n,{class:"w-3 h-3"})};y(Z,n=>{m()?n(j):n(te,!1)})}e(L);var B=o(L,2);let q;var ae=t(B),oe=t(ae),$=t(oe);Rt($,{class:"w-4 h-4 text-sidebar-primary-foreground"}),e(oe);var de=o(oe,2);{var v=n=>{var f=rr(),s=t(f,!0);e(f),J(()=>G(s,ne())),i(n,f)};y(de,n=>{m()||n(v)})}e(ae);var R=o(ae,2);{var se=n=>{var f=ar(),s=t(f);He(s,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"});var l=o(s,2);ke(2),e(f),J(()=>Be(l,"placeholder",Q())),i(n,f)},le=n=>{var f=or(),s=t(f),l=t(s);He(l,{class:"w-4 h-4 text-muted-foreground"}),e(s),e(f),i(n,f)};y(R,n=>{m()?n(le,!1):n(se)})}e(B);var me=o(B,2),_e=t(me);Se(_e,21,X,n=>n.id,(n,f)=>{var s=dr();s.__click=[sr,O,f];let l;var h=t(s);{let z=Oe(()=>m()?"":"mr-3");Qe(h,()=>a(f).icon,(b,P)=>{P(b,{get class(){return`w-4 h-4 ${a(z)??""}`}})})}var M=o(h,2);{var K=z=>{var b=nr(),P=fe(b),F=o(P);{var re=N=>{var V=ir(),Y=t(V,!0);e(V),J(()=>G(Y,a(f).badge)),i(N,V)};y(F,N=>{a(f).badge&&N(re)})}J(()=>G(P,`${a(f).label??""} `)),i(z,b)};y(M,z=>{m()||z(K)})}e(s),J(z=>{l=ce(s,1,"flex items-center w-full px-3 py-2 text-sm rounded-lg transition-colors svelte-947kzy",null,l,z),Be(s,"title",m()?a(f).label:"")},[()=>({"text-sidebar-primary-foreground":a(f).active,"bg-sidebar-primary":a(f).active,"text-sidebar-foreground":!a(f).active,"hover:bg-sidebar-accent":!a(f).active,"justify-center":m()})]),i(n,s)}),e(_e),e(me);var xe=o(me,2);let De;var Ee=t(xe),he=t(Ee);he.__click=[$t,I];let Pe;var Ie=t(he);let je;var W=t(Ie),ge=t(W,!0);e(W),e(Ie);var be=o(Ie,2);{var ee=n=>{var f=lr(),s=fe(f),l=t(s),h=t(l,!0);e(l);var M=o(l,2),K=t(M,!0);e(M),e(s);var z=o(s,2);{let b=Oe(()=>a(I)?"rotate-180":"");yt(z,{get class(){return`w-4 h-4 text-muted-foreground transition-transform duration-200 ${a(b)??""}`}})}J(()=>{G(h,a(k).name),G(K,a(k).email)}),i(n,f)};y(be,n=>{m()||n(ee)})}e(he);var ve=o(he,2);{var pe=n=>{var f=vr(),s=t(f);s.__click=[tr,I,A];var l=t(s);rt(l,{class:"w-4 h-4 mr-3"}),ke(),e(s);var h=o(s,2);h.__click=[er,I];var M=t(h);Ut(M,{class:"w-4 h-4 mr-3"}),ke(),e(h),e(f),i(n,f)};y(ve,n=>{a(I)&&!m()&&n(pe)})}e(Ee),bt(Ee,n=>g=n,()=>g),e(xe),e(_),J((n,f,s,l,h,M)=>{D=ce(_,1,"bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative",null,D,n),U=ce(L,1,"absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors svelte-947kzy",null,U,f),q=ce(B,1,"p-4 border-b border-sidebar-border",null,q,s),De=ce(xe,1,"p-4 border-t border-sidebar-border",null,De,l),Pe=ce(he,1,"flex items-center w-full p-2 hover:bg-sidebar-accent rounded-lg transition-colors group svelte-947kzy",null,Pe,h),Be(he,"title",m()?a(k).email:"Profile menu"),je=ce(Ie,1,"w-8 h-8 bg-primary rounded-full flex items-center justify-center svelte-947kzy",null,je,M),G(ge,a(k).initials)},[()=>({"w-64":!m(),"w-16":m()&&!w(),"w-0":m()&&w(),"overflow-hidden":m()&&w()}),()=>({hidden:w()&&m()}),()=>({hidden:m()&&w()}),()=>({hidden:m()&&w()}),()=>({"justify-center":m()}),()=>({"mr-3":!m()})]),i(x,_),Le(),C()}Ye(["click"]);function ft(x,r,d,C,A,m){S(r,!1),d(null),S(C,""),S(A,""),m()&&m()()}function fr(){window.location.href="/"}var mr=c('<button class="ml-auto text-xs text-destructive hover:text-destructive/80 underline">Retry</button>'),gr=c('<div class="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg"><!> <span class="text-sm text-destructive">Something went wrong</span> <!></div>'),br=c('<pre class="text-xs text-muted-foreground mt-2 overflow-auto max-h-32"> </pre>'),pr=c('<details class="mb-6 w-full max-w-md"><summary class="cursor-pointer text-sm text-muted-foreground hover:text-foreground">Error Details</summary> <div class="mt-2 p-3 bg-muted rounded-lg text-left"><p class="text-xs font-mono text-destructive break-all"> </p> <!></div></details>'),_r=c('<button class="btn-primary px-4 py-2 flex items-center gap-2"><!> Try Again</button>'),xr=c('<div class="flex flex-col items-center justify-center min-h-[400px] p-8 text-center"><div class="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4"><!></div> <h2 class="text-xl font-semibold text-foreground mb-2">Oops! Something went wrong</h2> <p class="text-muted-foreground mb-6 max-w-md">We encountered an unexpected error. This has been logged and our team will investigate.</p> <!> <div class="flex gap-3"><!> <button class="btn-secondary px-4 py-2 flex items-center gap-2"><!> Go Home</button></div></div>');function hr(x,r){Ne(r,!0);let d=p(r,"fallback",3,"full"),C=p(r,"onRetry",3,null),A=p(r,"error",7,null),m=ie(!1),w=ie(""),E=ie("");qe(()=>{A()&&(S(m,!0),S(w,A().message,!0),S(E,A().stack||"",!0))}),Je(()=>{const I=g=>{var u,k;console.error("Unhandled promise rejection:",g.reason),S(m,!0),S(w,((u=g.reason)==null?void 0:u.message)||"An unexpected error occurred",!0),S(E,((k=g.reason)==null?void 0:k.stack)||"",!0)};return window.addEventListener("unhandledrejection",I),()=>{window.removeEventListener("unhandledrejection",I)}});var H=Te(),X=fe(H);{var ne=I=>{var g=Te(),u=fe(g);{var k=O=>{var _=gr(),D=t(_);vt(D,{class:"w-4 h-4 text-destructive flex-shrink-0"});var L=o(D,4);{var U=Z=>{var j=mr();j.__click=[ft,m,A,w,E,C],i(Z,j)};y(L,Z=>{C()&&Z(U)})}e(_),i(O,_)},T=O=>{var _=xr(),D=t(_),L=t(D);vt(L,{class:"w-8 h-8 text-destructive"}),e(D);var U=o(D,6);{var Z=oe=>{var $=pr(),de=o(t($),2),v=t(de),R=t(v,!0);e(v);var se=o(v,2);{var le=me=>{var _e=br(),xe=t(_e);e(_e),J(()=>G(xe,`
                ${a(E)??""}
              `)),i(me,_e)};y(se,me=>{a(E)&&me(le)})}e(de),e($),J(()=>G(R,a(w))),i(oe,$)};y(U,oe=>{a(w)&&oe(Z)})}var j=o(U,2),te=t(j);{var B=oe=>{var $=_r();$.__click=[ft,m,A,w,E,C];var de=t($);Nt(de,{class:"w-4 h-4"}),ke(),e($),i(oe,$)};y(te,oe=>{C()&&oe(B)})}var q=o(te,2);q.__click=[fr];var ae=t(q);Ot(ae,{class:"w-4 h-4"}),ke(),e(q),e(j),e(_),i(O,_)};y(u,O=>{d()==="minimal"?O(k):O(T,!1)})}i(I,g)},Q=I=>{var g=Te(),u=fe(g);Xe(u,r,"default",{},null),i(I,g)};y(X,I=>{a(m)?I(ne):I(Q,!1)})}i(x,H),Le()}Ye(["click"]);var yr=c("<aside><!></aside>"),wr=c('<div class="flex h-screen bg-background text-foreground svelte-bm75gl"><!> <main class="flex-1 flex flex-col min-w-0 svelte-bm75gl"><div class="flex-1 overflow-hidden"><!></div></main> <!></div>');function kr(x,r){Ne(r,!0);let d=p(r,"session",3,null),C=p(r,"profile",3,null),A=p(r,"agentType",3,"research"),m=p(r,"navigationItems",19,()=>[]),w=p(r,"brandName",3,"Robynn AI"),E=p(r,"searchPlaceholder",3,"Search"),H=p(r,"leftSidebarCollapsed",15,!1),X=p(r,"rightSidebarCollapsed",15,!1),ne=p(r,"showRightSidebar",3,!0),Q=p(r,"rightSidebarWidth",3,"w-80"),I=ie(0),g=Oe(()=>a(I)<768);qe(()=>{a(g)&&(H(!0),X(!0))}),Je(()=>{S(I,window.innerWidth,!0)}),hr(x,{children:(u,k)=>{var T=wr(),O=t(T);cr(O,{get isMobile(){return a(g)},get session(){return d()},get profile(){return C()},get agentType(){return A()},get navigationItems(){return m()},get brandName(){return w()},get searchPlaceholder(){return E()},get collapsed(){return H()},set collapsed(j){H(j)}});var _=o(O,2),D=t(_),L=t(D);ut(L,()=>r.children??lt),e(D),e(_);var U=o(_,2);{var Z=j=>{var te=yr();let B;var q=t(te);ut(q,()=>r.rightSidebar??lt),e(te),J(ae=>B=ce(te,1,`bg-sidebar border-l border-sidebar-border flex flex-col transition-all duration-300 ease-in-out ${Q()??""}`,"svelte-bm75gl",B,ae),[()=>({"w-0":X(),"overflow-hidden":X()})]),i(j,te)};y(U,j=>{ne()&&j(Z)})}e(T),i(u,T)},$$slots:{default:!0}}),mt("innerWidth",u=>S(I,u,!0)),Le()}var Sr=c('<div class="flex items-start space-x-4 animate-pulse svelte-5pvuvw"><div class="w-10 h-10 bg-muted rounded-full flex-shrink-0 svelte-5pvuvw"></div> <div class="flex-1 space-y-3 svelte-5pvuvw"><div class="flex items-center gap-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-16 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-20 svelte-5pvuvw"></div></div> <div class="space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-4/5 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div></div></div></div>'),Cr=c('<div class="flex items-center space-x-3 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-20 svelte-5pvuvw"></div></div>'),Ar=c('<div class="animate-pulse space-y-4 svelte-5pvuvw"><div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-6 h-6 bg-muted rounded svelte-5pvuvw"></div> <div class="h-5 bg-muted rounded w-24 svelte-5pvuvw"></div></div> <div class="h-10 bg-muted rounded-lg svelte-5pvuvw"></div> <!></div>'),Tr=c('<div class="flex items-center space-x-2 svelte-5pvuvw"><div class="w-4 h-4 bg-muted rounded-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-40 svelte-5pvuvw"></div></div>'),Dr=c('<div class="animate-pulse space-y-3 svelte-5pvuvw"><div class="flex items-center justify-between svelte-5pvuvw"><div class="h-4 bg-muted rounded w-32 svelte-5pvuvw"></div> <div class="h-4 bg-muted rounded w-12 svelte-5pvuvw"></div></div> <div class="h-2 bg-muted rounded-full w-full svelte-5pvuvw"></div> <div class="space-y-2 svelte-5pvuvw"></div></div>'),Er=c('<div class="animate-pulse p-4 border border-border rounded-lg space-y-2 svelte-5pvuvw"><div class="h-4 bg-muted rounded w-3/4 svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-full svelte-5pvuvw"></div> <div class="h-3 bg-muted rounded w-2/3 svelte-5pvuvw"></div></div>'),Ir=c("<!> <!> <!> <!>",1);function Mr(x,r){let d=p(r,"type",3,"message"),C=p(r,"count",3,1);var A=Ir(),m=fe(A);{var w=g=>{var u=Te(),k=fe(u);Se(k,17,()=>Array(C()),Re,(T,O)=>{var _=Sr();i(T,_)}),i(g,u)};y(m,g=>{d()==="message"&&g(w)})}var E=o(m,2);{var H=g=>{var u=Ar(),k=o(t(u),4);Se(k,16,()=>Array(5),Re,(T,O)=>{var _=Cr();i(T,_)}),e(u),i(g,u)};y(E,g=>{d()==="sidebar"&&g(H)})}var X=o(E,2);{var ne=g=>{var u=Dr(),k=o(t(u),4);Se(k,20,()=>Array(3),Re,(T,O)=>{var _=Tr();i(T,_)}),e(k),e(u),i(g,u)};y(X,g=>{d()==="progress"&&g(ne)})}var Q=o(X,2);{var I=g=>{var u=Te(),k=fe(u);Se(k,17,()=>Array(C()),Re,(T,O)=>{var _=Er();i(T,_)}),i(g,u)};y(Q,g=>{d()==="project"&&g(I)})}i(x,A)}var zr=c('<div><!> <div class="flex-1 min-w-0"><p class="font-medium text-sm truncate"> </p> <p class="text-xs opacity-80 truncate"> </p></div></div>'),Pr=c('<div class="fixed top-4 right-4 w-80 bg-background border-2 border-border rounded-lg shadow-lg z-50 p-4 animate-slide-in svelte-lmyhl8" style="box-shadow: var(--shadow-lg);"><div class="flex items-center justify-between mb-4"><h3 class="font-semibold text-foreground flex items-center gap-2"><!> </h3> <span class="text-sm font-medium text-muted-foreground"> </span></div> <div class="mb-4"><div class="w-full bg-muted rounded-full h-2 overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"></div></div></div> <div class="space-y-2 max-h-64 overflow-y-auto"></div></div>');function jr(x,r){Ne(r,!0);let d=p(r,"steps",19,()=>[]),C=p(r,"currentProgress",3,0),A=p(r,"isVisible",3,!1),m=p(r,"title",3,"Progress"),w,E=ie(0);qe(()=>{A()&&C()!==a(E)&&H(C())});function H(u){const k=a(E),T=800,O=Date.now();function _(){const D=Date.now()-O,L=Math.min(D/T,1),U=1-Math.pow(1-L,3);S(E,k+(u-k)*U),L<1?requestAnimationFrame(_):S(E,u,!0)}requestAnimationFrame(_)}function X(u,k){return u.status==="completed"?Wt:u.status==="pending"&&k===d().findIndex(T=>T.status==="pending")?wt:Vt}function ne(u,k){const T="flex items-center space-x-3 p-3 rounded-lg transition-all duration-500";return u.status==="completed"?`${T} bg-green-50 border border-green-200 text-green-800`:u.status==="pending"&&k===d().findIndex(O=>O.status==="pending")?`${T} bg-blue-50 border border-blue-200 text-blue-800 animate-pulse`:`${T} bg-muted/30 border border-border text-muted-foreground`}var Q=Te(),I=fe(Q);{var g=u=>{var k=Pr(),T=t(k),O=t(T),_=t(O);Bt(_,{class:"w-4 h-4"});var D=o(_);e(O);var L=o(O,2),U=t(L);e(L),e(T);var Z=o(T,2),j=t(Z),te=t(j);e(j),e(Z);var B=o(Z,2);Se(B,23,d,q=>q.id,(q,ae,oe)=>{var $=zr(),de=t($);{let _e=Oe(()=>a(ae).status==="pending"&&a(oe)===d().findIndex(xe=>xe.status==="pending")?"animate-spin":"");Qe(de,()=>X(a(ae),a(oe)),(xe,De)=>{De(xe,{get class(){return`w-4 h-4 flex-shrink-0 ${a(_e)??""}`}})})}var v=o(de,2),R=t(v),se=t(R,!0);e(R);var le=o(R,2),me=t(le,!0);e(le),e(v),e($),J(_e=>{ce($,1,_e,"svelte-lmyhl8"),G(se,a(ae).title),G(me,a(ae).description)},[()=>Lt(ne(a(ae),a(oe)))]),i(q,$)}),e(B),e(k),bt(k,q=>w=q,()=>w),J(q=>{G(D,` ${m()??""}`),G(U,`${q??""}%`),Kt(te,`width: ${a(E)??""}%`)},[()=>Math.round(a(E))]),i(u,k)};y(I,u=>{A()&&d().length>0&&u(g)})}i(x,Q),Le()}function Rr(x,r){x.key==="Enter"&&!x.shiftKey&&(x.preventDefault(),r())}var Or=(x,r,d)=>r(a(d)),Nr=c('<button class="group p-6 bg-card border border-border rounded-xl hover:border-primary/50 transition-all duration-300 text-left hover:shadow-lg"><div class="flex items-start gap-4"><div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><!></div> <div class="flex-1"><h3 class="font-semibold text-foreground mb-2 group-hover:text-primary transition-colors"> </h3> <p class="text-sm text-muted-foreground mb-3"> </p> <div class="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 font-mono"> </div></div></div></button>'),Lr=c(`<div class="max-w-4xl mx-auto"><div class="text-center mb-12"><div class="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6"><!></div> <h1 class="text-3xl font-bold text-foreground mb-4">SEO Strategy Assistant</h1> <p class="text-lg text-muted-foreground max-w-2xl mx-auto">Discover high-value keywords, analyze competitor gaps, and build
            content clusters that drive organic traffic to your website.</p></div> <div class="grid md:grid-cols-2 gap-6 mb-12"></div></div>`),Kr=(x,r,d)=>r(a(d).content),Fr=(x,r,d)=>r(a(d).content,`seo-analysis-${Date.now()}.txt`),Gr=c('<button class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"><!> Download</button>'),Ur=c('<div class="flex items-center gap-2 mt-3"><button class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"><!> Copy</button> <!></div>'),Br=c('<div class="flex gap-4"><div><!></div> <div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-2"><span class="font-medium text-sm text-foreground"> </span> <span class="text-xs text-muted-foreground"> </span></div> <div class="prose prose-sm max-w-none text-foreground"><div class="whitespace-pre-wrap"> </div></div> <!></div></div>'),Wr=c('<div class="max-w-4xl mx-auto space-y-6"><!> <!></div>'),Vr=c('<div class="bg-muted/30 rounded-lg p-4"><div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2 text-foreground">Target Audience</label> <input placeholder="e.g., Small business owners" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-bold mb-2 text-foreground">Region Focus</label> <input placeholder="e.g., United States" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-bold mb-2 text-foreground">Funnel Stage</label> <select class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"><option>Awareness</option><option>Consideration</option><option>Decision</option></select></div></div></div>'),Hr=(x,r)=>S(r,!a(r)),qr=c('<!> <main class="flex-1 flex flex-col bg-background"><header class="flex items-center justify-between p-6 border-b border-border bg-background"><div class="flex items-center"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-2"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">SEO Agent</span></nav></div></header> <div class="flex-1 overflow-y-auto p-6"><!></div> <div class="border-t border-border p-6"><div class="max-w-4xl mx-auto space-y-4"><!> <div class="flex gap-3"><button><!></button> <div class="flex-1 relative"><textarea class="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-primary/50" rows="2"></textarea></div> <button class="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"><!> Analyze</button></div></div></div></main>',1);function Yr(x,r){Ne(r,!0);const[d,C]=pt(),A=()=>_t(m(),"$messages",d);let m=p(r,"messages",19,()=>gt([])),w=p(r,"isLoading",3,!1),E=p(r,"onSendMessage",3,null),H=p(r,"progressSteps",19,()=>[]),X=p(r,"currentProgress",3,0),ne=p(r,"envSlug",3,""),Q=ie(""),I="summary",g=ie(0),u=ie(""),k=ie(!1),T=ie(""),O=ie(""),_=ie("awareness");const D=["Find long-tail keywords for organic skincare...","Analyze competitor keywords for project management tools...","Discover niche keywords for sustainable fashion brands...","Research local SEO keywords for coffee shops in Seattle..."],L=[{icon:He,title:"Niche Keyword Discovery",description:"Find untapped long-tail keywords in your specific niche",prompt:"Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"},{icon:dt,title:"Competitor Gap Analysis",description:"Identify keyword opportunities your competitors are missing",prompt:"Analyze keyword gaps between my website and [Competitor Domain] in the [Industry] space"},{icon:Ht,title:"Content Cluster Mapping",description:"Build topic clusters for better content organization",prompt:"Create a content cluster strategy for [Main Topic] with supporting subtopics and internal linking"},{icon:tt,title:"Local SEO Research",description:"Optimize for location-based search queries",prompt:"Find local SEO opportunities for a [Business Type] in [City, State]"}];Je(()=>{const s=setInterval(()=>{S(g,(a(g)+1)%D.length),S(u,D[a(g)],!0)},3e3);return S(u,D[0],!0),()=>clearInterval(s)});function U(){if(!a(Q).trim()||w()||!E())return;const s=a(k)?{targetAudience:a(T),regionFocus:a(O),funnelStage:a(_),outputFormat:I}:void 0;E()(a(Q).trim(),s),S(Q,"")}function Z(s){S(Q,s.prompt,!0)}function j(s){return s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}function te(s){navigator.clipboard.writeText(s)}function B(s,l){const h=new Blob([s],{type:"text/plain"}),M=URL.createObjectURL(h),K=document.createElement("a");K.href=M,K.download=l,K.click(),URL.revokeObjectURL(M)}var q=qr(),ae=fe(q);{var oe=s=>{jr(s,{get steps(){return H()},get currentProgress(){return X()},get isVisible(){return w()},title:"SEO Analysis Progress"})};y(ae,s=>{H().length>0&&s(oe)})}var $=o(ae,2),de=t($),v=t(de),R=t(v),se=t(R),le=o(se,2);kt(le,{class:"w-4 h-4"}),ke(2),e(R),e(v),e(de);var me=o(de,2),_e=t(me);{var xe=s=>{var l=Lr(),h=t(l),M=t(h),K=t(M);tt(K,{class:"w-8 h-8 text-primary"}),e(M),ke(4),e(h);var z=o(h,2);Se(z,21,()=>L,b=>b.title,(b,P)=>{var F=Nr();F.__click=[Or,Z,P];var re=t(F),N=t(re),V=t(N);Qe(V,()=>a(P).icon,(Ke,Fe)=>{Fe(Ke,{class:"w-6 h-6 text-primary"})}),e(N);var Y=o(N,2),ue=t(Y),ye=t(ue,!0);e(ue);var Ce=o(ue,2),Ae=t(Ce,!0);e(Ce);var Me=o(Ce,2),ze=t(Me,!0);e(Me),e(Y),e(re),e(F),J(()=>{G(ye,a(P).title),G(Ae,a(P).description),G(ze,a(P).prompt)}),i(b,F)}),e(z),e(l),i(s,l)},De=s=>{var l=Wr(),h=t(l);Se(h,1,A,z=>z.id,(z,b)=>{var P=Br(),F=t(P),re=t(F);{var N=we=>{qt(we,{class:"w-4 h-4 text-primary-foreground"})},V=we=>{Yt(we,{class:"w-4 h-4 text-muted-foreground"})};y(re,we=>{a(b).role==="user"?we(N):we(V,!1)})}e(F);var Y=o(F,2),ue=t(Y),ye=t(ue),Ce=t(ye,!0);e(ye);var Ae=o(ye,2),Me=t(Ae,!0);e(Ae),e(ue);var ze=o(ue,2),Ke=t(ze),Fe=t(Ke,!0);e(Ke),e(ze);var At=o(ze,2);{var Tt=we=>{var Ze=Ur(),We=t(Ze);We.__click=[Kr,te,b];var Dt=t(We);Jt(Dt,{class:"w-3 h-3"}),ke(),e(We);var Et=o(We,2);{var It=$e=>{var Ve=Gr();Ve.__click=[Fr,B,b];var Mt=t(Ve);Qt(Mt,{class:"w-3 h-3"}),ke(),e(Ve),i($e,Ve)};y(Et,$e=>{a(b).isReport&&$e(It)})}e(Ze),i(we,Ze)};y(At,we=>{a(b).role==="assistant"&&we(Tt)})}e(Y),e(P),J(we=>{ce(F,1,`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${a(b).role==="user"?"bg-primary":"bg-muted"}`),G(Ce,a(b).role==="user"?"You":"SEO Assistant"),G(Me,we),G(Fe,a(b).content)},[()=>j(a(b).timestamp)]),at(3,P,()=>ot,()=>({duration:300})),i(z,P)});var M=o(h,2);{var K=z=>{Mr(z,{type:"message",count:1})};y(M,z=>{w()&&z(K)})}e(l),i(s,l)};y(_e,s=>{A().length===0?s(xe):s(De,!1)})}e(me);var Ee=o(me,2),he=t(Ee),Pe=t(he);{var Ie=s=>{var l=Vr(),h=t(l),M=t(h),K=o(t(M),2);ct(K),e(M);var z=o(M,2),b=o(t(z),2);ct(b),e(z);var P=o(z,2),F=o(t(P),2),re=t(F);re.value=re.__value="awareness";var N=o(re);N.value=N.__value="consideration";var V=o(N);V.value=V.__value="decision",e(F),e(P),e(h),e(l),et(K,()=>a(T),Y=>S(T,Y)),et(b,()=>a(O),Y=>S(O,Y)),Ft(F,()=>a(_),Y=>S(_,Y)),at(3,l,()=>ot),i(s,l)};y(Pe,s=>{a(k)&&s(Ie)})}var je=o(Pe,2),W=t(je);W.__click=[Hr,k];var ge=t(W);Xt(ge,{class:"w-4 h-4"}),e(W);var be=o(W,2),ee=t(be);Pt(ee),ee.__keydown=[Rr,U],e(be);var ve=o(be,2);ve.__click=U;var pe=t(ve);{var n=s=>{wt(s,{class:"w-4 h-4 animate-spin"})},f=s=>{tt(s,{class:"w-4 h-4"})};y(pe,s=>{w()?s(n):s(f,!1)})}ke(),e(ve),e(je),e(he),e(Ee),e($),J(s=>{Be(se,"href",`/dashboard/${ne()??""}`),ce(W,1,`px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors ${a(k)?"bg-muted":""}`),Be(ee,"placeholder",a(u)),ee.disabled=w(),ve.disabled=s},[()=>!a(Q).trim()||w()]),et(ee,()=>a(Q),s=>S(Q,s)),i(x,q),Le(),C()}Ye(["click","keydown"]);function Jr(x,r){r(!r())}function Qr(x,r){r()&&r()({seedKeywords:["example","keywords"],filters:{industry:"",location:"United States",volumeRange:{min:100,max:1e4},maxDifficulty:50}})}function Xr(x,r){r()&&r()({yourDomain:"",competitors:[""],location:"United States",minVolume:100,maxDifficulty:50,gapType:"missing"})}function Zr(x,r){r()&&r()({mainTopic:"",subtopics:[],contentTypes:["blog","landing-page"]})}var $r=c('<h2 class="text-lg font-semibold text-foreground">SEO Tools</h2> <button class="text-muted-foreground hover:text-foreground transition-colors"><!></button>',1),ea=c('<div class="flex flex-col items-center w-full"><button class="p-2 hover:bg-accent rounded-lg mb-2" title="SEO Tools"><!></button> <span class="text-xs text-muted-foreground">Tools</span></div>'),ta=(x,r)=>r("tools"),ra=(x,r,d)=>r(a(d).id),aa=c('<button><div class="flex items-start gap-3"><div><!></div> <div class="flex-1 min-w-0"><h3 class="font-medium text-sm text-foreground mb-1"> </h3> <p class="text-xs text-muted-foreground"> </p></div></div></button>'),oa=c('<div class="space-y-2"></div>'),sa=c('<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground"> </div> <div class="text-muted-foreground"> </div></div>'),ia=c('<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2"> </h4> <div class="space-y-2 max-h-40 overflow-y-auto"></div></div>'),na=c('<div class="space-y-4"><h3 class="font-medium text-foreground">Niche Discovery</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Seed Keywords</label> <textarea placeholder="organic skincare, natural beauty" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground resize-none" rows="2"></textarea></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Industry</label> <input type="text" placeholder="e.g., Beauty &amp; Cosmetics" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"><!></button></div> <!></div>'),da=c('<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground"> </div> <div class="text-muted-foreground"> </div></div>'),la=c('<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2"> </h4> <div class="space-y-2 max-h-40 overflow-y-auto"></div></div>'),va=c('<div class="space-y-4"><h3 class="font-medium text-sidebar-foreground">Gap Analysis</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Your Domain</label> <input type="text" placeholder="yourdomain.com" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Competitor Domain</label> <input type="text" placeholder="competitor.com" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"><!></button></div> <!></div>'),ua=c('<div class="p-2 bg-muted/30 rounded text-xs"><div class="font-medium text-foreground"> </div> <div class="text-muted-foreground"> </div></div>'),ca=c('<div class="mt-4 pt-4 border-t border-border"><h4 class="text-xs font-medium text-muted-foreground mb-2">Cluster Strategy</h4> <div class="space-y-2 max-h-40 overflow-y-auto"></div></div>'),fa=c('<div class="space-y-4"><h3 class="font-medium text-sidebar-foreground">Content Clusters</h3> <div class="space-y-3"><div><label class="block text-xs font-medium text-muted-foreground mb-1">Main Topic</label> <input type="text" placeholder="e.g., Digital Marketing" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <div><label class="block text-xs font-medium text-muted-foreground mb-1">Target Audience</label> <input type="text" placeholder="e.g., Small business owners" class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"/></div> <button class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"><!></button></div> <!></div>'),ma=c('<div class="space-y-4"><div><button class="flex items-center justify-between w-full text-left mb-3"><span class="font-medium text-foreground">Analysis Tools</span> <!></button> <!></div> <div class="border-t border-border pt-4"><!></div> <div class="border-t border-sidebar-border p-4"><h3 class="font-medium text-sidebar-foreground mb-3">Quick Actions</h3> <div class="space-y-2"><button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Export Keywords</button> <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Save Analysis</button> <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">Share Report</button></div></div></div>'),ga=c('<aside><button><!></button> <div><div class="flex items-center justify-between mb-6"><!></div> <!></div></aside>');function ba(x,r){Ne(r,!0);let d=p(r,"collapsed",15,!1),C=p(r,"isMobile",3,!1),A=p(r,"activeTab",15,"niche"),m=p(r,"onNicheDiscovery",3,null),w=p(r,"onGapAnalysis",3,null),E=p(r,"onContentCluster",3,null),H=p(r,"isLoading",3,!1),X=p(r,"nicheKeywords",19,()=>[]),ne=p(r,"gapKeywords",19,()=>[]),Q=p(r,"clusterData",19,()=>[]),I=ie(Ue(new Set(["tools"])));const g=[{id:"niche",title:"Niche Discovery",description:"Find untapped long-tail keywords",icon:St,color:"text-blue-500",bgColor:"bg-blue-50"},{id:"gap",title:"Gap Analysis",description:"Identify competitor keyword gaps",icon:dt,color:"text-green-500",bgColor:"bg-green-50"},{id:"cluster",title:"Content Clusters",description:"Build topic cluster strategies",icon:Ct,color:"text-purple-500",bgColor:"bg-purple-50"}];function u(v){a(I).has(v)?a(I).delete(v):a(I).add(v),S(I,new Set(a(I)),!0)}function k(v){A(v)}var T=ga();let O;var _=t(T);_.__click=[Jr,d];let D;var L=t(_);{var U=v=>{xt(v,{class:"w-3 h-3"})},Z=v=>{ht(v,{class:"w-3 h-3"})};y(L,v=>{d()?v(U):v(Z,!1)})}e(_);var j=o(_,2);let te;var B=t(j),q=t(B);{var ae=v=>{var R=$r(),se=o(fe(R),2),le=t(se);rt(le,{class:"w-5 h-5"}),e(se),i(v,R)},oe=v=>{var R=ea(),se=t(R),le=t(se);rt(le,{class:"w-5 h-5"}),e(se),ke(2),e(R),i(v,R)};y(q,v=>{d()?v(oe,!1):v(ae)})}e(B);var $=o(B,2);{var de=v=>{var R=ma(),se=t(R),le=t(se);le.__click=[ta,u];var me=o(t(le),2);{var _e=W=>{yt(W,{class:"w-4 h-4 text-muted-foreground"})},xe=W=>{kt(W,{class:"w-4 h-4 text-muted-foreground"})};y(me,W=>{a(I).has("tools")?W(_e):W(xe,!1)})}e(le);var De=o(le,2);{var Ee=W=>{var ge=oa();Se(ge,21,()=>g,be=>be.id,(be,ee)=>{var ve=aa();ve.__click=[ra,k,ee];var pe=t(ve),n=t(pe),f=t(n);Qe(f,()=>a(ee).icon,(z,b)=>{b(z,{get class(){return`w-4 h-4 ${a(ee).color??""}`}})}),e(n);var s=o(n,2),l=t(s),h=t(l,!0);e(l);var M=o(l,2),K=t(M,!0);e(M),e(s),e(pe),e(ve),J(()=>{ce(ve,1,`w-full p-3 rounded-lg border transition-all text-left ${A()===a(ee).id?"border-primary bg-primary/5":"border-border hover:border-primary/50 hover:bg-muted/50"}`),ce(n,1,`w-8 h-8 rounded-lg ${a(ee).bgColor??""} flex items-center justify-center`),G(h,a(ee).title),G(K,a(ee).description)}),i(be,ve)}),e(ge),at(3,ge,()=>ot),i(W,ge)};y(De,W=>{a(I).has("tools")&&W(Ee)})}e(se);var he=o(se,2),Pe=t(he);{var Ie=W=>{var ge=na(),be=o(t(ge),2),ee=o(t(be),4);ee.__click=[Qr,m];var ve=t(ee);{var pe=l=>{var h=Ge("Analyzing...");i(l,h)},n=l=>{var h=Ge("Discover Keywords");i(l,h)};y(ve,l=>{H()?l(pe):l(n,!1)})}e(ee),e(be);var f=o(be,2);{var s=l=>{var h=ia(),M=t(h),K=t(M);e(M);var z=o(M,2);Se(z,21,()=>X().slice(0,5),Re,(b,P)=>{var F=sa(),re=t(F),N=t(re,!0);e(re);var V=o(re,2),Y=t(V);e(V),e(F),J(()=>{G(N,a(P).keyword),G(Y,`Vol: ${a(P).search_volume??""} | Diff: ${a(P).difficulty??""}`)}),i(b,F)}),e(z),e(h),J(()=>G(K,`Found ${X().length??""} Keywords`)),i(l,h)};y(f,l=>{X().length>0&&l(s)})}e(ge),J(()=>ee.disabled=H()),i(W,ge)},je=W=>{var ge=Te(),be=fe(ge);{var ee=pe=>{var n=va(),f=o(t(n),2),s=o(t(f),4);s.__click=[Xr,w];var l=t(s);{var h=b=>{var P=Ge("Analyzing...");i(b,P)},M=b=>{var P=Ge("Find Gaps");i(b,P)};y(l,b=>{H()?b(h):b(M,!1)})}e(s),e(f);var K=o(f,2);{var z=b=>{var P=la(),F=t(P),re=t(F);e(F);var N=o(F,2);Se(N,21,()=>ne().slice(0,5),Re,(V,Y)=>{var ue=da(),ye=t(ue),Ce=t(ye,!0);e(ye);var Ae=o(ye,2),Me=t(Ae);e(Ae),e(ue),J(()=>{G(Ce,a(Y).keyword),G(Me,`Gap: ${a(Y).gap_type??""} | Vol: ${a(Y).search_volume??""}`)}),i(V,ue)}),e(N),e(P),J(()=>G(re,`Found ${ne().length??""} Opportunities`)),i(b,P)};y(K,b=>{ne().length>0&&b(z)})}e(n),J(()=>s.disabled=H()),i(pe,n)},ve=pe=>{var n=Te(),f=fe(n);{var s=l=>{var h=fa(),M=o(t(h),2),K=o(t(M),4);K.__click=[Zr,E];var z=t(K);{var b=N=>{var V=Ge("Creating...");i(N,V)},P=N=>{var V=Ge("Build Cluster");i(N,V)};y(z,N=>{H()?N(b):N(P,!1)})}e(K),e(M);var F=o(M,2);{var re=N=>{var V=ca(),Y=o(t(V),2);Se(Y,21,()=>Q().slice(0,3),Re,(ue,ye)=>{var Ce=ua(),Ae=t(Ce),Me=t(Ae,!0);e(Ae);var ze=o(Ae,2),Ke=t(ze);e(ze),e(Ce),J(()=>{var Fe;G(Me,a(ye).topic),G(Ke,`${(((Fe=a(ye).subtopics)==null?void 0:Fe.length)||0)??""} subtopics`)}),i(ue,Ce)}),e(Y),e(V),i(N,V)};y(F,N=>{Q().length>0&&N(re)})}e(h),J(()=>K.disabled=H()),i(l,h)};y(f,l=>{A()==="cluster"&&l(s)},!0)}i(pe,n)};y(be,pe=>{A()==="gap"?pe(ee):pe(ve,!1)},!0)}i(W,ge)};y(Pe,W=>{A()==="niche"?W(Ie):W(je,!1)})}e(he),ke(2),e(R),i(v,R)};y($,v=>{d()||v(de)})}e(j),e(T),J((v,R,se)=>{O=ce(T,1,"bg-background border-l border-border overflow-y-auto transition-all duration-300 ease-in-out relative",null,O,v),D=ce(_,1,"absolute -left-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors",null,D,R),te=ce(j,1,"p-6",null,te,se)},[()=>({"w-80":!d(),"w-16":d()&&!C(),"w-0":d()&&C(),"overflow-hidden":d()&&C()}),()=>({hidden:C()&&d()}),()=>({hidden:d()&&C()})]),i(x,T),Le()}Ye(["click"]);var pa=c('<meta name="description" content="AI-powered SEO strategy and keyword research assistant"/>');function eo(x,r){Ne(r,!0);const d=gt([]);let C=ie(!1),A=ie(Ue([])),m=ie(0),w=Ue([]),E=Ue([]),H=Ue([]),X=ie(!1),ne=ie("niche"),Q=ie(0),I=Oe(()=>a(Q)<768);qe(()=>{a(I)&&S(X,!0)});const g=[{id:"chat",label:"Chat",icon:He,active:!0},{id:"niche",label:"Niche Discovery",icon:St},{id:"gap",label:"Gap Analysis",icon:dt},{id:"cluster",label:"Content Clusters",icon:Ct}];function u(){return Math.random().toString(36).substr(2,9)}async function k(D,L){var U,Z;if(!(!D.trim()||a(C))){S(C,!0),S(A,[{id:1,title:"Industry Research",description:"Analyzing your business niche...",status:"pending"},{id:2,title:"Keyword Discovery",description:"Finding relevant keywords...",status:"pending"},{id:3,title:"Volume Analysis",description:"Checking search volumes...",status:"pending"},{id:4,title:"Competition Analysis",description:"Analyzing keyword difficulty...",status:"pending"},{id:5,title:"Report Generation",description:"Creating your SEO strategy...",status:"pending"}],!0),S(m,0),d.update(j=>[...j,{id:u(),role:"user",content:D,timestamp:new Date}]);try{const j=await fetch(`/dashboard/${(U=r.data.environment)==null?void 0:U.slug}/agent-seo?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:D})});if(!j.ok)throw new Error(`HTTP error! status: ${j.status}`);const te=(Z=j.body)==null?void 0:Z.getReader();if(!te)throw new Error("No response body");let B={id:u(),role:"assistant",content:"",timestamp:new Date,isReport:!0};for(d.update(q=>[...q,B]);;){const{done:q,value:ae}=await te.read();if(q)break;const $=new TextDecoder().decode(ae).split(`
`);for(const de of $)if(de.startsWith("data: "))try{const v=JSON.parse(de.slice(6));v.type==="final_response"?(B.content=v.response,d.update(R=>[...R.slice(0,-1),{...B}])):v.type==="error"?(console.error("Agent API Error:",v.error,v.details),B.content=`I apologize, but I encountered an error: ${v.error}. Please check your API configuration and try again.`,d.update(R=>[...R.slice(0,-1),{...B}])):v.content&&(B.content+=v.content,d.update(R=>[...R.slice(0,-1),{...B}])),v.progress&&(S(m,v.progress,!0),v.step&&S(A,a(A).map(R=>R.id===v.step?{...R,status:"active"}:R),!0))}catch(v){console.error("Error parsing SSE data:",v)}}}catch(j){console.error("Error sending message:",j),d.update(te=>[...te,{id:u(),role:"assistant",content:"I apologize, but I encountered an error while processing your request. Please try again.",timestamp:new Date}])}finally{S(C,!1),S(A,[],!0)}}}function T(D){console.log("Niche discovery:",D)}function O(D){console.log("Gap analysis:",D)}function _(D){console.log("Content cluster:",D)}jt(D=>{var L=pa();zt.title="SEO Agent - Robynn AI",i(D,L)}),kr(x,{get session(){return r.data.session},get profile(){return r.data.profile},agentType:"seo",get navigationItems(){return g},brandName:"Robynn AI",searchPlaceholder:"Search SEO tools...",get rightSidebarCollapsed(){return a(X)},showRightSidebar:!0,rightSidebarWidth:"320px",rightSidebar:L=>{ba(L,{get isMobile(){return a(I)},onNicheDiscovery:T,onGapAnalysis:O,onContentCluster:_,get isLoading(){return a(C)},get nicheKeywords(){return w},get gapKeywords(){return E},get clusterData(){return H},get collapsed(){return a(X)},set collapsed(U){S(X,U,!0)},get activeTab(){return a(ne)},set activeTab(U){S(ne,U,!0)}})},children:(L,U)=>{{let Z=Oe(()=>{var j;return(j=r.data.environment)==null?void 0:j.slug});Yr(L,{get messages(){return d},get isLoading(){return a(C)},onSendMessage:k,get progressSteps(){return a(A)},get currentProgress(){return a(m)},get envSlug(){return a(Z)}})}},$$slots:{rightSidebar:!0,default:!0}}),mt("innerWidth",D=>S(Q,D,!0)),Le()}export{eo as component};
