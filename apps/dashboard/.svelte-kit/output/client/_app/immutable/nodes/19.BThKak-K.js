import"../chunks/CWj6FrbW.js";import{p as n,f as r,s as o,d as s,a as d,e as u,$ as f,i as h,k as p}from"../chunks/DDiqt3uM.js";import{h as c}from"../chunks/DWulv87v.js";import{S as g}from"../chunks/DF2MXVHf.js";import{p as b}from"../chunks/CCJOWbOV.js";var _=r('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function V(l,a){n(a,!0);let{profile:e}=a.data;var t=_();c(i=>{f.title="Edit Profile"});var m=o(s(t),2);{let i=p(()=>[{id:"full_name",label:"Name",initialValue:(e==null?void 0:e.full_name)??"",placeholder:"Your full name",maxlength:50},{id:"company_name",label:"Company Name",initialValue:(e==null?void 0:e.company_name)??"",maxlength:50},{id:"website",label:"Company Website",initialValue:(e==null?void 0:e.website)??"",maxlength:50}]);g(m,{get data(){return a.data.form},get schema(){return b},editable:!0,title:"Edit Profile",successTitle:"Saved Profile",formTarget:"/api?/updateProfile",get fields(){return h(i)}})}d(l,t),u()}export{V as component};
