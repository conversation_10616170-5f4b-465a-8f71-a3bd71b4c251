import"../chunks/CWj6FrbW.js";import"../chunks/DhRTwODG.js";import{W as at,U as Ke,S as Xe,p as ie,l as H,g as Ae,b as N,d as $,a as l,e as de,aT as j,i as n,m as Ce,j as we,f as M,c as T,r as I,ac as Ye,h as re,ak as De,s as D,$ as st,t as pe,n as oe,q as Q,aY as je}from"../chunks/DDiqt3uM.js";import{e as Ze,h as nt,s as Te}from"../chunks/DWulv87v.js";import{i as q}from"../chunks/2C89X9tI.js";import{e as lt,i as ot}from"../chunks/OiKQa7Wx.js";import{a as ae,s as it}from"../chunks/C36Ip9GY.js";import{P as dt}from"../chunks/DJGghuvL.js";import{W as ct}from"../chunks/QNe64B_9.js";import{s as V}from"../chunks/iCEqKm8o.js";import{a as se}from"../chunks/D-ywOz1J.js";import{b as ne}from"../chunks/Dqu9JXqq.js";import{i as ce}from"../chunks/B_FgA42l.js";import{l as W,p as x,s as He}from"../chunks/C-ZVHnwW.js";import{a as Pe,s as le}from"../chunks/B82PTGnX.js";import{o as ut,m as xe,f as vt,j as Ee,e as ft,a as Ge,i as Ue,s as gt,k as J,l as mt}from"../chunks/BGh_Dfnt.js";import{d as _t,w as et}from"../chunks/rjRVMZXi.js";import{t as ht,g as bt,o as pt,b as Qe,r as xt,a as yt}from"../chunks/CMLKS1ED.js";import{c as $t,a as At}from"../chunks/DaUg0vjO.js";import{c as ve}from"../chunks/Bf9nHHn7.js";import{b as Ct}from"../chunks/B9BVeOQN.js";import{C as wt}from"../chunks/3OymkobC.js";import{t as ye}from"../chunks/A4ulxp7Q.js";import{s as Pt}from"../chunks/DSm1r-pw.js";const{name:$e,selector:Je}=vt("accordion"),St={multiple:!1,disabled:!1,forceVisible:!1},kt=g=>{const e={...St,...g},h=ht(ut(e,"value","onValueChange","defaultValue")),b=bt(["root"]),{disabled:p,forceVisible:A}=h,C=e.value??et(e.defaultValue),i=pt(C,e==null?void 0:e.onValueChange),c=(t,r)=>r===void 0?!1:typeof r=="string"?r===t:r.includes(t),o=_t(i,t=>r=>c(r,t)),_=xe($e(),{returned:()=>({"data-melt-id":b.root})}),m=t=>typeof t=="string"?{value:t}:t,y=t=>typeof t=="number"?{level:t}:t,E=xe($e("item"),{stores:i,returned:t=>r=>{const{value:a,disabled:s}=m(r);return{"data-state":c(a,t)?"open":"closed","data-disabled":Ee(s)}}}),w=xe($e("trigger"),{stores:[i,p],returned:([t,r])=>a=>{const{value:s,disabled:u}=m(a);return{disabled:Ee(r||u),"aria-expanded":!!c(s,t),"aria-disabled":!!u,"data-disabled":Ee(u),"data-value":s,"data-state":c(s,t)?"open":"closed"}},action:t=>({destroy:ft(Ge(t,"click",()=>{const a=t.dataset.disabled==="true",s=t.dataset.value;a||!s||f(s)}),Ge(t,"keydown",a=>{if(![J.ARROW_DOWN,J.ARROW_UP,J.HOME,J.END].includes(a.key))return;if(a.preventDefault(),a.key===J.SPACE||a.key===J.ENTER){const z=t.dataset.disabled==="true",U=t.dataset.value;if(z||!U)return;f(U);return}const s=a.target,u=mt(b.root);if(!u||!Ue(s))return;const d=Array.from(u.querySelectorAll(Je("trigger"))).filter(z=>Ue(z)?z.dataset.disabled!=="true":!1);if(!d.length)return;const O=d.indexOf(s);a.key===J.ARROW_DOWN&&d[(O+1)%d.length].focus(),a.key===J.ARROW_UP&&d[(O-1+d.length)%d.length].focus(),a.key===J.HOME&&d[0].focus(),a.key===J.END&&d[d.length-1].focus()}))})}),R=xe($e("content"),{stores:[i,p,A],returned:([t,r,a])=>s=>{const{value:u}=m(s),v=c(u,t)||a;return{"data-state":v?"open":"closed","data-disabled":Ee(r),"data-value":u,hidden:v?void 0:!0,style:gt({display:v?void 0:"none"})}},action:t=>{at().then(()=>{const r=Qe(),a=Qe(),s=document.querySelector(`${Je("trigger")}, [data-value="${t.dataset.value}"]`);Ue(s)&&(t.id=r,s.setAttribute("aria-controls",r),s.id=a)})}}),S=xe($e("heading"),{returned:()=>t=>{const{level:r}=y(t);return{role:"heading","aria-level":r,"data-heading-level":r}}});function f(t){i.update(r=>r===void 0?e.multiple?[t]:t:Array.isArray(r)?r.includes(t)?r.filter(a=>a!==t):(r.push(t),r):r===t?void 0:t)}return{ids:b,elements:{root:_,item:E,trigger:w,content:R,heading:S},states:{value:i},helpers:{isSelected:o},options:h}};function Re(){return{NAME:"accordion",ITEM_NAME:"accordion-item",PARTS:["root","content","header","item","trigger"]}}function It(g){const e=kt(xt(g)),{NAME:h,PARTS:b}=Re(),p=$t(h,b),A={...e,getAttrs:p,updateOption:yt(e.options)};return Ke(h,A),A}function Ve(){const{NAME:g}=Re();return Xe(g)}function Tt(g){const{ITEM_NAME:e}=Re(),h=et(g);return Ke(e,{propsStore:h}),{...Ve(),propsStore:h}}function tt(){const{ITEM_NAME:g}=Re();return Xe(g)}function Et(){const g=Ve(),{propsStore:e}=tt();return{...g,propsStore:e}}function Mt(){const g=Ve(),{propsStore:e}=tt();return{...g,props:e}}function Ot(g,e){return g.length!==e.length?!1:g.every((h,b)=>h===e[b])}var Nt=M("<div><!></div>");function jt(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["multiple","value","onValueChange","disabled","asChild","el"]);ie(e,!1);const[p,A]=Pe(),C=()=>le(w,"$root",p),i=Ce();let c=x(e,"multiple",8,!1),o=x(e,"value",28,()=>{}),_=x(e,"onValueChange",24,()=>{}),m=x(e,"disabled",8,!1),y=x(e,"asChild",8,!1),E=x(e,"el",28,()=>{});const{elements:{root:w},states:{value:R},updateOption:S,getAttrs:f}=It({multiple:c(),disabled:m(),defaultValue:o(),onValueChange:({next:v})=>{var d,O;return Array.isArray(v)?((!Array.isArray(o())||!Ot(o(),v))&&((d=_())==null||d(v),o(v)),v):(o()!==v&&((O=_())==null||O(v),o(v)),v)}}),t=f("root");H(()=>j(o()),()=>{o()!==void 0&&R.set(Array.isArray(o())?[...o()]:o())}),H(()=>j(c()),()=>{S("multiple",c())}),H(()=>j(m()),()=>{S("disabled",m())}),H(()=>C(),()=>{we(i,C())}),H(()=>n(i),()=>{Object.assign(n(i),t)}),Ae(),ce();var r=N(),a=$(r);{var s=v=>{var d=N(),O=$(d);V(O,e,"default",{get builder(){return n(i)}},null),l(v,d)},u=v=>{var d=Nt();ae(d,()=>({...n(i),...b}));var O=T(d);V(O,e,"default",{get builder(){return n(i)}},null),I(d),ne(d,z=>E(z),()=>E()),se(d,z=>{var U,k;return(k=(U=n(i)).action)==null?void 0:k.call(U,z)}),l(v,d)};q(a,v=>{y()?v(s):v(u,!1)})}l(g,r),de(),A()}var Rt=M("<div><!></div>");function Vt(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["value","disabled","asChild","el"]);ie(e,!1);const[p,A]=Pe(),C=()=>le(E,"$item",p),i=()=>le(w,"$propsStore",p),c=Ce();let o=x(e,"value",8),_=x(e,"disabled",24,()=>{}),m=x(e,"asChild",8,!1),y=x(e,"el",28,()=>{});const{elements:{item:E},propsStore:w,getAttrs:R}=Tt({value:o(),disabled:_()}),S=R("item");H(()=>(j(o()),j(_())),()=>{w.set({value:o(),disabled:_()})}),H(()=>(C(),i(),j(_())),()=>{we(c,C()({...i(),disabled:_()}))}),H(()=>n(c),()=>{Object.assign(n(c),S)}),Ae(),ce();var f=N(),t=$(f);{var r=s=>{var u=N(),v=$(u);V(v,e,"default",{get builder(){return n(c)}},null),l(s,u)},a=s=>{var u=Rt();ae(u,()=>({...n(c),...b}));var v=T(u);V(v,e,"default",{get builder(){return n(c)}},null),I(u),ne(u,d=>y(d),()=>y()),se(u,d=>{var O,z;return(z=(O=n(c)).action)==null?void 0:z.call(O,d)}),l(s,u)};q(t,s=>{m()?s(r):s(a,!1)})}l(g,f),de(),A()}var Wt=M("<div><!></div>");function zt(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["level","asChild","el"]);ie(e,!1);const[p,A]=Pe(),C=()=>le(m,"$header",p),i=Ce();let c=x(e,"level",8,3),o=x(e,"asChild",8,!1),_=x(e,"el",28,()=>{});const{elements:{heading:m},getAttrs:y}=Ve(),E=y("header");H(()=>(C(),j(c())),()=>{we(i,C()(c()))}),H(()=>n(i),()=>{Object.assign(n(i),E)}),Ae(),ce();var w=N(),R=$(w);{var S=t=>{var r=N(),a=$(r);V(a,e,"default",{get builder(){return n(i)}},null),l(t,r)},f=t=>{var r=Wt();ae(r,()=>({...n(i),...b}));var a=T(r);V(a,e,"default",{get builder(){return n(i)}},null),I(r),ne(r,s=>_(s),()=>_()),se(r,s=>{var u,v;return(v=(u=n(i)).action)==null?void 0:v.call(u,s)}),l(t,r)};q(R,t=>{o()?t(S):t(f,!1)})}l(g,w),de(),A()}var Ft=M("<button><!></button>");function qt(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["asChild","el"]);ie(e,!1);const[p,A]=Pe(),C=()=>le(m,"$trigger",p),i=()=>le(y,"$props",p),c=Ce();let o=x(e,"asChild",8,!1),_=x(e,"el",28,()=>{});const{elements:{trigger:m},props:y,getAttrs:E}=Mt(),w=At(),R=E("trigger");H(()=>(C(),i()),()=>{we(c,C()({...i()}))}),H(()=>n(c),()=>{Object.assign(n(c),R)}),Ae(),ce();var S=N(),f=$(S);{var t=a=>{var s=N(),u=$(s);V(u,e,"default",{get builder(){return n(c)}},null),l(a,s)},r=a=>{var s=Ft();ae(s,()=>({...n(c),type:"button",...b}));var u=T(s);V(u,e,"default",{get builder(){return n(c)}},null),I(s),ne(s,v=>_(v),()=>_()),se(s,v=>{var d,O;return(O=(d=n(c)).action)==null?void 0:O.call(d,v)}),Ye(()=>Ze("m-keydown",s,w)),Ye(()=>Ze("m-click",s,w)),l(a,s)};q(f,a=>{o()?a(t):a(r,!1)})}l(g,S),de(),A()}var Ut=M("<div><!></div>"),Dt=M("<div><!></div>"),Ht=M("<div><!></div>"),Bt=M("<div><!></div>"),Lt=M("<div><!></div>");function Yt(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]);ie(e,!1);const[p,A]=Pe(),C=()=>le(t,"$content",p),i=()=>le(a,"$propsStore",p),c=()=>le(r,"$isSelected",p),o=Ce();let _=x(e,"transition",24,()=>{}),m=x(e,"transitionConfig",24,()=>{}),y=x(e,"inTransition",24,()=>{}),E=x(e,"inTransitionConfig",24,()=>{}),w=x(e,"outTransition",24,()=>{}),R=x(e,"outTransitionConfig",24,()=>{}),S=x(e,"asChild",8,!1),f=x(e,"el",28,()=>{});const{elements:{content:t},helpers:{isSelected:r},propsStore:a,getAttrs:s}=Et(),u=s("content");H(()=>(C(),i()),()=>{we(o,C()({...i()}))}),H(()=>n(o),()=>{Object.assign(n(o),u)}),Ae(),ce();var v=N(),d=$(v);{var O=U=>{var k=N(),F=$(k);V(F,e,"default",{get builder(){return n(o)}},null),l(U,k)},z=U=>{var k=N(),F=$(k);{var fe=Y=>{var P=Ut();ae(P,()=>({...n(o),...b}));var B=T(P);V(B,e,"default",{get builder(){return n(o)}},null),I(P),ne(P,me=>f(me),()=>f()),se(P,me=>{var Se,K;return(K=(Se=n(o)).action)==null?void 0:K.call(Se,me)}),ye(3,P,_,m),l(Y,P)},ge=Y=>{var P=N(),B=$(P);{var me=K=>{var L=Dt();ae(L,()=>({...n(o),...b}));var We=T(L);V(We,e,"default",{get builder(){return n(o)}},null),I(L),ne(L,_e=>f(_e),()=>f()),se(L,_e=>{var ke,X;return(X=(ke=n(o)).action)==null?void 0:X.call(ke,_e)}),ye(1,L,y,E),ye(2,L,w,R),l(K,L)},Se=K=>{var L=N(),We=$(L);{var _e=X=>{var Z=Ht();ae(Z,()=>({...n(o),...b}));var ze=T(Z);V(ze,e,"default",{get builder(){return n(o)}},null),I(Z),ne(Z,he=>f(he),()=>f()),se(Z,he=>{var Ie,ee;return(ee=(Ie=n(o)).action)==null?void 0:ee.call(Ie,he)}),ye(1,Z,y,E),l(X,Z)},ke=X=>{var Z=N(),ze=$(Z);{var he=ee=>{var G=Bt();ae(G,()=>({...n(o),...b}));var Fe=T(G);V(Fe,e,"default",{get builder(){return n(o)}},null),I(G),ne(G,be=>f(be),()=>f()),se(G,be=>{var ue,te;return(te=(ue=n(o)).action)==null?void 0:te.call(ue,be)}),ye(2,G,w,R),l(ee,G)},Ie=ee=>{var G=N(),Fe=$(G);{var be=ue=>{var te=Lt();ae(te,()=>({...n(o),...b}));var rt=T(te);V(rt,e,"default",{get builder(){return n(o)}},null),I(te),ne(te,qe=>f(qe),()=>f()),se(te,qe=>{var Be,Le;return(Le=(Be=n(o)).action)==null?void 0:Le.call(Be,qe)}),l(ue,te)};q(Fe,ue=>{c(),i(),re(()=>c()(i().value))&&ue(be)},!0)}l(ee,G)};q(ze,ee=>{j(w()),c(),i(),re(()=>w()&&c()(i().value))?ee(he):ee(Ie,!1)},!0)}l(X,Z)};q(We,X=>{j(y()),c(),i(),re(()=>y()&&c()(i().value))?X(_e):X(ke,!1)},!0)}l(K,L)};q(B,K=>{j(y()),j(w()),c(),i(),re(()=>y()&&w()&&c()(i().value))?K(me):K(Se,!1)},!0)}l(Y,P)};q(F,Y=>{j(_()),c(),i(),re(()=>_()&&c()(i().value))?Y(fe):Y(ge,!1)},!0)}l(U,k)};q(d,U=>{j(S()),c(),i(),re(()=>S()&&c()(i().value))?U(O):U(z,!1)})}l(g,v),de(),A()}const Zt=!0,jr=Object.freeze(Object.defineProperty({__proto__:null,prerender:Zt},Symbol.toStringTag,{value:"Module"}));var Gt=M('<div class="pb-4 pt-0"><!></div>');function Me(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["class","transition","transitionConfig"]);ie(e,!1);let p=x(e,"class",8,void 0),A=x(e,"transition",8,Pt),C=x(e,"transitionConfig",24,()=>({duration:200}));ce();{let i=De(()=>(j(ve),j(p()),re(()=>ve("overflow-hidden text-sm transition-all",p()))));Yt(g,He({get class(){return n(i)},get transition(){return A()},get transitionConfig(){return C()}},()=>b,{children:(c,o)=>{var _=Gt(),m=T(_);V(m,e,"default",{},null),I(_),l(c,_)},$$slots:{default:!0}}))}de()}function Oe(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["class","value"]);ie(e,!1);let p=x(e,"class",8,void 0),A=x(e,"value",8);ce();{let C=De(()=>(j(ve),j(p()),re(()=>ve("border-b",p()))));Vt(g,He({get value(){return A()},get class(){return n(C)}},()=>b,{children:(i,c)=>{var o=N(),_=$(o);V(_,e,"default",{},null),l(i,o)},$$slots:{default:!0}}))}de()}var Qt=M("<!> <!>",1);function Ne(g,e){const h=W(e,["children","$$slots","$$events","$$legacy"]),b=W(h,["class","level"]);ie(e,!1);let p=x(e,"class",8,void 0),A=x(e,"level",8,3);ce(),zt(g,{get level(){return A()},class:"flex",children:(C,i)=>{{let c=De(()=>(j(ve),j(p()),re(()=>ve("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",p()))));qt(C,He({get class(){return n(c)}},()=>b,{$$events:{click(o){Ct.call(this,e,o)}},children:(o,_)=>{var m=Qt(),y=$(m);V(y,e,"default",{},null);var E=D(y,2);wt(E,{class:"h-4 w-4 transition-transform duration-200"}),l(o,m)},$$slots:{default:!0}}))}},$$slots:{default:!0}}),de()}const Jt=jt;var Kt=M('<meta name="description"/>'),Xt=M("<!> <!>",1),er=M("<!> <!>",1),tr=M("<!> <!>",1),rr=M("<!> <!>",1),ar=M("<!> <!> <!> <!>",1),sr=M('<tr class="bg-foreground text-background font-bold p-2"><td colspan="3"> </td></tr>'),nr=je('<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>'),lr=je('<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>'),or=je('<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>'),ir=je('<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>'),dr=M('<tr class="relative"><td> </td><td class="text-center"><!></td><td class="text-center"><!></td></tr>'),cr=M(`<div class="min-h-[70vh] pb-12 pt-16 px-6 bg-white"><div class="max-w-6xl mx-auto text-center"><h1 class="text-4xl md:text-5xl font-light tracking-tight text-gray-900 font-jakarta">Simple, transparent pricing</h1> <p class="text-lg text-gray-600 mt-4 font-light max-w-2xl mx-auto">Choose the plan that's right for your team. Start free and scale as you grow.</p></div> <div class="w-full my-8"><!> <h1 class="text-2xl font-bold text-center mt-24">Pricing FAQ</h1> <div class="flex place-content-center"><!></div> <svg style="display:none" version="2.0"><defs><symbol id="checkcircle" viewBox="0 0 24 24" stroke-width="2" fill="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M16.417 10.283A7.917 7.917 0 1 1 8.5 2.366a7.916 7.916 0 0 1 7.917 7.917zm-4.105-4.498a.791.791 0 0 0-1.082.29l-3.828 6.63-1.733-2.08a.791.791 0 1 0-1.216 1.014l2.459 2.952a.792.792 0 0 0 .608.285.83.83 0 0 0 .068-.003.791.791 0 0 0 .618-.393L12.6 6.866a.791.791 0 0 0-.29-1.081z"></path></symbol></defs></svg> <svg style="display:none" version="2.0"><defs><symbol id="nocircle" viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm4,11H8a1,1,0,0,1,0-2h8a1,1,0,0,1,0,2Z"></path></symbol></defs></svg> <h1 class="text-2xl font-bold text-center mt-16">Plan Features</h1> <h2 class="text-xl text-center mt-1 pb-3">Example feature table</h2> <div class="overflow-visible mx-auto max-w-xl mt-4"><table class="table w-full"><thead class="text-lg sticky top-0 bg-foreground text-background bg-opacity-50 z-10 backdrop-blur"><tr><th></th><th class="text-center">Free</th><th class="text-center">Pro</th></tr></thead><tbody></tbody></table></div></div></div>`);function Rr(g){const e=[{name:"Section 1",header:!0},{name:"Feature 1",freeIncluded:!0,proIncluded:!0},{name:"Feature 2",freeIncluded:!1,proIncluded:!0},{name:"Feature 3",freeString:"3",proString:"Unlimited"},{name:"Section 2",header:!0},{name:"Feature 4",freeIncluded:!0,proIncluded:!0},{name:"Feature 5",freeIncluded:!1,proIncluded:!0}];var h=cr();nt(_=>{var m=Kt();st.title="Pricing",pe(()=>it(m,"content",`Pricing - ${ct}`)),l(_,m)});var b=D(T(h),2),p=T(b);dt(p,{callToAction:"Get Started",highlightedPlanId:"pro"});var A=D(p,4),C=T(A);Jt(C,{class:"max-w-xl mx-auto",children:(_,m)=>{var y=ar(),E=$(y);Oe(E,{value:"faq1",children:(f,t)=>{var r=Xt(),a=$(r);Ne(a,{children:(u,v)=>{oe();var d=Q("Is this template free to use?");l(u,d)},$$slots:{default:!0}});var s=D(a,2);Me(s,{children:(u,v)=>{oe();var d=Q("Yup! This template is free to use for any project.");l(u,d)},$$slots:{default:!0}}),l(f,r)},$$slots:{default:!0}});var w=D(E,2);Oe(w,{value:"faq2",children:(f,t)=>{var r=er(),a=$(r);Ne(a,{children:(u,v)=>{oe();var d=Q("Why does a free template have a pricing page?");l(u,d)},$$slots:{default:!0}});var s=D(a,2);Me(s,{children:(u,v)=>{oe();var d=Q(`The pricing page is part of the boilerplate. It shows how the
            pricing page integrates into the billing portal and the Stripe
            Checkout flows.`);l(u,d)},$$slots:{default:!0}}),l(f,r)},$$slots:{default:!0}});var R=D(w,2);Oe(R,{value:"faq3",children:(f,t)=>{var r=tr(),a=$(r);Ne(a,{children:(u,v)=>{oe();var d=Q("What license is the template under?");l(u,d)},$$slots:{default:!0}});var s=D(a,2);Me(s,{children:(u,v)=>{oe();var d=Q("The template is under the MIT license.");l(u,d)},$$slots:{default:!0}}),l(f,r)},$$slots:{default:!0}});var S=D(R,2);Oe(S,{value:"Is this template free to use?",children:(f,t)=>{var r=rr(),a=$(r);Ne(a,{children:(u,v)=>{oe();var d=Q("Can I try out purchase flows without real a credit card?");l(u,d)},$$slots:{default:!0}});var s=D(a,2);Me(s,{children:(u,v)=>{oe();var d=Q(`You can use the credit card number 4242 4242 4242 4242 with any
            future expiry date to test the payment and upgrade flows.`);l(u,d)},$$slots:{default:!0}}),l(f,r)},$$slots:{default:!0}}),l(_,y)},$$slots:{default:!0}}),I(A);var i=D(A,10),c=T(i),o=D(T(c));lt(o,5,()=>e,ot,(_,m)=>{var y=N(),E=$(y);{var w=S=>{var f=sr(),t=T(f),r=T(t,!0);I(t),I(f),pe(()=>Te(r,n(m).name)),l(S,f)},R=S=>{var f=dr(),t=T(f),r=T(t,!0);I(t);var a=D(t),s=T(a);{var u=k=>{var F=Q();pe(()=>Te(F,n(m).freeString)),l(k,F)},v=k=>{var F=N(),fe=$(F);{var ge=P=>{var B=nr();l(P,B)},Y=P=>{var B=lr();l(P,B)};q(fe,P=>{n(m).freeIncluded?P(ge):P(Y,!1)},!0)}l(k,F)};q(s,k=>{n(m).freeString?k(u):k(v,!1)})}I(a);var d=D(a),O=T(d);{var z=k=>{var F=Q();pe(()=>Te(F,n(m).proString)),l(k,F)},U=k=>{var F=N(),fe=$(F);{var ge=P=>{var B=or();l(P,B)},Y=P=>{var B=ir();l(P,B)};q(fe,P=>{n(m).proIncluded?P(ge):P(Y,!1)},!0)}l(k,F)};q(O,k=>{n(m).proString?k(z):k(U,!1)})}I(d),I(f),pe(()=>Te(r,n(m).name)),l(S,f)};q(E,S=>{n(m).header?S(w):S(R,!1)})}l(_,y)}),I(o),I(c),I(i),I(b),I(h),l(g,h)}export{Rr as component,jr as universal};
