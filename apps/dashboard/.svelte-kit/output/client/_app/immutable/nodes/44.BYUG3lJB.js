import"../chunks/CWj6FrbW.js";import{p as I,b as S,d as c,a as e,e as W,$ as j,f as i,n,q as w,s as p,c as v,r as _,t as z}from"../chunks/DDiqt3uM.js";import{h as F,s as H}from"../chunks/DWulv87v.js";import{i as J}from"../chunks/2C89X9tI.js";import{c as f}from"../chunks/BCmD-YNt.js";import{a as K,C as L}from"../chunks/CfRLwaeF.js";import{C as M}from"../chunks/Ckrcpd9Z.js";import"../chunks/DhRTwODG.js";import{C as N,a as O}from"../chunks/Bls5ffmn.js";import"../chunks/ChutyBgo.js";import{B as Q}from"../chunks/BFbKPyUZ.js";var R=i("We've sent a confirmation link to <strong> </strong>",1),T=i("<!> <!>",1),U=i(`<p class="text-sm text-muted-foreground">Please check your email and click the confirmation link to verify your
          new email address.</p> <p class="text-sm text-muted-foreground">You may need to confirm the change on both your old and new email
          addresses.</p>`,1),V=i(`<p class="text-sm text-muted-foreground">Please check your email and click the confirmation link to verify your
          account.</p> <p class="text-sm text-muted-foreground">After confirming your email, you'll be able to sign in with your
          credentials.</p>`,1),X=i(`<div class="space-y-2"><!></div> <div class="pt-4"><a href="/login/sign_in" class="w-full"><!></a></div> <div class="text-xs text-muted-foreground"><p>Didn't receive the email? Check your spam folder or</p> <a href="/login/sign_up" class="underline hover:text-primary">try signing up again</a></div>`,1),Z=i("<!> <!>",1);function ft(Y,h){I(h,!0);var k=S();F(C=>{j.title="Check Your Email"});var B=c(k);f(B,()=>L,(C,E)=>{E(C,{class:"mt-6",children:(q,tt)=>{var P=Z(),b=c(P);f(b,()=>N,(g,x)=>{x(g,{children:($,D)=>{var l=T(),a=c(l);f(a,()=>O,(d,m)=>{m(d,{class:"text-2xl font-bold text-center",children:(r,u)=>{n();var o=w("Check Your Email");e(r,o)},$$slots:{default:!0}})});var y=p(a,2);f(y,()=>M,(d,m)=>{m(d,{class:"text-center",children:(r,u)=>{n();var o=R(),t=p(c(o)),s=v(t,!0);_(t),z(()=>H(s,h.data.email)),e(r,o)},$$slots:{default:!0}})}),e($,l)},$$slots:{default:!0}})});var A=p(b,2);f(A,()=>K,(g,x)=>{x(g,{class:"text-center space-y-4",children:($,D)=>{var l=X(),a=c(l),y=v(a);{var d=t=>{var s=U();n(2),e(t,s)},m=t=>{var s=V();n(2),e(t,s)};J(y,t=>{h.data.type==="email_change"?t(d):t(m,!1)})}_(a);var r=p(a,2),u=v(r),o=v(u);Q(o,{variant:"outline",class:"w-full",children:(t,s)=>{n();var G=w("Go to Sign In");e(t,G)},$$slots:{default:!0}}),_(u),_(r),n(2),e($,l)},$$slots:{default:!0}})}),e(q,P)},$$slots:{default:!0}})}),e(Y,k),W()}export{ft as component};
