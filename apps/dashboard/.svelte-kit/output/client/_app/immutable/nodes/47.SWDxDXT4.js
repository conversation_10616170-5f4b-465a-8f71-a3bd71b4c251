import"../chunks/CWj6FrbW.js";import{p as o,f as n,s as i,d,a as l,e as p,$ as u,i as m,k as c,n as g}from"../chunks/DDiqt3uM.js";import{h}from"../chunks/DWulv87v.js";import{A as f,s as v,o as b}from"../chunks/DlanGm9a.js";import"../chunks/DhRTwODG.js";var w=n('<h1 class="text-2xl font-bold mb-6">Forgot Password</h1> <!> <div class="text-l text-primary mt-4">Remember your password? <a class="underline" href="/login/sign_in">Sign in</a>.</div>',1);function A(r,a){o(a,!0);var e=w();h(t=>{u.title="Forgot Password"});var s=i(d(e),2);{let t=c(()=>`${a.data.url}/auth/callback?next=%2Faccount%2Fsettings%2Freset_password`);f(s,{get supabaseClient(){return a.data.supabase},view:"forgotten_password",get redirectTo(){return m(t)},get providers(){return b},socialLayout:"horizontal",showLinks:!1,get appearance(){return v},additionalData:void 0})}g(2),l(r,e),p()}export{A as component};
