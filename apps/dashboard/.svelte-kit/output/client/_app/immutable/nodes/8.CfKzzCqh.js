import{c as d,e as l}from"../chunks/DlLEFstF.js";import"../chunks/CWj6FrbW.js";import{p as m,j as u,I as v,f,t as _,a as x,e as b,s as h,c as r,r as n,i as g}from"../chunks/DDiqt3uM.js";import{s as y}from"../chunks/DtGADYZa.js";import{s as S}from"../chunks/DE2v8SHj.js";const A=async({data:a,depends:t})=>{t("supabase:auth");const s=d(l.PUBLIC_SUPABASE_URL,l.PUBLIC_SUPABASE_ANON_KEY),e=a.url;return{supabase:s,url:e}},L=Object.freeze(Object.defineProperty({__proto__:null,load:A},Symbol.toStringTag,{value:"Module"}));var B=f('<div class="content-center max-w-2xl mx-auto min-h-[70vh] pb-12 flex items-center place-content-center px-4"><div class="flex flex-col w-full max-w-[500px]"><div class="card-brutal p-8 mb-8"><!></div> <div><span class="text-sm font-bold text-muted-foreground">🍪 Logging in uses Cookies 🍪</span></div></div></div>');function O(a,t){m(t,!0);let s=v(!1);try{u(s,Intl.DateTimeFormat().resolvedOptions().timeZone.startsWith("Europe/"),!0)}catch{}var e=B(),i=r(e),o=r(i),c=r(o);y(c,()=>t.children),n(o);var p=h(o,2);n(i),n(e),_(()=>S(p,1,`mt-8 ${g(s)?"block":"hidden"} text-center`)),x(a,e),b()}export{O as component,L as universal};
