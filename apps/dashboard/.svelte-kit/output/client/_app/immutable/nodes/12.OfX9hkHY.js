import"../chunks/CWj6FrbW.js";import{p as B,f as s,d as c,t as C,a as l,e as V,$ as w,s as m,c as u,r as v,i as T,k as E}from"../chunks/DDiqt3uM.js";import{h as H,s as L}from"../chunks/DWulv87v.js";import{i as f}from"../chunks/2C89X9tI.js";import{S as M}from"../chunks/BvP8B5Vh.js";import{d as N,p as j,P as q}from"../chunks/DJGghuvL.js";var y=s('<div class="mt-10"><a href="/account/billing/manage" class="underline">View past invoices</a></div>'),z=s('<div class="mt-8"><!></div> <!>',1),D=s('<h1 class="text-2xl font-bold mb-2"> </h1> <div>View our <a href="/pricing" target="_blank" class="underline">pricing page</a> for details.</div> <!>',1);function R(g,e){var o;B(e,!0);let p=e.data.currentPlanId??N,_=(o=j.find(a=>a.id===e.data.currentPlanId))==null?void 0:o.name;var d=D();H(a=>{w.title="Billing"});var i=c(d),h=u(i,!0);v(i);var b=m(i,4);{var P=a=>{var t=z(),r=c(t),x=u(r);q(x,{get currentPlanId(){return p},callToAction:"Select Plan",center:!1}),v(r);var I=m(r,2);{var k=n=>{var A=y();l(n,A)};f(I,n=>{e.data.hasEverHadSubscription&&n(k)})}l(a,t)},S=a=>{{let t=E(()=>[{id:"plan",label:"Current Plan",initialValue:_||""}]);M(a,{title:"Subscription",editable:!1,get fields(){return T(t)},editButtonTitle:"Manage Subscription",editLink:"/account/billing/manage"})}};f(b,a=>{e.data.isActiveCustomer?a(S,!1):a(P)})}C(()=>L(h,e.data.isActiveCustomer?"Billing":"Select a Plan")),l(g,d),V()}export{R as component};
