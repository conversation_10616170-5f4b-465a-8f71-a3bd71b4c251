import"../chunks/CWj6FrbW.js";import"../chunks/DhRTwODG.js";import{p as B,f as v,t as o,a as i,e as I,s as l,c as t,$ as L,r as e,n as O,d as U,i as d}from"../chunks/DDiqt3uM.js";import{h as q,s as n}from"../chunks/DWulv87v.js";import{e as z,i as E}from"../chunks/OiKQa7Wx.js";import{s as F}from"../chunks/C36Ip9GY.js";import{i as G}from"../chunks/B_FgA42l.js";import{b,s as H}from"../chunks/n8BDlHG4.js";import{C as J,a as K}from"../chunks/CfRLwaeF.js";var M=v('<meta name="description" content="Our blog posts."/>'),N=v('<div class="flex-none w-6 md:w-32 bg-secondary"></div> <div class="py-6 px-6"><div class="text-xl"> </div> <div class="text-sm text-accent"> </div> <div class="text-muted-foreground"> </div></div>',1),Q=v("<a><!></a>"),R=v('<div class="py-8 lg:py-12 px-6 max-w-lg mx-auto"><div class="text-3xl lg:text-5xl font-medium text-primary flex gap-3 items-baseline text-center place-content-center"><div class="text-center leading-relaxed font-bold text-primary"> </div> <a href="/blog/rss.xml" target="_blank" rel="noreferrer"><img class="flex-none w-5 h-5 object-contain" src="/images/rss.svg" alt="rss feed"/></a></div> <div class="text-lg text-center">A demo blog with sample content.</div> <!></div>');function ot($,y){B(y,!1),G();var m=R();q(x=>{var a=M();o(()=>L.title=b.name),i(x,a)});var c=t(m),g=t(c),w=t(g,!0);e(g),O(2),e(c);var C=l(c,4);z(C,1,()=>H,E,(x,a)=>{var r=Q(),P=t(r);J(P,{class:"my-6",children:(k,T)=>{K(k,{class:"shadow-xl p-6 flex flex-row overflow-hidden",children:(D,V)=>{var _=N(),u=l(U(_),2),p=t(u),S=t(p,!0);e(p);var f=l(p,2),j=t(f,!0);e(f);var h=l(f,2),A=t(h,!0);e(h),e(u),o(s=>{n(S,d(a).title),n(j,s),n(A,d(a).description)},[()=>{var s;return(s=d(a).parsedDate)==null?void 0:s.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}]),i(D,_)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(r),o(()=>F(r,"href",d(a).link)),i(x,r)}),e(m),o(()=>n(w,b.name)),i($,m),I()}export{ot as component};
