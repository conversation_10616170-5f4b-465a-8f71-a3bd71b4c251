import"../chunks/CWj6FrbW.js";import"../chunks/DhRTwODG.js";import{o as Rt}from"../chunks/RnwjOPnl.js";import{b as Mr,d as Me,a as l,p as Mt,l as qr,g as Et,f as n,t as g,i as e,e as jt,m as Se,$ as Nt,c as t,s as o,h as s,j as R,r,n as M}from"../chunks/DDiqt3uM.js";import{h as Tt,e as Ie,r as Pt,s as d}from"../chunks/DWulv87v.js";import{i as u}from"../chunks/2C89X9tI.js";import{e as De,i as He}from"../chunks/OiKQa7Wx.js";import{h as Lt}from"../chunks/CYhDwGx0.js";import{c as Ut}from"../chunks/BCmD-YNt.js";import{s as Vr,b as Qe}from"../chunks/C36Ip9GY.js";import{s as Xr}from"../chunks/DE2v8SHj.js";import{t as et}from"../chunks/A4ulxp7Q.js";import{b as At}from"../chunks/DUGxtfU6.js";import{i as Ot}from"../chunks/B_FgA42l.js";import{a as zt,s as rt}from"../chunks/B82PTGnX.js";import{w as Ft}from"../chunks/rjRVMZXi.js";import{p as Kt}from"../chunks/CF5x3pQ4.js";import{s as Gt,a as Bt}from"../chunks/DSm1r-pw.js";import{s as ot}from"../chunks/iCEqKm8o.js";import{l as st,s as it}from"../chunks/C-ZVHnwW.js";import{I as dt}from"../chunks/CkoRhfQ8.js";import{C as Jt}from"../chunks/oaGF9CiI.js";import{Z as Ht}from"../chunks/BOkOaIl2.js";import{C as Qt,D as tt}from"../chunks/ZorUncli.js";import{U as sr}from"../chunks/C9gUsiOS.js";import{C as Wt}from"../chunks/CXeeNO9R.js";import{U as Yt,B as at}from"../chunks/ng6E9JCw.js";import{C as Zt}from"../chunks/CgBAmp64.js";import{M as qt}from"../chunks/bEcYyObW.js";import{C as Vt}from"../chunks/DR0c5pEj.js";import{L as Xt}from"../chunks/DWMPsgkI.js";import{C as ea}from"../chunks/C6jXKNMu.js";function Re(Ee,fe){const he=st(fe,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["path",{d:"m3 11 18-5v12L3 14v-3z"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6"}]];dt(Ee,it({name:"megaphone"},()=>he,{get iconNode(){return je},children:(_e,be)=>{var q=Mr(),F=Me(q);ot(F,fe,"default",{},null),l(_e,q)},$$slots:{default:!0}}))}function ra(Ee,fe){const he=st(fe,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["circle",{cx:"18",cy:"5",r:"3"}],["circle",{cx:"6",cy:"12",r:"3"}],["circle",{cx:"18",cy:"19",r:"3"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49"}]];dt(Ee,it({name:"share-2"},()=>he,{get iconNode(){return je},children:(_e,be)=>{var q=Mr(),F=Me(q);ot(F,fe,"default",{},null),l(_e,q)},$$slots:{default:!0}}))}var ta=n('<meta name="description" content="Comprehensive company research with competitor analysis and contact discovery powered by AI" class="svelte-i1g7n4"/>'),aa=n('<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group svelte-i1g7n4" style="background: var(--card); border-color: var(--border);"><div class="flex items-center gap-3 mb-3 svelte-i1g7n4"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform svelte-i1g7n4" style="background: var(--primary); border-color: var(--border);"><!></div> <h4 class="font-bold text-sm svelte-i1g7n4" style="color: var(--foreground);"> </h4></div> <p class="text-xs mb-3 svelte-i1g7n4" style="color: var(--muted-foreground);"> </p> <div class="text-xs font-mono p-2 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"> </div></button>'),oa=n(`<div class="text-center py-12 svelte-i1g7n4"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2 svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><!></div> <h3 class="text-xl font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Start Your Company Research</h3> <p class="font-medium mb-6 svelte-i1g7n4" style="color: var(--muted-foreground);">Get comprehensive company intelligence with competitor analysis
              and contacts</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto svelte-i1g7n4"></div></div>`),sa=n('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Download as Markdown"><!> Download</button>'),ia=n('<div class="p-4 border-2 svelte-i1g7n4" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium svelte-i1g7n4" style="color: var(--primary-foreground);"> </p></div>'),da=n('<div class="text-right svelte-i1g7n4"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Founded</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);"> </div></div>'),va=n('<div class="p-4 border-2 rounded svelte-i1g7n4" style="background: var(--card); border-color: var(--border);"><h5 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Company Overview</h5> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);"> </p></div>'),la=n('<div class="text-xs font-mono p-1 border rounded svelte-i1g7n4" style="background: var(--background); border-color: var(--border); color: var(--muted-foreground);"> </div>'),na=n('<div class="p-3 border-2 rounded flex items-center justify-between svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="svelte-i1g7n4"><div class="font-medium svelte-i1g7n4" style="color: var(--foreground);"> </div> <div class="text-sm svelte-i1g7n4" style="color: var(--muted-foreground);"> </div></div> <!></div>'),ca=n('<div class="mt-4 svelte-i1g7n4"><h5 class="font-bold mb-3 flex items-center svelte-i1g7n4" style="color: var(--foreground);"><!> </h5> <div class="grid gap-3 svelte-i1g7n4"></div></div>'),ua=n('<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border); box-shadow: var(--shadow);"><div class="p-4 svelte-i1g7n4" style="background: var(--primary);"><h3 class="text-lg font-bold flex items-center svelte-i1g7n4" style="color: var(--primary-foreground);"><!> Target Company Profile</h3></div> <div class="p-6 svelte-i1g7n4" style="background: var(--background);"><div class="flex items-start justify-between mb-4 svelte-i1g7n4"><div class="svelte-i1g7n4"><h4 class="text-xl font-bold svelte-i1g7n4" style="color: var(--foreground);"> </h4> <p class="text-sm font-medium svelte-i1g7n4" style="color: var(--muted-foreground);"> </p></div> <!></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 svelte-i1g7n4"><div class="p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs font-medium mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Industry</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);"> </div></div> <div class="p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs font-medium mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Employees</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);"> </div></div> <div class="p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs font-medium mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Revenue</div> <div class="font-bold svelte-i1g7n4" style="color: var(--foreground);"> </div></div></div> <!> <!></div></div>'),ga=n('<p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);"> </p>'),pa=n('<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Industry</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);"> </div></div>'),ma=n('<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Employees</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);"> </div></div>'),fa=n('<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Location</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);"> </div></div>'),_a=n('<div class="text-center p-2 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Position</div> <div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);"> </div></div>'),ba=n('<div class="text-xs font-mono px-2 py-1 border rounded svelte-i1g7n4" style="background: var(--background); border-color: var(--border); color: var(--muted-foreground);"> </div>'),ya=n('<div class="flex items-center justify-between p-2 border rounded svelte-i1g7n4" style="background: var(--card); border-color: var(--border);"><div class="svelte-i1g7n4"><div class="text-sm font-medium svelte-i1g7n4" style="color: var(--foreground);"> </div> <div class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);"> </div></div> <!></div>'),xa=n('<div class="border-t pt-3 svelte-i1g7n4" style="border-color: var(--border);"><h6 class="text-sm font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);"> </h6> <div class="grid gap-2 svelte-i1g7n4"></div></div>'),ha=n('<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border);"><div class="p-4 svelte-i1g7n4"><div class="flex items-start justify-between mb-3 svelte-i1g7n4"><div class="flex-1 svelte-i1g7n4"><div class="flex items-center gap-2 mb-1 svelte-i1g7n4"><span class="text-xs font-bold px-2 py-1 rounded svelte-i1g7n4" style="background: var(--accent); color: var(--accent-foreground);"></span> <h4 class="text-lg font-bold svelte-i1g7n4" style="color: var(--foreground);"> </h4></div> <p class="text-sm font-medium mb-2 svelte-i1g7n4" style="color: var(--muted-foreground);"> </p> <!></div> <div class="text-right ml-4 svelte-i1g7n4"><div class="text-xs mb-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Similarity</div> <div class="text-sm font-bold px-2 py-1 border rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border); color: var(--foreground);"> </div></div></div> <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3 svelte-i1g7n4"><!> <!> <!> <!></div> <!></div></div>'),wa=n('<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border); box-shadow: var(--shadow);"><div class="p-4 svelte-i1g7n4" style="background: var(--secondary);"><h3 class="text-lg font-bold flex items-center svelte-i1g7n4" style="color: var(--secondary-foreground);"><!> </h3></div> <div class="p-6 svelte-i1g7n4" style="background: var(--background);"><div class="grid gap-4 svelte-i1g7n4"></div></div></div>'),ka=n('<div class="svelte-i1g7n4"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Market Insights</h4> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);"> </p></div>'),Ca=n('<div class="svelte-i1g7n4"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Competitive Landscape</h4> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);"> </p></div>'),$a=n('<div class="svelte-i1g7n4"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Key Differentiators</h4> <p class="text-sm leading-relaxed svelte-i1g7n4" style="color: var(--muted-foreground);"> </p></div>'),Sa=n('<div class="border-2 rounded-lg overflow-hidden svelte-i1g7n4" style="border-color: var(--border); box-shadow: var(--shadow);"><div class="p-4 svelte-i1g7n4" style="background: var(--accent);"><h3 class="text-lg font-bold flex items-center svelte-i1g7n4" style="color: var(--accent-foreground);"><!> Market Intelligence Summary</h3></div> <div class="p-6 svelte-i1g7n4" style="background: var(--background);"><div class="grid md:grid-cols-3 gap-6 svelte-i1g7n4"><!> <!> <!></div></div></div>'),Ia=n('<div class="border-2 rounded-lg p-4 svelte-i1g7n4" style="border-color: var(--border); background: var(--muted);"><h4 class="font-bold mb-3 svelte-i1g7n4" style="color: var(--foreground);">Research Summary</h4> <div class="grid grid-cols-2 md:grid-cols-4 gap-4 svelte-i1g7n4"><div class="text-center svelte-i1g7n4"><div class="text-2xl font-black mb-1 svelte-i1g7n4" style="color: var(--primary);"> </div> <div class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Companies Analyzed</div></div> <div class="text-center svelte-i1g7n4"><div class="text-2xl font-black mb-1 svelte-i1g7n4" style="color: var(--primary);"> </div> <div class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Contacts Found</div></div> <div class="text-center svelte-i1g7n4"><div class="text-sm font-bold px-2 py-1 border rounded svelte-i1g7n4" style="background: var(--background); border-color: var(--border); color: var(--foreground);"> </div> <div class="text-xs font-medium mt-1 svelte-i1g7n4" style="color: var(--muted-foreground);">Data Quality</div></div> <div class="text-center svelte-i1g7n4"><div class="text-sm font-bold svelte-i1g7n4" style="color: var(--foreground);"> </div> <div class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Processing Time</div></div></div></div>'),Da=n(`<div class="space-y-6 svelte-i1g7n4"><div class="text-center border-b-2 pb-4 svelte-i1g7n4" style="border-color: var(--border);"><h2 class="text-2xl font-black mb-2 svelte-i1g7n4" style="color: var(--foreground);">Company Intelligence Report</h2> <p class="text-sm svelte-i1g7n4" style="color: var(--muted-foreground);">Comprehensive analysis with competitor landscape and
                          contact intelligence</p></div> <!> <!> <!> <!> <div class="border-t-2 pt-4 text-center svelte-i1g7n4" style="border-color: var(--border);"><div class="flex items-center justify-center gap-2 mb-2 svelte-i1g7n4"><!> <span class="font-bold svelte-i1g7n4" style="color: var(--foreground);">Catalyst Intelligence Report</span></div> <p class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);"> </p></div></div>`),Ra=n('<div class="mt-4 p-3 border-2 rounded svelte-i1g7n4" style="background: var(--muted); border-color: var(--border);"><h4 class="font-bold mb-2 svelte-i1g7n4" style="color: var(--foreground);">Debug: Raw Data</h4> <pre class="text-xs overflow-auto svelte-i1g7n4" style="color: var(--muted-foreground);"> </pre></div>'),Ma=n('<div class="prose prose-sm max-w-none svelte-i1g7n4"><!></div> <!>',1),Ea=n('<button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Download as Markdown file"><!> Download</button>'),ja=n('<div class="p-6 border-2 mb-4 svelte-i1g7n4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><!></div> <div class="flex flex-wrap gap-2 mb-4 svelte-i1g7n4"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Copy research report to clipboard"><!> Copy Report</button> <!> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Export contact list"><!> Export Contacts</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-i1g7n4" title="Share research findings"><!> Share Report</button></div>',1),Na=n('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-i1g7n4"><!></div> <div class="flex-1 max-w-3xl svelte-i1g7n4"><div class="flex items-center gap-2 mb-2 svelte-i1g7n4"><span class="text-sm font-bold svelte-i1g7n4" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1 svelte-i1g7n4"><!> <span class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),Ta=n('<div class="svelte-i1g7n4"><!></div>'),Pa=n('<div class="mt-2 h-1 rounded-full overflow-hidden svelte-i1g7n4" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out svelte-i1g7n4"></div></div>'),La=n('<div class="flex items-start gap-3 svelte-i1g7n4"><div class="flex-shrink-0 mt-0.5 svelte-i1g7n4"><!></div> <div class="flex-1 svelte-i1g7n4"><h4 class="text-sm font-bold mb-1 svelte-i1g7n4"> </h4> <p class="text-xs svelte-i1g7n4"> </p> <!></div></div>'),Ua=n('<div class="flex gap-4 svelte-i1g7n4"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-i1g7n4" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1 svelte-i1g7n4"><div class="flex items-center gap-2 mb-2 svelte-i1g7n4"><span class="text-sm font-bold svelte-i1g7n4" style="color: var(--foreground);">Catalyst</span> <span class="text-xs svelte-i1g7n4" style="color: var(--muted-foreground);">Analyzing company intelligence and competitive landscape...</span></div> <div class="p-6 border-2 svelte-i1g7n4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="space-y-4 svelte-i1g7n4"></div> <div class="mt-6 pt-4 border-t svelte-i1g7n4" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2 svelte-i1g7n4"><span class="text-xs font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold svelte-i1g7n4" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden svelte-i1g7n4" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out svelte-i1g7n4"></div></div></div></div></div></div>'),Aa=n('<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin svelte-i1g7n4"></div>'),Oa=n(`<div class="modern-chat-container svelte-i1g7n4"><div class="border-b-2 flex-shrink-0 svelte-i1g7n4" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6 svelte-i1g7n4"><div class="flex items-center justify-between svelte-i1g7n4"><div class="flex items-center space-x-4 svelte-i1g7n4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer svelte-i1g7n4" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="svelte-i1g7n4"><h1 class="text-3xl font-black svelte-i1g7n4" style="color: var(--foreground);">Catalyst</h1> <p class="text-lg font-medium svelte-i1g7n4" style="color: var(--muted-foreground);">AI-powered company research with competitor analysis and contact
              discovery</p></div></div> <div class="flex items-center space-x-4 svelte-i1g7n4"><div class="flex items-center space-x-2 px-4 py-2 border-2 svelte-i1g7n4" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold svelte-i1g7n4" style="color: var(--accent-foreground);">Multi-Channel</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4 svelte-i1g7n4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground svelte-i1g7n4"><a class="hover:text-foreground transition-colors svelte-i1g7n4">Dashboard</a> <!> <span class="text-foreground font-medium svelte-i1g7n4">Catalyst</span></nav></div> <div class="modern-messages-area svelte-i1g7n4"><div class="modern-message-container svelte-i1g7n4"><div class="space-y-6 svelte-i1g7n4"><!> <!> <!></div></div></div></div> <div><div class="max-w-4xl mx-auto svelte-i1g7n4"><div class="flex svelte-i1g7n4" style="gap: 15px;"><div class="flex-1 relative svelte-i1g7n4"><textarea class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full svelte-i1g7n4" style="min-height: 60px;"></textarea></div> <button class="btn-primary px-6 font-bold flex items-center gap-2 svelte-i1g7n4" style="height: auto; align-self: stretch;"><!> Research</button></div></div></div>`,1);function yo(Ee,fe){Mt(fe,!1);const[he,je]=zt(),_e=()=>rt(Kt,"$page",he),be=()=>rt(q,"$messages",he),q=Ft([]);let F=Se(""),K=Se(!1),ir=0,dr=Se(""),j=Se([]),we=Se(0);const vr=["coreweave.com","singlestore.com","CoreWeave","SingleStore","research stripe.com","analyze openai.com","investigate anthropic.com"],vt=[{icon:Ht,title:"Competitor Analysis",description:"Deep dive into competitor landscape and market positioning",prompt:"coreweave.com"},{icon:Qt,title:"Market Research",description:"Comprehensive company intelligence with contact discovery",prompt:"SingleStore"},{icon:sr,title:"Lead Generation",description:"Find similar companies and key decision-maker contacts",prompt:"research stripe.com"}];function lr(){return Math.random().toString(36).substring(2,11)}async function Er(){var a;if(!e(F).trim()||e(K))return;const v=e(F).trim();R(F,""),R(K,!0),R(j,[{id:1,title:"Input Processing",description:"Processing company information...",status:"pending"},{id:2,title:"Company Enrichment",description:"Enriching target company data...",status:"pending"},{id:3,title:"Intelligence Gathering",description:"Finding competitors and insights...",status:"pending"},{id:4,title:"Company Discovery",description:"Discovering similar companies...",status:"pending"},{id:5,title:"Contact Retrieval",description:"Retrieving contact information...",status:"pending"},{id:6,title:"Results Formatting",description:"Formatting comprehensive results...",status:"pending"}]),R(we,0),q.update(c=>[...c,{id:lr(),role:"user",content:v,timestamp:new Date}]);try{const c=await fetch(`/dashboard/${_e().params.envSlug}/campaign-orchestrator?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:v})});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const w=(a=c.body)==null?void 0:a.getReader(),re=new TextDecoder;if(w)for(;;){const{done:ce,value:S}=await w.read();if(ce)break;const L=re.decode(S).split(`
`);for(const U of L)if(U.startsWith("data: "))try{const f=JSON.parse(U.slice(6));if(f.step&&f.step>0&&(R(we,f.progress||0),R(j,e(j).map(k=>k.id<f.step?{...k,status:"completed"}:k.id===f.step?{...k,status:"active",description:f.action}:k))),f.status==="completed"&&f.response&&(console.log("Received completed response:",{response:f.response,data:f.data,hasData:!!f.data}),q.update(k=>[...k,{id:lr(),role:"assistant",content:f.response,timestamp:new Date,data:f.data||null,isReport:!0}])),f.status==="error")throw new Error(f.error||"Unknown error occurred")}catch(f){console.warn("Failed to parse SSE data:",f)}}}catch(c){console.error("Campaign Orchestrator error:",c),q.update(w=>[...w,{id:lr(),role:"assistant",content:`Error: ${c instanceof Error?c.message:"Unknown error occurred"}`,timestamp:new Date,isReport:!0}])}finally{R(K,!1),R(j,e(j).map(c=>({...c,status:"completed"}))),R(we,100)}}function lt(v){v.key==="Enter"&&!v.shiftKey&&(v.preventDefault(),Er())}function nt(v){return v.replace(/^### (.+)$/gm,'<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>').replace(/^## (.+)$/gm,'<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>').replace(/^# (.+)$/gm,'<h1 class="text-2xl font-bold mb-4">$1</h1>').replace(/^\* (.+)$/gm,'<li class="ml-4">• $1</li>').replace(/^- (.+)$/gm,'<li class="ml-4">• $1</li>').replace(/\*\*(.+?)\*\*/g,"<strong>$1</strong>").replace(/\n\n/g,'</p><p class="mb-4">').replace(/^/,'<p class="mb-4">').replace(/$/,"</p>")}function ct(v){navigator.clipboard.writeText(v)}function jr(v){const a=new Blob([v.content],{type:"text/markdown"}),c=URL.createObjectURL(a),w=document.createElement("a");w.href=c,w.download=`campaign-plan-${new Date().toISOString().split("T")[0]}.md`,w.click()}Rt(()=>{R(dr,vr[0]);const v=setInterval(()=>{ir=(ir+1)%vr.length,R(dr,vr[ir])},3e3);return()=>clearInterval(v)});let Ne=Se();qr(()=>(e(K),e(j),e(Ne)),()=>{e(K)&&e(j).length>0?R(Ne,setInterval(()=>{const v=e(j).find(c=>c.status==="pending"),a=e(j).find(c=>c.status==="active");a&&a.progress!==void 0?(a.progress=Math.min(100,a.progress+20),a.progress===100&&(a.status="completed",R(we,Math.round(e(j).filter(c=>c.status==="completed").length/e(j).length*100)))):v?(v.status="active",v.progress=0):clearInterval(e(Ne)),R(j,[...e(j)])},300)):e(Ne)&&clearInterval(e(Ne))}),qr(()=>be(),()=>{be().length>0&&setTimeout(()=>{window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})},100)}),Et(),Ot();var Nr=Oa();Tt(v=>{var a=ta();Nt.title="Catalyst - AI Company Research & Intelligence",l(v,a)});var nr=Me(Nr),cr=t(nr),Tr=t(cr),Pr=t(Tr),ur=t(Pr),Lr=t(ur),ut=t(Lr);Re(ut,{class:"w-6 h-6 animate-pulse",style:"color: var(--primary-foreground);"}),r(Lr),M(2),r(ur);var Ur=o(ur,2),Ar=t(Ur),gt=t(Ar);ra(gt,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),M(2),r(Ar),r(Ur),r(Pr),r(Tr),r(cr);var gr=o(cr,2),Or=t(gr),zr=t(Or),pt=o(zr,2);Jt(pt,{class:"w-4 h-4"}),M(2),r(Or),r(gr);var Fr=o(gr,2),Kr=t(Fr),Gr=t(Kr),Br=t(Gr);{var mt=v=>{var a=oa(),c=t(a),w=t(c);Re(w,{class:"w-8 h-8",style:"color: var(--muted-foreground);"}),r(c);var re=o(c,6);De(re,5,()=>vt,He,(ce,S)=>{var G=aa(),L=t(G),U=t(L),f=t(U);Ut(f,()=>e(S).icon,(Pe,Le)=>{Le(Pe,{class:"w-4 h-4",style:"color: var(--primary-foreground);"})}),r(U);var k=o(U,2),ke=t(k,!0);r(k),r(L);var V=o(L,2),_=t(V,!0);r(V);var te=o(V,2),ye=t(te,!0);r(te),r(G),g(()=>{G.disabled=e(K),d(ke,(e(S),s(()=>e(S).title))),d(_,(e(S),s(()=>e(S).description))),d(ye,(e(S),s(()=>e(S).prompt)))}),Ie("click",G,()=>{R(F,e(S).prompt)}),l(ce,G)}),r(re),r(a),l(v,a)};u(Br,v=>{be(),s(()=>be().length===0)&&v(mt)})}var Jr=o(Br,2);De(Jr,1,be,He,(v,a)=>{var c=Na(),w=t(c),re=t(w);{var ce=b=>{Yt(b,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},S=b=>{at(b,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};u(re,b=>{e(a),s(()=>e(a).role==="user")?b(ce):b(S,!1)})}r(w);var G=o(w,2),L=t(G),U=t(L),f=t(U,!0);r(U);var k=o(U,2),ke=t(k);Wt(ke,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var V=o(ke,2),_=t(V,!0);r(V),r(k);var te=o(k,2);{var ye=b=>{var I=sa(),X=t(I);tt(X,{class:"w-3 h-3"}),M(),r(I),Ie("click",I,()=>jr(e(a))),l(b,I)};u(te,b=>{e(a),s(()=>e(a).role==="assistant"&&e(a).isReport)&&b(ye)})}r(L);var Pe=o(L,2);{var Le=b=>{var I=ia(),X=t(I),ue=t(X,!0);r(X),r(I),g(()=>d(ue,(e(a),s(()=>e(a).content)))),l(b,I)},fr=b=>{var I=ja(),X=Me(I),ue=t(X);{var _r=ee=>{var W=Da(),me=o(t(W),2);{var xr=C=>{var $=ua(),N=t($),B=t(N),ae=t(B);Re(ae,{class:"w-5 h-5 mr-2"}),M(),r(B),r(N);var oe=o(N,2),O=t(oe),J=t(O),se=t(J),i=t(se,!0);r(se);var ie=o(se,2),de=t(ie,!0);r(ie),r(J);var ve=o(J,2);{var x=z=>{var T=da(),Z=o(t(T),2),le=t(Z,!0);r(Z),r(T),g(()=>d(le,(e(a),s(()=>e(a).data.targetCompany.founded_year)))),l(z,T)};u(ve,z=>{e(a),s(()=>e(a).data.targetCompany.founded_year)&&z(x)})}r(O);var y=o(O,2),D=t(y),Y=o(t(D),2),Ve=t(Y,!0);r(Y),r(D);var ze=o(D,2),$e=o(t(ze),2),Cr=t($e,!0);r($e),r(ze);var Xe=o(ze,2),er=o(t(Xe),2),rr=t(er,!0);r(er),r(Xe),r(y);var Fe=o(y,2);{var $r=z=>{var T=va(),Z=o(t(T),2),le=t(Z,!0);r(Z),r(T),g(()=>d(le,(e(a),s(()=>e(a).data.targetCompany.description)))),l(z,T)};u(Fe,z=>{e(a),s(()=>e(a).data.targetCompany.description)&&z($r)})}var Ke=o(Fe,2);{var tr=z=>{var T=ca(),Z=t(T),le=t(Z);sr(le,{class:"w-4 h-4 mr-2"});var Sr=o(le);r(Z);var ar=o(Z,2);De(ar,5,()=>(e(a),s(()=>e(a).data.targetCompany.contacts)),He,(Ir,H)=>{var Ge=na(),p=t(Ge),m=t(p),h=t(m,!0);r(m);var P=o(m,2),or=t(P,!0);r(P),r(p);var Dr=o(p,2);{var Q=ne=>{var xe=la(),Be=t(xe,!0);r(xe),g(()=>d(Be,(e(H),s(()=>e(H).email)))),l(ne,xe)};u(Dr,ne=>{e(H),s(()=>e(H).email)&&ne(Q)})}r(Ge),g(ne=>{d(h,ne),d(or,(e(H),s(()=>e(H).title||"No title available")))},[()=>(e(H),s(()=>e(H).name||`${e(H).first_name||""} ${e(H).last_name||""}`.trim()||"Unknown"))]),l(Ir,Ge)}),r(ar),r(T),g(()=>d(Sr,` Key Contacts (${e(a),s(()=>e(a).data.targetCompany.contacts.length)??""})`)),l(z,T)};u(Ke,z=>{e(a),s(()=>e(a).data.targetCompany.contacts&&e(a).data.targetCompany.contacts.length>0)&&z(tr)})}r(oe),r($),g(()=>{d(i,(e(a),s(()=>e(a).data.targetCompany.name||"Unknown Company"))),d(de,(e(a),s(()=>e(a).data.targetCompany.domain||"No domain available"))),d(Ve,(e(a),s(()=>e(a).data.targetCompany.industry||"Not specified"))),d(Cr,(e(a),s(()=>e(a).data.targetCompany.employees||"Unknown"))),d(rr,(e(a),s(()=>e(a).data.targetCompany.revenue||"Not disclosed")))}),l(C,$)};u(me,C=>{e(a),s(()=>e(a).data.targetCompany)&&C(xr)})}var Ze=o(me,2);{var hr=C=>{var $=wa(),N=t($),B=t(N),ae=t(B);sr(ae,{class:"w-5 h-5 mr-2"});var oe=o(ae);r(B),r(N);var O=o(N,2),J=t(O);De(J,5,()=>(e(a),s(()=>e(a).data.competitors.slice(0,8))),He,(se,i,ie)=>{var de=ha(),ve=t(de),x=t(ve),y=t(x),D=t(y),Y=t(D);Y.textContent=`#${ie+1}`;var Ve=o(Y,2),ze=t(Ve,!0);r(Ve),r(D);var $e=o(D,2),Cr=t($e,!0);r($e);var Xe=o($e,2);{var er=p=>{var m=ga(),h=t(m,!0);r(m),g(P=>d(h,P),[()=>(e(i),s(()=>e(i).description.length>150?e(i).description.substring(0,150)+"...":e(i).description))]),l(p,m)};u(Xe,p=>{e(i),s(()=>e(i).description)&&p(er)})}r(y);var rr=o(y,2),Fe=o(t(rr),2),$r=t(Fe,!0);r(Fe),r(rr),r(x);var Ke=o(x,2),tr=t(Ke);{var z=p=>{var m=pa(),h=o(t(m),2),P=t(h,!0);r(h),r(m),g(()=>d(P,(e(i),s(()=>e(i).industry)))),l(p,m)};u(tr,p=>{e(i),s(()=>e(i).industry)&&p(z)})}var T=o(tr,2);{var Z=p=>{var m=ma(),h=o(t(m),2),P=t(h,!0);r(h),r(m),g(()=>d(P,(e(i),s(()=>e(i).employees)))),l(p,m)};u(T,p=>{e(i),s(()=>e(i).employees)&&p(Z)})}var le=o(T,2);{var Sr=p=>{var m=fa(),h=o(t(m),2),P=t(h,!0);r(h),r(m),g(()=>d(P,(e(i),s(()=>e(i).location)))),l(p,m)};u(le,p=>{e(i),s(()=>e(i).location)&&p(Sr)})}var ar=o(le,2);{var Ir=p=>{var m=_a(),h=o(t(m),2),P=t(h,!0);r(h),r(m),g(()=>d(P,(e(i),s(()=>e(i).market_position)))),l(p,m)};u(ar,p=>{e(i),s(()=>e(i).market_position)&&p(Ir)})}r(Ke);var H=o(Ke,2);{var Ge=p=>{var m=xa(),h=t(m),P=t(h);r(h);var or=o(h,2);De(or,5,()=>(e(i),s(()=>e(i).contacts.slice(0,3))),He,(Dr,Q)=>{var ne=ya(),xe=t(ne),Be=t(xe),Ct=t(Be,!0);r(Be);var Zr=o(Be,2),$t=t(Zr,!0);r(Zr),r(xe);var St=o(xe,2);{var It=Je=>{var Rr=ba(),Dt=t(Rr,!0);r(Rr),g(()=>d(Dt,(e(Q),s(()=>e(Q).email)))),l(Je,Rr)};u(St,Je=>{e(Q),s(()=>e(Q).email)&&Je(It)})}r(ne),g(Je=>{d(Ct,Je),d($t,(e(Q),s(()=>e(Q).title||"No title")))},[()=>(e(Q),s(()=>e(Q).name||`${e(Q).first_name||""} ${e(Q).last_name||""}`.trim()||"Unknown"))]),l(Dr,ne)}),r(or),r(m),g(()=>d(P,`Key Contacts (${e(i),s(()=>e(i).contacts.length)??""})`)),l(p,m)};u(H,p=>{e(i),s(()=>e(i).contacts&&e(i).contacts.length>0)&&p(Ge)})}r(ve),r(de),g(()=>{d(ze,(e(i),s(()=>e(i).name||"Unknown Company"))),d(Cr,(e(i),s(()=>e(i).domain||"No domain available"))),d($r,(e(i),s(()=>e(i).similarity||"Similar")))}),l(se,de)}),r(J),r(O),r($),g(()=>d(oe,` Competitive Landscape (${e(a),s(()=>e(a).data.competitors.length)??""} companies)`)),l(C,$)};u(Ze,C=>{e(a),s(()=>e(a).data.competitors&&e(a).data.competitors.length>0)&&C(hr)})}var Ce=o(Ze,2);{var Ae=C=>{var $=Sa(),N=t($),B=t(N),ae=t(B);Re(ae,{class:"w-5 h-5 mr-2"}),M(),r(B),r(N);var oe=o(N,2),O=t(oe),J=t(O);{var se=x=>{var y=ka(),D=o(t(y),2),Y=t(D,!0);r(D),r(y),g(()=>d(Y,(e(a),s(()=>e(a).data.intelligence.market_insights)))),l(x,y)};u(J,x=>{e(a),s(()=>e(a).data.intelligence.market_insights)&&x(se)})}var i=o(J,2);{var ie=x=>{var y=Ca(),D=o(t(y),2),Y=t(D,!0);r(D),r(y),g(()=>d(Y,(e(a),s(()=>e(a).data.intelligence.competitive_landscape)))),l(x,y)};u(i,x=>{e(a),s(()=>e(a).data.intelligence.competitive_landscape)&&x(ie)})}var de=o(i,2);{var ve=x=>{var y=$a(),D=o(t(y),2),Y=t(D,!0);r(D),r(y),g(()=>d(Y,(e(a),s(()=>e(a).data.intelligence.key_differentiators)))),l(x,y)};u(de,x=>{e(a),s(()=>e(a).data.intelligence.key_differentiators)&&x(ve)})}r(O),r(oe),r($),l(C,$)};u(Ce,C=>{e(a),s(()=>e(a).data.intelligence)&&C(Ae)})}var Oe=o(Ce,2);{var wr=C=>{var $=Ia(),N=o(t($),2),B=t(N),ae=t(B),oe=t(ae,!0);r(ae),M(2),r(B);var O=o(B,2),J=t(O),se=t(J,!0);r(J),M(2),r(O);var i=o(O,2),ie=t(i),de=t(ie,!0);r(ie),M(2),r(i);var ve=o(i,2),x=t(ve),y=t(x,!0);r(x),M(2),r(ve),r(N),r($),g(()=>{d(oe,(e(a),s(()=>e(a).data.metadata.totalCompanies||0))),d(se,(e(a),s(()=>e(a).data.metadata.totalContacts||0))),d(de,(e(a),s(()=>e(a).data.metadata.dataQuality||"N/A"))),d(y,(e(a),s(()=>e(a).data.metadata.processingTime||"N/A")))}),l(C,$)};u(Oe,C=>{e(a),s(()=>e(a).data.metadata)&&C(wr)})}var qe=o(Oe,2),kr=t(qe),wt=t(kr);Re(wt,{class:"w-4 h-4",style:"color: var(--primary);"}),M(2),r(kr);var Yr=o(kr,2),kt=t(Yr);r(Yr),r(qe),r(W),g((C,$)=>d(kt,`Generated on ${C??""}
                          at ${$??""}
                          • Powered by Apollo & Exa APIs`),[()=>(e(a),s(()=>e(a).timestamp.toLocaleDateString())),()=>(e(a),s(()=>e(a).timestamp.toLocaleTimeString()))]),l(ee,W)},br=ee=>{var W=Ma(),me=Me(W),xr=t(me);Lt(xr,()=>(e(a),s(()=>nt(e(a).content)))),r(me);var Ze=o(me,2);{var hr=Ce=>{var Ae=Ra(),Oe=o(t(Ae),2),wr=t(Oe,!0);r(Oe),r(Ae),g(qe=>d(wr,qe),[()=>(e(a),s(()=>JSON.stringify(e(a).data,null,2)))]),l(Ce,Ae)};u(Ze,Ce=>{e(a),s(()=>e(a).data&&!e(a).data.targetCompany&&!e(a).data.competitors)&&Ce(hr)})}l(ee,W)};u(ue,ee=>{e(a),s(()=>e(a).data&&(e(a).data.targetCompany||e(a).data.competitors))?ee(_r):ee(br,!1)})}r(X);var Ye=o(X,2),E=t(Ye),A=t(E);Zt(A,{class:"w-3 h-3"}),M(),r(E);var ge=o(E,2);{var yr=ee=>{var W=Ea(),me=t(W);tt(me,{class:"w-3 h-3"}),M(),r(W),Ie("click",W,()=>jr(e(a))),l(ee,W)};u(ge,ee=>{e(a),s(()=>e(a).isReport)&&ee(yr)})}var Ue=o(ge,2),pe=t(Ue);sr(pe,{class:"w-3 h-3"}),M(),r(Ue);var Wr=o(Ue,2),ht=t(Wr);qt(ht,{class:"w-3 h-3"}),M(),r(Wr),r(Ye),Ie("click",E,()=>ct(e(a).content)),l(b,I)};u(Pe,b=>{e(a),s(()=>e(a).role==="user")?b(Le):b(fr,!1)})}r(G),r(c),g(b=>{Xr(c,1,`modern-message-item flex gap-4 ${e(a),s(()=>e(a).role==="user"?"flex-row-reverse":"")??""}`,"svelte-i1g7n4"),Qe(w,`background: var(--${e(a),s(()=>e(a).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),d(f,(e(a),s(()=>e(a).role==="user"?"You":"Catalyst"))),d(_,b)},[()=>(e(a),s(()=>e(a).timestamp.toLocaleTimeString()))]),l(v,c)});var ft=o(Jr,2);{var _t=v=>{var a=Ua(),c=t(a),w=t(c);at(w,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),r(c);var re=o(c,2),ce=o(t(re),2),S=t(ce);De(S,5,()=>e(j),V=>V.id,(V,_)=>{var te=La(),ye=t(te),Pe=t(ye);{var Le=E=>{var A=Ta(),ge=t(A);Vt(ge,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),r(A),et(3,A,()=>Bt,()=>({duration:200})),l(E,A)},fr=E=>{var A=Mr(),ge=Me(A);{var yr=pe=>{Xt(pe,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},Ue=pe=>{ea(pe,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};u(ge,pe=>{e(_),s(()=>e(_).status==="active")?pe(yr):pe(Ue,!1)},!0)}l(E,A)};u(Pe,E=>{e(_),s(()=>e(_).status==="completed")?E(Le):E(fr,!1)})}r(ye);var b=o(ye,2),I=t(b),X=t(I,!0);r(I);var ue=o(I,2),_r=t(ue,!0);r(ue);var br=o(ue,2);{var Ye=E=>{var A=Pa(),ge=t(A);r(A),g(()=>Qe(ge,`background: var(--primary); width: ${e(_),s(()=>e(_).progress)??""}%`)),l(E,A)};u(br,E=>{e(_),s(()=>e(_).status==="active"&&e(_).progress)&&E(Ye)})}r(b),r(te),g(()=>{Qe(I,`color: ${e(_),s(()=>e(_).status==="pending"?"var(--muted-foreground)":"var(--foreground)")??""};${e(_),s(()=>e(_).status==="pending"?"opacity: 0.5":"")??""}`),d(X,(e(_),s(()=>e(_).title))),Qe(ue,`color: var(--muted-foreground);${e(_),s(()=>e(_).status==="pending"?"opacity: 0.5":"")??""}`),d(_r,(e(_),s(()=>e(_).description)))}),et(3,te,()=>Gt,()=>({duration:300})),l(V,te)}),r(S);var G=o(S,2),L=t(G),U=o(t(L),2),f=t(U);r(U),r(L);var k=o(L,2),ke=t(k);r(k),r(G),r(ce),r(re),r(a),g(()=>{d(f,`${e(we)??""}%`),Qe(ke,`background: linear-gradient(to right, var(--primary), var(--accent)); width: ${e(we)??""}%`)}),l(v,a)};u(ft,v=>{e(K)&&v(_t)})}r(Gr),r(Kr),r(Fr),r(nr);var pr=o(nr,2),Hr=t(pr),Qr=t(Hr),mr=t(Qr),Te=t(mr);Pt(Te),r(mr);var We=o(mr,2),bt=t(We);{var yt=v=>{var a=Aa();l(v,a)},xt=v=>{Re(v,{class:"w-4 h-4 animate-pulse"})};u(bt,v=>{e(K)?v(yt):v(xt,!1)})}M(),r(We),r(Qr),r(Hr),r(pr),g(v=>{Vr(zr,"href",`/dashboard/${_e(),s(()=>_e().params.envSlug)??""}`),Xr(pr,1,`modern-input-area modern-input-area-seo ${e(K)?"opacity-50 pointer-events-none":""}`,"svelte-i1g7n4"),Vr(Te,"placeholder",e(dr)),Te.disabled=e(K),We.disabled=v},[()=>(e(F),e(K),s(()=>!e(F).trim()||e(K)))]),At(Te,()=>e(F),v=>R(F,v)),Ie("keydown",Te,lt),Ie("click",We,Er),l(Ee,Nr),jt(),je()}export{yo as component};
