import"../chunks/CWj6FrbW.js";import{o as p}from"../chunks/RnwjOPnl.js";import{p as m,u as f,b as c,d,a as s,e as h}from"../chunks/DDiqt3uM.js";import{s as b}from"../chunks/DtGADYZa.js";import{i as l}from"../chunks/Bo96ghGo.js";function M(n,a){m(a,!0);let{supabase:r,session:t}=a.data;f(()=>{({supabase:r,session:t}=a.data)}),p(()=>{const{data:i}=r.auth.onAuthStateChange((v,e)=>{(e==null?void 0:e.expires_at)!==(t==null?void 0:t.expires_at)&&l("supabase:auth")});return()=>i.subscription.unsubscribe()});var u=c(),o=d(u);b(o,()=>a.children),s(n,u),h()}export{M as component};
