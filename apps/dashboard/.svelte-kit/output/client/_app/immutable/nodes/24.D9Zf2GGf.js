import"../chunks/CWj6FrbW.js";import"../chunks/DhRTwODG.js";import{c as _t,o as yt}from"../chunks/RnwjOPnl.js";import{b as z,d as S,a as u,p as it,i as e,m as Z,be as Ke,j as h,l as zt,aT as At,g as Ht,f as T,c as i,r as o,s as p,t as de,h as y,e as lt,q as st,n as Xe,$ as It,bf as Ft,ak as tt}from"../chunks/DDiqt3uM.js";import{s as ae,e as E,r as Ut,h as Ot}from"../chunks/DWulv87v.js";import{i as J}from"../chunks/2C89X9tI.js";import{e as Ye,i as et}from"../chunks/OiKQa7Wx.js";import{r as $t,s as xt,d as Vt}from"../chunks/C36Ip9GY.js";import{b as dt}from"../chunks/DUGxtfU6.js";import{i as ct}from"../chunks/B_FgA42l.js";import{l as F,s as U,p as De}from"../chunks/C-ZVHnwW.js";import{a as wt,s as kt}from"../chunks/B82PTGnX.js";import{p as jt}from"../chunks/4KkXnDJG.js";import{g as rt}from"../chunks/Bm1TgOhB.js";import{s as Ge}from"../chunks/DE2v8SHj.js";import{b as mt}from"../chunks/Dqu9JXqq.js";import{d as ft}from"../chunks/Bf9nHHn7.js";import{s as O}from"../chunks/iCEqKm8o.js";import{I as V}from"../chunks/CkoRhfQ8.js";import{h as Bt}from"../chunks/CYhDwGx0.js";import{c as Rt}from"../chunks/BCmD-YNt.js";import{t as gt}from"../chunks/A4ulxp7Q.js";import{w as Gt}from"../chunks/rjRVMZXi.js";import{s as Kt,a as Qt}from"../chunks/DSm1r-pw.js";import{B as ht,U as Wt}from"../chunks/ng6E9JCw.js";import{X as Zt}from"../chunks/rNGuVYtO.js";import{S as St}from"../chunks/QFpxkGuO.js";import{C as Jt}from"../chunks/DR0c5pEj.js";import{L as bt}from"../chunks/DWMPsgkI.js";import{C as Xt}from"../chunks/oaGF9CiI.js";import{P as Yt}from"../chunks/BBWSBDCg.js";function er(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8"}]];V(m,U({name:"bold"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function tr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["circle",{cx:"12",cy:"12",r:"1"}],["circle",{cx:"12",cy:"5",r:"1"}],["circle",{cx:"12",cy:"19",r:"1"}]];V(m,U({name:"ellipsis-vertical"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function Ct(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M10 9H8"}],["path",{d:"M16 13H8"}],["path",{d:"M16 17H8"}]];V(m,U({name:"file-text"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function rr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M4 12h8"}],["path",{d:"M4 18V6"}],["path",{d:"M12 18V6"}],["path",{d:"m17 12 3-2v8"}]];V(m,U({name:"heading-1"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function nr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M4 12h8"}],["path",{d:"M4 18V6"}],["path",{d:"M12 18V6"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1"}]];V(m,U({name:"heading-2"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function or(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M4 12h8"}],["path",{d:"M4 18V6"}],["path",{d:"M12 18V6"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2"}]];V(m,U({name:"heading-3"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function ar(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["line",{x1:"19",x2:"10",y1:"4",y2:"4"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20"}]];V(m,U({name:"italic"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function sr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"}],["path",{d:"M9 18h6"}],["path",{d:"M10 22h4"}]];V(m,U({name:"lightbulb"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function ir(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["line",{x1:"10",x2:"21",y1:"6",y2:"6"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18"}],["path",{d:"M4 6h1v4"}],["path",{d:"M4 10h2"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"}]];V(m,U({name:"list-ordered"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function Mt(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["line",{x1:"8",x2:"21",y1:"6",y2:"6"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18"}]];V(m,U({name:"list"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function lr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}],["path",{d:"M15 3v18"}],["path",{d:"m8 9 3 3-3 3"}]];V(m,U({name:"panel-right-close"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function dr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}],["path",{d:"M15 3v18"}],["path",{d:"m10 15-3-3 3-3"}]];V(m,U({name:"panel-right-open"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function Dt(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18"}],["path",{d:"m2.3 2.3 7.286 7.286"}],["circle",{cx:"11",cy:"11",r:"2"}]];V(m,U({name:"pen-tool"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function Et(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z"}]];V(m,U({name:"quote"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function cr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M21 7v6h-6"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"}]];V(m,U({name:"redo"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function ur(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7"}]];V(m,U({name:"save"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function vr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"m22 2-7 20-4-9-9-4Z"}],["path",{d:"M22 2 11 13"}]];V(m,U({name:"send"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function pr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M3 6h18"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17"}]];V(m,U({name:"trash-2"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function mr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20"}]];V(m,U({name:"underline"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}function fr(m,t){const a=F(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M3 7v6h6"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"}]];V(m,U({name:"undo"},()=>a,{get iconNode(){return n},children:(r,f)=>{var s=z(),d=S(s);O(d,t,"default",{},null),u(r,s)},$$slots:{default:!0}}))}var gr=T('<span class="text-orange-500">Unsaved changes</span>'),hr=T("<span> </span>"),br=T('<div class="border-b border-border bg-card p-4"><div class="mb-4"><input class="w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-muted-foreground" placeholder="Document title..."/></div> <div class="flex items-center gap-2 flex-wrap"><div class="flex items-center gap-1 border-r border-border pr-2"><button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Bold (Ctrl+B)"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Italic (Ctrl+I)"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Underline (Ctrl+U)"><!></button></div> <div class="flex items-center gap-1 border-r border-border pr-2"><button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Heading 1"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Heading 2"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Heading 3"><!></button></div> <div class="flex items-center gap-1 border-r border-border pr-2"><button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Bullet List"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Numbered List"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Quote"><!></button></div> <div class="flex items-center gap-1 border-r border-border pr-2"><button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Undo (Ctrl+Z)"><!></button> <button type="button" class="p-2 hover:bg-muted rounded transition-colors" title="Redo (Ctrl+Shift+Z)"><!></button></div> <button type="button" class="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors" title="Save (Ctrl+S)"><!> <!></button></div> <div class="flex items-center justify-between mt-3 text-sm text-muted-foreground"><div class="flex items-center gap-4"><span> </span> <span> </span> <span> </span></div> <div class="flex items-center gap-2"><!></div></div></div> <div class="flex-1 p-6 overflow-y-auto"><div contenteditable="true"></div></div>',1);function _r(m,t){it(t,!1);let a=De(t,"content",12,""),n=De(t,"title",12,"Untitled Document"),r=De(t,"isLoading",8,!1),f=De(t,"autoSave",8,!0),s=De(t,"placeholder",8,"Start writing your content...");const d=_t();let C=Z(),j=Z(),P=Z(0),g=Z(0),H=new Date,q=Z(!1),B=Z(!1);const ke=ft(()=>{f()&&e(B)&&Le()},2e3),ce=ft(()=>{qe()},500);yt(()=>{e(C)&&(Ke(C,e(C).innerHTML=a()),qe())});function A(v,w){document.execCommand(v,!1,w),e(C).focus(),_e()}function _e(){e(C)&&(a(e(C).innerHTML),h(B,!0),ce(),ke(),d("contentChange",{content:a(),title:n()}))}function Pe(){h(B,!0),ke(),d("titleChange",{title:n()})}function qe(){if(e(C)){const v=e(C).innerText||"";h(P,v.trim()?v.trim().split(/\s+/).length:0),h(g,v.length)}}async function Le(){if(!e(q)){h(q,!0);try{await d("save",{content:a(),title:n()}),h(B,!1),H=new Date}catch(v){console.error("Save failed:",v)}finally{h(q,!1)}}}function ze(v){const w=window.getSelection();if(w&&w.rangeCount>0){const I=w.getRangeAt(0),me=document.createElement(`h${v}`);me.textContent=w.toString()||`Heading ${v}`,I.deleteContents(),I.insertNode(me),I.setStartAfter(me),I.collapse(!0),w.removeAllRanges(),w.addRange(I)}_e()}function je(v=!1){A(v?"insertOrderedList":"insertUnorderedList")}function Ue(){A("formatBlock","blockquote")}function R(v){if(v.ctrlKey||v.metaKey)switch(v.key){case"s":v.preventDefault(),Le();break;case"b":v.preventDefault(),A("bold");break;case"i":v.preventDefault(),A("italic");break;case"u":v.preventDefault(),A("underline");break;case"z":v.preventDefault(),v.shiftKey?A("redo"):A("undo");break}}function K(){return`${Math.ceil(e(P)/200)} min read`}function fe(){const w=new Date().getTime()-H.getTime(),I=Math.floor(w/6e4);if(I<1)return"Just now";if(I<60)return`${I}m ago`;const me=Math.floor(I/60);return me<24?`${me}h ago`:H.toLocaleDateString()}zt(()=>(e(C),At(a())),()=>{e(C)&&a()!==e(C).innerHTML&&(Ke(C,e(C).innerHTML=a()),qe())}),Ht(),ct();var ye=br(),te=S(ye),se=i(te),$e=i(se);$t($e),mt($e,v=>h(j,v),()=>e(j)),o(se);var ie=p(se,2),Ee=i(ie),ue=i(Ee),Se=i(ue);er(Se,{class:"w-4 h-4"}),o(ue);var ge=p(ue,2),Ae=i(ge);ar(Ae,{class:"w-4 h-4"}),o(ge);var ve=p(ge,2),D=i(ve);mr(D,{class:"w-4 h-4"}),o(ve),o(Ee);var G=p(Ee,2),le=i(G),Qe=i(le);rr(Qe,{class:"w-4 h-4"}),o(le);var Ne=p(le,2),Be=i(Ne);nr(Be,{class:"w-4 h-4"}),o(Ne);var M=p(Ne,2),$=i(M);or($,{class:"w-4 h-4"}),o(M),o(G);var X=p(G,2),L=i(X),Y=i(L);Mt(Y,{class:"w-4 h-4"}),o(L);var Q=p(L,2),he=i(Q);ir(he,{class:"w-4 h-4"}),o(Q);var re=p(Q,2),ne=i(re);Et(ne,{class:"w-4 h-4"}),o(re),o(X);var xe=p(X,2),l=i(xe),c=i(l);fr(c,{class:"w-4 h-4"}),o(l);var _=p(l,2),N=i(_);cr(N,{class:"w-4 h-4"}),o(_),o(xe);var b=p(xe,2),k=i(b);ur(k,{class:"w-4 h-4"});var W=p(k,2);{var x=v=>{var w=st("Saving...");u(v,w)},we=v=>{var w=z(),I=S(w);{var me=Me=>{var Ve=st("Save");u(Me,Ve)},ot=Me=>{var Ve=st("Saved");u(Me,Ve)};J(I,Me=>{e(B)?Me(me):Me(ot,!1)},!0)}u(v,w)};J(W,v=>{e(q)?v(x):v(we,!1)})}o(b),o(ie);var He=p(ie,2),We=i(He),Ze=i(We),nt=i(Ze);o(Ze);var ee=p(Ze,2),pe=i(ee);o(ee);var Ce=p(ee,2),Je=i(Ce,!0);o(Ce),o(We);var Oe=p(We,2),Ie=i(Oe);{var Te=v=>{var w=gr();u(v,w)},Re=v=>{var w=hr(),I=i(w);o(w),de(me=>ae(I,`Last saved: ${me??""}`),[()=>y(fe)]),u(v,w)};J(Ie,v=>{e(B)?v(Te):v(Re,!1)})}o(Oe),o(He),o(te);var oe=p(te,2),be=i(oe);let Fe;mt(be,v=>h(C,v),()=>e(C)),o(oe),de((v,w)=>{$e.disabled=r(),ue.disabled=r(),ge.disabled=r(),ve.disabled=r(),le.disabled=r(),Ne.disabled=r(),M.disabled=r(),L.disabled=r(),Q.disabled=r(),re.disabled=r(),l.disabled=r(),_.disabled=r(),b.disabled=r()||e(q)||!e(B),ae(nt,`${e(P)??""} words`),ae(pe,`${e(g)??""} characters`),ae(Je,v),Fe=Ge(be,1,`min-h-full outline-none prose prose-slate max-w-none
           prose-headings:text-foreground prose-p:text-foreground
           prose-strong:text-foreground prose-em:text-foreground
           prose-blockquote:text-muted-foreground prose-blockquote:border-border
           prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground
           focus:outline-none svelte-5h7mgy`,null,Fe,w),xt(be,"data-placeholder",s()),be.disabled=r()},[()=>y(K),()=>({empty:!a()})]),dt($e,n),E("input",$e,Pe),E("click",ue,()=>A("bold")),E("click",ge,()=>A("italic")),E("click",ve,()=>A("underline")),E("click",le,()=>ze(1)),E("click",Ne,()=>ze(2)),E("click",M,()=>ze(3)),E("click",L,()=>je(!1)),E("click",Q,()=>je(!0)),E("click",re,Ue),E("click",l,()=>A("undo")),E("click",_,()=>A("redo")),E("click",b,Le),E("input",be,_e),E("keydown",be,R),u(m,ye),lt()}var yr=T('<button class="p-3 text-left border border-border rounded hover:bg-muted transition-colors group"><div class="flex items-center gap-2 mb-1"><!> <span class="text-sm font-medium"> </span></div> <p class="text-xs text-muted-foreground"> </p></button>'),$r=T('<div class="p-4 border-b border-border"><h3 class="text-sm font-medium mb-3">Quick Actions</h3> <div class="grid grid-cols-2 gap-2"></div></div>'),xr=T('<div class="prose prose-sm max-w-none"><!></div>'),wr=T('<p class="text-sm"> </p>'),kr=T('<div><div><!></div> <div><div><!></div> <p class="text-xs text-muted-foreground mt-1"> </p></div></div>'),Sr=T('<div class="w-4 h-4 rounded-full border-2 border-muted-foreground/30"></div>'),Cr=T('<div class="flex items-center gap-2 p-2 rounded bg-muted/50"><!> <span> </span></div>'),Mr=T('<div class="space-y-2"></div>'),Dr=T('<div class="fixed inset-y-0 right-0 w-96 bg-card border-l border-border shadow-lg z-50 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-border"><div class="flex items-center gap-2"><!> <h2 class="font-semibold">Content Agent</h2></div> <button class="p-1 hover:bg-muted rounded transition-colors"><!></button></div> <!> <div class="flex-1 overflow-y-auto p-4 space-y-4"><!> <!></div> <div class="p-4 border-t border-border"><div class="flex gap-2"><textarea placeholder="Ask the Content Agent for help..." class="flex-1 resize-none border border-border rounded px-3 py-2 text-sm bg-background focus:outline-none focus:ring-2 focus:ring-primary" rows="2"></textarea> <button class="px-3 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"><!></button></div></div></div>');function Er(m,t){it(t,!1);const[a,n]=wt(),r=()=>kt(P,"$messages",a);let f=De(t,"isOpen",12,!1),s=De(t,"documentId",8,null),d=De(t,"currentContent",8,""),C=De(t,"envSlug",8);const j=_t(),P=Gt([]);let g=Z(""),H=Z(!1),q=Z([]),B=0;const ke=[{id:"generate-outline",title:"Generate Outline",description:"Create a structured outline for your content",icon:Mt,prompt:"Generate a detailed outline for this content topic"},{id:"improve-writing",title:"Improve Writing",description:"Enhance grammar, style, and clarity",icon:Dt,prompt:"Please review and improve the grammar, style, and clarity of this content"},{id:"summarize",title:"Summarize",description:"Create a concise summary",icon:Ct,prompt:"Please create a concise summary of this content"},{id:"research",title:"Research Topic",description:"Find relevant information and sources",icon:St,prompt:"Research this topic and provide relevant information with sources"},{id:"add-citations",title:"Add Citations",description:"Generate proper citations for sources",icon:Et,prompt:"Help me add proper citations to this content"},{id:"brainstorm",title:"Brainstorm Ideas",description:"Generate creative ideas and suggestions",icon:sr,prompt:"Help me brainstorm ideas to expand on this content"}];function ce(){return Math.random().toString(36).substr(2,9)}async function A(R,K){var ye;const fe=R||e(g).trim();if(!(!fe||e(H))){h(g,""),h(H,!0),h(q,[]),B=0,P.update(te=>[...te,{id:ce(),role:"user",content:fe,timestamp:new Date,action:K}]);try{const te=await fetch(`/dashboard/${C()}/content-agent?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:fe,documentId:s(),action:K,currentContent:d()})});if(!te.ok)throw new Error(`HTTP error! status: ${te.status}`);const se=(ye=te.body)==null?void 0:ye.getReader(),$e=new TextDecoder;if(!se)throw new Error("No response body");let ie="",Ee=ce();for(;;){const{done:ue,value:Se}=await se.read();if(ue)break;const Ae=$e.decode(Se).split(`
`);for(const ve of Ae)if(ve.startsWith("data: "))try{const D=JSON.parse(ve.slice(6));if(D.step!==void 0){B=D.progress||0;const G=e(q).findIndex(le=>le.id===D.step);G>=0?Ke(q,e(q)[G]={...e(q)[G],status:D.status,progress:D.progress}):h(q,[...e(q),{id:D.step,title:D.action,description:D.action,status:D.status,progress:D.progress}])}if(D.response&&(ie=D.response),D.status==="completed"&&ie&&(P.update(G=>[...G,{id:Ee,role:"assistant",content:ie,timestamp:new Date,action:K}]),(K==="generate-outline"||K==="generate-content")&&j("contentGenerated",{content:ie,action:K})),D.status==="error")throw new Error(D.error||"Unknown error occurred")}catch(D){console.warn("Failed to parse SSE data:",D)}}}catch(te){console.error("Error sending message:",te),P.update(se=>[...se,{id:ce(),role:"assistant",content:"I apologize, but I encountered an error while processing your request. Please try again.",timestamp:new Date}])}finally{h(H,!1),h(q,[]),B=0}}}function _e(R){let K=R.prompt;d()&&d().trim()&&(K+=`

Current content:
${d()}`),A(K,R.id)}function Pe(R){R.key==="Enter"&&!R.shiftKey&&(R.preventDefault(),A())}function qe(R){return R.replace(/^### (.+)$/gm,'<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>').replace(/^## (.+)$/gm,'<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>').replace(/^# (.+)$/gm,'<h1 class="text-2xl font-bold mb-4">$1</h1>').replace(/^\* (.+)$/gm,'<li class="ml-4">• $1</li>').replace(/^- (.+)$/gm,'<li class="ml-4">• $1</li>').replace(/\*\*(.+?)\*\*/g,"<strong>$1</strong>").replace(/\*(.+?)\*/g,"<em>$1</em>").replace(/\n\n/g,'</p><p class="mb-4">').replace(/^/,'<p class="mb-4">').replace(/$/,"</p>")}function Le(){f(!1),j("close")}ct();var ze=z(),je=S(ze);{var Ue=R=>{var K=Dr(),fe=i(K),ye=i(fe),te=i(ye);ht(te,{class:"w-5 h-5 text-primary"}),Xe(2),o(ye);var se=p(ye,2),$e=i(se);Zt($e,{class:"w-4 h-4"}),o(se),o(fe);var ie=p(fe,2);{var Ee=M=>{var $=$r(),X=p(i($),2);Ye(X,5,()=>ke,et,(L,Y)=>{var Q=yr(),he=i(Q),re=i(he);Rt(re,()=>e(Y).icon,(_,N)=>{N(_,{class:"w-4 h-4 text-primary"})});var ne=p(re,2),xe=i(ne,!0);o(ne),o(he);var l=p(he,2),c=i(l,!0);o(l),o(Q),de(()=>{Q.disabled=e(H),ae(xe,(e(Y),y(()=>e(Y).title))),ae(c,(e(Y),y(()=>e(Y).description)))}),E("click",Q,()=>_e(e(Y))),u(L,Q)}),o(X),o($),u(M,$)};J(ie,M=>{r(),y(()=>r().length===0)&&M(Ee)})}var ue=p(ie,2),Se=i(ue);Ye(Se,1,r,et,(M,$)=>{var X=kr(),L=i(X),Y=i(L);{var Q=b=>{Wt(b,{class:"w-4 h-4 text-primary-foreground"})},he=b=>{ht(b,{class:"w-4 h-4 text-secondary-foreground"})};J(Y,b=>{e($),y(()=>e($).role==="user")?b(Q):b(he,!1)})}o(L);var re=p(L,2),ne=i(re),xe=i(ne);{var l=b=>{var k=xr(),W=i(k);Bt(W,()=>(e($),y(()=>qe(e($).content)))),o(k),u(b,k)},c=b=>{var k=wr(),W=i(k,!0);o(k),de(()=>ae(W,(e($),y(()=>e($).content)))),u(b,k)};J(xe,b=>{e($),y(()=>e($).role==="assistant")?b(l):b(c,!1)})}o(ne);var _=p(ne,2),N=i(_,!0);o(_),o(re),o(X),de(b=>{Ge(X,1,`flex gap-3 ${e($),y(()=>e($).role==="user"?"flex-row-reverse":"")??""}`),Ge(L,1,`w-8 h-8 flex-shrink-0 flex items-center justify-center rounded-full border-2 border-border bg-${e($),y(()=>e($).role==="user"?"primary":"secondary")??""}`),Ge(re,1,`flex-1 ${e($),y(()=>e($).role==="user"?"text-right":"")??""}`),Ge(ne,1,`inline-block max-w-full p-3 rounded-lg ${e($),y(()=>e($).role==="user"?"bg-primary text-primary-foreground":"bg-muted")??""}`),ae(N,b)},[()=>(e($),y(()=>e($).timestamp.toLocaleTimeString()))]),u(M,X)});var ge=p(Se,2);{var Ae=M=>{var $=Mr();Ye($,5,()=>e(q),et,(X,L)=>{var Y=Cr(),Q=i(Y);{var he=l=>{Jt(l,{class:"w-4 h-4 text-green-500"})},re=l=>{var c=z(),_=S(c);{var N=k=>{bt(k,{class:"w-4 h-4 animate-spin text-primary"})},b=k=>{var W=Sr();u(k,W)};J(_,k=>{e(L),y(()=>e(L).status==="active")?k(N):k(b,!1)},!0)}u(l,c)};J(Q,l=>{e(L),y(()=>e(L).status==="completed")?l(he):l(re,!1)})}var ne=p(Q,2),xe=i(ne,!0);o(ne),o(Y),de(()=>{Ge(ne,1,`text-sm ${e(L),y(()=>e(L).status==="completed"?"text-muted-foreground":"text-foreground")??""}`),ae(xe,(e(L),y(()=>e(L).title)))}),u(X,Y)}),o($),gt(3,$,()=>Qt),u(M,$)};J(ge,M=>{e(H),e(q),y(()=>e(H)&&e(q).length>0)&&M(Ae)})}o(ue);var ve=p(ue,2),D=i(ve),G=i(D);Ut(G);var le=p(G,2),Qe=i(le);{var Ne=M=>{bt(M,{class:"w-4 h-4 animate-spin"})},Be=M=>{vr(M,{class:"w-4 h-4"})};J(Qe,M=>{e(H)?M(Ne):M(Be,!1)})}o(le),o(D),o(ve),o(K),de(M=>{G.disabled=e(H),le.disabled=M},[()=>(e(g),e(H),y(()=>!e(g).trim()||e(H)))]),E("click",se,Le),dt(G,()=>e(g),M=>h(g,M)),E("keydown",G,Pe),E("click",le,()=>A()),gt(3,K,()=>Kt,()=>({duration:300,axis:"x"})),u(R,K)};J(je,R=>{f()&&R(Ue)})}u(m,ze),lt(),n()}class Nr{constructor(t){this.supabase=t}async createDocument(t){const{data:a,error:n}=await this.supabase.from("content_documents").insert(t).select().single();if(n)throw console.error("Error creating document:",n),new Error(`Failed to create document: ${n.message}`);return a}async getDocument(t,a){const{data:n,error:r}=await this.supabase.from("content_documents").select("*").eq("id",t).eq("user_id",a).single();if(r){if(r.code==="PGRST116")return null;throw console.error("Error fetching document:",r),new Error(`Failed to fetch document: ${r.message}`)}return n}async updateDocument(t,a,n){const{data:r,error:f}=await this.supabase.from("content_documents").update(n).eq("id",t).eq("user_id",a).select().single();if(f)throw console.error("Error updating document:",f),new Error(`Failed to update document: ${f.message}`);return r}async deleteDocument(t,a){const{error:n}=await this.supabase.from("content_documents").delete().eq("id",t).eq("user_id",a);if(n)throw console.error("Error deleting document:",n),new Error(`Failed to delete document: ${n.message}`);return!0}async getUserDocuments(t,a,n={}){const{limit:r=50,offset:f=0,status:s,contentType:d,orderBy:C="updated_at",orderDirection:j="desc"}=n;let P=this.supabase.from("content_documents").select("*").eq("user_id",t).eq("environment_id",a);s&&(P=P.eq("status",s)),d&&(P=P.eq("content_type",d)),P=P.order(C,{ascending:j==="asc"}).range(f,f+r-1);const{data:g,error:H}=await P;if(H)throw console.error("Error fetching user documents:",H),new Error(`Failed to fetch documents: ${H.message}`);return g||[]}async searchDocuments(t,a,n,r={}){const{limit:f=20,contentType:s}=r;let d=this.supabase.from("content_documents").select("*").eq("user_id",t).eq("environment_id",a).or(`title.ilike.%${n}%,content->>'text'.ilike.%${n}%`);s&&(d=d.eq("content_type",s)),d=d.order("updated_at",{ascending:!1}).limit(f);const{data:C,error:j}=await d;if(j)throw console.error("Error searching documents:",j),new Error(`Failed to search documents: ${j.message}`);return C||[]}async createSession(t){const{data:a,error:n}=await this.supabase.from("content_sessions").insert(t).select().single();if(n)throw console.error("Error creating session:",n),new Error(`Failed to create session: ${n.message}`);return a}async getSession(t,a){const{data:n,error:r}=await this.supabase.from("content_sessions").select("*").eq("id",t).eq("user_id",a).single();if(r){if(r.code==="PGRST116")return null;throw console.error("Error fetching session:",r),new Error(`Failed to fetch session: ${r.message}`)}return n}async updateSession(t,a,n){const{data:r,error:f}=await this.supabase.from("content_sessions").update(n).eq("id",t).eq("user_id",a).select().single();if(f)throw console.error("Error updating session:",f),new Error(`Failed to update session: ${f.message}`);return r}async getDocumentSessions(t,a){const{data:n,error:r}=await this.supabase.from("content_sessions").select("*").eq("document_id",t).eq("user_id",a).order("created_at",{ascending:!1});if(r)throw console.error("Error fetching document sessions:",r),new Error(`Failed to fetch sessions: ${r.message}`);return n||[]}async addCitation(t){const{data:a,error:n}=await this.supabase.from("content_citations").insert(t).select().single();if(n)throw console.error("Error adding citation:",n),new Error(`Failed to add citation: ${n.message}`);return a}async getDocumentCitations(t){const{data:a,error:n}=await this.supabase.from("content_citations").select("*").eq("document_id",t).order("position_in_content",{ascending:!0});if(n)throw console.error("Error fetching citations:",n),new Error(`Failed to fetch citations: ${n.message}`);return a||[]}async updateCitation(t,a){const{data:n,error:r}=await this.supabase.from("content_citations").update(a).eq("id",t).select().single();if(r)throw console.error("Error updating citation:",r),new Error(`Failed to update citation: ${r.message}`);return n}async deleteCitation(t){const{error:a}=await this.supabase.from("content_citations").delete().eq("id",t);if(a)throw console.error("Error deleting citation:",a),new Error(`Failed to delete citation: ${a.message}`);return!0}async getDocumentStats(t,a){const{data:n,error:r}=await this.supabase.from("content_documents").select("status, content_type").eq("user_id",t).eq("environment_id",a);if(r)throw console.error("Error fetching document stats:",r),new Error(`Failed to fetch document stats: ${r.message}`);const f={total:(n==null?void 0:n.length)||0,byStatus:{},byType:{}};return n==null||n.forEach(s=>{f.byStatus[s.status]=(f.byStatus[s.status]||0)+1,f.byType[s.content_type]=(f.byType[s.content_type]||0)+1}),f}}var Tr=T('<div class="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full"> </div>'),Pr=T("<!> Close Agent",1),qr=T("<!> Open Agent",1),Lr=T('<button class="flex items-center gap-2 px-4 py-2 text-sm border-2 hover:scale-105 transition-transform" style="background: var(--secondary); border-color: var(--border); color: var(--secondary-foreground); box-shadow: var(--shadow-sm);">← Back to Documents</button> <button class="flex items-center gap-2 px-4 py-2 border-2 hover:scale-105 transition-transform" style="background: var(--primary); border-color: var(--border); color: var(--primary-foreground); box-shadow: var(--shadow-sm);"><!></button>',1),zr=T("<option> </option>"),Ar=T('<div class="flex items-center justify-center py-12"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div>'),Hr=T('<button class="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">Create Document</button>'),Ir=T('<div class="text-center py-12"><!> <h3 class="text-lg font-medium mb-2">No documents found</h3> <p class="text-muted-foreground mb-4"> </p> <!></div>'),Fr=T('<div class="border border-border rounded-lg p-4 hover:shadow-md transition-shadow bg-card"><div class="flex items-start justify-between mb-3"><h3 class="font-medium truncate flex-1 mr-2"> </h3> <div class="relative"><button class="p-1 hover:bg-muted rounded"><!></button></div></div> <div class="flex items-center gap-2 text-sm text-muted-foreground mb-3"><span class="px-2 py-1 bg-muted rounded text-xs"> </span> <span>•</span> <span> </span></div> <div class="flex items-center justify-between"><button class="text-primary hover:text-primary/80 text-sm font-medium">Open →</button> <button class="p-1 text-muted-foreground hover:text-destructive rounded"><!></button></div></div>'),Ur=T('<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"></div>'),Or=T('<div class="h-full"><div class="flex items-center justify-between mb-6"><div class="flex items-center gap-4"><div class="relative"><!> <input placeholder="Search documents..." class="pl-10 pr-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"/></div> <select class="px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"></select></div> <button class="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"><!> New Document</button></div> <!></div>'),Vr=T('<div class="h-full flex"><div class="flex-1 flex flex-col"><!></div> <!></div>'),jr=T('<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div><h1 class="text-3xl font-black" style="color: var(--foreground);">Nexus</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Your AI content creator</p></div> <!></div> <div class="flex items-center space-x-4"><!></div></div></div></div> <div class="max-w-7xl mx-auto px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">Nexus</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><!></div></div>');function xn(m,t){it(t,!1);const[a,n]=wt(),r=()=>kt(jt,"$page",a);let f=De(t,"data",8),{session:s,supabase:d}=f(),C=new Nr(d),j=Z(null),P=Z(!1),g=Z(null),H=[],q=Z(!1),B=Z(""),ke=Z("all"),ce=Z(!0),A=Z(""),_e="",Pe="",qe=Z(!1);const Le=[{value:"all",label:"All Types"},{value:"article",label:"Article"},{value:"blog-post",label:"Blog Post"},{value:"whitepaper",label:"Whitepaper"},{value:"document",label:"Document"},{value:"email",label:"Email"},{value:"social-media",label:"Social Media"},{value:"presentation",label:"Presentation"},{value:"report",label:"Report"},{value:"essay",label:"Essay"},{value:"tutorial",label:"Tutorial"},{value:"guide",label:"Guide"},{value:"proposal",label:"Proposal"}];yt(async()=>{await ze();const l=r().url.searchParams;h(A,l.get("topic")||""),_e=l.get("type")||"article",Pe=l.get("audience")||"general",e(A)&&_e&&Pe?(h(qe,!0),await je()):await Ue()});async function ze(){try{const{data:l,error:c}=await d.from("environments").select("*").eq("slug",r().params.envSlug).single();if(c||!l){console.error("Environment error:",c),rt("/dashboard");return}const{data:_}=await d.from("environments_profiles").select("*").eq("environment_id",l.id).eq("profile_id",s.user.id).single();if(!_){rt("/dashboard");return}h(j,l)}catch(l){console.error("Error loading environment:",l),rt("/dashboard")}}async function je(){try{h(q,!0),h(ce,!1);const l=await C.createDocument({title:`${e(A)} - ${_e}`,content:"",content_type:_e,target_audience:Pe,user_id:s.user.id,environment_id:e(j).id});l&&(h(g,l),h(P,!0),setTimeout(()=>{},500))}catch(l){console.error("Error creating document with auto-outline:",l)}finally{h(q,!1)}}async function Ue(){try{h(q,!0),H=await C.getUserDocuments(s.user.id,e(j).id,{limit:50,contentType:e(ke)==="all"?void 0:e(ke)})}catch(l){console.error("Error loading documents:",l)}finally{h(q,!1)}}async function R(){var l;try{if(!e(j)){console.error("Environment not loaded");return}if(!((l=s==null?void 0:s.user)!=null&&l.id)){console.error("User session not available");return}const c=await C.createDocument({title:"Untitled Document",content:"",content_type:"article",target_audience:"general",user_id:s.user.id,environment_id:e(j).id});c&&(h(g,c),h(ce,!1))}catch(c){console.error("Error creating document:",c),alert(`Failed to create document: ${c.message}`)}}async function K(l){h(g,l),h(ce,!1)}async function fe(l){if(e(g))try{const{content:c,title:_}=l.detail,b=c.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim().split(" ").filter(W=>W.length>0).length,k=Math.ceil(b/200);await C.updateDocument(e(g).id,s.user.id,{title:_,content:c,word_count:b,reading_time:k,updated_at:new Date().toISOString()}),h(g,{...e(g),title:_,content:c,word_count:b,reading_time:k})}catch(c){throw console.error("Error saving document:",c),c}}async function ye(l){var c;if(confirm("Are you sure you want to delete this document?"))try{await C.deleteDocument(l,s.user.id),await Ue(),((c=e(g))==null?void 0:c.id)===l&&(h(g,null),h(ce,!0))}catch(_){console.error("Error deleting document:",_)}}function te(){h(P,!e(P))}function se(){h(g,null),h(ce,!0),(e(A)||_e||Pe)&&rt(`/dashboard/${r().params.envSlug}/content-agent`,{replaceState:!0})}function $e(l){const{content:c,action:_}=l.detail;if(e(g)&&c){let N=c;typeof c=="string"&&!c.includes("<")&&(N=c.split(`
`).map(b=>b.trim()).filter(b=>b.length>0).map(b=>`<p>${b}</p>`).join("")),Ke(g,e(g).content=N),h(g,{...e(g)}),fe({detail:{content:N,title:e(g).title}})}}function ie(){let l=H;return e(B)&&(l=l.filter(c=>{var _,N;return c.title.toLowerCase().includes(e(B).toLowerCase())||((N=(_=c.content)==null?void 0:_.text)==null?void 0:N.toLowerCase().includes(e(B).toLowerCase()))})),l}function Ee(l){return new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function ue(l){const c=Le.find(_=>_.value===l);return c?c.label:l}ct();var Se=jr();Ot(l=>{de(()=>It.title=`Nexus - AI Content Assistant - ${e(j),y(()=>{var c;return((c=e(j))==null?void 0:c.name)||"Loading..."})??""}`)});var ge=i(Se),Ae=i(ge),ve=i(Ae),D=i(ve),G=i(D),le=i(G);Dt(le,{class:"w-6 h-6",style:"color: var(--primary-foreground);"}),o(G);var Qe=p(G,4);{var Ne=l=>{var c=Tr(),_=i(c);o(c),de(()=>ae(_,`Auto-generating outline for: ${e(A)??""}`)),u(l,c)};J(Qe,l=>{e(qe)&&l(Ne)})}o(D);var Be=p(D,2),M=i(Be);{var $=l=>{var c=Lr(),_=S(c),N=p(_,2),b=i(N);{var k=x=>{var we=Pr(),He=S(we);lr(He,{class:"w-4 h-4"}),Xe(),u(x,we)},W=x=>{var we=qr(),He=S(we);dr(He,{class:"w-4 h-4"}),Xe(),u(x,we)};J(b,x=>{e(P)?x(k):x(W,!1)})}o(N),E("click",_,se),E("click",N,te),u(l,c)};J(M,l=>{e(ce)||l($)})}o(Be),o(ve),o(Ae),o(ge);var X=p(ge,2),L=i(X),Y=i(L),Q=p(Y,2);Xt(Q,{class:"w-4 h-4"}),Xe(2),o(L),o(X);var he=p(X,2),re=i(he);{var ne=l=>{var c=Or(),_=i(c),N=i(_),b=i(N),k=i(b);St(k,{class:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"});var W=p(k,2);$t(W),o(b);var x=p(b,2);de(()=>{e(ke),Ft(()=>{})}),Ye(x,5,()=>Le,et,(ee,pe)=>{var Ce=zr(),Je=i(Ce,!0);o(Ce);var Oe={};de(()=>{ae(Je,(e(pe),y(()=>e(pe).label))),Oe!==(Oe=(e(pe),y(()=>e(pe).value)))&&(Ce.value=(Ce.__value=(e(pe),y(()=>e(pe).value)))??"")}),u(ee,Ce)}),o(x),o(N);var we=p(N,2),He=i(we);Yt(He,{class:"w-4 h-4"}),Xe(),o(we),o(_);var We=p(_,2);{var Ze=ee=>{var pe=Ar();u(ee,pe)},nt=ee=>{var pe=z(),Ce=S(pe);{var Je=Ie=>{var Te=Ir(),Re=i(Te);Ct(Re,{class:"w-12 h-12 mx-auto mb-4 text-muted-foreground"});var oe=p(Re,4),be=i(oe,!0);o(oe);var Fe=p(oe,2);{var v=w=>{var I=Hr();E("click",I,R),u(w,I)};J(Fe,w=>{e(B)||w(v)})}o(Te),de(()=>ae(be,e(B)?"Try adjusting your search terms":"Create your first document to get started")),u(Ie,Te)},Oe=Ie=>{var Te=Ur();Ye(Te,5,()=>y(ie),et,(Re,oe)=>{var be=Fr(),Fe=i(be),v=i(Fe),w=i(v,!0);o(v);var I=p(v,2),me=i(I),ot=i(me);tr(ot,{class:"w-4 h-4"}),o(me),o(I),o(Fe);var Me=p(Fe,2),Ve=i(Me),Nt=i(Ve,!0);o(Ve);var ut=p(Ve,4),Tt=i(ut,!0);o(ut),o(Me);var vt=p(Me,2),pt=i(vt),at=p(pt,2),Pt=i(at);pr(Pt,{class:"w-4 h-4"}),o(at),o(vt),o(be),de((qt,Lt)=>{ae(w,(e(oe),y(()=>e(oe).title))),ae(Nt,qt),ae(Tt,Lt)},[()=>(e(oe),y(()=>ue(e(oe).content_type))),()=>(e(oe),y(()=>Ee(e(oe).updated_at)))]),E("click",pt,()=>K(e(oe))),E("click",at,()=>ye(e(oe).id)),u(Re,be)}),o(Te),u(Ie,Te)};J(Ce,Ie=>{y(()=>ie().length===0)?Ie(Je):Ie(Oe,!1)},!0)}u(ee,pe)};J(We,ee=>{e(q)?ee(Ze):ee(nt,!1)})}o(c),dt(W,()=>e(B),ee=>h(B,ee)),Vt(x,()=>e(ke),ee=>h(ke,ee)),E("change",x,Ue),E("click",we,R),u(l,c)},xe=l=>{var c=Vr(),_=i(c),N=i(_);{let k=tt(()=>(e(g),y(()=>{var x;return((x=e(g))==null?void 0:x.content)||""}))),W=tt(()=>(e(g),y(()=>{var x;return((x=e(g))==null?void 0:x.title)||""})));_r(N,{get content(){return e(k)},get title(){return e(W)},get isLoading(){return e(q)},$$events:{save:fe,contentChange:x=>{e(g)&&Ke(g,e(g).content=x.detail.content)},titleChange:x=>{e(g)&&Ke(g,e(g).title=x.detail.title)}}})}o(_);var b=p(_,2);{let k=tt(()=>(e(g),y(()=>{var x;return(x=e(g))==null?void 0:x.id}))),W=tt(()=>(e(g),y(()=>{var x;return((x=e(g))==null?void 0:x.content)||""})));Er(b,{get documentId(){return e(k)},get currentContent(){return e(W)},get envSlug(){return r(),y(()=>r().params.envSlug)},get isOpen(){return e(P)},set isOpen(x){h(P,x)},$$events:{close:()=>h(P,!1),contentGenerated:$e},$$legacy:!0})}o(c),u(l,c)};J(re,l=>{e(ce)?l(ne):l(xe,!1)})}o(he),o(Se),de(()=>xt(Y,"href",`/dashboard/${r(),y(()=>r().params.envSlug)??""}`)),u(m,Se),lt(),n()}export{xn as component};
