var kt=Object.defineProperty;var Ot=(f,e,E)=>e in f?kt(f,e,{enumerable:!0,configurable:!0,writable:!0,value:E}):f[e]=E;var He=(f,e,E)=>Ot(f,typeof e!="symbol"?e+"":e,E);import"../chunks/CWj6FrbW.js";import{b as X,d as M,a as n,W as Tt,S as St,U as Et,p as Te,l as ae,aT as ue,g as Me,e as Se,j as be,m as Le,i as t,f as L,c as A,r as S,ac as l,ak as bt,h as mt,s as de,n as Pt,u as Ke,I as qe,al as Dt,t as De,q as et}from"../chunks/DDiqt3uM.js";import{e as d,d as At,s as Ge}from"../chunks/DWulv87v.js";import{s as Ft}from"../chunks/DtGADYZa.js";import{i as G}from"../chunks/2C89X9tI.js";import{e as lt,i as dt}from"../chunks/OiKQa7Wx.js";import{c as tt}from"../chunks/BCmD-YNt.js";import{a as fe,b as jt,s as Ce}from"../chunks/C36Ip9GY.js";import{s as ze}from"../chunks/DE2v8SHj.js";import{s as Oe,a as Re}from"../chunks/B82PTGnX.js";import{a as It,f as Rt}from"../chunks/DSm1r-pw.js";import"../chunks/ChutyBgo.js";import"../chunks/DhRTwODG.js";import{o as zt,w as Nt,m as Fe,e as at,a as Xe,k as Ye,f as Mt,u as ct,n as je,s as ut,g as Ne,p as Vt,i as ft,h as Lt}from"../chunks/BGh_Dfnt.js";import{s as ie}from"../chunks/iCEqKm8o.js";import{i as Ae}from"../chunks/B_FgA42l.js";import{l as se,s as Qe,p as g}from"../chunks/C-ZVHnwW.js";import{w as Ut,d as ht}from"../chunks/rjRVMZXi.js";import{t as vt,g as Wt,o as Bt,r as Ht,a as Kt}from"../chunks/CMLKS1ED.js";import{u as qt,c as Gt,g as Xt,a as Yt,h as gt,r as Jt,S as Qt}from"../chunks/CsZUEjWq.js";import{c as Zt,a as _t}from"../chunks/DaUg0vjO.js";import{a as ve}from"../chunks/D-ywOz1J.js";import{b as ge}from"../chunks/Dqu9JXqq.js";import{p as $t}from"../chunks/CF5x3pQ4.js";import{g as ea}from"../chunks/CaxpRkM3.js";import{M as ta}from"../chunks/Bxi4P_5L.js";import{t as we}from"../chunks/A4ulxp7Q.js";import{b as u}from"../chunks/B9BVeOQN.js";import{X as aa}from"../chunks/rNGuVYtO.js";import{c as Je,f as ra}from"../chunks/Bf9nHHn7.js";import{C as na}from"../chunks/oaGF9CiI.js";import{I as oa}from"../chunks/CkoRhfQ8.js";import{L as ia,S as sa}from"../chunks/Rh-W-Viu.js";function la(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"m15 18-6-6 6-6"}]];oa(f,Qe({name:"chevron-left"},()=>E,{get iconNode(){return k},children:(y,U)=>{var m=X(),a=M(m);ie(a,e,"default",{},null),n(y,m)},$$slots:{default:!0}}))}const{name:Ie}=Mt("dialog"),da={preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,role:"dialog",defaultOpen:!1,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},ca=["content","title","description"];function ua(f){const e={...da,...f},E=vt(zt(e,"ids")),{preventScroll:k,closeOnEscape:y,closeOnOutsideClick:U,role:m,portal:a,forceVisible:o,openFocus:P,closeFocus:Z,onOutsideClick:V}=E,O=Nt.writable(null),x=vt({...Wt(ca),...e.ids}),Y=e.open??Ut(e.defaultOpen),h=Bt(Y,e==null?void 0:e.onOpenChange),C=ht([h,o],([i,D])=>i||D);let w=je;function v(i){const D=i.currentTarget,_=i.currentTarget;!ft(D)||!ft(_)||(h.set(!0),O.set(_))}function s(){h.set(!1),gt({prop:Z.get(),defaultEl:O.get()})}const F=Fe(Ie("trigger"),{stores:[h],returned:([i])=>({"aria-haspopup":"dialog","aria-expanded":i,type:"button"}),action:i=>({destroy:at(Xe(i,"click",_=>{v(_)}),Xe(i,"keydown",_=>{_.key!==Ye.ENTER&&_.key!==Ye.SPACE||(_.preventDefault(),v(_))}))})}),z=Fe(Ie("overlay"),{stores:[C,h],returned:([i,D])=>({hidden:i?void 0:!0,tabindex:-1,style:ut({display:i?void 0:"none"}),"aria-hidden":!0,"data-state":D?"open":"closed"}),action:i=>{let D=je;if(y.get()){const _=ct(i,{handler:()=>{s()}});_&&_.destroy&&(D=_.destroy)}return{destroy(){D()}}}}),re=Fe(Ie("content"),{stores:[C,x.content,x.description,x.title,h],returned:([i,D,_,$,H])=>({id:D,role:m.get(),"aria-describedby":_,"aria-labelledby":$,"aria-modal":i?"true":void 0,"data-state":H?"open":"closed",tabindex:-1,hidden:i?void 0:!0,style:ut({display:i?void 0:"none"})}),action:i=>{let D=je,_=je;const $=at(Ne([h,U,y],([H,J,j])=>{if(!H)return;const Q=Gt({immediate:!1,escapeDeactivates:j,clickOutsideDeactivates:J,allowOutsideClick:!0,returnFocusOnDeactivate:!1,fallbackFocus:i});D=Q.activate,_=Q.deactivate;const T=Q.useFocusTrap(i);return T&&T.destroy?T.destroy:Q.deactivate}),Ne([U,h],([H,J])=>qt(i,{open:J,closeOnInteractOutside:H,onClose(){s()},shouldCloseOnInteractOutside(j){var Q;return(Q=V.get())==null||Q(j),!j.defaultPrevented}}).destroy),Ne([y],([H])=>H?ct(i,{handler:s}).destroy:je),Ne([C],([H])=>{Tt().then(()=>{H?D():_()})}));return{destroy:()=>{w(),$()}}}}),ce=Fe(Ie("portalled"),{stores:a,returned:i=>({"data-portal":Vt(i)}),action:i=>{const D=Ne([a],([_])=>{if(_===null)return je;const $=Xt(i,_);return $===null?je:Yt(i,$).destroy});return{destroy(){D()}}}}),pe=Fe(Ie("title"),{stores:[x.title],returned:([i])=>({id:i})}),me=Fe(Ie("description"),{stores:[x.description],returned:([i])=>({id:i})}),_e=Fe(Ie("close"),{returned:()=>({type:"button"}),action:i=>({destroy:at(Xe(i,"click",()=>{s()}),Xe(i,"keydown",_=>{_.key!==Ye.SPACE&&_.key!==Ye.ENTER||(_.preventDefault(),s())}))})});return Ne([h,k],([i,D])=>{if(Lt){if(D&&i&&(w=Jt()),i){const _=document.getElementById(x.content.get());gt({prop:P.get(),defaultEl:_})}return()=>{o.get()||w()}}}),{ids:x,elements:{content:re,trigger:F,title:pe,description:me,overlay:z,close:_e,portalled:ce},states:{open:h},options:E}}function pt(){return{NAME:"dialog",PARTS:["close","content","description","overlay","portal","title","trigger"]}}function fa(f){const{NAME:e,PARTS:E}=pt(),k=Zt(e,E),y={...ua({...Ht(f),role:"dialog",forceVisible:!0}),getAttrs:k};return Et(e,y),{...y,updateOption:Kt(y.options)}}function Ue(){const{NAME:f}=pt();return St(f)}function va(f,e){Te(e,!1);const[E,k]=Re(),y=()=>Oe(w,"$idValues",E);let U=g(e,"preventScroll",24,()=>{}),m=g(e,"closeOnEscape",24,()=>{}),a=g(e,"closeOnOutsideClick",24,()=>{}),o=g(e,"portal",24,()=>{}),P=g(e,"open",28,()=>{}),Z=g(e,"onOpenChange",24,()=>{}),V=g(e,"openFocus",24,()=>{}),O=g(e,"closeFocus",24,()=>{}),x=g(e,"onOutsideClick",24,()=>{});const{states:{open:Y},updateOption:h,ids:C}=fa({closeOnEscape:m(),preventScroll:U(),closeOnOutsideClick:a(),portal:o(),forceVisible:!0,defaultOpen:P(),openFocus:V(),closeFocus:O(),onOutsideClick:x(),onOpenChange:({next:F})=>{var z;return P()!==F&&((z=Z())==null||z(F),P(F)),F}}),w=ht([C.content,C.description,C.title],([F,z,re])=>({content:F,description:z,title:re}));ae(()=>ue(P()),()=>{P()!==void 0&&Y.set(P())}),ae(()=>ue(U()),()=>{h("preventScroll",U())}),ae(()=>ue(m()),()=>{h("closeOnEscape",m())}),ae(()=>ue(a()),()=>{h("closeOnOutsideClick",a())}),ae(()=>ue(o()),()=>{h("portal",o())}),ae(()=>ue(V()),()=>{h("openFocus",V())}),ae(()=>ue(O()),()=>{h("closeFocus",O())}),ae(()=>ue(x()),()=>{h("onOutsideClick",x())}),Me(),Ae();var v=X(),s=M(v);ie(s,e,"default",{get ids(){return y()}},null),n(f,v),Se(),k()}var ga=L("<button><!></button>");function ba(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["asChild","el"]);Te(e,!1);const[y,U]=Re(),m=()=>Oe(Z,"$close",y),a=Le();let o=g(e,"asChild",8,!1),P=g(e,"el",28,()=>{});const{elements:{close:Z},getAttrs:V}=Ue(),O=_t(),x=V("close");ae(()=>m(),()=>{be(a,m())}),ae(()=>t(a),()=>{Object.assign(t(a),x)}),Me(),Ae();var Y=X(),h=M(Y);{var C=v=>{var s=X(),F=M(s);ie(F,e,"default",{get builder(){return t(a)}},null),n(v,s)},w=v=>{var s=ga();fe(s,()=>({...t(a),type:"button",...k}));var F=A(s);ie(F,e,"default",{get builder(){return t(a)}},null),S(s),ge(s,z=>P(z),()=>P()),ve(s,z=>{var re,ce;return(ce=(re=t(a)).action)==null?void 0:ce.call(re,z)}),l(()=>d("m-click",s,O)),l(()=>d("m-keydown",s,O)),n(v,s)};G(h,v=>{o()?v(C):v(w,!1)})}n(f,Y),Se(),U()}var ma=L("<div><!></div>");function ha(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["asChild","el"]);Te(e,!1);const[y,U]=Re(),m=()=>Oe(Z,"$portalled",y),a=Le();let o=g(e,"asChild",8,!1),P=g(e,"el",28,()=>{});const{elements:{portalled:Z},getAttrs:V}=Ue(),O=V("portal");ae(()=>m(),()=>{be(a,m())}),ae(()=>t(a),()=>{Object.assign(t(a),O)}),Me(),Ae();var x=X(),Y=M(x);{var h=w=>{var v=X(),s=M(v);ie(s,e,"default",{get builder(){return t(a)}},null),n(w,v)},C=w=>{var v=ma();fe(v,()=>({...t(a),...k}));var s=A(v);ie(s,e,"default",{get builder(){return t(a)}},null),S(v),ge(v,F=>P(F),()=>P()),ve(v,F=>{var z,re;return(re=(z=t(a)).action)==null?void 0:re.call(z,F)}),n(w,v)};G(Y,w=>{o()?w(h):w(C,!1)})}n(f,x),Se(),U()}var _a=L("<div><!></div>"),pa=L("<div><!></div>"),ya=L("<div><!></div>"),xa=L("<div><!></div>"),Ca=L("<div><!></div>");function wa(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","el"]);Te(e,!1);const[y,U]=Re(),m=()=>Oe(v,"$content",y),a=()=>Oe(s,"$open",y),o=Le();let P=g(e,"transition",24,()=>{}),Z=g(e,"transitionConfig",24,()=>{}),V=g(e,"inTransition",24,()=>{}),O=g(e,"inTransitionConfig",24,()=>{}),x=g(e,"outTransition",24,()=>{}),Y=g(e,"outTransitionConfig",24,()=>{}),h=g(e,"asChild",8,!1),C=g(e,"id",24,()=>{}),w=g(e,"el",28,()=>{});const{elements:{content:v},states:{open:s},ids:F,getAttrs:z}=Ue(),re=z("content");ae(()=>ue(C()),()=>{C()&&F.content.set(C())}),ae(()=>m(),()=>{be(o,m())}),ae(()=>t(o),()=>{Object.assign(t(o),re)}),Me(),Ae();var ce=X(),pe=M(ce);{var me=i=>{var D=X(),_=M(D);ie(_,e,"default",{get builder(){return t(o)}},null),n(i,D)},_e=i=>{var D=X(),_=M(D);{var $=J=>{var j=_a();fe(j,()=>({...t(o),...k}));var Q=A(j);ie(Q,e,"default",{get builder(){return t(o)}},null),S(j),ge(j,T=>w(T),()=>w()),ve(j,T=>{var ne,ee;return(ee=(ne=t(o)).action)==null?void 0:ee.call(ne,T)}),l(()=>d("pointerdown",j,function(T){u.call(this,e,T)})),l(()=>d("pointermove",j,function(T){u.call(this,e,T)})),l(()=>d("pointerup",j,function(T){u.call(this,e,T)})),l(()=>d("touchcancel",j,function(T){u.call(this,e,T)})),l(()=>d("touchend",j,function(T){u.call(this,e,T)})),l(()=>d("touchmove",j,function(T){u.call(this,e,T)},void 0,!1)),l(()=>d("touchstart",j,function(T){u.call(this,e,T)},void 0,!1)),we(3,j,P,Z),n(J,j)},H=J=>{var j=X(),Q=M(j);{var T=ee=>{var W=pa();fe(W,()=>({...t(o),...k}));var he=A(W);ie(he,e,"default",{get builder(){return t(o)}},null),S(W),ge(W,I=>w(I),()=>w()),ve(W,I=>{var le,te;return(te=(le=t(o)).action)==null?void 0:te.call(le,I)}),l(()=>d("pointerdown",W,function(I){u.call(this,e,I)})),l(()=>d("pointermove",W,function(I){u.call(this,e,I)})),l(()=>d("pointerup",W,function(I){u.call(this,e,I)})),l(()=>d("touchcancel",W,function(I){u.call(this,e,I)})),l(()=>d("touchend",W,function(I){u.call(this,e,I)})),l(()=>d("touchmove",W,function(I){u.call(this,e,I)},void 0,!1)),l(()=>d("touchstart",W,function(I){u.call(this,e,I)},void 0,!1)),we(1,W,V,O),we(2,W,x,Y),n(ee,W)},ne=ee=>{var W=X(),he=M(W);{var I=te=>{var B=ya();fe(B,()=>({...t(o),...k}));var ye=A(B);ie(ye,e,"default",{get builder(){return t(o)}},null),S(B),ge(B,r=>w(r),()=>w()),ve(B,r=>{var c,p;return(p=(c=t(o)).action)==null?void 0:p.call(c,r)}),l(()=>d("pointerdown",B,function(r){u.call(this,e,r)})),l(()=>d("pointermove",B,function(r){u.call(this,e,r)})),l(()=>d("pointerup",B,function(r){u.call(this,e,r)})),l(()=>d("touchcancel",B,function(r){u.call(this,e,r)})),l(()=>d("touchend",B,function(r){u.call(this,e,r)})),l(()=>d("touchmove",B,function(r){u.call(this,e,r)},void 0,!1)),l(()=>d("touchstart",B,function(r){u.call(this,e,r)},void 0,!1)),we(1,B,V,O),n(te,B)},le=te=>{var B=X(),ye=M(B);{var r=p=>{var R=xa();fe(R,()=>({...t(o),...k}));var oe=A(R);ie(oe,e,"default",{get builder(){return t(o)}},null),S(R),ge(R,b=>w(b),()=>w()),ve(R,b=>{var K,N;return(N=(K=t(o)).action)==null?void 0:N.call(K,b)}),l(()=>d("pointerdown",R,function(b){u.call(this,e,b)})),l(()=>d("pointermove",R,function(b){u.call(this,e,b)})),l(()=>d("pointerup",R,function(b){u.call(this,e,b)})),l(()=>d("touchcancel",R,function(b){u.call(this,e,b)})),l(()=>d("touchend",R,function(b){u.call(this,e,b)})),l(()=>d("touchmove",R,function(b){u.call(this,e,b)},void 0,!1)),l(()=>d("touchstart",R,function(b){u.call(this,e,b)},void 0,!1)),we(2,R,x,Y),n(p,R)},c=p=>{var R=X(),oe=M(R);{var b=K=>{var N=Ca();fe(N,()=>({...t(o),...k}));var Ee=A(N);ie(Ee,e,"default",{get builder(){return t(o)}},null),S(N),ge(N,q=>w(q),()=>w()),ve(N,q=>{var We,ke;return(ke=(We=t(o)).action)==null?void 0:ke.call(We,q)}),l(()=>d("pointerdown",N,function(q){u.call(this,e,q)})),l(()=>d("pointermove",N,function(q){u.call(this,e,q)})),l(()=>d("pointerup",N,function(q){u.call(this,e,q)})),l(()=>d("touchcancel",N,function(q){u.call(this,e,q)})),l(()=>d("touchend",N,function(q){u.call(this,e,q)})),l(()=>d("touchmove",N,function(q){u.call(this,e,q)},void 0,!1)),l(()=>d("touchstart",N,function(q){u.call(this,e,q)},void 0,!1)),n(K,N)};G(oe,K=>{a()&&K(b)},!0)}n(p,R)};G(ye,p=>{x()&&a()?p(r):p(c,!1)},!0)}n(te,B)};G(he,te=>{V()&&a()?te(I):te(le,!1)},!0)}n(ee,W)};G(Q,ee=>{V()&&x()&&a()?ee(T):ee(ne,!1)},!0)}n(J,j)};G(_,J=>{P()&&a()?J($):J(H,!1)},!0)}n(i,D)};G(pe,i=>{h()&&a()?i(me):i(_e,!1)})}n(f,ce),Se(),U()}var ka=L("<div></div>"),Oa=L("<div></div>"),Ta=L("<div></div>"),Sa=L("<div></div>"),Ea=L("<div></div>");function Pa(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]);Te(e,!1);const[y,U]=Re(),m=()=>Oe(w,"$overlay",y),a=()=>Oe(v,"$open",y),o=Le();let P=g(e,"transition",24,()=>{}),Z=g(e,"transitionConfig",24,()=>{}),V=g(e,"inTransition",24,()=>{}),O=g(e,"inTransitionConfig",24,()=>{}),x=g(e,"outTransition",24,()=>{}),Y=g(e,"outTransitionConfig",24,()=>{}),h=g(e,"asChild",8,!1),C=g(e,"el",28,()=>{});const{elements:{overlay:w},states:{open:v},getAttrs:s}=Ue(),F=s("overlay");ae(()=>m(),()=>{be(o,m())}),ae(()=>t(o),()=>{Object.assign(t(o),F)}),Me(),Ae();var z=X(),re=M(z);{var ce=me=>{var _e=X(),i=M(_e);ie(i,e,"default",{get builder(){return t(o)}},null),n(me,_e)},pe=me=>{var _e=X(),i=M(_e);{var D=$=>{var H=ka();fe(H,()=>({...t(o),...k})),l(()=>d("mouseup",H,function(J){u.call(this,e,J)})),ge(H,J=>C(J),()=>C()),ve(H,J=>{var j,Q;return(Q=(j=t(o)).action)==null?void 0:Q.call(j,J)}),we(3,H,P,Z),n($,H)},_=$=>{var H=X(),J=M(H);{var j=T=>{var ne=Oa();fe(ne,()=>({...t(o),...k})),ge(ne,ee=>C(ee),()=>C()),ve(ne,ee=>{var W,he;return(he=(W=t(o)).action)==null?void 0:he.call(W,ee)}),l(()=>d("mouseup",ne,function(ee){u.call(this,e,ee)})),we(1,ne,V,O),we(2,ne,x,Y),n(T,ne)},Q=T=>{var ne=X(),ee=M(ne);{var W=I=>{var le=Ta();fe(le,()=>({...t(o),...k})),ge(le,te=>C(te),()=>C()),ve(le,te=>{var B,ye;return(ye=(B=t(o)).action)==null?void 0:ye.call(B,te)}),l(()=>d("mouseup",le,function(te){u.call(this,e,te)})),we(1,le,V,O),n(I,le)},he=I=>{var le=X(),te=M(le);{var B=r=>{var c=Sa();fe(c,()=>({...t(o),...k})),ge(c,p=>C(p),()=>C()),ve(c,p=>{var R,oe;return(oe=(R=t(o)).action)==null?void 0:oe.call(R,p)}),l(()=>d("mouseup",c,function(p){u.call(this,e,p)})),we(2,c,x,Y),n(r,c)},ye=r=>{var c=X(),p=M(c);{var R=oe=>{var b=Ea();fe(b,()=>({...t(o),...k})),ge(b,K=>C(K),()=>C()),ve(b,K=>{var N,Ee;return(Ee=(N=t(o)).action)==null?void 0:Ee.call(N,K)}),l(()=>d("mouseup",b,function(K){u.call(this,e,K)})),n(oe,b)};G(p,oe=>{a()&&oe(R)},!0)}n(r,c)};G(te,r=>{x()&&a()?r(B):r(ye,!1)},!0)}n(I,le)};G(ee,I=>{V()&&a()?I(W):I(he,!1)},!0)}n(T,ne)};G(J,T=>{V()&&x()&&a()?T(j):T(Q,!1)},!0)}n($,H)};G(i,$=>{P()&&a()?$(D):$(_,!1)},!0)}n(me,_e)};G(re,me=>{h()&&a()?me(ce):me(pe,!1)})}n(f,z),Se(),U()}var Da=L("<button><!></button>");function Aa(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["asChild","el"]);Te(e,!1);const[y,U]=Re(),m=()=>Oe(Z,"$trigger",y),a=Le();let o=g(e,"asChild",8,!1),P=g(e,"el",28,()=>{});const{elements:{trigger:Z},getAttrs:V}=Ue(),O=_t(),x=V("trigger");ae(()=>m(),()=>{be(a,m())}),ae(()=>t(a),()=>{Object.assign(t(a),x)}),Me(),Ae();var Y=X(),h=M(Y);{var C=v=>{var s=X(),F=M(s);ie(F,e,"default",{get builder(){return t(a)}},null),n(v,s)},w=v=>{var s=Da();fe(s,()=>({...t(a),type:"button",...k}));var F=A(s);ie(F,e,"default",{get builder(){return t(a)}},null),S(s),ge(s,z=>P(z),()=>P()),ve(s,z=>{var re,ce;return(ce=(re=t(a)).action)==null?void 0:ce.call(re,z)}),l(()=>d("m-click",s,O)),l(()=>d("m-keydown",s,O)),n(v,s)};G(h,v=>{o()?v(C):v(w,!1)})}n(f,Y),Se(),U()}function Fa(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,[]);ha(f,Qe(()=>k,{children:(y,U)=>{var m=X(),a=M(m);ie(a,e,"default",{},null),n(y,m)},$$slots:{default:!0}}))}function ja(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["class","transition","transitionConfig"]);Te(e,!1);let y=g(e,"class",8,void 0),U=g(e,"transition",8,It),m=g(e,"transitionConfig",24,()=>({duration:150}));Ae();{let a=bt(()=>(ue(Je),ue(y()),mt(()=>Je("bg-background/80 fixed inset-0 z-50 backdrop-blur-sm",y()))));Pa(f,Qe({get transition(){return U()},get transitionConfig(){return m()},get class(){return t(a)}},()=>k))}Se()}var Ia=L('<!> <span class="sr-only">Close</span>',1),Ra=L("<!> <!>",1),za=L("<!> <!>",1);function Na(f,e){const E=se(e,["children","$$slots","$$events","$$legacy"]),k=se(E,["class","transition","transitionConfig"]);Te(e,!1);let y=g(e,"class",8,void 0),U=g(e,"transition",8,ra),m=g(e,"transitionConfig",24,()=>({duration:200}));Ae(),Fa(f,{children:(a,o)=>{var P=za(),Z=M(P);ja(Z,{});var V=de(Z,2);{let O=bt(()=>(ue(Je),ue(y()),mt(()=>Je("bg-background fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg sm:rounded-lg md:w-full",y()))));wa(V,Qe({get transition(){return U()},get transitionConfig(){return m()},get class(){return t(O)}},()=>k,{children:(x,Y)=>{var h=Ra(),C=M(h);ie(C,e,"default",{},null);var w=de(C,2);ba(w,{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none",children:(v,s)=>{var F=Ia(),z=M(F);aa(z,{class:"h-4 w-4"}),Pt(2),n(v,F)},$$slots:{default:!0}}),n(x,h)},$$slots:{default:!0}}))}n(a,P)},$$slots:{default:!0}}),Se()}const Ma=va,Va=Aa;var La=L('<button aria-label="open navigation" class="p-2 text-sidebar-foreground hover:text-sidebar-primary"><!></button>'),Ua=(f,e)=>be(e,!1),Wa=L("<li><a> </a></li>"),Ba=(f,e)=>be(e,!1),Ha=L("<a> </a>"),Ka=(f,e)=>be(e,!1),qa=L('<div class="p-4 border-b-2 border-sidebar-border"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a></div></div> <ul class="flex flex-col p-4 space-y-1"><!> <div class="flex-grow"></div> <li class="pt-4 border-t-2 border-sidebar-border"><div class="flex items-center justify-between px-3 py-2"><!> <a class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors">Sign Out</a></div></li></ul>',1),Ga=L("<!> <!>",1),Xa=L('<a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a>'),Ya=(f,e)=>be(e,!t(e)),Ja=L('<span class="block text-center">🏠</span>'),Qa=L("<a><!></a>"),Za=L("<a><!></a>"),$a=L(`<div class="text-sm bg-primary text-primary-foreground sticky px-4 py-3 text-center border-2 border-border shadow-brutal mb-6 font-bold">You're signed in as an anonymous user. <a href="/login/sign_up" class="underline font-bold hover:opacity-70">Sign Up to persist your changes</a></div>`),er=L('<div class="min-h-screen relative"><div class="grid grid-rows-[auto_1fr] lg:grid-rows-1 overflow-hidden absolute inset-0"><nav><div class="flex items-center space-x-2 lg:hidden"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-lg font-black">Robynn.ai</a></div> <!> <ul class="hidden flex-col h-full lg:flex"><li class="mb-8"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <!></div> <button class="p-1.5 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors rounded border-2 border-transparent hover:border-sidebar-border"><!></button></div></li> <nav class="space-y-1"></nav> <div class="flex-grow"></div> <div class="border-t-2 border-sidebar-border pt-4"><div><!> <a class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors border-2 border-transparent p-1.5 rounded hover:border-sidebar-border"><!></a></div></div></ul></nav> <div class="px-6 lg:px-12 py-6 overflow-y-scroll relative bg-background min-h-full flex flex-col"><div class="flex-1"><!> <!></div> <div class="mt-auto"><!></div></div></div></div>');function Rr(f,e){Te(e,!0);const[E,k]=Re(),y=()=>Oe($t,"$page",E);let{session:U}=e.data,m=qe(!1),a=qe(!0);Ke(()=>{if(typeof window<"u"){const r=localStorage.getItem("sidebar-collapsed");r!==null&&be(a,r==="true")}}),Ke(()=>{typeof window<"u"&&localStorage.setItem("sidebar-collapsed",String(t(a)))});const o="/dashboard/",P=ea();class Z{constructor(c,p,R){He(this,"href");He(this,"label");He(this,"active");this.href=c,this.label=p,this.active=R(this.href)}}let V=qe(Dt([]));Ke(()=>{var r;be(V,[new Z(`${o}${(r=P.value)==null?void 0:r.slug}`,"Home",c=>y().url.pathname===c)],!0)});let O=qe(void 0);Ke(()=>{var r;be(O,new Z(`${o}${(r=P.value)==null?void 0:r.slug}/settings`,"⚙️",c=>y().url.pathname.startsWith(c)),!0)});var x=er(),Y=A(x),h=A(Y);let C;var w=de(A(h),2);tt(w,()=>Ma,(r,c)=>{c(r,{get open(){return t(m)},set open(p){be(m,p,!0)},children:(p,R)=>{var oe=Ga(),b=M(oe);tt(b,()=>Va,(N,Ee)=>{Ee(N,{class:"lg:hidden",children:(q,We)=>{var ke=La(),Be=A(ke);ta(Be,{class:"h-5 w-5"}),S(ke),n(q,ke)},$$slots:{default:!0}})});var K=de(b,2);tt(K,()=>Na,(N,Ee)=>{Ee(N,{transition:q=>Rt(q,{x:300,duration:300}),class:"left-auto right-0 flex h-dvh max-h-screen w-full max-w-sm translate-x-1 flex-col overflow-y-scroll border-y-0 sm:rounded-none bg-sidebar",children:(q,We)=>{var ke=qa(),Be=de(M(ke),2),rt=A(Be);lt(rt,17,()=>t(V),dt,(Pe,xe)=>{let Ze=()=>t(xe).href,xt=()=>t(xe).label,Ct=()=>t(xe).active;var $e=Wa(),Ve=A($e);Ve.__click=[Ua,m];var wt=A(Ve,!0);S(Ve),S($e),De(()=>{Ce(Ve,"href",Ze()),ze(Ve,1,`block w-full px-3 py-2 text-sm font-bold transition-colors border-2 ${Ct()?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),Ge(wt,xt())}),n(Pe,$e)});var nt=de(rt,4),ot=A(nt),it=A(ot);{var yt=Pe=>{var xe=Ha();xe.__click=[Ba,m];var Ze=A(xe,!0);S(xe),De(()=>{Ce(xe,"href",t(O).href),ze(xe,1,`text-sm font-bold transition-colors border-2 p-1 rounded ${t(O).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),Ge(Ze,t(O).label)}),n(Pe,xe)};G(it,Pe=>{t(O)&&Pe(yt)})}var st=de(it,2);st.__click=[Ka,m],S(ot),S(nt),S(Be),De(()=>{var Pe;return Ce(st,"href",`/dashboard/${((Pe=P.value)==null?void 0:Pe.slug)??""}/../../sign_out`)}),n(q,ke)},$$slots:{default:!0}})}),n(p,oe)},$$slots:{default:!0}})});var v=de(w,2),s=A(v),F=A(s),z=A(F),re=de(A(z),2);{var ce=r=>{var c=Xa();n(r,c)};G(re,r=>{t(a)||r(ce)})}S(z);var pe=de(z,2);pe.__click=[Ya,a];var me=A(pe);{var _e=r=>{na(r,{class:"h-4 w-4"})},i=r=>{la(r,{class:"h-4 w-4"})};G(me,r=>{t(a)?r(_e):r(i,!1)})}S(pe),S(F),S(s);var D=de(s,2);lt(D,21,()=>t(V),dt,(r,c)=>{var p=Qa(),R=A(p);{var oe=K=>{var N=Ja();n(K,N)},b=K=>{var N=et();De(()=>Ge(N,t(c).label)),n(K,N)};G(R,K=>{t(a)?K(oe):K(b,!1)})}S(p),De(()=>{Ce(p,"href",t(c).href),ze(p,1,`block px-3 py-2 text-sm font-bold transition-colors border-2 ${t(c).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),Ce(p,"title",t(a)?t(c).label:void 0)}),n(r,p)}),S(D);var _=de(D,4),$=A(_);let H;var J=A($);{var j=r=>{var c=Za(),p=A(c);{var R=b=>{sa(b,{class:"h-4 w-4"})},oe=b=>{var K=et();De(()=>Ge(K,t(O).label)),n(b,K)};G(p,b=>{t(a)?b(R):b(oe,!1)})}S(c),De(()=>{Ce(c,"href",t(O).href),ze(c,1,`text-sm font-bold transition-colors border-2 p-1.5 rounded ${t(O).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),Ce(c,"title",t(a)?"Settings":void 0)}),n(r,c)};G(J,r=>{t(O)&&r(j)})}var Q=de(J,2),T=A(Q);{var ne=r=>{ia(r,{class:"h-4 w-4"})},ee=r=>{var c=et("Sign Out");n(r,c)};G(T,r=>{t(a)?r(ne):r(ee,!1)})}S(Q),S($),S(_),S(v),S(h);var W=de(h,2),he=A(W),I=A(he);{var le=r=>{var c=$a();n(r,c)};G(I,r=>{U!=null&&U.user.is_anonymous&&r(le)})}var te=de(I,2);Ft(te,()=>e.children),S(he);var B=de(he,2),ye=A(B);Qt(ye,{}),S(B),S(W),S(Y),S(x),De((r,c)=>{var p;jt(Y,`grid-template-columns: ${t(a)?"4.5rem":"18rem"} 1fr`),C=ze(h,1,"w-full h-16 flex items-center justify-between lg:block lg:h-dvh p-4 bg-sidebar border-r-2 border-sidebar-border text-sidebar-foreground transition-all duration-300",null,C,r),Ce(pe,"aria-label",t(a)?"Expand sidebar":"Collapse sidebar"),H=ze($,1,"flex items-center gap-2 px-3 py-2",null,H,c),Ce(Q,"href",`/dashboard/${((p=P.value)==null?void 0:p.slug)??""}/../../sign_out`),Ce(Q,"title",t(a)?"Sign Out":void 0)},[()=>({"lg:w-[4.5rem]":t(a),"lg:w-72":!t(a)}),()=>({"justify-center":t(a)})]),n(f,x),Se(),k()}At(["click"]);export{Rr as component};
