import"../chunks/CWj6FrbW.js";import{o as n}from"../chunks/RnwjOPnl.js";import{p as m,f as p,t as f,a as u,e as g,j as l,I as h,c,r as d,i as x}from"../chunks/DDiqt3uM.js";import{s as v}from"../chunks/DWulv87v.js";import{i as _,g as b}from"../chunks/DE1qutZV.js";var w=p('<h1 class="text-2xl font-bold m-6"> </h1>');function S(e,a){m(a,!0);let{supabase:o}=a.data,s=h("Signing out....");n(()=>{o.auth.signOut().then(async({error:r})=>{r?l(s,"There was an issue signing out."):(await _("data:init"),b("/"))})});var t=w(),i=c(t,!0);d(t),f(()=>v(i,x(s))),u(e,t),g()}export{S as component};
