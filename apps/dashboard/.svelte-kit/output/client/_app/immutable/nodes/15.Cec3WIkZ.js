import"../chunks/CWj6FrbW.js";import{p as s,f as n,s as o,d,a as h,e as c,$ as u,i as f,k as g}from"../chunks/DDiqt3uM.js";import{h as p}from"../chunks/DWulv87v.js";import{e as E}from"../chunks/CCJOWbOV.js";import{S as b}from"../chunks/CGb8OEdM.js";var v=n('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function w(r,e){s(e,!0);let{session:a}=e.data;var t=v();p(i=>{u.title="Change Email"});var m=o(d(t),2);{let i=g(()=>{var l;return[{id:"email",label:"Email",initialValue:((l=a==null?void 0:a.user)==null?void 0:l.email)??"",placeholder:"Email address"}]});b(m,{get data(){return e.data.form},get schema(){return E},title:"Change Email",editable:!0,successTitle:"Email change initiated",successBody:"You should receive an email at the new address to confirm the change. Please click the link in the email to finalized the change. Until finalized, you must sign in with your current email.",formTarget:"/api?/updateEmail",get fields(){return f(i)}})}h(r,t),c()}export{w as component};
