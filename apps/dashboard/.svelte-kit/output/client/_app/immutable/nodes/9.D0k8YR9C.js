import"../chunks/CWj6FrbW.js";import"../chunks/DhRTwODG.js";import{c as qr,o as pr,a as Wr}from"../chunks/RnwjOPnl.js";import{b as Ke,d as le,a as x,p as at,l as Yt,g as vr,e as ot,i as r,aT as ir,m as ae,j as w,f as k,c as t,s as a,r as e,ac as qe,be as He,h as E,t as q,n as F,q as gr,$ as Kr}from"../chunks/DDiqt3uM.js";import{r as Hr,e as C,s as L,h as Jr}from"../chunks/DWulv87v.js";import{i as G}from"../chunks/2C89X9tI.js";import{e as Ne,i as Ie}from"../chunks/OiKQa7Wx.js";import{h as rt}from"../chunks/CYhDwGx0.js";import{a as T}from"../chunks/D-ywOz1J.js";import{s as Ae}from"../chunks/DE2v8SHj.js";import{i as nt}from"../chunks/B_FgA42l.js";import{l as st,s as it,p as fr}from"../chunks/C-ZVHnwW.js";import{a as zt,s as Rt}from"../chunks/B82PTGnX.js";import{w as Xt,d as lt,a as Ur}from"../chunks/rjRVMZXi.js";import{r as lr,l as Vr}from"../chunks/BGGTUj09.js";import{r as Je}from"../chunks/C36Ip9GY.js";import{b as We}from"../chunks/DUGxtfU6.js";import{b as dr}from"../chunks/Dqu9JXqq.js";import{p as Be,a as de,A as R,i as Zr,s as Qr,h as ea,b as ta,c as ra}from"../chunks/twFvH6JT.js";import{m as aa}from"../chunks/CCJOWbOV.js";import{X as Bt}from"../chunks/rNGuVYtO.js";import{B as jt,U as xr}from"../chunks/ng6E9JCw.js";import{S as br}from"../chunks/QFpxkGuO.js";import{C as Ue}from"../chunks/oaGF9CiI.js";import{C as hr}from"../chunks/CXeeNO9R.js";import{Z as Nt}from"../chunks/BOkOaIl2.js";import{T as Dt,C as oa,S as na,a as sa}from"../chunks/BxkoWS-i.js";import{U as ia}from"../chunks/C9gUsiOS.js";import{s as dt}from"../chunks/iCEqKm8o.js";import{I as ct}from"../chunks/CkoRhfQ8.js";import{M as la}from"../chunks/Bxi4P_5L.js";function da(s,p){const n=st(p,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18"}]];ct(s,it({name:"brain"},()=>n,{get iconNode(){return y},children:(d,b)=>{var l=Ke(),_=le(l);dt(_,p,"default",{},null),x(d,l)},$$slots:{default:!0}}))}function ca(s,p){const n=st(p,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M18 17V9"}],["path",{d:"M13 17V5"}],["path",{d:"M8 17v-3"}]];ct(s,it({name:"chart-column"},()=>n,{get iconNode(){return y},children:(d,b)=>{var l=Ke(),_=le(l);dt(_,p,"default",{},null),x(d,l)},$$slots:{default:!0}}))}function tt(s,p){const n=st(p,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335"}],["path",{d:"m9 11 3 3L22 4"}]];ct(s,it({name:"circle-check-big"},()=>n,{get iconNode(){return y},children:(d,b)=>{var l=Ke(),_=le(l);dt(_,p,"default",{},null),x(d,l)},$$slots:{default:!0}}))}function ma(s,p){const n=st(p,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["circle",{cx:"12",cy:"12",r:"3"}],["path",{d:"M3 7V5a2 2 0 0 1 2-2h2"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2"}]];ct(s,it({name:"focus"},()=>n,{get iconNode(){return y},children:(d,b)=>{var l=Ke(),_=le(l);dt(_,p,"default",{},null),x(d,l)},$$slots:{default:!0}}))}function cr(s){return Object.prototype.toString.call(s)==="[object Date]"}function Gt(s,p,n,y){if(typeof n=="number"||cr(n)){const d=y-n,b=(n-p)/(s.dt||1/60),l=s.opts.stiffness*d,_=s.opts.damping*b,g=(l-_)*s.inv_mass,z=(b+g)*s.dt;return Math.abs(z)<s.opts.precision&&Math.abs(d)<s.opts.precision?y:(s.settled=!1,cr(n)?new Date(n.getTime()+z):n+z)}else{if(Array.isArray(n))return n.map((d,b)=>Gt(s,p[b],n[b],y[b]));if(typeof n=="object"){const d={};for(const b in n)d[b]=Gt(s,p[b],n[b],y[b]);return d}else throw new Error(`Cannot spring ${typeof n} values`)}}function mr(s,p={}){const n=Xt(s),{stiffness:y=.15,damping:d=.8,precision:b=.01}=p;let l,_,g,z=s,X=s,B=1,oe=0,ne=!1;function U(me,W={}){X=me;const Se=g={};return s==null||W.hard||se.stiffness>=1&&se.damping>=1?(ne=!0,l=lr.now(),z=me,n.set(s=X),Promise.resolve()):(W.soft&&(oe=1/((W.soft===!0?.5:+W.soft)*60),B=0),_||(l=lr.now(),ne=!1,_=Vr(Q=>{if(ne)return ne=!1,_=null,!1;B=Math.min(B+oe,1);const Ce=Math.min(Q-l,1e3/30),xe={inv_mass:B,opts:se,settled:!0,dt:Ce*60/1e3},M=Gt(xe,z,s,X);return l=Q,z=s,n.set(s=M),xe.settled&&(_=null),!xe.settled})),new Promise(Q=>{_.promise.then(()=>{Se===g&&Q()})}))}const se={set:U,update:(me,W)=>U(me(X,s),W),subscribe:n.subscribe,stiffness:y,damping:d,precision:b};return se}function ua(s,p){let n;return(...y)=>{n||(s.apply(this,y),n=!0,setTimeout(()=>n=!1,p))}}const mt=Ur({y:0,direction:"up",prevY:0},s=>{let p=0;const n=ua(()=>{const y=window.scrollY,d=y>p?"down":"up";s({y,direction:d,prevY:p}),p=y},16);return window.addEventListener("scroll",n,{passive:!0}),n(),()=>window.removeEventListener("scroll",n)}),pa=lt(mt,s=>s.y);lt(mt,s=>s.direction);lt(mt,s=>s.y>50);lt(mt,s=>s.direction==="down"&&s.y>100);const va=(s,p)=>{const n=document.createElement("div");if(n.innerHTML=`
    <div class="flex items-center gap-3 bg-green-500 text-white px-4 py-3 rounded-lg shadow-lg">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span>${s}</span>
    </div>
  `,n.style.position="fixed",n.style.top="20px",n.style.right="20px",n.style.zIndex="10000",n.style.opacity="0",n.style.transform="translateX(100%)",document.body.appendChild(n),Be()){n.style.opacity="1",n.style.transform="translateX(0)",setTimeout(()=>n.remove(),3e3);return}de(n,{opacity:[0,1],transform:["translateX(100%)","translateX(0)"]},{duration:R.duration.normal,easing:R.easing.easeOut}).finished.then(()=>{setTimeout(()=>{de(n,{opacity:[1,0],transform:["translateX(0)","translateX(100%)"]},{duration:R.duration.normal,easing:R.easing.easeIn}).finished.then(()=>{n.remove()})},3e3)})},ur=(s,p)=>{const n=document.createElement("div");if(n.innerHTML=`
    <div class="flex items-center gap-3 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
      <span>${s}</span>
    </div>
  `,n.style.position="fixed",n.style.top="20px",n.style.right="20px",n.style.zIndex="10000",n.style.opacity="0",n.style.transform="translateX(100%)",document.body.appendChild(n),Be()){n.style.opacity="1",n.style.transform="translateX(0)",setTimeout(()=>n.remove(),4e3);return}de(n,{opacity:[0,1],transform:["translateX(100%)","translateX(0)"]},{duration:R.duration.normal,easing:R.easing.easeOut}).finished.then(()=>{de(n,{transform:["translateX(0)","translateX(-5px)","translateX(5px)","translateX(0)"]},{duration:.5,easing:R.easing.easeInOut}),setTimeout(()=>{de(n,{opacity:[1,0],transform:["translateX(0)","translateX(100%)"]},{duration:R.duration.normal,easing:R.easing.easeIn}).finished.then(()=>{n.remove()})},4e3)})},v=(s,p={})=>{if(Be())return{};const{delay:n=0,duration:y=R.duration.normal,distance:d=20,direction:b="up",threshold:l=.1}=p;return de(s,{opacity:0,transform:(z=>{switch(b){case"up":return`translateY(${z}px)`;case"down":return`translateY(${-z}px)`;case"left":return`translateX(${z}px)`;case"right":return`translateX(${-z}px)`;default:return`translateY(${z}px)`}})(d)},{duration:0}),{destroy:Zr(s,()=>{de(s,{opacity:1,transform:"translate(0px, 0px)"},{duration:y,delay:n,easing:R.easing.easeOut})},{margin:`0px 0px -${l*100}% 0px`})}},Fe=(s,p={})=>{if(Be())return{};const{scale:n=1.05,duration:y=R.duration.fast}=p;let d=null;const b=()=>{d&&d.stop(),d=de(s,{transform:`scale(${n})`},{duration:y,easing:R.easing.easeOut})},l=()=>{d&&d.stop(),d=de(s,{transform:"scale(1)"},{duration:y,easing:R.easing.easeOut})};return s.addEventListener("mouseenter",b),s.addEventListener("mouseleave",l),{destroy(){s.removeEventListener("mouseenter",b),s.removeEventListener("mouseleave",l),d&&d.stop()}}},Pt=(s,p={})=>{if(Be())return{};const{from:n=1,to:y=0,offset:d=["start start","end start"]}=p;return{destroy:Qr(de(s,{opacity:[n,y]}),{target:s,offset:d})}},we=s=>{if(Be())return{};const p=n=>{const y=s.getBoundingClientRect(),d=document.createElement("div"),b=n.clientX-y.left,l=n.clientY-y.top;d.style.position="absolute",d.style.borderRadius="50%",d.style.background="rgba(255, 255, 255, 0.3)",d.style.pointerEvents="none",d.style.width="20px",d.style.height="20px",d.style.left=`${b}px`,d.style.top=`${l}px`,d.style.transform="translate(-50%, -50%)",d.style.zIndex="1000",s.style.position="relative",s.style.overflow="hidden",s.appendChild(d),de(d,{transform:["translate(-50%, -50%) scale(0)","translate(-50%, -50%) scale(4)"],opacity:[.6,0]},{duration:.6,easing:R.easing.easeOut}).finished.then(()=>{d.remove()}),de(s,{transform:["scale(1)","scale(0.95)","scale(1)"]},{duration:.2,easing:R.easing.easeOut})};return s.addEventListener("click",p),{destroy(){s.removeEventListener("click",p)}}},_e=s=>{if(Be())return{};const p=()=>{de(s,{transform:"scale(1.02)",boxShadow:"0 0 0 2px rgba(59, 130, 246, 0.3)"},{duration:R.duration.fast,easing:R.easing.easeOut})},n=()=>{de(s,{transform:"scale(1)",boxShadow:"0 0 0 0px rgba(59, 130, 246, 0)"},{duration:R.duration.fast,easing:R.easing.easeOut})};return s.addEventListener("focus",p),s.addEventListener("blur",n),{destroy(){s.removeEventListener("focus",p),s.removeEventListener("blur",n)}}},je=s=>{if(Be())return{};let p=null;const n=()=>{p&&p.stop(),p=de(s,{transform:"translateY(-4px)",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.15)"},{duration:R.duration.normal,easing:R.easing.easeOut})},y=()=>{p&&p.stop(),p=de(s,{transform:"translateY(0px)",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)"},{duration:R.duration.normal,easing:R.easing.easeOut})};return s.addEventListener("mouseenter",n),s.addEventListener("mouseleave",y),{destroy(){s.removeEventListener("mouseenter",n),s.removeEventListener("mouseleave",y),p&&p.stop()}}};var ga=k('<p class="text-red-500 text-sm mt-1"> </p>'),fa=k('<p class="text-red-500 text-sm mt-1"> </p>'),xa=k('<p class="text-red-500 text-sm mt-1"> </p>'),ba=k('<p class="text-red-500 text-sm mt-1"> </p>'),ha=k('<div class="modal-success-message"> </div>'),ya=k('<div class="modal-error-message"> </div>'),_a=k(`<div class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background: rgba(0, 0, 0, 0.8);" role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1"><div class="relative w-full max-w-lg border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-xl);" role="document"><div class="flex items-center justify-between p-6 border-b-2" style="border-color: var(--border);"><h2 id="modal-title" class="text-2xl font-black" style="color: var(--foreground);">Let's Start Your Growth Story</h2> <button class="p-2 transition-colors hover:opacity-70" style="color: var(--muted-foreground);" aria-label="Close modal"><!></button></div> <form class="p-6 space-y-6"><div><label for="name" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Full Name *</label> <input id="name" type="text" required placeholder="Your full name"/> <!></div> <div><label for="email" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Email Address *</label> <input id="email" type="email" required placeholder="<EMAIL>"/> <!></div> <div><label for="website" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Company Website</label> <input id="website" type="url" placeholder="https://yourcompany.com"/> <!></div> <div><label for="description" class="block text-sm font-bold mb-2" style="color: var(--foreground);">What would you like to learn or discuss? *</label> <textarea id="description" required rows="4" placeholder="Tell us about your marketing challenges, goals, or what you'd like to explore with our team..."></textarea> <!></div> <!> <!> <div class="flex gap-4 pt-4"><button type="submit"> </button> <button type="button" class="btn-secondary px-6 py-3 font-bold">Cancel</button></div></form></div></div>`);function wa(s,p){at(p,!1);let n=fr(p,"isOpen",12,!1);const y=qr();let d=ae({name:"",email:"",website:"",description:""}),b=ae(!1),l=ae(""),_=ae(""),g=ae({}),z=ae(),X=ae(),B=ae(null),oe=ae(!1);function ne(){n(!1),w(l,""),w(_,""),w(g,{}),r(B)&&(r(B).focus(),w(B,null)),typeof document<"u"&&document.body.classList.remove("modal-open"),y("close")}function U(){w(d,{name:"",email:"",website:"",description:""}),w(l,""),w(_,""),w(g,{})}function se(){try{return aa.parse(r(d)),w(g,{}),!0}catch(c){if(w(g,{}),c.errors)for(const f of c.errors){const h=f.path[0];He(g,r(g)[h]=f.message)}return!1}}async function me(c){if(c.preventDefault(),w(l,""),w(_,""),!se()){w(_,"Please fix the errors below and try again.");return}w(b,!0);try{const f=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r(d))});if(!f.ok)throw new Error(`HTTP error! status: ${f.status}`);const h=await f.json();if(h.success){w(l,"Thank you! Your message has been sent successfully. We'll get back to you soon.");try{va("Message sent successfully! We'll get back to you soon.")}catch(Y){console.debug("Notification error:",Y)}U(),y("submit",{success:!0,data:h.data}),setTimeout(()=>{n()&&ne()},2e3)}else{w(_,h.error||"Failed to send message. Please try again.");try{ur(h.error||"Failed to send message. Please try again.")}catch(Y){console.debug("Notification error:",Y)}}}catch(f){console.error("Form submission error:",f),w(_,"Network error. Please check your connection and try again.");try{ur("Network error. Please check your connection and try again.")}catch(h){console.debug("Notification error:",h)}}finally{w(b,!1)}}function W(c){c.target===c.currentTarget&&ne()}function Se(c){if(!r(z))return;const f=r(z).querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),h=f[0],Y=f[f.length-1];c.key==="Tab"&&(c.shiftKey?document.activeElement===h&&(c.preventDefault(),Y.focus()):document.activeElement===Y&&(c.preventDefault(),h.focus()))}function Q(c){n()&&c.key==="Escape"&&ne()}function Ce(){setTimeout(()=>{try{n()&&r(X)&&r(X).focus()}catch(c){console.debug("Error focusing first input:",c)}},350)}pr(()=>{w(oe,!0),typeof document<"u"&&document.addEventListener("keydown",Q)}),Wr(()=>{typeof document<"u"&&(document.removeEventListener("keydown",Q),document.body.classList.remove("modal-open"))}),Yt(()=>(ir(n()),r(oe)),()=>{if(n()&&typeof document<"u"&&r(oe))try{w(B,document.activeElement),document.body.classList.add("modal-open"),Ce()}catch(c){console.debug("Error during modal opening:",c)}}),Yt(()=>ir(n()),()=>{if(!n()&&typeof document<"u")try{document.body.classList.remove("modal-open")}catch(c){console.debug("Error during modal closing:",c)}}),vr(),nt();var xe=Ke(),M=le(xe);{var P=c=>{var f=_a(),h=t(f),Y=t(h),ie=a(t(Y),2),be=t(ie);Bt(be,{class:"w-6 h-6"}),e(ie),e(Y);var I=a(Y,2),m=t(I),A=a(t(m),2);Je(A),dr(A,u=>w(X,u),()=>r(X)),qe(()=>We(A,()=>r(d).name,u=>He(d,r(d).name=u))),T(A,u=>_e==null?void 0:_e(u));var j=a(A,2);{var ee=u=>{var O=ga(),Z=t(O,!0);e(O),q(()=>L(Z,(r(g),E(()=>r(g).name)))),x(u,O)};G(j,u=>{r(g),E(()=>r(g).name)&&u(ee)})}e(m);var ce=a(m,2),te=a(t(ce),2);Je(te),qe(()=>We(te,()=>r(d).email,u=>He(d,r(d).email=u))),T(te,u=>_e==null?void 0:_e(u));var pe=a(te,2);{var V=u=>{var O=fa(),Z=t(O,!0);e(O),q(()=>L(Z,(r(g),E(()=>r(g).email)))),x(u,O)};G(pe,u=>{r(g),E(()=>r(g).email)&&u(V)})}e(ce);var re=a(ce,2),ue=a(t(re),2);Je(ue),qe(()=>We(ue,()=>r(d).website,u=>He(d,r(d).website=u))),T(ue,u=>_e==null?void 0:_e(u));var Ee=a(ue,2);{var Te=u=>{var O=xa(),Z=t(O,!0);e(O),q(()=>L(Z,(r(g),E(()=>r(g).website)))),x(u,O)};G(Ee,u=>{r(g),E(()=>r(g).website)&&u(Te)})}e(re);var ke=a(re,2),ve=a(t(ke),2);Hr(ve),qe(()=>We(ve,()=>r(d).description,u=>He(d,r(d).description=u))),T(ve,u=>_e==null?void 0:_e(u));var Ge=a(ve,2);{var De=u=>{var O=ba(),Z=t(O,!0);e(O),q(()=>L(Z,(r(g),E(()=>r(g).description)))),x(u,O)};G(Ge,u=>{r(g),E(()=>r(g).description)&&u(De)})}e(ke);var $=a(ke,2);{var K=u=>{var O=ha(),Z=t(O,!0);e(O),q(()=>L(Z,r(l))),x(u,O)};G($,u=>{r(l)&&u(K)})}var J=a($,2);{var he=u=>{var O=ya(),Z=t(O,!0);e(O),q(()=>L(Z,r(_))),x(u,O)};G(J,u=>{r(_)&&u(he)})}var Oe=a(J,2),ye=t(Oe),ge=t(ye,!0);e(ye),T(ye,u=>we==null?void 0:we(u));var H=a(ye,2);qe(()=>C("click",H,ne)),T(H,u=>we==null?void 0:we(u)),e(Oe),e(I),e(h),dr(h,u=>w(z,u),()=>r(z)),e(f),q(()=>{Ae(A,1,`input-brutal w-full ${r(g),E(()=>r(g).name?"border-red-500":"")??""}`),Ae(te,1,`input-brutal w-full ${r(g),E(()=>r(g).email?"border-red-500":"")??""}`),Ae(ue,1,`input-brutal w-full ${r(g),E(()=>r(g).website?"border-red-500":"")??""}`),Ae(ve,1,`textarea-brutal w-full ${r(g),E(()=>r(g).description?"border-red-500":"")??""}`),ye.disabled=r(b),Ae(ye,1,`btn-primary flex-1 px-6 py-3 font-bold ${r(b)?"opacity-75 cursor-not-allowed":""}`),L(ge,r(b)?"Sending...":"Send Message")}),C("click",ie,ne),C("submit",I,me),C("click",f,W),C("keydown",f,Se),x(c,f)};G(M,c=>{n()&&c(P)})}x(s,xe),ot()}var ka=k('<button class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"><div class="flex items-center gap-2 mb-1"><!> <span class="text-xs font-medium text-foreground"> </span></div> <div class="flex items-center justify-between"><span class="text-xs text-muted-foreground">Click to try →</span> <!></div></button>'),$a=k('<div class="grid gap-3 mb-4"></div> <div class="flex gap-2"><input placeholder="Or ask about any company..." class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"/> <button class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50"><!></button></div>',1),Ma=k('<div class="p-3 bg-primary/10 border border-primary/20 rounded-lg"><p class="text-xs text-foreground"> </p></div>'),Aa=k('<span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded"> </span>'),Sa=k('<div class="mt-3 pt-3 border-t border-border/50"><div class="flex flex-wrap gap-1"></div></div>'),Ea=k('<div class="p-3 bg-background border border-border rounded-lg"><div class="prose prose-xs max-w-none"><!></div> <!></div>'),Ta=k('<div><div><!></div> <div class="flex-1 max-w-md"><div class="flex items-center gap-2 mb-1"><span class="text-xs font-medium text-foreground"> </span> <!> <span class="text-xs text-muted-foreground"> </span></div> <!></div></div>'),Ca=k('<div class="flex gap-3"><div class="w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded bg-secondary"><!></div> <div class="flex-1"><div class="p-3 bg-background border border-border rounded-lg"><div class="flex items-center gap-2"><div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-200"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-400"></div> <span class="text-xs text-muted-foreground">Analyzing...</span></div></div></div></div>'),Oa=k('<div class="space-y-4 max-h-96 overflow-y-auto"><!> <!></div> <div class="mt-4 pt-4 border-t border-border/50 text-center"><p class="text-xs text-muted-foreground mb-2">This is a demo with limited capabilities</p> <button class="text-xs text-primary hover:underline">Try another query</button></div>',1),La=k('<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center mb-6"><div class="flex items-center justify-center gap-2 mb-4"><div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded"><!></div> <h3 class="text-lg font-bold">Try Athena Live</h3></div> <p class="text-sm text-muted-foreground mb-4">Get instant competitive intelligence on any company</p></div> <!></div>');function ja(s,p){at(p,!1);const[n,y]=zt(),d=()=>Rt(b,"$messages",n),b=Xt([]);let l=ae(""),_=ae(!1),g=ae(!1);const z=["Research Stripe's recent marketing strategy","Analyze Notion's competitive positioning","Tell me about Figma's go-to-market approach"];function X(){return Math.random().toString(36).substr(2,9)}async function B(M){const P=M||r(l).trim();if(!(!P||r(_))){w(l,""),w(_,!0),w(g,!0),b.update(c=>[...c,{id:X(),role:"user",content:P,timestamp:new Date}]);try{const c=await fetch("/api/demo/researcher",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:P})}),f=await c.json();if(c.ok)b.update(h=>[...h,{id:X(),role:"assistant",content:f.response,timestamp:new Date,metadata:f.metadata}]);else throw new Error(f.error||"Failed to get response")}catch(c){console.error("Demo error:",c),b.update(f=>[...f,{id:X(),role:"assistant",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date}])}finally{w(_,!1)}}}function oe(M){M.key==="Enter"&&!M.shiftKey&&(M.preventDefault(),B())}function ne(M){return M.replace(/^# (.*$)/gim,'<h1 class="text-xl font-bold mb-3" style="color: var(--foreground);">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-lg font-bold mb-2" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-base font-bold mb-2" style="color: var(--foreground);">$1</h3>').replace(/^\*\*(.*?)\*\*/gim,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^\* (.*$)/gim,'<li class="ml-4 text-sm" style="color: var(--muted-foreground);">• $1</li>').replace(/\n\n/gim,'</p><p class="text-sm mb-2" style="color: var(--muted-foreground);">').replace(/^(?!<[h|l|s])(.+)$/gim,'<p class="text-sm mb-2" style="color: var(--muted-foreground);">$1</p>')}nt();var U=La(),se=t(U),me=t(se),W=t(me),Se=t(W);jt(Se,{class:"w-4 h-4 text-primary-foreground"}),e(W),F(2),e(me),F(2),e(se);var Q=a(se,2);{var Ce=M=>{var P=$a(),c=le(P);Ne(c,5,()=>z,Ie,(m,A)=>{var j=ka(),ee=t(j),ce=t(ee);br(ce,{class:"w-3 h-3 text-primary"});var te=a(ce,2),pe=t(te,!0);e(te),e(ee);var V=a(ee,2),re=a(t(V),2);Ue(re,{class:"w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors"}),e(V),e(j),q(()=>L(pe,r(A))),C("click",j,()=>B(r(A))),x(m,j)}),e(c);var f=a(c,2),h=t(f);Je(h);var Y=a(h,2),ie=t(Y);{var be=m=>{Nt(m,{class:"w-3 h-3 animate-spin"})},I=m=>{var A=gr("Try");x(m,A)};G(ie,m=>{r(_)?m(be):m(I,!1)})}e(Y),e(f),q(m=>{h.disabled=r(_),Y.disabled=m},[()=>!r(l).trim()||r(_)]),We(h,()=>r(l),m=>w(l,m)),C("keydown",h,oe),C("click",Y,()=>B()),x(M,P)},xe=M=>{var P=Oa(),c=le(P),f=t(c);Ne(f,1,d,Ie,(I,m)=>{var A=Ta(),j=t(A),ee=t(j);{var ce=$=>{xr($,{class:"w-3 h-3 text-primary-foreground"})},te=$=>{jt($,{class:"w-3 h-3 text-secondary-foreground"})};G(ee,$=>{r(m).role==="user"?$(ce):$(te,!1)})}e(j);var pe=a(j,2),V=t(pe),re=t(V),ue=t(re,!0);e(re);var Ee=a(re,2);hr(Ee,{class:"w-2 h-2 text-muted-foreground"});var Te=a(Ee,2),ke=t(Te,!0);e(Te),e(V);var ve=a(V,2);{var Ge=$=>{var K=Ma(),J=t(K),he=t(J,!0);e(J),e(K),q(()=>L(he,r(m).content)),x($,K)},De=$=>{var K=Ea(),J=t(K),he=t(J);rt(he,()=>ne(r(m).content)),e(J);var Oe=a(J,2);{var ye=ge=>{var H=Sa(),u=t(H);Ne(u,5,()=>r(m).metadata.insights.tags,Ie,(O,Z)=>{var ze=Aa(),Pe=t(ze,!0);e(ze),q(()=>L(Pe,r(Z))),x(O,ze)}),e(u),e(H),x(ge,H)};G(Oe,ge=>{var H;(H=r(m).metadata)!=null&&H.insights&&ge(ye)})}e(K),x($,K)};G(ve,$=>{r(m).role==="user"?$(Ge):$(De,!1)})}e(pe),e(A),q($=>{Ae(A,1,`flex gap-3 ${r(m).role==="user"?"flex-row-reverse":""}`),Ae(j,1,`w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded ${r(m).role==="user"?"bg-primary":"bg-secondary"}`),L(ue,r(m).role==="user"?"You":"Athena"),L(ke,$)},[()=>r(m).timestamp.toLocaleTimeString()]),x(I,A)});var h=a(f,2);{var Y=I=>{var m=Ca(),A=t(m),j=t(A);jt(j,{class:"w-3 h-3 text-secondary-foreground"}),e(A),F(2),e(m),x(I,m)};G(h,I=>{r(_)&&I(Y)})}e(c);var ie=a(c,2),be=a(t(ie),2);e(ie),C("click",be,()=>{w(g,!1),b.set([])}),x(M,P)};G(Q,M=>{r(g)?M(xe,!1):M(Ce)})}e(U),x(s,U),ot(),y()}var Da=k('<button class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"><div class="flex items-center gap-2 mb-1"><!> <span class="text-xs font-medium text-foreground"> </span></div> <div class="flex items-center justify-between"><span class="text-xs text-muted-foreground">Click to analyze →</span> <!></div></button>'),Pa=k('<div class="grid gap-3 mb-4"></div> <div class="flex gap-2"><input placeholder="e.g., vegan protein powder, sustainable fashion..." class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"/> <button class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50"><!></button></div>',1),Ya=k('<div class="p-3 bg-primary/10 border border-primary/20 rounded-lg"><p class="text-xs text-foreground"> </p></div>'),Ba=k('<span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded"> </span>'),Ga=k('<div class="mt-3 pt-3 border-t border-border/50"><div class="flex items-center gap-2 mb-2"><!> <span class="text-xs font-medium text-foreground">Top Keywords</span></div> <div class="flex flex-wrap gap-1"></div></div>'),za=k('<div class="p-3 bg-background border border-border rounded-lg"><div class="prose prose-xs max-w-none"><!></div> <!></div>'),Ra=k('<div><div><!></div> <div class="flex-1 max-w-md"><div class="flex items-center gap-2 mb-1"><span class="text-xs font-medium text-foreground"> </span> <!> <span class="text-xs text-muted-foreground"> </span></div> <!></div></div>'),Xa=k('<div class="flex gap-3"><div class="w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded bg-secondary"><!></div> <div class="flex-1"><div class="p-3 bg-background border border-border rounded-lg"><div class="flex items-center gap-2"><div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-200"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-400"></div> <span class="text-xs text-muted-foreground">Researching keywords...</span></div></div></div></div>'),Na=k('<div class="space-y-4 max-h-96 overflow-y-auto"><!> <!></div> <div class="mt-4 pt-4 border-t border-border/50 text-center"><p class="text-xs text-muted-foreground mb-2">This is a demo with limited capabilities</p> <button class="text-xs text-primary hover:underline">Try another query</button></div>',1),Ia=k('<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center mb-6"><div class="flex items-center justify-center gap-2 mb-4"><div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded"><!></div> <h3 class="text-lg font-bold">Try Lexi Live</h3></div> <p class="text-sm text-muted-foreground mb-4">Get instant SEO keyword analysis for any topic</p></div> <!></div>');function Fa(s,p){at(p,!1);const[n,y]=zt(),d=()=>Rt(b,"$messages",n),b=Xt([]);let l=ae(""),_=ae(!1),g=ae(!1);const z=["Find long-tail keywords for organic skincare products","Analyze local SEO keywords for coffee shops in Seattle","Research B2B keywords for project management software"];function X(){return Math.random().toString(36).substr(2,9)}async function B(M){const P=M||r(l).trim();if(!(!P||r(_))){w(l,""),w(_,!0),w(g,!0),b.update(c=>[...c,{id:X(),role:"user",content:P,timestamp:new Date}]);try{const c=await fetch("/api/demo/seo",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:P})}),f=await c.json();if(c.ok)b.update(h=>[...h,{id:X(),role:"assistant",content:f.response,timestamp:new Date,metadata:f.metadata}]);else throw new Error(f.error||"Failed to get response")}catch(c){console.error("Demo error:",c),b.update(f=>[...f,{id:X(),role:"assistant",content:c instanceof Error&&c.message.includes("rate limit")?"You've reached the demo limit (5 requests per hour). Sign up for unlimited access!":"Sorry, there was an error processing your request. Please try again.",timestamp:new Date}])}finally{w(_,!1)}}}function oe(M){M.key==="Enter"&&!M.shiftKey&&(M.preventDefault(),B())}function ne(M){return M.replace(/^# (.*$)/gim,'<h1 class="text-xl font-bold mb-3" style="color: var(--foreground);">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-lg font-bold mb-2" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-base font-bold mb-2" style="color: var(--foreground);">$1</h3>').replace(/^\*\*(.*?)\*\*/gim,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^[\-\*] (.*$)/gim,'<li class="ml-4 text-sm" style="color: var(--muted-foreground);">• $1</li>').replace(/\n\n/gim,'</p><p class="text-sm mb-2" style="color: var(--muted-foreground);">').replace(/^(?!<[h|l|s])(.+)$/gim,'<p class="text-sm mb-2" style="color: var(--muted-foreground);">$1</p>').replace(/\|(.+)\|/g,P=>{const c=P.split("|").filter(h=>h.trim());return c.some(h=>h.trim().match(/^[\-:]+$/))?"":`<tr>${c.map(h=>`<td class="px-2 py-1 text-xs border-b border-border/50">${h.trim()}</td>`).join("")}</tr>`})}nt();var U=Ia(),se=t(U),me=t(se),W=t(me),Se=t(W);Dt(Se,{class:"w-4 h-4 text-primary-foreground"}),e(W),F(2),e(me),F(2),e(se);var Q=a(se,2);{var Ce=M=>{var P=Pa(),c=le(P);Ne(c,5,()=>z,Ie,(m,A)=>{var j=Da(),ee=t(j),ce=t(ee);br(ce,{class:"w-3 h-3 text-primary"});var te=a(ce,2),pe=t(te,!0);e(te),e(ee);var V=a(ee,2),re=a(t(V),2);Ue(re,{class:"w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors"}),e(V),e(j),q(()=>L(pe,r(A))),C("click",j,()=>B(r(A))),x(m,j)}),e(c);var f=a(c,2),h=t(f);Je(h);var Y=a(h,2),ie=t(Y);{var be=m=>{Nt(m,{class:"w-3 h-3 animate-spin"})},I=m=>{var A=gr("Analyze");x(m,A)};G(ie,m=>{r(_)?m(be):m(I,!1)})}e(Y),e(f),q(m=>{h.disabled=r(_),Y.disabled=m},[()=>!r(l).trim()||r(_)]),We(h,()=>r(l),m=>w(l,m)),C("keydown",h,oe),C("click",Y,()=>B()),x(M,P)},xe=M=>{var P=Na(),c=le(P),f=t(c);Ne(f,1,d,Ie,(I,m)=>{var A=Ra(),j=t(A),ee=t(j);{var ce=$=>{xr($,{class:"w-3 h-3 text-primary-foreground"})},te=$=>{Dt($,{class:"w-3 h-3 text-secondary-foreground"})};G(ee,$=>{r(m).role==="user"?$(ce):$(te,!1)})}e(j);var pe=a(j,2),V=t(pe),re=t(V),ue=t(re,!0);e(re);var Ee=a(re,2);hr(Ee,{class:"w-2 h-2 text-muted-foreground"});var Te=a(Ee,2),ke=t(Te,!0);e(Te),e(V);var ve=a(V,2);{var Ge=$=>{var K=Ya(),J=t(K),he=t(J,!0);e(J),e(K),q(()=>L(he,r(m).content)),x($,K)},De=$=>{var K=za(),J=t(K),he=t(J);rt(he,()=>ne(r(m).content)),e(J);var Oe=a(J,2);{var ye=ge=>{var H=Ga(),u=t(H),O=t(u);oa(O,{class:"w-3 h-3 text-primary"}),F(2),e(u);var Z=a(u,2);Ne(Z,5,()=>r(m).metadata.keywords.slice(0,5),Ie,(ze,Pe)=>{var Re=Ba(),ut=t(Re,!0);e(Re),q(()=>L(ut,r(Pe))),x(ze,Re)}),e(Z),e(H),x(ge,H)};G(Oe,ge=>{var H;(H=r(m).metadata)!=null&&H.keywords&&ge(ye)})}e(K),x($,K)};G(ve,$=>{r(m).role==="user"?$(Ge):$(De,!1)})}e(pe),e(A),q($=>{Ae(A,1,`flex gap-3 ${r(m).role==="user"?"flex-row-reverse":""}`),Ae(j,1,`w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded ${r(m).role==="user"?"bg-primary":"bg-secondary"}`),L(ue,r(m).role==="user"?"You":"Lexi"),L(ke,$)},[()=>r(m).timestamp.toLocaleTimeString()]),x(I,A)});var h=a(f,2);{var Y=I=>{var m=Xa(),A=t(m),j=t(A);Dt(j,{class:"w-3 h-3 text-secondary-foreground"}),e(A),F(2),e(m),x(I,m)};G(h,I=>{r(_)&&I(Y)})}e(c);var ie=a(c,2),be=a(t(ie),2);e(ie),C("click",be,()=>{w(g,!1),b.set([])}),x(M,P)};G(Q,M=>{r(g)?M(xe,!1):M(Ce)})}e(U),x(s,U),ot(),y()}var qa=k('<meta name="description" content="Fractional CMO team with Agents working for you 24/7"/> <meta property="og:title" content="Your unfair advantage - Agentic marketing team"/> <meta property="og:description" content="Fractional CMO team with Agents working for you 24/7"/> <meta property="og:image" content="/og-preview.jpg"/> <meta property="og:image:width" content="1200"/> <meta property="og:image:height" content="630"/> <meta property="og:image:alt" content="Robynn AI - Your unfair advantage - Agentic marketing team"/> <meta property="og:type" content="website"/> <meta property="og:url" content="https://robynn.ai"/> <meta property="og:site_name" content="Robynn AI"/> <meta name="twitter:card" content="summary_large_image"/> <meta name="twitter:title" content="Your unfair advantage - Agentic marketing team"/> <meta name="twitter:description" content="Fractional CMO team with Agents working for you 24/7"/> <meta name="twitter:image" content="/og-preview.jpg"/> <link rel="preconnect" href="https://fonts.googleapis.com"/> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/> <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=JetBrains+Mono:wght@400;500;600&amp;display=swap" rel="stylesheet"/>',1),Wa=k('<div class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" role="button" tabindex="0" aria-label="Close mobile menu"></div>'),Ka=k('<h1 class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight"> <span class="text-primary"> </span></h1> <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto"><!></p>',1),Ha=k(`<h1 class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight">The <span class="text-primary">10X fractional CMO</span><br/> team you've been <span class="text-primary">looking for</span></h1> <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto">Strategic 10x marketing team that scales with your ambitions. We
            combine seasoned CMO expertise with cutting-edge AI to transform
            your go-to-market strategy build your GTM machine.</p>`,1),Ja=k('<span class="linear-tag linear-tag-green"> </span>'),Ua=k('<span class="linear-tag linear-tag-green">Strategic Partnership</span> <span class="linear-tag linear-tag-purple">AI-Powered</span>',1),Va=k('<div class="linear-card linear-fade-in p-6 rounded-lg"><span> </span> <h3 class="linear-heading text-xl mb-4"> </h3> <p class="linear-body text-muted-foreground"><!></p></div>'),Za=k(`<div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-green mb-4 inline-block">Content Agent</span> <h3 class="linear-heading text-xl mb-4">Custom Content Agent</h3> <p class="linear-body text-muted-foreground">Generate SEO optimized content in your brand voice. This agent
              analyzes your existing content, understands your unique voice and
              tone, then creates high-performing content that ranks while
              staying true to your brand identity.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-blue mb-4 inline-block">Research Agent</span> <h3 class="linear-heading text-xl mb-4">Competitive Researcher</h3> <p class="linear-body text-muted-foreground">Custom agent to do deep competitive analysis on real-time data.
              Continuously monitors your competitors' strategies, pricing,
              content, and market positioning to give you actionable insights
              and strategic advantages.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-purple mb-4 inline-block">Social Agent</span> <h3 class="linear-heading text-xl mb-4">Social Media Agent</h3> <p class="linear-body text-muted-foreground">Listen to your brand and your competitors' signals, assess
              sentiment and act upon them. This agent monitors social
              conversations, tracks brand mentions, analyzes sentiment trends,
              and provides real-time insights to optimize your social media
              strategy.</p></div>`,1),Qa=k('<h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6"> <span class="text-primary"> </span></h2> <p class="linear-body text-xl text-muted-foreground mb-12"> </p> <div class="flex justify-center"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center"> <!></button></div> <p class="linear-mono text-sm text-muted-foreground mt-8"> </p>',1),eo=k(`<h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Your next chapter starts with <span class="text-primary">us</span></h2> <p class="linear-body text-xl text-muted-foreground mb-12">We don't believe in hard sells or high-pressure tactics. We believe in
          finding the right fit. If you're ready to transform your marketing
          from a cost center into a growth engine, let's talk.</p> <div class="flex justify-center"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center">Start Your Growth Journey <!></button></div> <p class="linear-mono text-sm text-muted-foreground mt-8">Ready to grow smarter, not just faster?</p>`,1),to=k(`<div class="min-h-screen bg-background text-foreground"><nav class="linear-nav fixed top-0 left-0 right-0 z-50 px-6 py-4 transition-all duration-300 ease-in-out"><div class="max-w-7xl mx-auto flex items-center justify-between"><div class="flex items-center space-x-8"><button class="linear-heading text-xl font-bold text-foreground hover:text-primary transition-colors cursor-pointer" aria-label="Scroll to top">Robynn.ai</button> <div class="hidden md:flex items-center space-x-6"><button class="nav-link text-foreground font-medium transition-all">Approach</button> <button class="nav-link text-foreground font-medium transition-all">Services</button> <button class="nav-link text-foreground font-medium transition-all">Agents</button> <button class="nav-link text-foreground font-medium transition-all">Stories</button></div></div> <button class="hidden md:block linear-btn-primary px-6 py-2 rounded-lg">↗ Get Started</button> <button class="md:hidden p-2 text-foreground hover:text-primary transition-colors" aria-label="Toggle mobile menu"><!></button></div></nav> <!> <div><div class="p-6"><div class="flex items-center justify-between mb-8"><div class="linear-heading text-xl font-bold text-foreground">Robynn.ai</div> <button class="p-2 text-foreground hover:text-primary transition-colors" aria-label="Close menu"><!></button></div> <nav class="space-y-2 touch-spacing"><button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Approach</button> <button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Services</button> <button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Agents</button> <button class="block w-full text-left py-3 px-4 text-foreground font-medium hover:bg-muted rounded-lg transition-colors">Stories</button> <div class="pt-4"><button class="w-full linear-btn-primary px-6 py-3 rounded-lg text-center">↗ Get Started</button></div></nav></div></div> <section class="linear-hero linear-grid min-h-screen flex items-center justify-center px-6 py-20" style="padding-top: calc(5rem + 40px);"><div class="max-w-7xl mx-auto text-center hero-content"><div class="mb-8" data-animated=""><!></div> <div class="mb-12" data-animated=""><div class="flex flex-wrap justify-center gap-3 mb-8"><!></div> <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8 touch-spacing"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center"> <!></button> <a href="#agents" class="linear-btn-secondary px-8 py-3 text-lg rounded-lg inline-flex items-center justify-center">See Agents In Action</a></div> <p class="linear-mono text-sm text-muted-foreground"> </p></div> <div data-animated=""><div class="max-w-4xl mx-auto"><h3 class="linear-heading text-2xl text-center mb-8 flex items-center justify-center gap-3"><!> Everything you need. Nothing you don't.</h3> <div class="grid md:grid-cols-3 gap-6 mb-6"><div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded text-sm font-medium mb-4">Fractional CMO</div> <p class="linear-body text-muted-foreground">Strategic clarity without full-time overhead</p></div></div> <div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-blue-500/10 text-blue-600 rounded text-sm font-medium mb-4">Expert Marketers</div> <p class="linear-body text-muted-foreground">Campaigns crafted by hands-on pros</p></div></div> <div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-purple-500/10 text-purple-600 rounded text-sm font-medium mb-4">AI Agents</div> <p class="linear-body text-muted-foreground">Automations that move fast—and scale faster</p></div></div></div> <div class="text-center"><p class="linear-mono text-sm text-muted-foreground/80">Custom-assembled for your company. No bloat. No fluff.</p></div></div></div></div></section> <div class="linear-section-separator"></div> <section class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">You've built something <span class="text-primary">remarkable</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">You're past the early stage hustle. Your product works. Your customers
          love it. You've hit that magical 5M+ revenue milestone. But now you're
          facing a new challenge.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Expensive Team Building</h3> <p class="linear-body text-muted-foreground">VP of Marketing + team costs $500K+ annually</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Scattered Marketing</h3> <p class="linear-body text-muted-foreground">Tactics without strategy, campaigns without cohesion</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Unclear ROI</h3> <p class="linear-body text-muted-foreground">Spending money without knowing what's working</p></div></div></div></section> <div class="linear-dotted-line"></div> <section id="approach" class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Think different about marketing <br/> <span class="text-primary">in the world of AI</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">While others offer fractional hours, we offer strategic embedded team
          members and custom agents for your team. Our CMO-led team doesn't just
          provide strategy, we build your entire GTM machine from the tech stack
          to a GTM campaign system built for your needs.</p></div> <div class="grid md:grid-cols-3 gap-8 mb-16"><div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">01</div> <span class="linear-tag linear-tag-green mb-4 inline-block">Embedded Team</span> <h3 class="linear-heading text-xl mb-4">Your marketing co-founder and your agents when and where you need
            them</h3> <p class="linear-body text-muted-foreground">We think like an owner, act like a partner, and deliver like the CMO
            you've been looking for.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">02</div> <span class="linear-tag linear-tag-purple mb-4 inline-block">AI-Native Approach</span> <h3 class="linear-heading text-xl mb-4">Built for efficiency, powered by intelligence. Automation for
            scaling up.</h3> <p class="linear-body text-muted-foreground">Our methodology leverages AI at every step, making us faster and
            more efficient than traditional approaches.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">03</div> <span class="linear-tag linear-tag-green mb-4 inline-block">Continuous Learning</span> <h3 class="linear-heading text-xl mb-4">End-to-end campaigns tried and tested to generate pipelines and
            revenue</h3> <p class="linear-body text-muted-foreground">Deep product understanding drives differentiated messaging that
            actually resonates with your market.</p></div></div> <div class="linear-card linear-fade-in p-8 rounded-lg"><h3 class="linear-heading text-2xl text-center mb-8">Strategic Leadership, Not Just Services</h3> <p class="linear-body text-center text-muted-foreground mb-8">You get the strategic thinking of a seasoned CMO, the execution power
          of a full marketing team, and the efficiency of AI-native tools. All
          without the politics, overhead, or six-figure salaries.</p> <div class="grid md:grid-cols-4 gap-6"><div class="text-center"><!> <div class="linear-body font-semibold">Strategy</div> <div class="linear-body text-sm text-muted-foreground">GTM Strategy & Execution</div></div> <div class="text-center"><!> <div class="linear-body font-semibold">Messaging</div> <div class="linear-body text-sm text-muted-foreground">Differentiated Positioning</div></div> <div class="text-center"><!> <div class="linear-body font-semibold">Campaigns</div> <div class="linear-body text-sm text-muted-foreground">AI-Powered 1-to-1 Marketing</div></div> <div class="text-center"><!> <div class="linear-body font-semibold">Growth</div> <div class="linear-body text-sm text-muted-foreground">Predictable Pipeline</div></div></div></div></div></section> <section id="services" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Our <span class="text-primary">Services</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">We don't just advise—we build, deploy, and run the complete marketing
          infrastructure your growing business needs to scale efficiently.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Custom Marketing Agents</h3> <p class="linear-body text-muted-foreground">Build and run your custom marketing agents centered around your
            product, your audience and your business requirements.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">AI Campaign Orchestration</h3> <p class="linear-body text-muted-foreground">Build and setup an entire AI based campaign orchestration machine
            for your business.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Complete Marketing Tech Stack</h3> <p class="linear-body text-muted-foreground">Build, setup, and run your entire marketing tech stack from website
            to AI-powered automation, data pipelines and analytics dashboard.</p></div></div></div></section> <section id="agents" class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in animate-on-load"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Marketing <span class="text-primary">Agents</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Our AI Agents That Scale Your Marketing. Meet your new marketing team.
          Each agent is specifically designed to handle complex marketing tasks
          that typically require hours of manual work, delivering results in
          minutes with unprecedented accuracy and insight.</p></div> <div class="grid md:grid-cols-3 gap-8"><!></div> <div class="mt-16 max-w-4xl mx-auto linear-fade-in"><div class="text-center mb-8"><h3 class="linear-heading text-2xl font-bold mb-4">See Our Agents in Action</h3> <p class="linear-body text-muted-foreground">Try Athena, our competitive researcher, and see real-time analysis
            in action.</p></div> <!></div> <div class="max-w-3xl mx-auto mt-16"><div class="text-center mb-8"><h3 class="linear-heading text-3xl font-bold mb-4">🎯 SEO Keyword Research Demo</h3> <p class="linear-body text-muted-foreground">Try Lexi, our SEO strategist, and discover high-value keywords
            instantly.</p></div> <!></div></div></section> <section id="stories" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Real companies. <span class="text-primary">Real results</span>.</h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">See how we've helped AI and technology companies transform their
          go-to-market strategy and achieve remarkable growth.</p></div> <div class="grid md:grid-cols-2 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-green mb-4 inline-block">Protecto</span> <h3 class="linear-heading text-xl mb-4">Data Guardrails for Enterprise AI Agents</h3> <p class="linear-body text-muted-foreground mb-6">A brilliant AI privacy startup helping prevent data leaks, privacy
            violations, and compliance risks in AI automation couldn't break
            through in a crowded market. We repositioned them around "Data
            Guardrails" and built a comprehensive GTM strategy.</p> <div class="linear-testimonial mb-6"><p class="linear-body italic text-muted-foreground">"Working with Robynn was like having a strategic co-founder who
              understood both our technology and our market better than we did."</p> <div class="mt-4"><div class="linear-body font-semibold">Amar Kanagaraj</div> <div class="linear-mono text-sm text-muted-foreground">CEO, Protecto</div></div></div> <div class="grid grid-cols-3 gap-4 text-center"><div><div class="linear-mono text-xl font-bold text-primary">300%</div> <div class="linear-body text-sm text-muted-foreground">Pipeline Growth</div></div> <div><div class="linear-mono text-xl font-bold text-primary">60%</div> <div class="linear-body text-sm text-muted-foreground">Shorter Cycles</div></div> <div><div class="linear-mono text-xl font-bold text-primary">3x</div> <div class="linear-body text-sm text-muted-foreground">Valuation</div></div></div></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-pink mb-4 inline-block">Apptware</span> <h3 class="linear-heading text-xl mb-4">Design first AI Services</h3> <p class="linear-body text-muted-foreground mb-6">A successful AI services company was struggling to gain traction in
            the US market. We rebuilt their market entry strategy from the
            ground up, created campaigns and helped them achieve a 2X pipeline
            growth.</p> <div class="linear-testimonial mb-6"><p class="linear-body italic text-muted-foreground">"They transformed our US market entry from a costly experiment
              into our fastest-growing revenue stream."</p> <div class="mt-4"><div class="linear-body font-semibold">Harish Rohokale</div> <div class="linear-mono text-sm text-muted-foreground">CEO, Apptware</div></div></div> <div class="grid grid-cols-3 gap-4 text-center"><div><div class="linear-mono text-xl font-bold text-primary">$8M</div> <div class="linear-body text-sm text-muted-foreground">Pipeline Built</div></div> <div><div class="linear-mono text-xl font-bold text-primary">150%</div> <div class="linear-body text-sm text-muted-foreground">Goal Exceeded</div></div> <div><div class="linear-mono text-xl font-bold text-primary">4</div> <div class="linear-body text-sm text-muted-foreground">Months</div></div></div></div></div></div></section> <section class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">The Robynn <span class="text-primary">Team</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Meet the strategic minds behind your growth. We're not just
          marketers—we're growth architects with deep expertise in AI,
          technology, and scaling businesses.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-primary">MK</span></div> <h3 class="linear-heading text-xl mb-2">Madhukar Kumar</h3> <p class="linear-body text-muted-foreground mb-4">CEO, Co-Founder</p> <p class="linear-body text-muted-foreground mb-4">Former CMO at two unicorn startups. Two decades plus years scaling
            B2B SaaS companies from $5M to $100M+ ARR. Expert in product-led
            growth and AI-native marketing strategies including building
            compelling and memorable brands.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-green">Go-to-Market Strategy</span> <span class="linear-tag linear-tag-purple">Product-Led Growth</span></div></div> <div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-blue-400/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-blue-400">JH</span></div> <h3 class="linear-heading text-xl mb-2">Joel Horwitz</h3> <p class="linear-body text-muted-foreground mb-4">The AI Demand Gen Maestro</p> <p class="linear-body text-muted-foreground mb-4">Former Head of Growth at Series B AI company. Built marketing
            automation systems that scaled 10x without proportional team growth.
            Expert in AI Demand Gen Orchestration.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-blue">Marketing Automation</span> <span class="linear-tag linear-tag-orange">Demand Generation</span></div></div> <div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-orange-400/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-orange-400">MT</span></div> <h3 class="linear-heading text-xl mb-2">Matt Tanner</h3> <p class="linear-body text-muted-foreground mb-4">SEO and Content 10Xer</p> <p class="linear-body text-muted-foreground mb-4">Software engineer turned SEO expert. Analyze and optimize content
            for AI-powered search engines. Helped large and small companies
            scale their organic traffic by orders of magnitude.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-orange">AI Implementation</span> <span class="linear-tag linear-tag-purple">SEO</span></div></div></div></div></section> <section class="py-20 px-6 linear-grid"><div class="max-w-4xl mx-auto text-center linear-fade-in"><!></div></section></div> <!>`,1);function Do(s,p){at(p,!1);const[n,y]=zt(),d=()=>Rt(pa,"$scrollY",n);let b=fr(p,"data",8);const{homeContent:l,meta:_}=b(),g=mr(1),z=mr(1);let X=ae(!1),B=ae(!1);function oe(){w(X,!0)}function ne(o){console.log("Contact form submitted:",o.detail),w(X,!1)}function U(o){var i;(i=document.getElementById(o))==null||i.scrollIntoView({behavior:"smooth"})}function se(){window.scrollTo({top:0,behavior:"smooth"})}function me(){w(B,!r(B)),Se()}function W(){w(B,!1),Se()}function Se(){typeof document<"u"&&(r(B)?document.body.classList.add("mobile-menu-open"):document.body.classList.remove("mobile-menu-open"))}function Q(o){U(o),W()}function Ce(o){const i=["linear-tag-green","linear-tag-blue","linear-tag-purple"];return i[o%i.length]}function xe(o){return o.toLowerCase().includes("content")||o.toLowerCase().includes("seo")?"Content Agent":o.toLowerCase().includes("competitive")||o.toLowerCase().includes("research")?"Research Agent":o.toLowerCase().includes("social")||o.toLowerCase().includes("campaign")||o.toLowerCase().includes("orchestrator")?"Social Agent":"Agent"}pr(()=>{setTimeout(()=>{try{ea(".linear-hero"),ta(".linear-card",{stagger:.1,duration:.6}),ra(".linear-btn-primary, .linear-btn-secondary")}catch(N){console.debug("Animation initialization error:",N)}},100);const o={threshold:.1,rootMargin:"0px 0px -50px 0px"},i=new IntersectionObserver(N=>{N.forEach(D=>{D.isIntersecting&&(D.target.classList.add("visible"),i.unobserve(D.target))})},o);return document.querySelectorAll(".linear-fade-in:not([data-animated])").forEach(N=>{i.observe(N)}),()=>{i.disconnect()}}),Yt(()=>d(),()=>{g.set(1-Math.min(d(),300)/300),z.set(1-Math.min(d(),300)/300*.2)}),vr(),nt();var M=to();Jr(o=>{var i=qa();Kr.title="Your unfair advantage - Agentic marketing team",F(32),x(o,i)});var P=le(M),c=t(P),f=t(c),h=t(f),Y=t(h),ie=a(Y,2),be=t(ie),I=a(be,2),m=a(I,2),A=a(m,2);e(ie),e(h);var j=a(h,2),ee=a(j,2),ce=t(ee);{var te=o=>{Bt(o,{class:"h-6 w-6"})},pe=o=>{la(o,{class:"h-6 w-6"})};G(ce,o=>{r(B)?o(te):o(pe,!1)})}e(ee),e(f),e(c);var V=a(c,2);{var re=o=>{var i=Wa();C("click",i,W),C("keydown",i,S=>S.key==="Escape"&&W()),x(o,i)};G(V,o=>{r(B)&&o(re)})}var ue=a(V,2);let Ee;var Te=t(ue),ke=t(Te),ve=a(t(ke),2),Ge=t(ve);Bt(Ge,{class:"h-6 w-6"}),e(ve),e(ke);var De=a(ke,2),$=t(De),K=a($,2),J=a(K,2),he=a(J,2),Oe=a(he,2),ye=t(Oe);e(Oe),e(De),e(Te),e(ue);var ge=a(ue,2),H=t(ge),u=t(H),O=t(u);{var Z=o=>{var i=Ka(),S=le(i),N=t(S,!0),D=a(N),fe=t(D,!0);e(D),e(S),T(S,(Le,Me)=>v==null?void 0:v(Le,Me),()=>({delay:.4,duration:1,distance:30}));var $e=a(S,2),Ye=t($e);rt(Ye,()=>E(()=>l.hero.content)),e($e),T($e,(Le,Me)=>v==null?void 0:v(Le,Me),()=>({delay:.6,duration:.8,distance:20})),q(()=>{L(N,E(()=>l.hero.frontmatter.title)),L(fe,E(()=>l.hero.frontmatter.titleHighlight))}),x(o,i)},ze=o=>{var i=Ha(),S=le(i);T(S,(D,fe)=>v==null?void 0:v(D,fe),()=>({delay:.4,duration:1,distance:30}));var N=a(S,2);T(N,(D,fe)=>v==null?void 0:v(D,fe),()=>({delay:.6,duration:.8,distance:20})),x(o,i)};G(O,o=>{E(()=>l==null?void 0:l.hero)?o(Z):o(ze,!1)})}e(u),T(u,(o,i)=>v==null?void 0:v(o,i),()=>({delay:.2,duration:.8}));var Pe=a(u,2),Re=t(Pe),ut=t(Re);{var yr=o=>{var i=Ja(),S=t(i,!0);e(i),T(i,(N,D)=>v==null?void 0:v(N,D),()=>({delay:.9,duration:.6})),q(()=>L(S,E(()=>l.hero.frontmatter.badge))),x(o,i)},_r=o=>{var i=Ua(),S=le(i);T(S,(D,fe)=>v==null?void 0:v(D,fe),()=>({delay:.9,duration:.6}));var N=a(S,2);T(N,(D,fe)=>v==null?void 0:v(D,fe),()=>({delay:1,duration:.6})),x(o,i)};G(ut,o=>{E(()=>{var i,S;return(S=(i=l==null?void 0:l.hero)==null?void 0:i.frontmatter)==null?void 0:S.badge})?o(yr):o(_r,!1)})}e(Re);var pt=a(Re,2),Xe=t(pt),It=t(Xe),wr=a(It);Ue(wr,{class:"ml-2 h-4 w-4"}),e(Xe),qe(()=>C("click",Xe,oe)),T(Xe,(o,i)=>Fe==null?void 0:Fe(o,i),()=>({scale:1.05})),T(Xe,o=>we==null?void 0:we(o)),T(Xe,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1,duration:.6}));var vt=a(Xe,2);T(vt,(o,i)=>Fe==null?void 0:Fe(o,i),()=>({scale:1.05})),T(vt,o=>we==null?void 0:we(o)),T(vt,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1.15,duration:.6})),e(pt);var gt=a(pt,2),kr=t(gt,!0);e(gt),T(gt,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1.2,duration:.5})),e(Pe),T(Pe,(o,i)=>v==null?void 0:v(o,i),()=>({delay:.8,duration:.6}));var ft=a(Pe,2),Ft=t(ft),Ve=t(Ft),$r=t(Ve);na($r,{class:"h-6 w-6 text-primary"}),F(),e(Ve),T(Ve,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1.5,duration:.6}));var qt=a(Ve,2),xt=t(qt);T(xt,o=>je==null?void 0:je(o)),T(xt,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1.6,duration:.6}));var bt=a(xt,2);T(bt,o=>je==null?void 0:je(o)),T(bt,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1.8,duration:.6}));var Wt=a(bt,2);T(Wt,o=>je==null?void 0:je(o)),T(Wt,(o,i)=>v==null?void 0:v(o,i),()=>({delay:2,duration:.6})),e(qt),F(2),e(Ft),e(ft),T(ft,(o,i)=>v==null?void 0:v(o,i),()=>({delay:1.4,duration:.8})),e(H),T(H,(o,i)=>Pt==null?void 0:Pt(o,i),()=>({from:1,to:.3,offset:["start start","end start"]})),e(ge);var ht=a(ge,4),Kt=t(ht),Ht=a(t(Kt),2),yt=t(Ht),Mr=t(yt);ia(Mr,{class:"h-8 w-8 text-primary mb-4"}),F(4),e(yt);var _t=a(yt,2),Ar=t(_t);ma(Ar,{class:"h-8 w-8 text-blue-400 mb-4"}),F(4),e(_t);var Jt=a(_t,2),Sr=t(Jt);ca(Sr,{class:"h-8 w-8 text-orange-400 mb-4"}),F(4),e(Jt),e(Ht),e(Kt),e(ht);var wt=a(ht,4),Ut=t(wt),Vt=a(t(Ut),4),Zt=a(t(Vt),4),kt=t(Zt),Er=t(kt);tt(Er,{class:"h-6 w-6 text-primary mx-auto mb-2"}),F(4),e(kt);var $t=a(kt,2),Tr=t($t);tt(Tr,{class:"h-6 w-6 text-primary mx-auto mb-2"}),F(4),e($t);var Mt=a($t,2),Cr=t(Mt);tt(Cr,{class:"h-6 w-6 text-primary mx-auto mb-2"}),F(4),e(Mt);var Qt=a(Mt,2),Or=t(Qt);tt(Or,{class:"h-6 w-6 text-primary mx-auto mb-2"}),F(4),e(Qt),e(Zt),e(Vt),e(Ut),e(wt);var At=a(wt,2),er=t(At),tr=a(t(er),2),St=t(tr),Lr=t(St);da(Lr,{class:"h-8 w-8 text-primary mb-4"}),F(4),e(St);var Et=a(St,2),jr=t(Et);Nt(jr,{class:"h-8 w-8 text-blue-400 mb-4"}),F(4),e(Et);var rr=a(Et,2),Dr=t(rr);sa(Dr,{class:"h-8 w-8 text-orange-400 mb-4"}),F(4),e(rr),e(tr),e(er),e(At);var Tt=a(At,2),ar=t(Tt),Ct=a(t(ar),2),Pr=t(Ct);{var Yr=o=>{var i=Ke(),S=le(i);Ne(S,1,()=>E(()=>l.marketingAgents),Ie,(N,D,fe)=>{var $e=Va(),Ye=t($e),Le=t(Ye,!0);e(Ye);var Me=a(Ye,2),Ze=t(Me,!0);e(Me);var Qe=a(Me,2),et=t(Qe);rt(et,()=>(r(D),E(()=>r(D).content))),e(Qe),e($e),q((Lt,Fr)=>{Ae(Ye,1,`linear-tag ${Lt??""} mb-4 inline-block`),L(Le,Fr),L(Ze,(r(D),E(()=>r(D).frontmatter.name)))},[()=>E(()=>Ce(fe)),()=>(r(D),E(()=>xe(r(D).frontmatter.name)))]),x(N,$e)}),x(o,i)},Br=o=>{var i=Za();F(4),x(o,i)};G(Pr,o=>{E(()=>(l==null?void 0:l.marketingAgents)&&l.marketingAgents.length>0)?o(Yr):o(Br,!1)})}e(Ct);var Ot=a(Ct,2),Gr=a(t(Ot),2);ja(Gr,{}),e(Ot);var or=a(Ot,2),zr=a(t(or),2);Fa(zr,{}),e(or),e(ar),e(Tt);var nr=a(Tt,6),sr=t(nr),Rr=t(sr);{var Xr=o=>{var i=Qa(),S=le(i),N=t(S),D=a(N),fe=t(D,!0);e(D),e(S);var $e=a(S,2),Ye=t($e,!0);e($e);var Le=a($e,2),Me=t(Le),Ze=t(Me),Qe=a(Ze);Ue(Qe,{class:"ml-2 h-4 w-4"}),e(Me),e(Le);var et=a(Le,2),Lt=t(et,!0);e(et),q(()=>{L(N,`${E(()=>l.sections.cta.frontmatter.title)??""} `),L(fe,E(()=>l.sections.cta.frontmatter.titleHighlight)),L(Ye,E(()=>l.sections.cta.frontmatter.subtitle)),L(Ze,`${E(()=>l.sections.cta.frontmatter.ctaPrimary)??""} `),L(Lt,E(()=>l.sections.cta.frontmatter.footer))}),C("click",Me,oe),x(o,i)},Nr=o=>{var i=eo(),S=a(le(i),4),N=t(S),D=a(t(N));Ue(D,{class:"ml-2 h-4 w-4"}),e(N),e(S),F(2),C("click",N,oe),x(o,i)};G(Rr,o=>{E(()=>{var i;return(i=l==null?void 0:l.sections)==null?void 0:i.cta})?o(Xr):o(Nr,!1)})}e(sr),e(nr),e(P);var Ir=a(P,2);wa(Ir,{get isOpen(){return r(X)},set isOpen(o){w(X,o)},$$events:{close:()=>w(X,!1),submit:ne},$$legacy:!0}),q(o=>{Ee=Ae(ue,1,"fixed top-0 right-0 h-full w-80 bg-background border-l border-border z-50 transform transition-transform duration-300 ease-in-out md:hidden",null,Ee,o),L(It,`${E(()=>{var i,S;return((S=(i=l==null?void 0:l.hero)==null?void 0:i.frontmatter)==null?void 0:S.ctaPrimary)||"Talk to Us"})??""} `),L(kr,E(()=>{var i,S;return((S=(i=l==null?void 0:l.hero)==null?void 0:i.frontmatter)==null?void 0:S.trustBadge)||"Trusted by 50+ scaling startups"}))},[()=>({"translate-x-0":r(B),"translate-x-full":!r(B)})]),C("click",Y,se),C("click",be,()=>U("approach")),C("click",I,()=>U("services")),C("click",m,()=>U("agents")),C("click",A,()=>U("stories")),C("click",j,oe),C("click",ee,me),C("click",ve,W),C("click",$,()=>Q("approach")),C("click",K,()=>Q("services")),C("click",J,()=>Q("agents")),C("click",he,()=>Q("stories")),C("click",ye,()=>{oe(),W()}),x(s,M),ot(),y()}export{Do as component};
