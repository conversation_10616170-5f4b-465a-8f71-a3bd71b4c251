/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){.svelte-1dxq2jv,.svelte-1dxq2jv:before,.svelte-1dxq2jv:after,.svelte-1dxq2jv::backdrop{--tw-duration:initial}}}.progress{position:relative;overflow:hidden}.progress::-webkit-progress-value{-webkit-transition-property:all;transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s);--tw-duration:.3s;transition-duration:.3s}.progress::-moz-progress-bar{-moz-transition-property:all;transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s);--tw-duration:.3s;transition-duration:.3s}@property --tw-duration{syntax:"*";inherits:false}
