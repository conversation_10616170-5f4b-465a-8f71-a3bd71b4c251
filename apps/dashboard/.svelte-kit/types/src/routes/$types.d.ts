import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = {  };
type RouteId = '/';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type PageServerParentData = EnsureDefined<LayoutServerData>;
type PageParentData = EnsureDefined<LayoutData>;
type LayoutRouteId = RouteId | "/" | "/(admin)/api" | "/(admin)/dashboard/[envSlug]/(menu)" | "/(admin)/dashboard/[envSlug]/(menu)/billing" | "/(admin)/dashboard/[envSlug]/(menu)/billing/manage" | "/(admin)/dashboard/[envSlug]/(menu)/settings" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email_subscription" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_password" | "/(admin)/dashboard/[envSlug]/(menu)/settings/delete_account" | "/(admin)/dashboard/[envSlug]/(menu)/settings/edit_profile" | "/(admin)/dashboard/[envSlug]/(menu)/settings/reset_password" | "/(admin)/dashboard/[envSlug]/agent-seo" | "/(admin)/dashboard/[envSlug]/brand-monitor" | "/(admin)/dashboard/[envSlug]/campaign-orchestrator" | "/(admin)/dashboard/[envSlug]/content-agent" | "/(admin)/dashboard/[envSlug]/create_profile" | "/(admin)/dashboard/[envSlug]/researcher" | "/(admin)/dashboard/[envSlug]/select_plan" | "/(admin)/dashboard/[envSlug]/subscribe/[slug]" | "/(admin)/sign_out" | "/(marketing)/account" | "/(marketing)/account/api" | "/(marketing)/account/billing" | "/(marketing)/account/billing/manage" | "/(marketing)/account/select_plan" | "/(marketing)/account/sign_out" | "/(marketing)/account/subscribe/[slug]" | "/(marketing)/blog" | "/(marketing)/blog/(posts)/awesome_post" | "/(marketing)/blog/(posts)/example_blog_post" | "/(marketing)/blog/(posts)/how_we_built_our_41kb_saas_website" | "/(marketing)/contact_us" | "/(marketing)/find-env" | "/(marketing)/login" | "/(marketing)/login/check_email" | "/(marketing)/login/confirm" | "/(marketing)/login/current_password_error" | "/(marketing)/login/forgot_password" | "/(marketing)/login/sign_in" | "/(marketing)/login/sign_up" | "/(marketing)/onboarding" | "/(marketing)/pricing" | "/(marketing)/search" | null
type LayoutParams = RouteParams & { envSlug?: string; slug?: string }
type LayoutServerParentData = EnsureDefined<{}>;
type LayoutParentData = EnsureDefined<{}>;

export type PageServerLoad<OutputData extends OutputDataShape<PageServerParentData> = OutputDataShape<PageServerParentData>> = Kit.ServerLoad<RouteParams, PageServerParentData, OutputData, RouteId>;
export type PageServerLoadEvent = Parameters<PageServerLoad>[0];
export type ActionData = unknown;
export type PageServerData = Expand<OptionalUnion<EnsureDefined<Kit.LoadProperties<Awaited<ReturnType<typeof import('../../../../src/routes/+page.server.js').load>>>>>>;
export type PageData = Expand<Omit<PageParentData, keyof PageServerData> & EnsureDefined<PageServerData>>;
export type Action<OutputData extends Record<string, any> | void = Record<string, any> | void> = Kit.Action<RouteParams, OutputData, RouteId>
export type Actions<OutputData extends Record<string, any> | void = Record<string, any> | void> = Kit.Actions<RouteParams, OutputData, RouteId>
export type PageProps = { params: RouteParams; data: PageData; form: ActionData }
export type LayoutServerLoad<OutputData extends OutputDataShape<LayoutServerParentData> = OutputDataShape<LayoutServerParentData>> = Kit.ServerLoad<LayoutParams, LayoutServerParentData, OutputData, LayoutRouteId>;
export type LayoutServerLoadEvent = Parameters<LayoutServerLoad>[0];
export type LayoutServerData = Expand<OptionalUnion<EnsureDefined<Kit.LoadProperties<Awaited<ReturnType<typeof import('../../../../src/routes/+layout.server.js').load>>>>>>;
export type LayoutData = Expand<Omit<LayoutParentData, keyof LayoutServerData> & EnsureDefined<LayoutServerData>>;
export type LayoutProps = { params: LayoutParams; data: LayoutData; children: import("svelte").Snippet }
export type RequestEvent = Kit.RequestEvent<RouteParams, RouteId>;