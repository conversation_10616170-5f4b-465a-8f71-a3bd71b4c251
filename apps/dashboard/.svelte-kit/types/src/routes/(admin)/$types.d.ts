import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = {  };
type RouteId = '/(admin)';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type LayoutRouteId = RouteId | "/(admin)/api" | "/(admin)/dashboard/[envSlug]/(menu)" | "/(admin)/dashboard/[envSlug]/(menu)/billing" | "/(admin)/dashboard/[envSlug]/(menu)/billing/manage" | "/(admin)/dashboard/[envSlug]/(menu)/settings" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email_subscription" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_password" | "/(admin)/dashboard/[envSlug]/(menu)/settings/delete_account" | "/(admin)/dashboard/[envSlug]/(menu)/settings/edit_profile" | "/(admin)/dashboard/[envSlug]/(menu)/settings/reset_password" | "/(admin)/dashboard/[envSlug]/agent-seo" | "/(admin)/dashboard/[envSlug]/brand-monitor" | "/(admin)/dashboard/[envSlug]/campaign-orchestrator" | "/(admin)/dashboard/[envSlug]/content-agent" | "/(admin)/dashboard/[envSlug]/create_profile" | "/(admin)/dashboard/[envSlug]/researcher" | "/(admin)/dashboard/[envSlug]/select_plan" | "/(admin)/dashboard/[envSlug]/subscribe/[slug]" | "/(admin)/sign_out"
type LayoutParams = RouteParams & { envSlug?: string; slug?: string }
type LayoutServerParentData = EnsureDefined<import('../$types.js').LayoutServerData>;
type LayoutParentData = EnsureDefined<import('../$types.js').LayoutData>;

export type LayoutServerLoad<OutputData extends Partial<App.PageData> & Record<string, any> | void = Partial<App.PageData> & Record<string, any> | void> = Kit.ServerLoad<LayoutParams, LayoutServerParentData, OutputData, LayoutRouteId>;
export type LayoutServerLoadEvent = Parameters<LayoutServerLoad>[0];
export type LayoutServerData = Expand<OptionalUnion<EnsureDefined<Kit.LoadProperties<Awaited<ReturnType<typeof import('../../../../../src/routes/(admin)/+layout.server.js').load>>>>>>;
export type LayoutLoad<OutputData extends OutputDataShape<LayoutParentData> = OutputDataShape<LayoutParentData>> = Kit.Load<LayoutParams, LayoutServerData, LayoutParentData, OutputData, LayoutRouteId>;
export type LayoutLoadEvent = Parameters<LayoutLoad>[0];
export type LayoutData = Expand<Omit<LayoutParentData, keyof Kit.LoadProperties<Awaited<ReturnType<typeof import('../../../../../src/routes/(admin)/+layout.js').load>>>> & OptionalUnion<EnsureDefined<Kit.LoadProperties<Awaited<ReturnType<typeof import('../../../../../src/routes/(admin)/+layout.js').load>>>>>>;
export type LayoutProps = { params: LayoutParams; data: LayoutData; children: import("svelte").Snippet }
export type RequestEvent = Kit.RequestEvent<RouteParams, RouteId>;