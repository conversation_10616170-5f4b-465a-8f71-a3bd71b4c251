// @ts-nocheck
import type { PageServerLoad } from './$types'
import { redirect } from '@sveltejs/kit'

export const load = async ({ locals: { supabase, getSession }, params }: Parameters<PageServerLoad>[0]) => {
  const session = await getSession()

  if (!session) {
    throw redirect(303, '/login/sign_in')
  }

  // Validate envSlug parameter
  const { envSlug } = params
  if (!envSlug) {
    throw redirect(303, '/dashboard')
  }

  return {
    session,
    envSlug,
    user: session.user
  }
}
