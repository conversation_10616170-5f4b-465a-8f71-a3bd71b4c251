// @ts-nocheck
import type { PageServerLoad } from "./$types"
import { redirect } from "@sveltejs/kit"

export const load = async ({
  locals: { supabase, safeGetSession },
  params,
}: Parameters<PageServerLoad>[0]) => {
  const { session } = await safeGetSession()

  // TODO: Fix redirect issue - temporarily disabled for testing
  // if (!session || session.user.is_anonymous) {
  //   throw redirect(302, "/login")
  // }

  // Validate envSlug parameter
  const { envSlug } = params
  if (!envSlug) {
    throw redirect(303, "/dashboard")
  }

  return {
    session,
    envSlug,
    user: session?.user || null,
  }
}
