{"framework": "nextjs", "buildCommand": "pnpm build", "installCommand": "pnpm install", "devCommand": "pnpm dev", "env": {"SUPABASE_URL": "@supabase_url", "SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "API_SECRET_KEY": "@api_secret_key", "NODE_ENV": "production", "NEXT_PUBLIC_APP_NAME": "FireGeo API", "NEXT_PUBLIC_APP_VERSION": "1.0.0"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}]}], "functions": {"app/api/brand-monitor/analyze/route.ts": {"maxDuration": 300}}}