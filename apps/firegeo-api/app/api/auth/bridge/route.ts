import { NextRequest, NextResponse } from 'next/server'
import { validateSupaba<PERSON>Token, createFireGeoSession } from '@/lib/auth-bridge'
import type { AuthBridgeRequest, AuthBridgeResponse } from 'shared'

export async function POST(request: NextRequest) {
  try {
    // TEMPORARILY DISABLED: Authentication validation for testing
    console.log('[Auth Bridge] Authentication temporarily disabled for testing')

    // Create a mock auth context for testing
    const mockAuthContext = {
      userId: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }

    // Create FireGeo session with mock data
    const session = createFireGeoSession(mockAuthContext)

    const response: AuthBridgeResponse = {
      success: true,
      session,
      user: mockAuthContext
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Auth bridge error:', error)

    const response: AuthBridgeResponse = {
      success: false,
      error: 'Authentication failed'
    }

    return NextResponse.json(response, { status: 500 })
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({ 
    status: 'ok', 
    service: 'auth-bridge',
    timestamp: new Date().toISOString()
  })
}
