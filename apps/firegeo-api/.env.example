# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Better Auth
BETTER_AUTH_SECRET="your-32-character-secret-key"
NEXT_PUBLIC_APP_URL="http://localhost:3001"
BETTER_AUTH_URL="http://localhost:3001"

# Autumn Billing
AUTUMN_SECRET_KEY="your-autumn-api-key"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Email (Resend)
RESEND_API_KEY="re_..."

# AI Providers (Optional)
OPENAI_API_KEY="sk-..."
ANTHROPIC_API_KEY="sk-ant-..."
GOOGLE_GENERATIVE_AI_API_KEY="..."
PERPLEXITY_API_KEY="pplx-..."

# Firecrawl (Optional)
FIRECRAWL_API_KEY="fc-..."

# Integration with Dashboard (Supabase)
SUPABASE_URL="your_supabase_project_url"
SUPABASE_SERVICE_KEY="your_supabase_service_role_key"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,https://yourdomain.com"
DASHBOARD_URL="http://localhost:3000"

# API Security
API_SECRET="your_shared_api_secret_key"

# Environment
NODE_ENV="development"
