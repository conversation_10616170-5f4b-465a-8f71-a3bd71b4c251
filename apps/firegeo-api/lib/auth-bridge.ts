import { createClient } from '@supabase/supabase-js'
import { NextRequest } from 'next/server'
import type { SupabaseUser, FireGeoAuthContext, FireGeoSession } from 'shared'

// Initialize Supabase client for server-side operations
const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY

let supabase: ReturnType<typeof createClient> | null = null

if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey)
}

/**
 * Validates a Supabase JWT token and returns user context
 */
export async function validateSupabaseToken(token: string): Promise<FireGeoAuthContext | null> {
  if (!supabase) {
    console.error('Supabase client not initialized - missing SUPABASE_URL or SUPABASE_SERVICE_KEY')
    return null
  }

  try {
    const { data: { user }, error } = await supabase.auth.getUser(token)

    if (error || !user) {
      console.error('Token validation error:', error)
      return null
    }

    return {
      userId: user.id,
      email: user.email!,
      name: user.user_metadata?.full_name || user.email!,
      avatarUrl: user.user_metadata?.avatar_url
    }
  } catch (error) {
    console.error('Token validation exception:', error)
    return null
  }
}

/**
 * Extracts and validates auth from request headers
 */
export async function extractAuthFromRequest(request: NextRequest): Promise<FireGeoAuthContext | null> {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader?.startsWith('Bearer ')) {
    return null
  }
  
  const token = authHeader.substring(7)
  return validateSupabaseToken(token)
}

/**
 * Creates a FireGeo-compatible session from auth context
 */
export function createFireGeoSession(authContext: FireGeoAuthContext): FireGeoSession {
  return {
    user: {
      id: authContext.userId,
      email: authContext.email,
      name: authContext.name || authContext.email,
      image: authContext.avatarUrl
    }
  }
}

/**
 * Validates API secret for internal service communication
 */
export function validateApiSecret(request: NextRequest): boolean {
  const apiSecret = request.headers.get('x-api-secret')
  const expectedSecret = process.env.API_SECRET
  
  if (!expectedSecret) {
    console.warn('API_SECRET not configured')
    return false
  }
  
  return apiSecret === expectedSecret
}

/**
 * Enhanced auth extraction that supports both Bearer tokens and API secrets
 */
export async function extractAuthContext(request: NextRequest): Promise<{
  authContext: FireGeoAuthContext | null
  isApiCall: boolean
}> {
  // Check for API secret first (for internal service calls)
  const isApiCall = validateApiSecret(request)
  
  if (isApiCall) {
    // For API calls, we might need to extract user context from request body or headers
    const userIdHeader = request.headers.get('x-user-id')
    const userEmailHeader = request.headers.get('x-user-email')
    
    if (userIdHeader && userEmailHeader) {
      return {
        authContext: {
          userId: userIdHeader,
          email: userEmailHeader,
          name: request.headers.get('x-user-name') || userEmailHeader
        },
        isApiCall: true
      }
    }
  }
  
  // Fall back to Bearer token validation
  const authContext = await extractAuthFromRequest(request)
  return {
    authContext,
    isApiCall: false
  }
}
