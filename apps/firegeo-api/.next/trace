[{"name": "generate-buildid", "duration": 81, "timestamp": 651774132637, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753157001355, "traceId": "4b32661fe6e4e0bb"}, {"name": "load-custom-routes", "duration": 111, "timestamp": 651774132749, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753157001355, "traceId": "4b32661fe6e4e0bb"}, {"name": "create-dist-dir", "duration": 989, "timestamp": 651774154142, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753157001377, "traceId": "4b32661fe6e4e0bb"}, {"name": "create-pages-mapping", "duration": 71, "timestamp": 651774182487, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753157001405, "traceId": "4b32661fe6e4e0bb"}, {"name": "collect-app-paths", "duration": 1185, "timestamp": 651774182571, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753157001405, "traceId": "4b32661fe6e4e0bb"}, {"name": "create-app-mapping", "duration": 1187, "timestamp": 651774183765, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753157001406, "traceId": "4b32661fe6e4e0bb"}, {"name": "public-dir-conflict-check", "duration": 160, "timestamp": 651774185180, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753157001408, "traceId": "4b32661fe6e4e0bb"}, {"name": "generate-routes-manifest", "duration": 1210, "timestamp": 651774185397, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753157001408, "traceId": "4b32661fe6e4e0bb"}, {"name": "next-build", "duration": 3973335, "timestamp": 651774082177, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.5", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1753157001305, "traceId": "4b32661fe6e4e0bb"}]