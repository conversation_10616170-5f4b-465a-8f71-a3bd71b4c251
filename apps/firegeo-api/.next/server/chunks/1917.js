"use strict";exports.id=1917,exports.ids=[1917],exports.modules={21917:(e,t,a)=>{var r,s,i,n;let d;a.d(t,{kY:()=>n,bz:()=>e$,YO:()=>eF,zM:()=>eE,Ie:()=>eN,gM:()=>ez,k5:()=>eW,Nl:()=>eS,RZ:()=>eU,eu:()=>eK,ch:()=>eR,ai:()=>eI,Ik:()=>eM,lq:()=>eB,g1:()=>eV,Yj:()=>ej,PV:()=>eD,KC:()=>eL,L5:()=>eP}),function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let a of e)t[a]=a;return t},e.getValidEnumValues=t=>{let a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(let e of a)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(let a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(r||(r={})),(s||(s={})).mergeShapes=(e,t)=>({...e,...t});let u=r.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),o=e=>{switch(typeof e){case"undefined":return u.undefined;case"string":return u.string;case"number":return Number.isNaN(e)?u.nan:u.number;case"boolean":return u.boolean;case"function":return u.function;case"bigint":return u.bigint;case"symbol":return u.symbol;case"object":if(Array.isArray(e))return u.array;if(null===e)return u.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return u.promise;if("undefined"!=typeof Map&&e instanceof Map)return u.map;if("undefined"!=typeof Set&&e instanceof Set)return u.set;if("undefined"!=typeof Date&&e instanceof Date)return u.date;return u.object;default:return u.unknown}},l=r.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class c extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},a={_errors:[]},r=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(r);else if("invalid_return_type"===s.code)r(s.returnTypeError);else if("invalid_arguments"===s.code)r(s.argumentsError);else if(0===s.path.length)a._errors.push(t(s));else{let e=a,r=0;for(;r<s.path.length;){let a=s.path[r];r===s.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(s))):e[a]=e[a]||{_errors:[]},e=e[a],r++}}};return r(this),a}static assert(e){if(!(e instanceof c))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,r.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},a=[];for(let r of this.issues)if(r.path.length>0){let a=r.path[0];t[a]=t[a]||[],t[a].push(e(r))}else a.push(e(r));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}c.create=e=>new c(e);let h=(e,t)=>{let a;switch(e.code){case l.invalid_type:a=e.received===u.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case l.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,r.jsonStringifyReplacer)}`;break;case l.unrecognized_keys:a=`Unrecognized key(s) in object: ${r.joinValues(e.keys,", ")}`;break;case l.invalid_union:a="Invalid input";break;case l.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${r.joinValues(e.options)}`;break;case l.invalid_enum_value:a=`Invalid enum value. Expected ${r.joinValues(e.options)}, received '${e.received}'`;break;case l.invalid_arguments:a="Invalid function arguments";break;case l.invalid_return_type:a="Invalid function return type";break;case l.invalid_date:a="Invalid date";break;case l.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:r.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case l.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type||"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case l.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case l.custom:a="Invalid input";break;case l.invalid_intersection_types:a="Intersection results could not be merged";break;case l.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case l.not_finite:a="Number must be finite";break;default:a=t.defaultError,r.assertNever(e)}return{message:a}};!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(i||(i={}));let p=e=>{let{data:t,path:a,errorMaps:r,issueData:s}=e,i=[...a,...s.path||[]],n={...s,path:i};if(void 0!==s.message)return{...s,path:i,message:s.message};let d="";for(let e of r.filter(e=>!!e).slice().reverse())d=e(n,{data:t,defaultError:d}).message;return{...s,path:i,message:d}};function m(e,t){let a=p({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,h,h==h?void 0:h].filter(e=>!!e)});e.common.issues.push(a)}class f{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let a=[];for(let r of t){if("aborted"===r.status)return _;"dirty"===r.status&&e.dirty(),a.push(r.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){let a=[];for(let e of t){let t=await e.key,r=await e.value;a.push({key:t,value:r})}return f.mergeObjectSync(e,a)}static mergeObjectSync(e,t){let a={};for(let r of t){let{key:t,value:s}=r;if("aborted"===t.status||"aborted"===s.status)return _;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||r.alwaysSet)&&(a[t.value]=s.value)}return{status:e.value,value:a}}}let _=Object.freeze({status:"aborted"}),y=e=>({status:"dirty",value:e}),g=e=>({status:"valid",value:e}),v=e=>"aborted"===e.status,k=e=>"dirty"===e.status,x=e=>"valid"===e.status,b=e=>"undefined"!=typeof Promise&&e instanceof Promise;class w{constructor(e,t,a,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let T=(e,t)=>{if(x(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new c(e.common.issues);return this._error=t,this._error}}};function Z(e){if(!e)return{};let{errorMap:t,invalid_type_error:a,required_error:r,description:s}=e;if(t&&(a||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??s.defaultError}:void 0===s.data?{message:i??r??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:i??a??s.defaultError}},description:s}}class O{get description(){return this._def.description}_getType(e){return o(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:o(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new f,ctx:{common:e.parent.common,data:e.data,parsedType:o(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(b(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){let a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:o(e)},r=this._parseSync({data:e,path:a.path,parent:a});return T(a,r)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:o(e)};if(!this["~standard"].async)try{let a=this._parseSync({data:e,path:[],parent:t});return x(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>x(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){let a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:o(e)},r=this._parse({data:e,path:a.path,parent:a});return T(a,await (b(r)?r:Promise.resolve(r)))}refine(e,t){let a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let s=e(t),i=()=>r.addIssue({code:l.custom,...a(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(i(),!1)):!!s||(i(),!1)})}refinement(e,t){return this._refinement((a,r)=>!!e(a)||(r.addIssue("function"==typeof t?t(a,r):t),!1))}_refinement(e){return new ev({schema:this,typeName:n.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ek.create(this,this._def)}nullable(){return ex.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ea.create(this)}promise(){return eg.create(this,this._def)}or(e){return es.create([this,e],this._def)}and(e){return ed.create(this,e,this._def)}transform(e){return new ev({...Z(this._def),schema:this,typeName:n.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eb({...Z(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:n.ZodDefault})}brand(){return new eZ({typeName:n.ZodBranded,type:this,...Z(this._def)})}catch(e){return new ew({...Z(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:n.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eO.create(this,e)}readonly(){return eC.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let C=/^c[^\s-]{8,}$/i,A=/^[0-9a-z]+$/,N=/^[0-9A-HJKMNP-TV-Z]{26}$/i,S=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,j=/^[a-z0-9_-]{21}$/i,I=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,E=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,R=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,$=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,P=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,F=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,M=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,L=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,z=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,D="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",V=RegExp(`^${D}$`);function U(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let a=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${a}`}class K extends O{_parse(e){var t,a,s,i;let n;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==u.string){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.string,received:t.parsedType}),_}let o=new f;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(m(n=this._getOrReturnCtx(e,n),{code:l.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),o.dirty());else if("max"===u.kind)e.data.length>u.value&&(m(n=this._getOrReturnCtx(e,n),{code:l.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),o.dirty());else if("length"===u.kind){let t=e.data.length>u.value,a=e.data.length<u.value;(t||a)&&(n=this._getOrReturnCtx(e,n),t?m(n,{code:l.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):a&&m(n,{code:l.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),o.dirty())}else if("email"===u.kind)R.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"email",code:l.invalid_string,message:u.message}),o.dirty());else if("emoji"===u.kind)d||(d=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),d.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"emoji",code:l.invalid_string,message:u.message}),o.dirty());else if("uuid"===u.kind)S.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"uuid",code:l.invalid_string,message:u.message}),o.dirty());else if("nanoid"===u.kind)j.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"nanoid",code:l.invalid_string,message:u.message}),o.dirty());else if("cuid"===u.kind)C.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"cuid",code:l.invalid_string,message:u.message}),o.dirty());else if("cuid2"===u.kind)A.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"cuid2",code:l.invalid_string,message:u.message}),o.dirty());else if("ulid"===u.kind)N.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"ulid",code:l.invalid_string,message:u.message}),o.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{m(n=this._getOrReturnCtx(e,n),{validation:"url",code:l.invalid_string,message:u.message}),o.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"regex",code:l.invalid_string,message:u.message}),o.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(m(n=this._getOrReturnCtx(e,n),{code:l.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),o.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(m(n=this._getOrReturnCtx(e,n),{code:l.invalid_string,validation:{startsWith:u.value},message:u.message}),o.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(m(n=this._getOrReturnCtx(e,n),{code:l.invalid_string,validation:{endsWith:u.value},message:u.message}),o.dirty()):"datetime"===u.kind?(function(e){let t=`${D}T${U(e)}`,a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,RegExp(`^${t}$`)})(u).test(e.data)||(m(n=this._getOrReturnCtx(e,n),{code:l.invalid_string,validation:"datetime",message:u.message}),o.dirty()):"date"===u.kind?V.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{code:l.invalid_string,validation:"date",message:u.message}),o.dirty()):"time"===u.kind?RegExp(`^${U(u)}$`).test(e.data)||(m(n=this._getOrReturnCtx(e,n),{code:l.invalid_string,validation:"time",message:u.message}),o.dirty()):"duration"===u.kind?E.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"duration",code:l.invalid_string,message:u.message}),o.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(a=u.version)||!a)&&$.test(t)||("v6"===a||!a)&&F.test(t))&&1&&(m(n=this._getOrReturnCtx(e,n),{validation:"ip",code:l.invalid_string,message:u.message}),o.dirty())):"jwt"===u.kind?!function(e,t){if(!I.test(e))return!1;try{let[a]=e.split(".");if(!a)return!1;let r=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),s=JSON.parse(atob(r));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(m(n=this._getOrReturnCtx(e,n),{validation:"jwt",code:l.invalid_string,message:u.message}),o.dirty()):"cidr"===u.kind?(s=e.data,!(("v4"===(i=u.version)||!i)&&P.test(s)||("v6"===i||!i)&&M.test(s))&&1&&(m(n=this._getOrReturnCtx(e,n),{validation:"cidr",code:l.invalid_string,message:u.message}),o.dirty())):"base64"===u.kind?L.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"base64",code:l.invalid_string,message:u.message}),o.dirty()):"base64url"===u.kind?z.test(e.data)||(m(n=this._getOrReturnCtx(e,n),{validation:"base64url",code:l.invalid_string,message:u.message}),o.dirty()):r.assertNever(u);return{status:o.value,value:e.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:l.invalid_string,...i.errToObj(a)})}_addCheck(e){return new K({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...i.errToObj(e)})}url(e){return this._addCheck({kind:"url",...i.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...i.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...i.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...i.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...i.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...i.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...i.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...i.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...i.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...i.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...i.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...i.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...i.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...i.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...i.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...i.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...i.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...i.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...i.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...i.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...i.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...i.errToObj(t)})}nonempty(e){return this.min(1,i.errToObj(e))}trim(){return new K({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new K({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new K({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}K.create=e=>new K({checks:[],typeName:n.ZodString,coerce:e?.coerce??!1,...Z(e)});class W extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==u.number){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.number,received:t.parsedType}),_}let a=new f;for(let s of this._def.checks)"int"===s.kind?r.isInteger(e.data)||(m(t=this._getOrReturnCtx(e,t),{code:l.invalid_type,expected:"integer",received:"float",message:s.message}),a.dirty()):"min"===s.kind?(s.inclusive?e.data<s.value:e.data<=s.value)&&(m(t=this._getOrReturnCtx(e,t),{code:l.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):"max"===s.kind?(s.inclusive?e.data>s.value:e.data>=s.value)&&(m(t=this._getOrReturnCtx(e,t),{code:l.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):"multipleOf"===s.kind?0!==function(e,t){let a=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=a>r?a:r;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,s.value)&&(m(t=this._getOrReturnCtx(e,t),{code:l.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):"finite"===s.kind?Number.isFinite(e.data)||(m(t=this._getOrReturnCtx(e,t),{code:l.not_finite,message:s.message}),a.dirty()):r.assertNever(s);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,a,r){return new W({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:i.toString(r)}]})}_addCheck(e){return new W({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:i.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:i.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:i.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:i.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&r.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let a of this._def.checks)if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;else"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return Number.isFinite(t)&&Number.isFinite(e)}}W.create=e=>new W({checks:[],typeName:n.ZodNumber,coerce:e?.coerce||!1,...Z(e)});class B extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==u.bigint)return this._getInvalidInput(e);let a=new f;for(let s of this._def.checks)"min"===s.kind?(s.inclusive?e.data<s.value:e.data<=s.value)&&(m(t=this._getOrReturnCtx(e,t),{code:l.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):"max"===s.kind?(s.inclusive?e.data>s.value:e.data>=s.value)&&(m(t=this._getOrReturnCtx(e,t),{code:l.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):"multipleOf"===s.kind?e.data%s.value!==BigInt(0)&&(m(t=this._getOrReturnCtx(e,t),{code:l.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):r.assertNever(s);return{status:a.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.bigint,received:t.parsedType}),_}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,a,r){return new B({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:i.toString(r)}]})}_addCheck(e){return new B({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}B.create=e=>new B({checks:[],typeName:n.ZodBigInt,coerce:e?.coerce??!1,...Z(e)});class q extends O{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==u.boolean){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.boolean,received:t.parsedType}),_}return g(e.data)}}q.create=e=>new q({typeName:n.ZodBoolean,coerce:e?.coerce||!1,...Z(e)});class Y extends O{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==u.date){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.date,received:t.parsedType}),_}if(Number.isNaN(e.data.getTime()))return m(this._getOrReturnCtx(e),{code:l.invalid_date}),_;let a=new f;for(let s of this._def.checks)"min"===s.kind?e.data.getTime()<s.value&&(m(t=this._getOrReturnCtx(e,t),{code:l.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),a.dirty()):"max"===s.kind?e.data.getTime()>s.value&&(m(t=this._getOrReturnCtx(e,t),{code:l.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),a.dirty()):r.assertNever(s);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Y({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:i.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:i.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}Y.create=e=>new Y({checks:[],coerce:e?.coerce||!1,typeName:n.ZodDate,...Z(e)});class J extends O{_parse(e){if(this._getType(e)!==u.symbol){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.symbol,received:t.parsedType}),_}return g(e.data)}}J.create=e=>new J({typeName:n.ZodSymbol,...Z(e)});class H extends O{_parse(e){if(this._getType(e)!==u.undefined){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.undefined,received:t.parsedType}),_}return g(e.data)}}H.create=e=>new H({typeName:n.ZodUndefined,...Z(e)});class G extends O{_parse(e){if(this._getType(e)!==u.null){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.null,received:t.parsedType}),_}return g(e.data)}}G.create=e=>new G({typeName:n.ZodNull,...Z(e)});class X extends O{constructor(){super(...arguments),this._any=!0}_parse(e){return g(e.data)}}X.create=e=>new X({typeName:n.ZodAny,...Z(e)});class Q extends O{constructor(){super(...arguments),this._unknown=!0}_parse(e){return g(e.data)}}Q.create=e=>new Q({typeName:n.ZodUnknown,...Z(e)});class ee extends O{_parse(e){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.never,received:t.parsedType}),_}}ee.create=e=>new ee({typeName:n.ZodNever,...Z(e)});class et extends O{_parse(e){if(this._getType(e)!==u.undefined){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.void,received:t.parsedType}),_}return g(e.data)}}et.create=e=>new et({typeName:n.ZodVoid,...Z(e)});class ea extends O{_parse(e){let{ctx:t,status:a}=this._processInputParams(e),r=this._def;if(t.parsedType!==u.array)return m(t,{code:l.invalid_type,expected:u.array,received:t.parsedType}),_;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,s=t.data.length<r.exactLength.value;(e||s)&&(m(t,{code:e?l.too_big:l.too_small,minimum:s?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),a.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(m(t,{code:l.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),a.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(m(t,{code:l.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new w(t,e,t.path,a)))).then(e=>f.mergeArray(a,e));let s=[...t.data].map((e,a)=>r.type._parseSync(new w(t,e,t.path,a)));return f.mergeArray(a,s)}get element(){return this._def.type}min(e,t){return new ea({...this._def,minLength:{value:e,message:i.toString(t)}})}max(e,t){return new ea({...this._def,maxLength:{value:e,message:i.toString(t)}})}length(e,t){return new ea({...this._def,exactLength:{value:e,message:i.toString(t)}})}nonempty(e){return this.min(1,e)}}ea.create=(e,t)=>new ea({type:e,minLength:null,maxLength:null,exactLength:null,typeName:n.ZodArray,...Z(t)});class er extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=r.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==u.object){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.object,received:t.parsedType}),_}let{status:t,ctx:a}=this._processInputParams(e),{shape:r,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof ee&&"strip"===this._def.unknownKeys))for(let e in a.data)s.includes(e)||i.push(e);let n=[];for(let e of s){let t=r[e],s=a.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new w(a,s,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof ee){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)i.length>0&&(m(a,{code:l.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let r=a.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new w(a,r,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let a=await t.key,r=await t.value;e.push({key:a,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>f.mergeObjectSync(t,e)):f.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return i.errToObj,new er({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{let r=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:i.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new er({...this._def,unknownKeys:"strip"})}passthrough(){return new er({...this._def,unknownKeys:"passthrough"})}extend(e){return new er({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new er({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:n.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new er({...this._def,catchall:e})}pick(e){let t={};for(let a of r.objectKeys(e))e[a]&&this.shape[a]&&(t[a]=this.shape[a]);return new er({...this._def,shape:()=>t})}omit(e){let t={};for(let a of r.objectKeys(this.shape))e[a]||(t[a]=this.shape[a]);return new er({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof er){let a={};for(let r in t.shape){let s=t.shape[r];a[r]=ek.create(e(s))}return new er({...t._def,shape:()=>a})}if(t instanceof ea)return new ea({...t._def,type:e(t.element)});if(t instanceof ek)return ek.create(e(t.unwrap()));if(t instanceof ex)return ex.create(e(t.unwrap()));if(t instanceof eu)return eu.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let a of r.objectKeys(this.shape)){let r=this.shape[a];e&&!e[a]?t[a]=r:t[a]=r.optional()}return new er({...this._def,shape:()=>t})}required(e){let t={};for(let a of r.objectKeys(this.shape))if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof ek;)e=e._def.innerType;t[a]=e}return new er({...this._def,shape:()=>t})}keyof(){return ef(r.objectKeys(this.shape))}}er.create=(e,t)=>new er({shape:()=>e,unknownKeys:"strip",catchall:ee.create(),typeName:n.ZodObject,...Z(t)}),er.strictCreate=(e,t)=>new er({shape:()=>e,unknownKeys:"strict",catchall:ee.create(),typeName:n.ZodObject,...Z(t)}),er.lazycreate=(e,t)=>new er({shape:e,unknownKeys:"strip",catchall:ee.create(),typeName:n.ZodObject,...Z(t)});class es extends O{_parse(e){let{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{let a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;let a=e.map(e=>new c(e.ctx.common.issues));return m(t,{code:l.invalid_union,unionErrors:a}),_});{let e,r=[];for(let s of a){let a={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:a});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:a}),a.common.issues.length&&r.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=r.map(e=>new c(e));return m(t,{code:l.invalid_union,unionErrors:s}),_}}get options(){return this._def.options}}es.create=(e,t)=>new es({options:e,typeName:n.ZodUnion,...Z(t)});let ei=e=>{if(e instanceof ep)return ei(e.schema);if(e instanceof ev)return ei(e.innerType());if(e instanceof em)return[e.value];if(e instanceof e_)return e.options;if(e instanceof ey)return r.objectValues(e.enum);else if(e instanceof eb)return ei(e._def.innerType);else if(e instanceof H)return[void 0];else if(e instanceof G)return[null];else if(e instanceof ek)return[void 0,...ei(e.unwrap())];else if(e instanceof ex)return[null,...ei(e.unwrap())];else if(e instanceof eZ)return ei(e.unwrap());else if(e instanceof eC)return ei(e.unwrap());else if(e instanceof ew)return ei(e._def.innerType);else return[]};class en extends O{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==u.object)return m(t,{code:l.invalid_type,expected:u.object,received:t.parsedType}),_;let a=this.discriminator,r=t.data[a],s=this.optionsMap.get(r);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(m(t,{code:l.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),_)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){let r=new Map;for(let a of t){let t=ei(a.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(r.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);r.set(s,a)}}return new en({typeName:n.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...Z(a)})}}class ed extends O{_parse(e){let{status:t,ctx:a}=this._processInputParams(e),s=(e,s)=>{if(v(e)||v(s))return _;let i=function e(t,a){let s=o(t),i=o(a);if(t===a)return{valid:!0,data:t};if(s===u.object&&i===u.object){let s=r.objectKeys(a),i=r.objectKeys(t).filter(e=>-1!==s.indexOf(e)),n={...t,...a};for(let r of i){let s=e(t[r],a[r]);if(!s.valid)return{valid:!1};n[r]=s.data}return{valid:!0,data:n}}if(s===u.array&&i===u.array){if(t.length!==a.length)return{valid:!1};let r=[];for(let s=0;s<t.length;s++){let i=e(t[s],a[s]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}if(s===u.date&&i===u.date&&+t==+a)return{valid:!0,data:t};return{valid:!1}}(e.value,s.value);return i.valid?((k(e)||k(s))&&t.dirty(),{status:t.value,value:i.data}):(m(a,{code:l.invalid_intersection_types}),_)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>s(e,t)):s(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}ed.create=(e,t,a)=>new ed({left:e,right:t,typeName:n.ZodIntersection,...Z(a)});class eu extends O{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==u.array)return m(a,{code:l.invalid_type,expected:u.array,received:a.parsedType}),_;if(a.data.length<this._def.items.length)return m(a,{code:l.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),_;!this._def.rest&&a.data.length>this._def.items.length&&(m(a,{code:l.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...a.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new w(a,e,a.path,t)):null}).filter(e=>!!e);return a.common.async?Promise.all(r).then(e=>f.mergeArray(t,e)):f.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new eu({...this._def,rest:e})}}eu.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new eu({items:e,typeName:n.ZodTuple,rest:null,...Z(t)})};class eo extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==u.object)return m(a,{code:l.invalid_type,expected:u.object,received:a.parsedType}),_;let r=[],s=this._def.keyType,i=this._def.valueType;for(let e in a.data)r.push({key:s._parse(new w(a,e,a.path,e)),value:i._parse(new w(a,a.data[e],a.path,e)),alwaysSet:e in a.data});return a.common.async?f.mergeObjectAsync(t,r):f.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,a){return new eo(t instanceof O?{keyType:e,valueType:t,typeName:n.ZodRecord,...Z(a)}:{keyType:K.create(),valueType:e,typeName:n.ZodRecord,...Z(t)})}}class el extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==u.map)return m(a,{code:l.invalid_type,expected:u.map,received:a.parsedType}),_;let r=this._def.keyType,s=this._def.valueType,i=[...a.data.entries()].map(([e,t],i)=>({key:r._parse(new w(a,e,a.path,[i,"key"])),value:s._parse(new w(a,t,a.path,[i,"value"]))}));if(a.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let a of i){let r=await a.key,s=await a.value;if("aborted"===r.status||"aborted"===s.status)return _;("dirty"===r.status||"dirty"===s.status)&&t.dirty(),e.set(r.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let a of i){let r=a.key,s=a.value;if("aborted"===r.status||"aborted"===s.status)return _;("dirty"===r.status||"dirty"===s.status)&&t.dirty(),e.set(r.value,s.value)}return{status:t.value,value:e}}}}el.create=(e,t,a)=>new el({valueType:t,keyType:e,typeName:n.ZodMap,...Z(a)});class ec extends O{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==u.set)return m(a,{code:l.invalid_type,expected:u.set,received:a.parsedType}),_;let r=this._def;null!==r.minSize&&a.data.size<r.minSize.value&&(m(a,{code:l.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&a.data.size>r.maxSize.value&&(m(a,{code:l.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let s=this._def.valueType;function i(e){let a=new Set;for(let r of e){if("aborted"===r.status)return _;"dirty"===r.status&&t.dirty(),a.add(r.value)}return{status:t.value,value:a}}let n=[...a.data.values()].map((e,t)=>s._parse(new w(a,e,a.path,t)));return a.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new ec({...this._def,minSize:{value:e,message:i.toString(t)}})}max(e,t){return new ec({...this._def,maxSize:{value:e,message:i.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ec.create=(e,t)=>new ec({valueType:e,minSize:null,maxSize:null,typeName:n.ZodSet,...Z(t)});class eh extends O{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==u.function)return m(t,{code:l.invalid_type,expected:u.function,received:t.parsedType}),_;function a(e,a){return p({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,h,h].filter(e=>!!e),issueData:{code:l.invalid_arguments,argumentsError:a}})}function r(e,a){return p({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,h,h].filter(e=>!!e),issueData:{code:l.invalid_return_type,returnTypeError:a}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof eg){let e=this;return g(async function(...t){let n=new c([]),d=await e._def.args.parseAsync(t,s).catch(e=>{throw n.addIssue(a(t,e)),n}),u=await Reflect.apply(i,this,d);return await e._def.returns._def.type.parseAsync(u,s).catch(e=>{throw n.addIssue(r(u,e)),n})})}{let e=this;return g(function(...t){let n=e._def.args.safeParse(t,s);if(!n.success)throw new c([a(t,n.error)]);let d=Reflect.apply(i,this,n.data),u=e._def.returns.safeParse(d,s);if(!u.success)throw new c([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eh({...this._def,args:eu.create(e).rest(Q.create())})}returns(e){return new eh({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new eh({args:e||eu.create([]).rest(Q.create()),returns:t||Q.create(),typeName:n.ZodFunction,...Z(a)})}}class ep extends O{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ep.create=(e,t)=>new ep({getter:e,typeName:n.ZodLazy,...Z(t)});class em extends O{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return m(t,{received:t.data,code:l.invalid_literal,expected:this._def.value}),_}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ef(e,t){return new e_({values:e,typeName:n.ZodEnum,...Z(t)})}em.create=(e,t)=>new em({value:e,typeName:n.ZodLiteral,...Z(t)});class e_ extends O{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),a=this._def.values;return m(t,{expected:r.joinValues(a),received:t.parsedType,code:l.invalid_type}),_}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),a=this._def.values;return m(t,{received:t.data,code:l.invalid_enum_value,options:a}),_}return g(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return e_.create(e,{...this._def,...t})}exclude(e,t=this._def){return e_.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}e_.create=ef;class ey extends O{_parse(e){let t=r.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==u.string&&a.parsedType!==u.number){let e=r.objectValues(t);return m(a,{expected:r.joinValues(e),received:a.parsedType,code:l.invalid_type}),_}if(this._cache||(this._cache=new Set(r.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=r.objectValues(t);return m(a,{received:a.data,code:l.invalid_enum_value,options:e}),_}return g(e.data)}get enum(){return this._def.values}}ey.create=(e,t)=>new ey({values:e,typeName:n.ZodNativeEnum,...Z(t)});class eg extends O{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==u.promise&&!1===t.common.async?(m(t,{code:l.invalid_type,expected:u.promise,received:t.parsedType}),_):g((t.parsedType===u.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eg.create=(e,t)=>new eg({type:e,typeName:n.ZodPromise,...Z(t)});class ev extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===n.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:a}=this._processInputParams(e),s=this._def.effect||null,i={addIssue:e=>{m(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===s.type){let e=s.transform(a.data,i);if(a.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return _;let r=await this._def.schema._parseAsync({data:e,path:a.path,parent:a});return"aborted"===r.status?_:"dirty"===r.status||"dirty"===t.value?y(r.value):r});{if("aborted"===t.value)return _;let r=this._def.schema._parseSync({data:e,path:a.path,parent:a});return"aborted"===r.status?_:"dirty"===r.status||"dirty"===t.value?y(r.value):r}}if("refinement"===s.type){let e=e=>{let t=s.refinement(e,i);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(a=>"aborted"===a.status?_:("dirty"===a.status&&t.dirty(),e(a.value).then(()=>({status:t.value,value:a.value}))));{let r=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===r.status?_:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===s.type)if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(e=>x(e)?Promise.resolve(s.transform(e.value,i)).then(e=>({status:t.value,value:e})):_);else{let e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!x(e))return _;let r=s.transform(e.value,i);if(r instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:r}}r.assertNever(s)}}ev.create=(e,t,a)=>new ev({schema:e,typeName:n.ZodEffects,effect:t,...Z(a)}),ev.createWithPreprocess=(e,t,a)=>new ev({schema:t,effect:{type:"preprocess",transform:e},typeName:n.ZodEffects,...Z(a)});class ek extends O{_parse(e){return this._getType(e)===u.undefined?g(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ek.create=(e,t)=>new ek({innerType:e,typeName:n.ZodOptional,...Z(t)});class ex extends O{_parse(e){return this._getType(e)===u.null?g(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ex.create=(e,t)=>new ex({innerType:e,typeName:n.ZodNullable,...Z(t)});class eb extends O{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return t.parsedType===u.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eb.create=(e,t)=>new eb({innerType:e,typeName:n.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...Z(t)});class ew extends O{_parse(e){let{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return b(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new c(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new c(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}ew.create=(e,t)=>new ew({innerType:e,typeName:n.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...Z(t)});class eT extends O{_parse(e){if(this._getType(e)!==u.nan){let t=this._getOrReturnCtx(e);return m(t,{code:l.invalid_type,expected:u.nan,received:t.parsedType}),_}return{status:"valid",value:e.data}}}eT.create=e=>new eT({typeName:n.ZodNaN,...Z(e)}),Symbol("zod_brand");class eZ extends O{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class eO extends O{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?_:"dirty"===e.status?(t.dirty(),y(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})();{let e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?_:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new eO({in:e,out:t,typeName:n.ZodPipeline})}}class eC extends O{_parse(e){let t=this._def.innerType._parse(e),a=e=>(x(e)&&(e.value=Object.freeze(e.value)),e);return b(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}function eA(e,t){let a="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof a?{message:a}:a}function eN(e,t={},a){return e?X.create().superRefine((r,s)=>{let i=e(r);if(i instanceof Promise)return i.then(e=>{if(!e){let e=eA(t,r),i=e.fatal??a??!0;s.addIssue({code:"custom",...e,fatal:i})}});if(!i){let e=eA(t,r),i=e.fatal??a??!0;s.addIssue({code:"custom",...e,fatal:i})}}):X.create()}eC.create=(e,t)=>new eC({innerType:e,typeName:n.ZodReadonly,...Z(t)}),er.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(n||(n={}));let eS=(e,t={message:`Input not instance of ${e.name}`})=>eN(t=>t instanceof e,t),ej=K.create,eI=W.create;eT.create,B.create;let eE=q.create;Y.create,J.create,H.create;let eR=G.create,e$=X.create,eP=Q.create;ee.create,et.create;let eF=ea.create,eM=er.create;er.strictCreate;let eL=es.create,ez=en.create;ed.create;let eD=eu.create,eV=eo.create;el.create,ec.create,eh.create;let eU=ep.create,eK=em.create,eW=e_.create;ey.create,eg.create,ev.create;let eB=ek.create;ex.create,ev.createWithPreprocess,eO.create}};