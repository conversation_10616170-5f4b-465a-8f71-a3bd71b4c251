exports.id=333,exports.ids=[333],exports.modules={1395:()=>{},55648:(e,t,s)=>{"use strict";s.d(t,{D7:()=>i,O:()=>r,UU:()=>c,Xv:()=>n,cK:()=>o,l_:()=>a});let r="messages",a="user",o="assistant",i=1,n={MESSAGE_PREVIEW_LENGTH:100,TITLE_MAX_LENGTH:50,PROGRESS_ROUNDING:10},c={UNAUTHORIZED:"Unauthorized",MESSAGE_ID_REQUIRED:"Message ID required",CONVERSATION_NOT_FOUND:"Conversation not found",INVALID_THEME:"Invalid theme value",NO_VALID_FIELDS:"No valid fields to update",COMPANY_INFO_REQUIRED:"Company information is required",INVALID_MESSAGE:"Invalid message",FAILED_TO_CHECK_ACCESS:"Failed to check access",FAILED_TO_TRACK_USAGE:"Failed to track usage",NO_CREDITS_REMAINING:"No credits remaining. Please upgrade your plan",INSUFFICIENT_CREDITS_BRAND_ANALYSIS:"You need at least 10 credits for a brand analysis"}},70472:(e,t,s)=>{"use strict";s.d(t,{HD:()=>l,Yo:()=>d,hS:()=>h,m_:()=>n,v3:()=>o,yI:()=>i});var r=s(578);class a extends Error{constructor(e,t,s,r=!0){super(e),this.statusCode=t,this.code=s,this.isOperational=r,Object.setPrototypeOf(this,a.prototype)}}class o extends a{constructor(e="Authentication required",t="UNAUTHORIZED"){super(e,401,t)}}class i extends a{constructor(e="Validation failed",t,s="VALIDATION_ERROR"){super(e,400,s),this.fields=t}}class n extends a{constructor(e="Resource",t="NOT_FOUND"){super(`${e} not found`,404,t)}}class c extends a{constructor(e="Rate limit exceeded",t,s="RATE_LIMIT_EXCEEDED"){super(e,429,s),this.retryAfter=t}}class d extends a{constructor(e="Insufficient credits",t,s){super(e,403,"INSUFFICIENT_CREDITS"),this.creditsRequired=t,this.creditsAvailable=s}}class l extends a{constructor(e,t,s="EXTERNAL_SERVICE_ERROR"){super(e,503,s),this.service=t}}class u extends a{constructor(e="Database operation failed",t="DATABASE_ERROR"){super(e,500,t,!1)}}function p(e){let t={error:{message:e.message,code:e.code,statusCode:e.statusCode,timestamp:new Date().toISOString()}};return e instanceof i&&e.fields&&(t.error.fields=e.fields),e instanceof c&&e.retryAfter&&(t.error.metadata={retryAfter:e.retryAfter}),e instanceof d&&(t.error.metadata={creditsRequired:e.creditsRequired,creditsAvailable:e.creditsAvailable}),e instanceof l&&e.service&&(t.error.metadata={service:e.service}),t}function h(e){if(console.error("[API Error]",e),e instanceof a){let t=p(e);return r.NextResponse.json(t,{status:e.statusCode})}if(e&&"object"==typeof e&&"response"in e&&e.response?.data?.error){let t=new l(e.response.data.error.message||"External service error","payment");return r.NextResponse.json(p(t),{status:503})}if(e instanceof Error){if(e.message.includes("Unauthorized")||e.message.includes("unauthorized")){let e=new o;return r.NextResponse.json(p(e),{status:401})}if(e.message.includes("Database")||e.message.includes("ECONNREFUSED")){let e=new u;return r.NextResponse.json(p(e),{status:500})}let t=new a("An unexpected error occurred",500,"INTERNAL_ERROR",!1);return r.NextResponse.json(p(t),{status:500})}let t=new a("An unexpected error occurred",500,"INTERNAL_ERROR",!1);return r.NextResponse.json(p(t),{status:500})}},77439:(e,t,s)=>{"use strict";s.d(t,{F3:()=>F});var r=s(37141),a=s(21917),o=Object.defineProperty,i=(e,t,s)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,n=(e,t,s)=>i(e,"symbol"!=typeof t?t+"":t,s),c=class e extends Error{constructor(e){super(e.message),n(this,"message"),n(this,"code"),this.message=e.message,this.code=e.code}static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return`${this.message} (code: ${this.code})`}toJSON(){return{message:this.message,code:this.code}}},d=async({instance:e,params:t})=>e.post("/attach",t),l=async({instance:e,params:t})=>e.post("/setup_payment",t),u=async({instance:e,params:t})=>e.post("/cancel",t),p=async({instance:e,params:t})=>e.post("/entitled",t),h=async({instance:e,params:t})=>e.post("/events",t),_=async({instance:e,params:t})=>e.post("/track",t),m=async({instance:e,params:t})=>e.post("/usage",t),y=async({instance:e,params:t})=>e.post("/check",t),g=(e,t,s)=>(t||(t=new F),e({instance:t,...s})),E=e=>({get:(t,s)=>g(I,e,{id:t,params:s}),create:t=>g(j,e,{params:t}),update:(t,s)=>g(R,e,{id:t,params:s}),delete:t=>g(Y,e,{id:t}),billingPortal:(t,s)=>g(v,e,{id:t,params:s})}),f=e=>e?`expand=${e.join(",")}`:"",I=async({instance:e,id:t,params:s})=>t?e.get(`/customers/${t}?${f(s?.expand)}`):{data:null,error:new c({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})},j=async({instance:e,params:t})=>e.post(`/customers?${f(t?.expand)}`,t),R=async({instance:e,id:t,params:s})=>e.post(`/customers/${t}`,s),Y=async({instance:e,id:t})=>e.delete(`/customers/${t}`),v=async({instance:e,id:t,params:s})=>e.post(`/customers/${t}/billing_portal`,s),A=e=>({get:(t,s,r)=>g(b,e,{customer_id:t,entity_id:s,params:r}),create:(t,s)=>g(O,e,{customer_id:t,params:s}),delete:(t,s)=>g(S,e,{customer_id:t,entity_id:s})}),N=e=>e?`expand=${e.join(",")}`:"",b=async({instance:e,customer_id:t,entity_id:s,params:r})=>e.get(`/customers/${t}/entities/${s}?${N(r?.expand)}`),O=async({instance:e,customer_id:t,params:s})=>e.post(`/customers/${t}/entities`,s),S=async({instance:e,customer_id:t,entity_id:s})=>e.delete(`/customers/${t}/entities/${s}`),D=e=>({get:t=>g(T,e,{id:t}),create:t=>g(k,e,{params:t}),list:t=>g(x,e,{params:t})}),x=async({instance:e,params:t})=>{let s="/products_beta";if(t){let e=new URLSearchParams;for(let[s,r]of Object.entries(t))void 0!==r&&e.append(s,String(r));let r=e.toString();r&&(s+=`?${r}`)}return e.get(s)},T=async({instance:e,id:t})=>e.get(`/products/${t}`),k=async({instance:e,params:t})=>e.post("/products",t),w=e=>({createCode:t=>g(C,e,{params:t}),redeemCode:t=>g(U,e,{params:t})}),C=async({instance:e,params:t})=>e.post("/referrals/code",t),U=async({instance:e,params:t})=>e.post("/referrals/redeem",t),$=async({response:e,logger:t,logError:s=!0})=>{if(e.status<200||e.status>=300){let r;try{r=await e.json(),s&&t.error(`[Autumn] ${r.message}`)}catch(e){throw e}return{data:null,error:new c({message:r.message,code:r.code}),statusCode:e.status}}try{return{data:await e.json(),error:null,statusCode:e?.status}}catch(e){throw e}},L=()=>{let e=new Date().toISOString();return`[${e.split("T")[1].split(".")[0]}]`},M=e=>z.indexOf(e)>=z.indexOf(P.level),z=["debug","info","warn","error","fatal"],P={...console,level:"info",debug:(...e)=>{M("debug")&&console.log(L(),r.default.gray("DEBUG"),...e)},log:(...e)=>{console.log(L(),r.default.blue("INFO"),...e)},info:(...e)=>{M("info")&&console.log(L(),r.default.blue("INFO"),...e)},warn:(...e)=>{M("warn")&&console.log(L(),r.default.yellow("WARN"),...e)},error:(...e)=>{M("error")&&console.log(L(),r.default.red("ERROR"),...e)}},F=class{constructor(e){n(this,"secretKey"),n(this,"publishableKey"),n(this,"headers"),n(this,"url"),n(this,"logger",console),n(this,"customers",E(this)),n(this,"products",D(this)),n(this,"entities",A(this)),n(this,"referrals",w(this));try{this.secretKey=e?.secretKey||process.env.AUTUMN_SECRET_KEY,this.publishableKey=e?.publishableKey||process.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!e?.headers)throw Error("Autumn secret key or publishable key is required");this.headers=e?.headers||{Authorization:`Bearer ${this.secretKey||this.publishableKey}`,"Content-Type":"application/json"};let t=e?.version||"1.2";this.headers["x-api-version"]=t,this.url=e?.url||"https://api.useautumn.com/v1",this.logger=P,this.logger.level=e?.logLevel||"info"}async get(e){return $({response:await fetch(`${this.url}${e}`,{headers:this.headers}),logger:this.logger})}async post(e,t){try{let s=await fetch(`${this.url}${e}`,{method:"POST",headers:this.headers,body:JSON.stringify(t)});return $({response:s,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return $({response:await fetch(`${this.url}${e}`,{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return d({instance:this,params:e})}async setupPayment(e){return l({instance:this,params:e})}async cancel(e){return u({instance:this,params:e})}async entitled(e){return p({instance:this,params:e})}async check(e){return y({instance:this,params:e})}async event(e){return h({instance:this,params:e})}async track(e){return _({instance:this,params:e})}async usage(e){return m({instance:this,params:e})}};n(F,"customers",E()),n(F,"products",D()),n(F,"entities",A()),n(F,"referrals",w()),n(F,"attach",e=>g(d,void 0,{params:e})),n(F,"usage",e=>g(m,void 0,{params:e})),n(F,"setupPayment",e=>g(l,void 0,{params:e})),n(F,"cancel",e=>g(u,void 0,{params:e})),n(F,"entitled",e=>g(p,void 0,{params:e})),n(F,"check",e=>g(y,void 0,{params:e})),n(F,"event",e=>g(h,void 0,{params:e})),n(F,"track",e=>g(_,void 0,{params:e}));var K=a.Ik({name:a.Yj().optional(),feature_id:a.Yj()});a.Ik({feature_id:a.Yj(),quantity:a.ai()}),a.Ik({customer_id:a.Yj(),product_id:a.Yj().optional(),entity_id:a.Yj().optional(),options:a.YO(a.Ik({feature_id:a.Yj(),quantity:a.ai()})).optional(),product_ids:a.YO(a.Yj()).optional(),free_trial:a.zM().optional(),success_url:a.Yj().optional(),metadata:a.g1(a.Yj()).optional(),force_checkout:a.zM().optional(),customer_data:a.bz().optional(),entity_data:a.bz().optional(),checkout_session_params:a.g1(a.bz()).optional(),reward:a.Yj().optional()}),a.Ik({checkout_url:a.Yj().optional(),customer_id:a.Yj(),product_ids:a.YO(a.Yj()),code:a.Yj(),message:a.Yj(),customer_data:a.bz().optional()}),a.Ik({customer_id:a.Yj(),product_id:a.Yj(),entity_id:a.Yj().optional(),cancel_immediately:a.zM().optional()}),a.Ik({success:a.zM(),customer_id:a.Yj(),product_id:a.Yj()}),a.Ik({customer_id:a.Yj(),value:a.ai().optional(),feature_id:a.Yj().optional(),event_name:a.Yj().optional(),entity_id:a.Yj().optional(),customer_data:a.bz().optional(),idempotency_key:a.Yj().optional(),entity_data:a.bz().optional()}),a.Ik({id:a.Yj(),code:a.Yj(),customer_id:a.Yj(),feature_id:a.Yj().optional(),event_name:a.Yj().optional()}),a.Ik({customer_id:a.Yj(),feature_id:a.Yj().optional(),product_id:a.Yj().optional(),entity_id:a.Yj().optional(),customer_data:a.bz().optional(),required_balance:a.ai().optional(),send_event:a.zM().optional(),with_preview:a.zM().optional(),entity_data:K.optional()});var q=(e=>(e.Sandbox="sandbox",e.Live="live",e))(q||{}),G=(e=>(e.Active="active",e.Expired="expired",e.Trialing="trialing",e.Scheduled="scheduled",e.PastDue="past_due",e))(G||{}),H=a.k5(["invoices","rewards","trials_used","entities","referrals","payment_method"]);a.Ik({id:a.Yj().nullish(),email:a.Yj().nullish(),name:a.Yj().nullish(),fingerprint:a.Yj().nullish(),metadata:a.g1(a.bz()).optional(),expand:a.YO(H).optional()}),a.Ik({return_url:a.Yj().optional()});var V=(e=>(e.Day="day",e))(V||{}),B=(e=>(e.Prepaid="prepaid",e.PayPerUse="pay_per_use",e))(B||{}),Q=(e=>(e.Minute="minute",e.Hour="hour",e.Day="day",e.Week="week",e.Month="month",e.Quarter="quarter",e.SemiAnnual="semi_annual",e.Year="year",e.Multiple="multiple",e))(Q||{});a.Ik({customer_id:a.Yj(),program_id:a.Yj()}),a.Ik({code:a.Yj(),customer_id:a.Yj()})},95819:()=>{}};