/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/bridge/route";
exports.ids = ["app/api/auth/bridge/route"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fbridge%2Froute&page=%2Fapi%2Fauth%2Fbridge%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fbridge%2Froute.ts&appDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fbridge%2Froute&page=%2Fapi%2Fauth%2Fbridge%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fbridge%2Froute.ts&appDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_madhukarkumar_Dropbox_madhukar_robynnv3_code_robynnv3_apps_firegeo_api_app_api_auth_bridge_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/bridge/route.ts */ \"(rsc)/./app/api/auth/bridge/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/bridge/route\",\n        pathname: \"/api/auth/bridge\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/bridge/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/apps/firegeo-api/app/api/auth/bridge/route.ts\",\n    nextConfigOutput,\n    userland: _Users_madhukarkumar_Dropbox_madhukar_robynnv3_code_robynnv3_apps_firegeo_api_app_api_auth_bridge_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fbridge%2Froute&page=%2Fapi%2Fauth%2Fbridge%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fbridge%2Froute.ts&appDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/auth/bridge/route.ts":
/*!**************************************!*\
  !*** ./app/api/auth/bridge/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_bridge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-bridge */ \"(rsc)/./lib/auth-bridge.ts\");\n\n\nasync function POST(request) {\n    try {\n        // TEMPORARILY DISABLED: Authentication validation for testing\n        console.log('[Auth Bridge] Authentication temporarily disabled for testing');\n        // Create a mock auth context for testing\n        const mockAuthContext = {\n            userId: 'test-user-id',\n            email: '<EMAIL>',\n            name: 'Test User'\n        };\n        // Create FireGeo session with mock data\n        const session = (0,_lib_auth_bridge__WEBPACK_IMPORTED_MODULE_1__.createFireGeoSession)(mockAuthContext);\n        const response = {\n            success: true,\n            session,\n            user: mockAuthContext\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Auth bridge error:', error);\n        const response = {\n            success: false,\n            error: 'Authentication failed'\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 500\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        status: 'ok',\n        service: 'auth-bridge',\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/bridge/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth-bridge.ts":
/*!****************************!*\
  !*** ./lib/auth-bridge.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createFireGeoSession: () => (/* binding */ createFireGeoSession),\n/* harmony export */   extractAuthContext: () => (/* binding */ extractAuthContext),\n/* harmony export */   extractAuthFromRequest: () => (/* binding */ extractAuthFromRequest),\n/* harmony export */   validateApiSecret: () => (/* binding */ validateApiSecret),\n/* harmony export */   validateSupabaseToken: () => (/* binding */ validateSupabaseToken)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/.pnpm/@supabase+supabase-js@2.52.0_bufferutil@4.0.9_utf-8-validate@6.0.5/node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Initialize Supabase client for server-side operations\nconst supabaseUrl = process.env.SUPABASE_URL;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;\nlet supabase = null;\nif (supabaseUrl && supabaseServiceKey) {\n    supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n}\n/**\n * Validates a Supabase JWT token and returns user context\n */ async function validateSupabaseToken(token) {\n    if (!supabase) {\n        console.error('Supabase client not initialized - missing SUPABASE_URL or SUPABASE_SERVICE_KEY');\n        return null;\n    }\n    try {\n        const { data: { user }, error } = await supabase.auth.getUser(token);\n        if (error || !user) {\n            console.error('Token validation error:', error);\n            return null;\n        }\n        return {\n            userId: user.id,\n            email: user.email,\n            name: user.user_metadata?.full_name || user.email,\n            avatarUrl: user.user_metadata?.avatar_url\n        };\n    } catch (error) {\n        console.error('Token validation exception:', error);\n        return null;\n    }\n}\n/**\n * Extracts and validates auth from request headers\n */ async function extractAuthFromRequest(request) {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader?.startsWith('Bearer ')) {\n        return null;\n    }\n    const token = authHeader.substring(7);\n    return validateSupabaseToken(token);\n}\n/**\n * Creates a FireGeo-compatible session from auth context\n */ function createFireGeoSession(authContext) {\n    return {\n        user: {\n            id: authContext.userId,\n            email: authContext.email,\n            name: authContext.name || authContext.email,\n            image: authContext.avatarUrl\n        }\n    };\n}\n/**\n * Validates API secret for internal service communication\n */ function validateApiSecret(request) {\n    const apiSecret = request.headers.get('x-api-secret');\n    const expectedSecret = process.env.API_SECRET;\n    if (!expectedSecret) {\n        console.warn('API_SECRET not configured');\n        return false;\n    }\n    return apiSecret === expectedSecret;\n}\n/**\n * Enhanced auth extraction that supports both Bearer tokens and API secrets\n */ async function extractAuthContext(request) {\n    // Check for API secret first (for internal service calls)\n    const isApiCall = validateApiSecret(request);\n    if (isApiCall) {\n        // For API calls, we might need to extract user context from request body or headers\n        const userIdHeader = request.headers.get('x-user-id');\n        const userEmailHeader = request.headers.get('x-user-email');\n        if (userIdHeader && userEmailHeader) {\n            return {\n                authContext: {\n                    userId: userIdHeader,\n                    email: userEmailHeader,\n                    name: request.headers.get('x-user-name') || userEmailHeader\n                },\n                isApiCall: true\n            };\n        }\n    }\n    // Fall back to Bearer token validation\n    const authContext = await extractAuthFromRequest(request);\n    return {\n        authContext,\n        isApiCall: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-bridge.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/@supabase+auth-js@2.71.1","vendor-chunks/ws@8.18.3_bufferutil@4.0.9_utf-8-validate@6.0.5","vendor-chunks/@supabase+realtime-js@2.11.15_bufferutil@4.0.9_utf-8-validate@6.0.5","vendor-chunks/@supabase+postgrest-js@1.19.4","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@supabase+supabase-js@2.52.0_bufferutil@4.0.9_utf-8-validate@6.0.5","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+functions-js@2.4.5","vendor-chunks/isows@1.0.7_ws@8.18.3_bufferutil@4.0.9_utf-8-validate@6.0.5_","vendor-chunks/utf-8-validate@6.0.5","vendor-chunks/tr46@0.0.3","vendor-chunks/node-gyp-build@4.8.4","vendor-chunks/bufferutil@4.0.9","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/@supabase+node-fetch@2.6.15"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fbridge%2Froute&page=%2Fapi%2Fauth%2Fbridge%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fbridge%2Froute.ts&appDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmadhukarkumar%2FDropbox%2Fmadhukar%2Frobynnv3%2Fcode%2Frobynnv3%2Fapps%2Ffiregeo-api&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();