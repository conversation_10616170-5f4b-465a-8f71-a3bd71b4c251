"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+perplexity@1.1.9_zod@3.25.76";
exports.ids = ["vendor-chunks/@ai-sdk+perplexity@1.1.9_zod@3.25.76"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@ai-sdk+perplexity@1.1.9_zod@3.25.76/node_modules/@ai-sdk/perplexity/dist/index.mjs":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@ai-sdk+perplexity@1.1.9_zod@3.25.76/node_modules/@ai-sdk/perplexity/dist/index.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPerplexity: () => (/* binding */ createPerplexity),\n/* harmony export */   perplexity: () => (/* binding */ perplexity)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/types.js\");\n// src/perplexity-provider.ts\n\n\n\n// src/perplexity-language-model.ts\n\n\n\n\n// src/convert-to-perplexity-messages.ts\n\nfunction convertToPerplexityMessages(prompt) {\n  const messages = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        messages.push({ role: \"system\", content });\n        break;\n      }\n      case \"user\":\n      case \"assistant\": {\n        messages.push({\n          role,\n          content: content.filter(\n            (part) => part.type !== \"reasoning\" && part.type !== \"redacted-reasoning\"\n          ).map((part) => {\n            switch (part.type) {\n              case \"text\": {\n                return part.text;\n              }\n              case \"image\": {\n                throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n                  functionality: \"Image content parts in user messages\"\n                });\n              }\n              case \"file\": {\n                throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n                  functionality: \"File content parts in user messages\"\n                });\n              }\n              case \"tool-call\": {\n                throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n                  functionality: \"Tool calls in assistant messages\"\n                });\n              }\n              default: {\n                const _exhaustiveCheck = part;\n                throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n              }\n            }\n          }).join(\"\")\n        });\n        break;\n      }\n      case \"tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n          functionality: \"Tool messages\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return messages;\n}\n\n// src/map-perplexity-finish-reason.ts\nfunction mapPerplexityFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n    case \"length\":\n      return finishReason;\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/perplexity-language-model.ts\nvar PerplexityLanguageModel = class {\n  constructor(modelId, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = \"json\";\n    this.supportsStructuredOutputs = true;\n    this.supportsImageUrls = false;\n    this.provider = \"perplexity\";\n    this.modelId = modelId;\n    this.config = config;\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata\n  }) {\n    var _a;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (stopSequences != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"stopSequences\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"seed\"\n      });\n    }\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // standardized settings:\n      frequency_penalty: frequencyPenalty,\n      max_tokens: maxTokens,\n      presence_penalty: presencePenalty,\n      temperature,\n      top_k: topK,\n      top_p: topP,\n      // response format:\n      response_format: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? {\n        type: \"json_schema\",\n        json_schema: { schema: responseFormat.schema }\n      } : void 0,\n      // provider extensions\n      ...(_a = providerMetadata == null ? void 0 : providerMetadata.perplexity) != null ? _a : {},\n      // messages:\n      messages: convertToPerplexityMessages(prompt)\n    };\n    switch (type) {\n      case \"regular\": {\n        return { args: baseArgs, warnings };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: {\n              type: \"json_schema\",\n              json_schema: { schema: mode.schema }\n            }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n          functionality: \"tool-mode object generation\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k;\n    const { args, warnings } = this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: `${this.config.baseURL}/chat/completions`,\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonErrorResponseHandler)({\n        errorSchema: perplexityErrorSchema,\n        errorToMessage\n      }),\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonResponseHandler)(\n        perplexityResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    const text = choice.message.content;\n    return {\n      text,\n      toolCalls: [],\n      finishReason: mapPerplexityFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: (_b = (_a = response.usage) == null ? void 0 : _a.prompt_tokens) != null ? _b : Number.NaN,\n        completionTokens: (_d = (_c = response.usage) == null ? void 0 : _c.completion_tokens) != null ? _d : Number.NaN\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      request: { body: JSON.stringify(args) },\n      response: getResponseMetadata(response),\n      warnings,\n      sources: (_e = response.citations) == null ? void 0 : _e.map((url) => ({\n        sourceType: \"url\",\n        id: this.config.generateId(),\n        url\n      })),\n      providerMetadata: {\n        perplexity: {\n          images: (_g = (_f = response.images) == null ? void 0 : _f.map((image) => ({\n            imageUrl: image.image_url,\n            originUrl: image.origin_url,\n            height: image.height,\n            width: image.width\n          }))) != null ? _g : null,\n          usage: {\n            citationTokens: (_i = (_h = response.usage) == null ? void 0 : _h.citation_tokens) != null ? _i : null,\n            numSearchQueries: (_k = (_j = response.usage) == null ? void 0 : _j.num_search_queries) != null ? _k : null\n          }\n        }\n      }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = this.getArgs(options);\n    const body = { ...args, stream: true };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: `${this.config.baseURL}/chat/completions`,\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonErrorResponseHandler)({\n        errorSchema: perplexityErrorSchema,\n        errorToMessage\n      }),\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createEventSourceResponseHandler)(\n        perplexityChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    const providerMetadata = {\n      perplexity: {\n        usage: {\n          citationTokens: null,\n          numSearchQueries: null\n        },\n        images: null\n      }\n    };\n    let isFirstChunk = true;\n    const self = this;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c;\n            if (!chunk.success) {\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (isFirstChunk) {\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n              (_a = value.citations) == null ? void 0 : _a.forEach((url) => {\n                controller.enqueue({\n                  type: \"source\",\n                  source: {\n                    sourceType: \"url\",\n                    id: self.config.generateId(),\n                    url\n                  }\n                });\n              });\n              isFirstChunk = false;\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n              providerMetadata.perplexity.usage = {\n                citationTokens: (_b = value.usage.citation_tokens) != null ? _b : null,\n                numSearchQueries: (_c = value.usage.num_search_queries) != null ? _c : null\n              };\n            }\n            if (value.images != null) {\n              providerMetadata.perplexity.images = value.images.map((image) => ({\n                imageUrl: image.image_url,\n                originUrl: image.origin_url,\n                height: image.height,\n                width: image.width\n              }));\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapPerplexityFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            const textContent = delta.content;\n            if (textContent != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: textContent\n              });\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage,\n              providerMetadata\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings\n    };\n  }\n};\nfunction getResponseMetadata({\n  id,\n  model,\n  created\n}) {\n  return {\n    id,\n    modelId: model,\n    timestamp: new Date(created * 1e3)\n  };\n}\nvar perplexityUsageSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  citation_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  num_search_queries: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish()\n});\nvar perplexityImageSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  image_url: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  origin_url: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  height: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  width: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n});\nvar perplexityResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      message: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"assistant\"),\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n      }),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n    })\n  ),\n  citations: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()).nullish(),\n  images: zod__WEBPACK_IMPORTED_MODULE_2__.array(perplexityImageSchema).nullish(),\n  usage: perplexityUsageSchema.nullish()\n});\nvar perplexityChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      delta: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"assistant\"),\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n      }),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n    })\n  ),\n  citations: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()).nullish(),\n  images: zod__WEBPACK_IMPORTED_MODULE_2__.array(perplexityImageSchema).nullish(),\n  usage: perplexityUsageSchema.nullish()\n});\nvar perplexityErrorSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  error: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    code: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n    type: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n  })\n});\nvar errorToMessage = (data) => {\n  var _a, _b;\n  return (_b = (_a = data.error.message) != null ? _a : data.error.type) != null ? _b : \"unknown error\";\n};\n\n// src/perplexity-provider.ts\nfunction createPerplexity(options = {}) {\n  const getHeaders = () => ({\n    Authorization: `Bearer ${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"PERPLEXITY_API_KEY\",\n      description: \"Perplexity\"\n    })}`,\n    ...options.headers\n  });\n  const createLanguageModel = (modelId) => {\n    var _a;\n    return new PerplexityLanguageModel(modelId, {\n      baseURL: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.withoutTrailingSlash)(\n        (_a = options.baseURL) != null ? _a : \"https://api.perplexity.ai\"\n      ),\n      headers: getHeaders,\n      generateId: _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n      fetch: options.fetch\n    });\n  };\n  const provider = (modelId) => createLanguageModel(modelId);\n  provider.languageModel = createLanguageModel;\n  provider.textEmbeddingModel = (modelId) => {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.NoSuchModelError({ modelId, modelType: \"textEmbeddingModel\" });\n  };\n  return provider;\n}\nvar perplexity = createPerplexity();\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@ai-sdk+perplexity@1.1.9_zod@3.25.76/node_modules/@ai-sdk/perplexity/dist/index.mjs\n");

/***/ })

};
;