"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.25.76";
exports.ids = ["vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.25.76"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createBinaryResponseHandler: () => (/* binding */ createBinaryResponseHandler),\n/* harmony export */   createEventSourceParserStream: () => (/* binding */ createEventSourceParserStream),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   createStatusCodeErrorResponseHandler: () => (/* binding */ createStatusCodeErrorResponseHandler),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getFromApi: () => (/* binding */ getFromApi),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   parseProviderOptions: () => (/* binding */ parseProviderOptions),\n/* harmony export */   postFormDataToApi: () => (/* binding */ postFormDataToApi),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   removeUndefinedEntries: () => (/* binding */ removeUndefinedEntries),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/../../node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/../../node_modules/.pnpm/secure-json-parse@2.7.0/node_modules/secure-json-parse/index.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/delay.ts\nasync function delay(delayInMs) {\n  return delayInMs == null ? Promise.resolve() : new Promise((resolve2) => setTimeout(resolve2, delayInMs));\n}\n\n// src/event-source-parser-stream.ts\nfunction createEventSourceParserStream() {\n  let buffer = \"\";\n  let event = void 0;\n  let data = [];\n  let lastEventId = void 0;\n  let retry = void 0;\n  function parseLine(line, controller) {\n    if (line === \"\") {\n      dispatchEvent(controller);\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      return;\n    }\n    const colonIndex = line.indexOf(\":\");\n    if (colonIndex === -1) {\n      handleField(line, \"\");\n      return;\n    }\n    const field = line.slice(0, colonIndex);\n    const valueStart = colonIndex + 1;\n    const value = valueStart < line.length && line[valueStart] === \" \" ? line.slice(valueStart + 1) : line.slice(valueStart);\n    handleField(field, value);\n  }\n  function dispatchEvent(controller) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join(\"\\n\"),\n        id: lastEventId,\n        retry\n      });\n      data = [];\n      event = void 0;\n      retry = void 0;\n    }\n  }\n  function handleField(field, value) {\n    switch (field) {\n      case \"event\":\n        event = value;\n        break;\n      case \"data\":\n        data.push(value);\n        break;\n      case \"id\":\n        lastEventId = value;\n        break;\n      case \"retry\":\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n      buffer = incompleteLine;\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    }\n  });\n}\nfunction splitLines(buffer, chunk) {\n  const lines = [];\n  let currentLine = buffer;\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n    if (char === \"\\n\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n    } else if (char === \"\\r\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n      if (chunk[i] === \"\\n\") {\n        i++;\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n  return { lines, incompleteLine: currentLine };\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/get-from-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/get-from-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar getFromApi = async ({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"GET\",\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {}\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {}\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {}\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {}\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {}\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return { success: true, value, rawValue: value };\n    }\n    const validationResult = safeValidateTypes({ value, schema });\n    return validationResult.success ? { ...validationResult, rawValue: value } : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/parse-provider-options.ts\n\nfunction parseProviderOptions({\n  provider,\n  providerOptions,\n  schema\n}) {\n  if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {\n    return void 0;\n  }\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema\n  });\n  if (!parsedProviderOptions.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"providerOptions\",\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error\n    });\n  }\n  return parsedProviderOptions.value;\n}\n\n// src/post-to-api.ts\n\nvar getOriginalFetch2 = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postFormDataToApi = async ({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers,\n  body: {\n    content: formData,\n    values: Object.fromEntries(formData.entries())\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch2()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value,\n    rawValue: parsedResult.rawValue\n  };\n};\nvar createBinaryResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (!response.body) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Response body is empty\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0\n    });\n  }\n  try {\n    const buffer = await response.arrayBuffer();\n    return {\n      responseHeaders,\n      value: new Uint8Array(buffer)\n    };\n  } catch (error) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Failed to read response as array buffer\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0,\n      cause: error\n    });\n  }\n};\nvar createStatusCodeErrorResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  const responseBody = await response.text();\n  return {\n    responseHeaders,\n    value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: response.statusText,\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody\n    })\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ })

};
;