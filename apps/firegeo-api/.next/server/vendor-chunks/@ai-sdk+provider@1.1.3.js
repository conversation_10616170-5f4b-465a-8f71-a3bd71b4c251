"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+provider@1.1.3";
exports.ids = ["vendor-chunks/@ai-sdk+provider@1.1.3"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AISDKError: () => (/* binding */ AISDKError),\n/* harmony export */   APICallError: () => (/* binding */ APICallError),\n/* harmony export */   EmptyResponseBodyError: () => (/* binding */ EmptyResponseBodyError),\n/* harmony export */   InvalidArgumentError: () => (/* binding */ InvalidArgumentError),\n/* harmony export */   InvalidPromptError: () => (/* binding */ InvalidPromptError),\n/* harmony export */   InvalidResponseDataError: () => (/* binding */ InvalidResponseDataError),\n/* harmony export */   JSONParseError: () => (/* binding */ JSONParseError),\n/* harmony export */   LoadAPIKeyError: () => (/* binding */ LoadAPIKeyError),\n/* harmony export */   LoadSettingError: () => (/* binding */ LoadSettingError),\n/* harmony export */   NoContentGeneratedError: () => (/* binding */ NoContentGeneratedError),\n/* harmony export */   NoSuchModelError: () => (/* binding */ NoSuchModelError),\n/* harmony export */   TooManyEmbeddingValuesForCallError: () => (/* binding */ TooManyEmbeddingValuesForCallError),\n/* harmony export */   TypeValidationError: () => (/* binding */ TypeValidationError),\n/* harmony export */   UnsupportedFunctionalityError: () => (/* binding */ UnsupportedFunctionalityError),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isJSONArray: () => (/* binding */ isJSONArray),\n/* harmony export */   isJSONObject: () => (/* binding */ isJSONObject),\n/* harmony export */   isJSONValue: () => (/* binding */ isJSONValue)\n/* harmony export */ });\n// src/errors/ai-sdk-error.ts\nvar marker = \"vercel.ai.error\";\nvar symbol = Symbol.for(marker);\nvar _a;\nvar _AISDKError = class _AISDKError extends Error {\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name: name14,\n    message,\n    cause\n  }) {\n    super(message);\n    this[_a] = true;\n    this.name = name14;\n    this.cause = cause;\n  }\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error) {\n    return _AISDKError.hasMarker(error, marker);\n  }\n  static hasMarker(error, marker15) {\n    const markerSymbol = Symbol.for(marker15);\n    return error != null && typeof error === \"object\" && markerSymbol in error && typeof error[markerSymbol] === \"boolean\" && error[markerSymbol] === true;\n  }\n};\n_a = symbol;\nvar AISDKError = _AISDKError;\n\n// src/errors/api-call-error.ts\nvar name = \"AI_APICallError\";\nvar marker2 = `vercel.ai.error.${name}`;\nvar symbol2 = Symbol.for(marker2);\nvar _a2;\nvar APICallError = class extends AISDKError {\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null && (statusCode === 408 || // request timeout\n    statusCode === 409 || // conflict\n    statusCode === 429 || // too many requests\n    statusCode >= 500),\n    // server error\n    data\n  }) {\n    super({ name, message, cause });\n    this[_a2] = true;\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker2);\n  }\n};\n_a2 = symbol2;\n\n// src/errors/empty-response-body-error.ts\nvar name2 = \"AI_EmptyResponseBodyError\";\nvar marker3 = `vercel.ai.error.${name2}`;\nvar symbol3 = Symbol.for(marker3);\nvar _a3;\nvar EmptyResponseBodyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message = \"Empty response body\" } = {}) {\n    super({ name: name2, message });\n    this[_a3] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker3);\n  }\n};\n_a3 = symbol3;\n\n// src/errors/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/errors/invalid-argument-error.ts\nvar name3 = \"AI_InvalidArgumentError\";\nvar marker4 = `vercel.ai.error.${name3}`;\nvar symbol4 = Symbol.for(marker4);\nvar _a4;\nvar InvalidArgumentError = class extends AISDKError {\n  constructor({\n    message,\n    cause,\n    argument\n  }) {\n    super({ name: name3, message, cause });\n    this[_a4] = true;\n    this.argument = argument;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker4);\n  }\n};\n_a4 = symbol4;\n\n// src/errors/invalid-prompt-error.ts\nvar name4 = \"AI_InvalidPromptError\";\nvar marker5 = `vercel.ai.error.${name4}`;\nvar symbol5 = Symbol.for(marker5);\nvar _a5;\nvar InvalidPromptError = class extends AISDKError {\n  constructor({\n    prompt,\n    message,\n    cause\n  }) {\n    super({ name: name4, message: `Invalid prompt: ${message}`, cause });\n    this[_a5] = true;\n    this.prompt = prompt;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker5);\n  }\n};\n_a5 = symbol5;\n\n// src/errors/invalid-response-data-error.ts\nvar name5 = \"AI_InvalidResponseDataError\";\nvar marker6 = `vercel.ai.error.${name5}`;\nvar symbol6 = Symbol.for(marker6);\nvar _a6;\nvar InvalidResponseDataError = class extends AISDKError {\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`\n  }) {\n    super({ name: name5, message });\n    this[_a6] = true;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker6);\n  }\n};\n_a6 = symbol6;\n\n// src/errors/json-parse-error.ts\nvar name6 = \"AI_JSONParseError\";\nvar marker7 = `vercel.ai.error.${name6}`;\nvar symbol7 = Symbol.for(marker7);\nvar _a7;\nvar JSONParseError = class extends AISDKError {\n  constructor({ text, cause }) {\n    super({\n      name: name6,\n      message: `JSON parsing failed: Text: ${text}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a7] = true;\n    this.text = text;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker7);\n  }\n};\n_a7 = symbol7;\n\n// src/errors/load-api-key-error.ts\nvar name7 = \"AI_LoadAPIKeyError\";\nvar marker8 = `vercel.ai.error.${name7}`;\nvar symbol8 = Symbol.for(marker8);\nvar _a8;\nvar LoadAPIKeyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name7, message });\n    this[_a8] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker8);\n  }\n};\n_a8 = symbol8;\n\n// src/errors/load-setting-error.ts\nvar name8 = \"AI_LoadSettingError\";\nvar marker9 = `vercel.ai.error.${name8}`;\nvar symbol9 = Symbol.for(marker9);\nvar _a9;\nvar LoadSettingError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name8, message });\n    this[_a9] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker9);\n  }\n};\n_a9 = symbol9;\n\n// src/errors/no-content-generated-error.ts\nvar name9 = \"AI_NoContentGeneratedError\";\nvar marker10 = `vercel.ai.error.${name9}`;\nvar symbol10 = Symbol.for(marker10);\nvar _a10;\nvar NoContentGeneratedError = class extends AISDKError {\n  // used in isInstance\n  constructor({\n    message = \"No content generated.\"\n  } = {}) {\n    super({ name: name9, message });\n    this[_a10] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker10);\n  }\n};\n_a10 = symbol10;\n\n// src/errors/no-such-model-error.ts\nvar name10 = \"AI_NoSuchModelError\";\nvar marker11 = `vercel.ai.error.${name10}`;\nvar symbol11 = Symbol.for(marker11);\nvar _a11;\nvar NoSuchModelError = class extends AISDKError {\n  constructor({\n    errorName = name10,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`\n  }) {\n    super({ name: errorName, message });\n    this[_a11] = true;\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker11);\n  }\n};\n_a11 = symbol11;\n\n// src/errors/too-many-embedding-values-for-call-error.ts\nvar name11 = \"AI_TooManyEmbeddingValuesForCallError\";\nvar marker12 = `vercel.ai.error.${name11}`;\nvar symbol12 = Symbol.for(marker12);\nvar _a12;\nvar TooManyEmbeddingValuesForCallError = class extends AISDKError {\n  constructor(options) {\n    super({\n      name: name11,\n      message: `Too many values for a single embedding call. The ${options.provider} model \"${options.modelId}\" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`\n    });\n    this[_a12] = true;\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker12);\n  }\n};\n_a12 = symbol12;\n\n// src/errors/type-validation-error.ts\nvar name12 = \"AI_TypeValidationError\";\nvar marker13 = `vercel.ai.error.${name12}`;\nvar symbol13 = Symbol.for(marker13);\nvar _a13;\nvar _TypeValidationError = class _TypeValidationError extends AISDKError {\n  constructor({ value, cause }) {\n    super({\n      name: name12,\n      message: `Type validation failed: Value: ${JSON.stringify(value)}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a13] = true;\n    this.value = value;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker13);\n  }\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause\n  }) {\n    return _TypeValidationError.isInstance(cause) && cause.value === value ? cause : new _TypeValidationError({ value, cause });\n  }\n};\n_a13 = symbol13;\nvar TypeValidationError = _TypeValidationError;\n\n// src/errors/unsupported-functionality-error.ts\nvar name13 = \"AI_UnsupportedFunctionalityError\";\nvar marker14 = `vercel.ai.error.${name13}`;\nvar symbol14 = Symbol.for(marker14);\nvar _a14;\nvar UnsupportedFunctionalityError = class extends AISDKError {\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`\n  }) {\n    super({ name: name13, message });\n    this[_a14] = true;\n    this.functionality = functionality;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker14);\n  }\n};\n_a14 = symbol14;\n\n// src/json-value/is-json.ts\nfunction isJSONValue(value) {\n  if (value === null || typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n    return true;\n  }\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n  if (typeof value === \"object\") {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n    );\n  }\n  return false;\n}\nfunction isJSONArray(value) {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\nfunction isJSONObject(value) {\n  return value != null && typeof value === \"object\" && Object.entries(value).every(\n    ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\n");

/***/ })

};
;