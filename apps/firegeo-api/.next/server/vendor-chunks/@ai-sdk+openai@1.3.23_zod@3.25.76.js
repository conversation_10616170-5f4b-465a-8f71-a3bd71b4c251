"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+openai@1.3.23_zod@3.25.76";
exports.ids = ["vendor-chunks/@ai-sdk+openai@1.3.23_zod@3.25.76"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@ai-sdk+openai@1.3.23_zod@3.25.76/node_modules/@ai-sdk/openai/dist/index.mjs":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@ai-sdk+openai@1.3.23_zod@3.25.76/node_modules/@ai-sdk/openai/dist/index.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOpenAI: () => (/* binding */ createOpenAI),\n/* harmony export */   openai: () => (/* binding */ openai)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/types.js\");\n// src/openai-provider.ts\n\n\n// src/openai-chat-language-model.ts\n\n\n\n\n// src/convert-to-openai-chat-messages.ts\n\n\nfunction convertToOpenAIChatMessages({\n  prompt,\n  useLegacyFunctionCalling = false,\n  systemMessageMode = \"system\"\n}) {\n  const messages = [];\n  const warnings = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        switch (systemMessageMode) {\n          case \"system\": {\n            messages.push({ role: \"system\", content });\n            break;\n          }\n          case \"developer\": {\n            messages.push({ role: \"developer\", content });\n            break;\n          }\n          case \"remove\": {\n            warnings.push({\n              type: \"other\",\n              message: \"system messages are removed for this model\"\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`\n            );\n          }\n        }\n        break;\n      }\n      case \"user\": {\n        if (content.length === 1 && content[0].type === \"text\") {\n          messages.push({ role: \"user\", content: content[0].text });\n          break;\n        }\n        messages.push({\n          role: \"user\",\n          content: content.map((part, index) => {\n            var _a, _b, _c, _d;\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.convertUint8ArrayToBase64)(part.image)}`,\n                    // OpenAI specific extension: image detail\n                    detail: (_c = (_b = part.providerMetadata) == null ? void 0 : _b.openai) == null ? void 0 : _c.imageDetail\n                  }\n                };\n              }\n              case \"file\": {\n                if (part.data instanceof URL) {\n                  throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                    functionality: \"'File content parts with URL data' functionality not supported.\"\n                  });\n                }\n                switch (part.mimeType) {\n                  case \"audio/wav\": {\n                    return {\n                      type: \"input_audio\",\n                      input_audio: { data: part.data, format: \"wav\" }\n                    };\n                  }\n                  case \"audio/mp3\":\n                  case \"audio/mpeg\": {\n                    return {\n                      type: \"input_audio\",\n                      input_audio: { data: part.data, format: \"mp3\" }\n                    };\n                  }\n                  case \"application/pdf\": {\n                    return {\n                      type: \"file\",\n                      file: {\n                        filename: (_d = part.filename) != null ? _d : `part-${index}.pdf`,\n                        file_data: `data:application/pdf;base64,${part.data}`\n                      }\n                    };\n                  }\n                  default: {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                      functionality: `File content part type ${part.mimeType} in user messages`\n                    });\n                  }\n                }\n              }\n            }\n          })\n        });\n        break;\n      }\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls = [];\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args)\n                }\n              });\n              break;\n            }\n          }\n        }\n        if (useLegacyFunctionCalling) {\n          if (toolCalls.length > 1) {\n            throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n              functionality: \"useLegacyFunctionCalling with multiple tool calls in one message\"\n            });\n          }\n          messages.push({\n            role: \"assistant\",\n            content: text,\n            function_call: toolCalls.length > 0 ? toolCalls[0].function : void 0\n          });\n        } else {\n          messages.push({\n            role: \"assistant\",\n            content: text,\n            tool_calls: toolCalls.length > 0 ? toolCalls : void 0\n          });\n        }\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          if (useLegacyFunctionCalling) {\n            messages.push({\n              role: \"function\",\n              name: toolResponse.toolName,\n              content: JSON.stringify(toolResponse.result)\n            });\n          } else {\n            messages.push({\n              role: \"tool\",\n              tool_call_id: toolResponse.toolCallId,\n              content: JSON.stringify(toolResponse.result)\n            });\n          }\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return { messages, warnings };\n}\n\n// src/map-openai-chat-logprobs.ts\nfunction mapOpenAIChatLogProbsOutput(logprobs) {\n  var _a, _b;\n  return (_b = (_a = logprobs == null ? void 0 : logprobs.content) == null ? void 0 : _a.map(({ token, logprob, top_logprobs }) => ({\n    token,\n    logprob,\n    topLogprobs: top_logprobs ? top_logprobs.map(({ token: token2, logprob: logprob2 }) => ({\n      token: token2,\n      logprob: logprob2\n    })) : []\n  }))) != null ? _b : void 0;\n}\n\n// src/map-openai-finish-reason.ts\nfunction mapOpenAIFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/openai-error.ts\n\n\nvar openaiErrorDataSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  error: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n    param: zod__WEBPACK_IMPORTED_MODULE_2__.any().nullish(),\n    code: zod__WEBPACK_IMPORTED_MODULE_2__.union([zod__WEBPACK_IMPORTED_MODULE_2__.string(), zod__WEBPACK_IMPORTED_MODULE_2__.number()]).nullish()\n  })\n});\nvar openaiFailedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonErrorResponseHandler)({\n  errorSchema: openaiErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/get-response-metadata.ts\nfunction getResponseMetadata({\n  id,\n  model,\n  created\n}) {\n  return {\n    id: id != null ? id : void 0,\n    modelId: model != null ? model : void 0,\n    timestamp: created != null ? new Date(created * 1e3) : void 0\n  };\n}\n\n// src/openai-prepare-tools.ts\n\nfunction prepareTools({\n  mode,\n  useLegacyFunctionCalling = false,\n  structuredOutputs\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  if (useLegacyFunctionCalling) {\n    const openaiFunctions = [];\n    for (const tool of tools) {\n      if (tool.type === \"provider-defined\") {\n        toolWarnings.push({ type: \"unsupported-tool\", tool });\n      } else {\n        openaiFunctions.push({\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters\n        });\n      }\n    }\n    if (toolChoice == null) {\n      return {\n        functions: openaiFunctions,\n        function_call: void 0,\n        toolWarnings\n      };\n    }\n    const type2 = toolChoice.type;\n    switch (type2) {\n      case \"auto\":\n      case \"none\":\n      case void 0:\n        return {\n          functions: openaiFunctions,\n          function_call: void 0,\n          toolWarnings\n        };\n      case \"required\":\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"useLegacyFunctionCalling and toolChoice: required\"\n        });\n      default:\n        return {\n          functions: openaiFunctions,\n          function_call: { name: toolChoice.toolName },\n          toolWarnings\n        };\n    }\n  }\n  const openaiTools2 = [];\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      openaiTools2.push({\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: structuredOutputs ? true : void 0\n        }\n      });\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: openaiTools2, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: openaiTools2, tool_choice: type, toolWarnings };\n    case \"tool\":\n      return {\n        tools: openaiTools2,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName\n          }\n        },\n        toolWarnings\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/openai-chat-language-model.ts\nvar OpenAIChatLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get supportsStructuredOutputs() {\n    var _a;\n    return (_a = this.settings.structuredOutputs) != null ? _a : isReasoningModel(this.modelId);\n  }\n  get defaultObjectGenerationMode() {\n    if (isAudioModel(this.modelId)) {\n      return \"tool\";\n    }\n    return this.supportsStructuredOutputs ? \"json\" : \"tool\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get supportsImageUrls() {\n    return !this.settings.downloadImages;\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata\n  }) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if ((responseFormat == null ? void 0 : responseFormat.type) === \"json\" && responseFormat.schema != null && !this.supportsStructuredOutputs) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format schema is only supported with structuredOutputs\"\n      });\n    }\n    const useLegacyFunctionCalling = this.settings.useLegacyFunctionCalling;\n    if (useLegacyFunctionCalling && this.settings.parallelToolCalls === true) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: \"useLegacyFunctionCalling with parallelToolCalls\"\n      });\n    }\n    if (useLegacyFunctionCalling && this.supportsStructuredOutputs) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: \"structuredOutputs with useLegacyFunctionCalling\"\n      });\n    }\n    const { messages, warnings: messageWarnings } = convertToOpenAIChatMessages(\n      {\n        prompt,\n        useLegacyFunctionCalling,\n        systemMessageMode: getSystemMessageMode(this.modelId)\n      }\n    );\n    warnings.push(...messageWarnings);\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs: this.settings.logprobs === true || typeof this.settings.logprobs === \"number\" ? true : void 0,\n      top_logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      response_format: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? this.supportsStructuredOutputs && responseFormat.schema != null ? {\n        type: \"json_schema\",\n        json_schema: {\n          schema: responseFormat.schema,\n          strict: true,\n          name: (_a = responseFormat.name) != null ? _a : \"response\",\n          description: responseFormat.description\n        }\n      } : { type: \"json_object\" } : void 0,\n      stop: stopSequences,\n      seed,\n      // openai specific settings:\n      // TODO remove in next major version; we auto-map maxTokens now\n      max_completion_tokens: (_b = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _b.maxCompletionTokens,\n      store: (_c = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _c.store,\n      metadata: (_d = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _d.metadata,\n      prediction: (_e = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _e.prediction,\n      reasoning_effort: (_g = (_f = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _f.reasoningEffort) != null ? _g : this.settings.reasoningEffort,\n      // messages:\n      messages\n    };\n    if (isReasoningModel(this.modelId)) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topP\",\n          details: \"topP is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.frequency_penalty != null) {\n        baseArgs.frequency_penalty = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"frequencyPenalty\",\n          details: \"frequencyPenalty is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.presence_penalty != null) {\n        baseArgs.presence_penalty = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"presencePenalty\",\n          details: \"presencePenalty is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.logit_bias != null) {\n        baseArgs.logit_bias = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"logitBias is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.logprobs != null) {\n        baseArgs.logprobs = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"logprobs is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_logprobs != null) {\n        baseArgs.top_logprobs = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"topLogprobs is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.max_tokens != null) {\n        if (baseArgs.max_completion_tokens == null) {\n          baseArgs.max_completion_tokens = baseArgs.max_tokens;\n        }\n        baseArgs.max_tokens = void 0;\n      }\n    } else if (this.modelId.startsWith(\"gpt-4o-search-preview\") || this.modelId.startsWith(\"gpt-4o-mini-search-preview\")) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for the search preview models and has been removed.\"\n        });\n      }\n    }\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, functions, function_call, toolWarnings } = prepareTools({\n          mode,\n          useLegacyFunctionCalling,\n          structuredOutputs: this.supportsStructuredOutputs\n        });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n            functions,\n            function_call\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: this.supportsStructuredOutputs && mode.schema != null ? {\n              type: \"json_schema\",\n              json_schema: {\n                schema: mode.schema,\n                strict: true,\n                name: (_h = mode.name) != null ? _h : \"response\",\n                description: mode.description\n              }\n            } : { type: \"json_object\" }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: useLegacyFunctionCalling ? {\n            ...baseArgs,\n            function_call: {\n              name: mode.tool.name\n            },\n            functions: [\n              {\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters\n              }\n            ]\n          } : {\n            ...baseArgs,\n            tool_choice: {\n              type: \"function\",\n              function: { name: mode.tool.name }\n            },\n            tools: [\n              {\n                type: \"function\",\n                function: {\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters,\n                  strict: this.supportsStructuredOutputs ? true : void 0\n                }\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const { args: body, warnings } = this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = body;\n    const choice = response.choices[0];\n    const completionTokenDetails = (_a = response.usage) == null ? void 0 : _a.completion_tokens_details;\n    const promptTokenDetails = (_b = response.usage) == null ? void 0 : _b.prompt_tokens_details;\n    const providerMetadata = { openai: {} };\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.reasoning_tokens) != null) {\n      providerMetadata.openai.reasoningTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.reasoning_tokens;\n    }\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.accepted_prediction_tokens) != null) {\n      providerMetadata.openai.acceptedPredictionTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.accepted_prediction_tokens;\n    }\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.rejected_prediction_tokens) != null) {\n      providerMetadata.openai.rejectedPredictionTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.rejected_prediction_tokens;\n    }\n    if ((promptTokenDetails == null ? void 0 : promptTokenDetails.cached_tokens) != null) {\n      providerMetadata.openai.cachedPromptTokens = promptTokenDetails == null ? void 0 : promptTokenDetails.cached_tokens;\n    }\n    return {\n      text: (_c = choice.message.content) != null ? _c : void 0,\n      toolCalls: this.settings.useLegacyFunctionCalling && choice.message.function_call ? [\n        {\n          toolCallType: \"function\",\n          toolCallId: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n          toolName: choice.message.function_call.name,\n          args: choice.message.function_call.arguments\n        }\n      ] : (_d = choice.message.tool_calls) == null ? void 0 : _d.map((toolCall) => {\n        var _a2;\n        return {\n          toolCallType: \"function\",\n          toolCallId: (_a2 = toolCall.id) != null ? _a2 : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n          toolName: toolCall.function.name,\n          args: toolCall.function.arguments\n        };\n      }),\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: (_f = (_e = response.usage) == null ? void 0 : _e.prompt_tokens) != null ? _f : NaN,\n        completionTokens: (_h = (_g = response.usage) == null ? void 0 : _g.completion_tokens) != null ? _h : NaN\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      request: { body: JSON.stringify(body) },\n      response: getResponseMetadata(response),\n      warnings,\n      logprobs: mapOpenAIChatLogProbsOutput(choice.logprobs),\n      providerMetadata\n    };\n  }\n  async doStream(options) {\n    if (this.settings.simulateStreaming) {\n      const result = await this.doGenerate(options);\n      const simulatedStream = new ReadableStream({\n        start(controller) {\n          controller.enqueue({ type: \"response-metadata\", ...result.response });\n          if (result.text) {\n            controller.enqueue({\n              type: \"text-delta\",\n              textDelta: result.text\n            });\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                type: \"tool-call-delta\",\n                toolCallType: \"function\",\n                toolCallId: toolCall.toolCallId,\n                toolName: toolCall.toolName,\n                argsTextDelta: toolCall.args\n              });\n              controller.enqueue({\n                type: \"tool-call\",\n                ...toolCall\n              });\n            }\n          }\n          controller.enqueue({\n            type: \"finish\",\n            finishReason: result.finishReason,\n            usage: result.usage,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata\n          });\n          controller.close();\n        }\n      });\n      return {\n        stream: simulatedStream,\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        warnings: result.warnings\n      };\n    }\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true,\n      // only include stream_options when in strict compatibility mode:\n      stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n    };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        openaiChatChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const toolCalls = [];\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: void 0,\n      completionTokens: void 0\n    };\n    let logprobs;\n    let isFirstChunk = true;\n    const { useLegacyFunctionCalling } = this.settings;\n    const providerMetadata = { openai: {} };\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              const {\n                prompt_tokens,\n                completion_tokens,\n                prompt_tokens_details,\n                completion_tokens_details\n              } = value.usage;\n              usage = {\n                promptTokens: prompt_tokens != null ? prompt_tokens : void 0,\n                completionTokens: completion_tokens != null ? completion_tokens : void 0\n              };\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.reasoning_tokens) != null) {\n                providerMetadata.openai.reasoningTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.reasoning_tokens;\n              }\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.accepted_prediction_tokens) != null) {\n                providerMetadata.openai.acceptedPredictionTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.accepted_prediction_tokens;\n              }\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.rejected_prediction_tokens) != null) {\n                providerMetadata.openai.rejectedPredictionTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.rejected_prediction_tokens;\n              }\n              if ((prompt_tokens_details == null ? void 0 : prompt_tokens_details.cached_tokens) != null) {\n                providerMetadata.openai.cachedPromptTokens = prompt_tokens_details == null ? void 0 : prompt_tokens_details.cached_tokens;\n              }\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            if (delta.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content\n              });\n            }\n            const mappedLogprobs = mapOpenAIChatLogProbsOutput(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n            const mappedToolCalls = useLegacyFunctionCalling && delta.function_call != null ? [\n              {\n                type: \"function\",\n                id: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                function: delta.function_call,\n                index: 0\n              }\n            ] : delta.tool_calls;\n            if (mappedToolCalls != null) {\n              for (const toolCallDelta of mappedToolCalls) {\n                const index = toolCallDelta.index;\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`\n                    });\n                  }\n                  if (toolCallDelta.id == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`\n                    });\n                  }\n                  if (((_a = toolCallDelta.function) == null ? void 0 : _a.name) == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`\n                    });\n                  }\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: (_b = toolCallDelta.function.arguments) != null ? _b : \"\"\n                    },\n                    hasFinished: false\n                  };\n                  const toolCall2 = toolCalls[index];\n                  if (((_c = toolCall2.function) == null ? void 0 : _c.name) != null && ((_d = toolCall2.function) == null ? void 0 : _d.arguments) != null) {\n                    if (toolCall2.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: \"tool-call-delta\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall2.id,\n                        toolName: toolCall2.function.name,\n                        argsTextDelta: toolCall2.function.arguments\n                      });\n                    }\n                    if ((0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.isParsableJson)(toolCall2.function.arguments)) {\n                      controller.enqueue({\n                        type: \"tool-call\",\n                        toolCallType: \"function\",\n                        toolCallId: (_e = toolCall2.id) != null ? _e : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                        toolName: toolCall2.function.name,\n                        args: toolCall2.function.arguments\n                      });\n                      toolCall2.hasFinished = true;\n                    }\n                  }\n                  continue;\n                }\n                const toolCall = toolCalls[index];\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n                if (((_f = toolCallDelta.function) == null ? void 0 : _f.arguments) != null) {\n                  toolCall.function.arguments += (_h = (_g = toolCallDelta.function) == null ? void 0 : _g.arguments) != null ? _h : \"\";\n                }\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: (_i = toolCallDelta.function.arguments) != null ? _i : \"\"\n                });\n                if (((_j = toolCall.function) == null ? void 0 : _j.name) != null && ((_k = toolCall.function) == null ? void 0 : _k.arguments) != null && (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.isParsableJson)(toolCall.function.arguments)) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_l = toolCall.id) != null ? _l : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n          flush(controller) {\n            var _a, _b;\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage: {\n                promptTokens: (_a = usage.promptTokens) != null ? _a : NaN,\n                completionTokens: (_b = usage.completionTokens) != null ? _b : NaN\n              },\n              ...providerMetadata != null ? { providerMetadata } : {}\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings\n    };\n  }\n};\nvar openaiTokenUsageSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  prompt_tokens_details: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    cached_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish()\n  }).nullish(),\n  completion_tokens_details: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    reasoning_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    accepted_prediction_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    rejected_prediction_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish()\n  }).nullish()\n}).nullish();\nvar openaiChatResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      message: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"assistant\").nullish(),\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n        function_call: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n          name: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n        }).nullish(),\n        tool_calls: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n          zod__WEBPACK_IMPORTED_MODULE_2__.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n            type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"function\"),\n            function: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n              name: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n              arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n            })\n          })\n        ).nullish()\n      }),\n      index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n      logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n          zod__WEBPACK_IMPORTED_MODULE_2__.object({\n            token: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n            logprob: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n            top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n              zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                token: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n                logprob: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n              })\n            )\n          })\n        ).nullable()\n      }).nullish(),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n    })\n  ),\n  usage: openaiTokenUsageSchema\n});\nvar openaiChatChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.union([\n  zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n      zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        delta: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          role: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\"assistant\"]).nullish(),\n          content: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n          function_call: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n            name: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n            arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n          }).nullish(),\n          tool_calls: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n            zod__WEBPACK_IMPORTED_MODULE_2__.object({\n              index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n              id: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n              type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"function\").nullish(),\n              function: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                name: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n                arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n              })\n            })\n          ).nullish()\n        }).nullish(),\n        logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          content: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n            zod__WEBPACK_IMPORTED_MODULE_2__.object({\n              token: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n              logprob: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n              top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n                zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                  token: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n                  logprob: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n                })\n              )\n            })\n          ).nullable()\n        }).nullish(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n        index: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n      })\n    ),\n    usage: openaiTokenUsageSchema\n  }),\n  openaiErrorDataSchema\n]);\nfunction isReasoningModel(modelId) {\n  return modelId.startsWith(\"o\");\n}\nfunction isAudioModel(modelId) {\n  return modelId.startsWith(\"gpt-4o-audio-preview\");\n}\nfunction getSystemMessageMode(modelId) {\n  var _a, _b;\n  if (!isReasoningModel(modelId)) {\n    return \"system\";\n  }\n  return (_b = (_a = reasoningModels[modelId]) == null ? void 0 : _a.systemMessageMode) != null ? _b : \"developer\";\n}\nvar reasoningModels = {\n  \"o1-mini\": {\n    systemMessageMode: \"remove\"\n  },\n  \"o1-mini-2024-09-12\": {\n    systemMessageMode: \"remove\"\n  },\n  \"o1-preview\": {\n    systemMessageMode: \"remove\"\n  },\n  \"o1-preview-2024-09-12\": {\n    systemMessageMode: \"remove\"\n  },\n  o3: {\n    systemMessageMode: \"developer\"\n  },\n  \"o3-2025-04-16\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o3-mini\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o3-mini-2025-01-31\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o4-mini\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o4-mini-2025-04-16\": {\n    systemMessageMode: \"developer\"\n  }\n};\n\n// src/openai-completion-language-model.ts\n\n\n\n\n// src/convert-to-openai-completion-prompt.ts\n\nfunction convertToOpenAICompletionPrompt({\n  prompt,\n  inputFormat,\n  user = \"user\",\n  assistant = \"assistant\"\n}) {\n  if (inputFormat === \"prompt\" && prompt.length === 1 && prompt[0].role === \"user\" && prompt[0].content.length === 1 && prompt[0].content[0].type === \"text\") {\n    return { prompt: prompt[0].content[0].text };\n  }\n  let text = \"\";\n  if (prompt[0].role === \"system\") {\n    text += `${prompt[0].content}\n\n`;\n    prompt = prompt.slice(1);\n  }\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidPromptError({\n          message: \"Unexpected system message in prompt: ${content}\",\n          prompt\n        });\n      }\n      case \"user\": {\n        const userMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"image\": {\n              throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                functionality: \"images\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${user}:\n${userMessage}\n\n`;\n        break;\n      }\n      case \"assistant\": {\n        const assistantMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"tool-call\": {\n              throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                functionality: \"tool-call messages\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${assistant}:\n${assistantMessage}\n\n`;\n        break;\n      }\n      case \"tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"tool messages\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  text += `${assistant}:\n`;\n  return {\n    prompt: text,\n    stopSequences: [`\n${user}:`]\n  };\n}\n\n// src/map-openai-completion-logprobs.ts\nfunction mapOpenAICompletionLogProbs(logprobs) {\n  return logprobs == null ? void 0 : logprobs.tokens.map((token, index) => ({\n    token,\n    logprob: logprobs.token_logprobs[index],\n    topLogprobs: logprobs.top_logprobs ? Object.entries(logprobs.top_logprobs[index]).map(\n      ([token2, logprob]) => ({\n        token: token2,\n        logprob\n      })\n    ) : []\n  }));\n}\n\n// src/openai-completion-language-model.ts\nvar OpenAICompletionLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = void 0;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences: userStopSequences,\n    responseFormat,\n    seed\n  }) {\n    var _a;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (responseFormat != null && responseFormat.type !== \"text\") {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format is not supported.\"\n      });\n    }\n    const { prompt: completionPrompt, stopSequences } = convertToOpenAICompletionPrompt({ prompt, inputFormat });\n    const stop = [...stopSequences != null ? stopSequences : [], ...userStopSequences != null ? userStopSequences : []];\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n      // prompt:\n      prompt: completionPrompt,\n      // stop sequences:\n      stop: stop.length > 0 ? stop : void 0\n    };\n    switch (type) {\n      case \"regular\": {\n        if ((_a = mode.tools) == null ? void 0 : _a.length) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n            functionality: \"tools\"\n          });\n        }\n        if (mode.toolChoice) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n            functionality: \"toolChoice\"\n          });\n        }\n        return { args: baseArgs, warnings };\n      }\n      case \"object-json\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"object-json mode\"\n        });\n      }\n      case \"object-tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"object-tool mode\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    const { args, warnings } = this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiCompletionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens\n      },\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      logprobs: mapOpenAICompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body: JSON.stringify(args) }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true,\n      // only include stream_options when in strict compatibility mode:\n      stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n    };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        openaiCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let logprobs;\n    let isFirstChunk = true;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.text) != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: choice.text\n              });\n            }\n            const mappedLogprobs = mapOpenAICompletionLogProbs(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) }\n    };\n  }\n};\nvar openaiCompletionResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      text: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        tokens: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()),\n        token_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()),\n        top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.record(zod__WEBPACK_IMPORTED_MODULE_2__.string(), zod__WEBPACK_IMPORTED_MODULE_2__.number())).nullable()\n      }).nullish()\n    })\n  ),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n    completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n  })\n});\nvar openaiCompletionChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.union([\n  zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n      zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n        index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n        logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          tokens: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()),\n          token_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()),\n          top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.record(zod__WEBPACK_IMPORTED_MODULE_2__.string(), zod__WEBPACK_IMPORTED_MODULE_2__.number())).nullable()\n        }).nullish()\n      })\n    ),\n    usage: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n      completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n    }).nullish()\n  }),\n  openaiErrorDataSchema\n]);\n\n// src/openai-embedding-model.ts\n\n\n\nvar OpenAIEmbeddingModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get maxEmbeddingsPerCall() {\n    var _a;\n    return (_a = this.settings.maxEmbeddingsPerCall) != null ? _a : 2048;\n  }\n  get supportsParallelCalls() {\n    var _a;\n    return (_a = this.settings.supportsParallelCalls) != null ? _a : true;\n  }\n  async doEmbed({\n    values,\n    headers,\n    abortSignal\n  }) {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values\n      });\n    }\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/embeddings\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        input: values,\n        encoding_format: \"float\",\n        dimensions: this.settings.dimensions,\n        user: this.settings.user\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiTextEmbeddingResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      embeddings: response.data.map((item) => item.embedding),\n      usage: response.usage ? { tokens: response.usage.prompt_tokens } : void 0,\n      rawResponse: { headers: responseHeaders }\n    };\n  }\n};\nvar openaiTextEmbeddingResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  data: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.object({ embedding: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()) })),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.object({ prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number() }).nullish()\n});\n\n// src/openai-image-model.ts\n\n\n\n// src/openai-image-settings.ts\nvar modelMaxImagesPerCall = {\n  \"dall-e-3\": 1,\n  \"dall-e-2\": 10,\n  \"gpt-image-1\": 10\n};\nvar hasDefaultResponseFormat = /* @__PURE__ */ new Set([\"gpt-image-1\"]);\n\n// src/openai-image-model.ts\nvar OpenAIImageModel = class {\n  constructor(modelId, settings, config) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get maxImagesPerCall() {\n    var _a, _b;\n    return (_b = (_a = this.settings.maxImagesPerCall) != null ? _a : modelMaxImagesPerCall[this.modelId]) != null ? _b : 1;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal\n  }) {\n    var _a, _b, _c, _d;\n    const warnings = [];\n    if (aspectRatio != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"aspectRatio\",\n        details: \"This model does not support aspect ratio. Use `size` instead.\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({ type: \"unsupported-setting\", setting: \"seed\" });\n    }\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { value: response, responseHeaders } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/images/generations\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        prompt,\n        n,\n        size,\n        ...(_d = providerOptions.openai) != null ? _d : {},\n        ...!hasDefaultResponseFormat.has(this.modelId) ? { response_format: \"b64_json\" } : {}\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiImageResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      images: response.data.map((item) => item.b64_json),\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders\n      }\n    };\n  }\n};\nvar openaiImageResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  data: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.object({ b64_json: zod__WEBPACK_IMPORTED_MODULE_2__.string() }))\n});\n\n// src/openai-transcription-model.ts\n\n\nvar openAIProviderOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  include: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()).nullish(),\n  language: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  prompt: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  temperature: zod__WEBPACK_IMPORTED_MODULE_2__.number().min(0).max(1).nullish().default(0),\n  timestampGranularities: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\"word\", \"segment\"])).nullish().default([\"segment\"])\n});\nvar languageMap = {\n  afrikaans: \"af\",\n  arabic: \"ar\",\n  armenian: \"hy\",\n  azerbaijani: \"az\",\n  belarusian: \"be\",\n  bosnian: \"bs\",\n  bulgarian: \"bg\",\n  catalan: \"ca\",\n  chinese: \"zh\",\n  croatian: \"hr\",\n  czech: \"cs\",\n  danish: \"da\",\n  dutch: \"nl\",\n  english: \"en\",\n  estonian: \"et\",\n  finnish: \"fi\",\n  french: \"fr\",\n  galician: \"gl\",\n  german: \"de\",\n  greek: \"el\",\n  hebrew: \"he\",\n  hindi: \"hi\",\n  hungarian: \"hu\",\n  icelandic: \"is\",\n  indonesian: \"id\",\n  italian: \"it\",\n  japanese: \"ja\",\n  kannada: \"kn\",\n  kazakh: \"kk\",\n  korean: \"ko\",\n  latvian: \"lv\",\n  lithuanian: \"lt\",\n  macedonian: \"mk\",\n  malay: \"ms\",\n  marathi: \"mr\",\n  maori: \"mi\",\n  nepali: \"ne\",\n  norwegian: \"no\",\n  persian: \"fa\",\n  polish: \"pl\",\n  portuguese: \"pt\",\n  romanian: \"ro\",\n  russian: \"ru\",\n  serbian: \"sr\",\n  slovak: \"sk\",\n  slovenian: \"sl\",\n  spanish: \"es\",\n  swahili: \"sw\",\n  swedish: \"sv\",\n  tagalog: \"tl\",\n  tamil: \"ta\",\n  thai: \"th\",\n  turkish: \"tr\",\n  ukrainian: \"uk\",\n  urdu: \"ur\",\n  vietnamese: \"vi\",\n  welsh: \"cy\"\n};\nvar OpenAITranscriptionModel = class {\n  constructor(modelId, config) {\n    this.modelId = modelId;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    audio,\n    mediaType,\n    providerOptions\n  }) {\n    var _a, _b, _c, _d, _e;\n    const warnings = [];\n    const openAIOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.parseProviderOptions)({\n      provider: \"openai\",\n      providerOptions,\n      schema: openAIProviderOptionsSchema\n    });\n    const formData = new FormData();\n    const blob = audio instanceof Uint8Array ? new Blob([audio]) : new Blob([(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.convertBase64ToUint8Array)(audio)]);\n    formData.append(\"model\", this.modelId);\n    formData.append(\"file\", new File([blob], \"audio\", { type: mediaType }));\n    if (openAIOptions) {\n      const transcriptionModelOptions = {\n        include: (_a = openAIOptions.include) != null ? _a : void 0,\n        language: (_b = openAIOptions.language) != null ? _b : void 0,\n        prompt: (_c = openAIOptions.prompt) != null ? _c : void 0,\n        temperature: (_d = openAIOptions.temperature) != null ? _d : void 0,\n        timestamp_granularities: (_e = openAIOptions.timestampGranularities) != null ? _e : void 0\n      };\n      for (const key in transcriptionModelOptions) {\n        const value = transcriptionModelOptions[key];\n        if (value !== void 0) {\n          formData.append(key, String(value));\n        }\n      }\n    }\n    return {\n      formData,\n      warnings\n    };\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f;\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { formData, warnings } = this.getArgs(options);\n    const {\n      value: response,\n      responseHeaders,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postFormDataToApi)({\n      url: this.config.url({\n        path: \"/audio/transcriptions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      formData,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiTranscriptionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const language = response.language != null && response.language in languageMap ? languageMap[response.language] : void 0;\n    return {\n      text: response.text,\n      segments: (_e = (_d = response.words) == null ? void 0 : _d.map((word) => ({\n        text: word.word,\n        startSecond: word.start,\n        endSecond: word.end\n      }))) != null ? _e : [],\n      language,\n      durationInSeconds: (_f = response.duration) != null ? _f : void 0,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse\n      }\n    };\n  }\n};\nvar openaiTranscriptionResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  text: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  language: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  duration: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  words: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      word: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      start: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n      end: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n    })\n  ).nullish()\n});\n\n// src/responses/openai-responses-language-model.ts\n\n\n\n\n// src/responses/convert-to-openai-responses-messages.ts\n\n\nfunction convertToOpenAIResponsesMessages({\n  prompt,\n  systemMessageMode\n}) {\n  const messages = [];\n  const warnings = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        switch (systemMessageMode) {\n          case \"system\": {\n            messages.push({ role: \"system\", content });\n            break;\n          }\n          case \"developer\": {\n            messages.push({ role: \"developer\", content });\n            break;\n          }\n          case \"remove\": {\n            warnings.push({\n              type: \"other\",\n              message: \"system messages are removed for this model\"\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`\n            );\n          }\n        }\n        break;\n      }\n      case \"user\": {\n        messages.push({\n          role: \"user\",\n          content: content.map((part, index) => {\n            var _a, _b, _c, _d;\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"input_text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"input_image\",\n                  image_url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.convertUint8ArrayToBase64)(part.image)}`,\n                  // OpenAI specific extension: image detail\n                  detail: (_c = (_b = part.providerMetadata) == null ? void 0 : _b.openai) == null ? void 0 : _c.imageDetail\n                };\n              }\n              case \"file\": {\n                if (part.data instanceof URL) {\n                  throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                    functionality: \"File URLs in user messages\"\n                  });\n                }\n                switch (part.mimeType) {\n                  case \"application/pdf\": {\n                    return {\n                      type: \"input_file\",\n                      filename: (_d = part.filename) != null ? _d : `part-${index}.pdf`,\n                      file_data: `data:application/pdf;base64,${part.data}`\n                    };\n                  }\n                  default: {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                      functionality: \"Only PDF files are supported in user messages\"\n                    });\n                  }\n                }\n              }\n            }\n          })\n        });\n        break;\n      }\n      case \"assistant\": {\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              messages.push({\n                role: \"assistant\",\n                content: [{ type: \"output_text\", text: part.text }]\n              });\n              break;\n            }\n            case \"tool-call\": {\n              messages.push({\n                type: \"function_call\",\n                call_id: part.toolCallId,\n                name: part.toolName,\n                arguments: JSON.stringify(part.args)\n              });\n              break;\n            }\n          }\n        }\n        break;\n      }\n      case \"tool\": {\n        for (const part of content) {\n          messages.push({\n            type: \"function_call_output\",\n            call_id: part.toolCallId,\n            output: JSON.stringify(part.result)\n          });\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return { messages, warnings };\n}\n\n// src/responses/map-openai-responses-finish-reason.ts\nfunction mapOpenAIResponseFinishReason({\n  finishReason,\n  hasToolCalls\n}) {\n  switch (finishReason) {\n    case void 0:\n    case null:\n      return hasToolCalls ? \"tool-calls\" : \"stop\";\n    case \"max_output_tokens\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    default:\n      return hasToolCalls ? \"tool-calls\" : \"unknown\";\n  }\n}\n\n// src/responses/openai-responses-prepare-tools.ts\n\nfunction prepareResponsesTools({\n  mode,\n  strict\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  const openaiTools2 = [];\n  for (const tool of tools) {\n    switch (tool.type) {\n      case \"function\":\n        openaiTools2.push({\n          type: \"function\",\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: strict ? true : void 0\n        });\n        break;\n      case \"provider-defined\":\n        switch (tool.id) {\n          case \"openai.web_search_preview\":\n            openaiTools2.push({\n              type: \"web_search_preview\",\n              search_context_size: tool.args.searchContextSize,\n              user_location: tool.args.userLocation\n            });\n            break;\n          default:\n            toolWarnings.push({ type: \"unsupported-tool\", tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: \"unsupported-tool\", tool });\n        break;\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: openaiTools2, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: openaiTools2, tool_choice: type, toolWarnings };\n    case \"tool\": {\n      if (toolChoice.toolName === \"web_search_preview\") {\n        return {\n          tools: openaiTools2,\n          tool_choice: {\n            type: \"web_search_preview\"\n          },\n          toolWarnings\n        };\n      }\n      return {\n        tools: openaiTools2,\n        tool_choice: {\n          type: \"function\",\n          name: toolChoice.toolName\n        },\n        toolWarnings\n      };\n    }\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/responses/openai-responses-language-model.ts\nvar OpenAIResponsesLanguageModel = class {\n  constructor(modelId, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = \"json\";\n    this.supportsStructuredOutputs = true;\n    this.modelId = modelId;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    maxTokens,\n    temperature,\n    stopSequences,\n    topP,\n    topK,\n    presencePenalty,\n    frequencyPenalty,\n    seed,\n    prompt,\n    providerMetadata,\n    responseFormat\n  }) {\n    var _a, _b, _c;\n    const warnings = [];\n    const modelConfig = getResponsesModelConfig(this.modelId);\n    const type = mode.type;\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"seed\"\n      });\n    }\n    if (presencePenalty != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"presencePenalty\"\n      });\n    }\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"frequencyPenalty\"\n      });\n    }\n    if (stopSequences != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"stopSequences\"\n      });\n    }\n    const { messages, warnings: messageWarnings } = convertToOpenAIResponsesMessages({\n      prompt,\n      systemMessageMode: modelConfig.systemMessageMode\n    });\n    warnings.push(...messageWarnings);\n    const openaiOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.parseProviderOptions)({\n      provider: \"openai\",\n      providerOptions: providerMetadata,\n      schema: openaiResponsesProviderOptionsSchema\n    });\n    const isStrict = (_a = openaiOptions == null ? void 0 : openaiOptions.strictSchemas) != null ? _a : true;\n    const baseArgs = {\n      model: this.modelId,\n      input: messages,\n      temperature,\n      top_p: topP,\n      max_output_tokens: maxTokens,\n      ...(responseFormat == null ? void 0 : responseFormat.type) === \"json\" && {\n        text: {\n          format: responseFormat.schema != null ? {\n            type: \"json_schema\",\n            strict: isStrict,\n            name: (_b = responseFormat.name) != null ? _b : \"response\",\n            description: responseFormat.description,\n            schema: responseFormat.schema\n          } : { type: \"json_object\" }\n        }\n      },\n      // provider options:\n      metadata: openaiOptions == null ? void 0 : openaiOptions.metadata,\n      parallel_tool_calls: openaiOptions == null ? void 0 : openaiOptions.parallelToolCalls,\n      previous_response_id: openaiOptions == null ? void 0 : openaiOptions.previousResponseId,\n      store: openaiOptions == null ? void 0 : openaiOptions.store,\n      user: openaiOptions == null ? void 0 : openaiOptions.user,\n      instructions: openaiOptions == null ? void 0 : openaiOptions.instructions,\n      // model-specific settings:\n      ...modelConfig.isReasoningModel && ((openaiOptions == null ? void 0 : openaiOptions.reasoningEffort) != null || (openaiOptions == null ? void 0 : openaiOptions.reasoningSummary) != null) && {\n        reasoning: {\n          ...(openaiOptions == null ? void 0 : openaiOptions.reasoningEffort) != null && {\n            effort: openaiOptions.reasoningEffort\n          },\n          ...(openaiOptions == null ? void 0 : openaiOptions.reasoningSummary) != null && {\n            summary: openaiOptions.reasoningSummary\n          }\n        }\n      },\n      ...modelConfig.requiredAutoTruncation && {\n        truncation: \"auto\"\n      }\n    };\n    if (modelConfig.isReasoningModel) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topP\",\n          details: \"topP is not supported for reasoning models\"\n        });\n      }\n    }\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, toolWarnings } = prepareResponsesTools({\n          mode,\n          strict: isStrict\n          // TODO support provider options on tools\n        });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            text: {\n              format: mode.schema != null ? {\n                type: \"json_schema\",\n                strict: isStrict,\n                name: (_c = mode.name) != null ? _c : \"response\",\n                description: mode.description,\n                schema: mode.schema\n              } : { type: \"json_object\" }\n            }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: { type: \"function\", name: mode.tool.name },\n            tools: [\n              {\n                type: \"function\",\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters,\n                strict: isStrict\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const { args: body, warnings } = this.getArgs(options);\n    const url = this.config.url({\n      path: \"/responses\",\n      modelId: this.modelId\n    });\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url,\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n          created_at: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n          error: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n            message: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n            code: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n          }).nullish(),\n          model: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n          output: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n            zod__WEBPACK_IMPORTED_MODULE_2__.discriminatedUnion(\"type\", [\n              zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"message\"),\n                role: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"assistant\"),\n                content: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n                  zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                    type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"output_text\"),\n                    text: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n                    annotations: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n                      zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                        type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"url_citation\"),\n                        start_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n                        end_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n                        url: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n                        title: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n                      })\n                    )\n                  })\n                )\n              }),\n              zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"function_call\"),\n                call_id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n                name: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n                arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n              }),\n              zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"web_search_call\")\n              }),\n              zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"computer_call\")\n              }),\n              zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"reasoning\"),\n                summary: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n                  zod__WEBPACK_IMPORTED_MODULE_2__.object({\n                    type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"summary_text\"),\n                    text: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n                  })\n                )\n              })\n            ])\n          ),\n          incomplete_details: zod__WEBPACK_IMPORTED_MODULE_2__.object({ reason: zod__WEBPACK_IMPORTED_MODULE_2__.string() }).nullable(),\n          usage: usageSchema\n        })\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    if (response.error) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.error.message,\n        url,\n        requestBodyValues: body,\n        statusCode: 400,\n        responseHeaders,\n        responseBody: rawResponse,\n        isRetryable: false\n      });\n    }\n    const outputTextElements = response.output.filter((output) => output.type === \"message\").flatMap((output) => output.content).filter((content) => content.type === \"output_text\");\n    const toolCalls = response.output.filter((output) => output.type === \"function_call\").map((output) => ({\n      toolCallType: \"function\",\n      toolCallId: output.call_id,\n      toolName: output.name,\n      args: output.arguments\n    }));\n    const reasoningSummary = (_b = (_a = response.output.find((item) => item.type === \"reasoning\")) == null ? void 0 : _a.summary) != null ? _b : null;\n    return {\n      text: outputTextElements.map((content) => content.text).join(\"\\n\"),\n      sources: outputTextElements.flatMap(\n        (content) => content.annotations.map((annotation) => {\n          var _a2, _b2, _c2;\n          return {\n            sourceType: \"url\",\n            id: (_c2 = (_b2 = (_a2 = this.config).generateId) == null ? void 0 : _b2.call(_a2)) != null ? _c2 : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n            url: annotation.url,\n            title: annotation.title\n          };\n        })\n      ),\n      finishReason: mapOpenAIResponseFinishReason({\n        finishReason: (_c = response.incomplete_details) == null ? void 0 : _c.reason,\n        hasToolCalls: toolCalls.length > 0\n      }),\n      toolCalls: toolCalls.length > 0 ? toolCalls : void 0,\n      reasoning: reasoningSummary ? reasoningSummary.map((summary) => ({\n        type: \"text\",\n        text: summary.text\n      })) : void 0,\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens\n      },\n      rawCall: {\n        rawPrompt: void 0,\n        rawSettings: {}\n      },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse\n      },\n      request: {\n        body: JSON.stringify(body)\n      },\n      response: {\n        id: response.id,\n        timestamp: new Date(response.created_at * 1e3),\n        modelId: response.model\n      },\n      providerMetadata: {\n        openai: {\n          responseId: response.id,\n          cachedPromptTokens: (_e = (_d = response.usage.input_tokens_details) == null ? void 0 : _d.cached_tokens) != null ? _e : null,\n          reasoningTokens: (_g = (_f = response.usage.output_tokens_details) == null ? void 0 : _f.reasoning_tokens) != null ? _g : null\n        }\n      },\n      warnings\n    };\n  }\n  async doStream(options) {\n    const { args: body, warnings } = this.getArgs(options);\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/responses\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: {\n        ...body,\n        stream: true\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        openaiResponsesChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const self = this;\n    let finishReason = \"unknown\";\n    let promptTokens = NaN;\n    let completionTokens = NaN;\n    let cachedPromptTokens = null;\n    let reasoningTokens = null;\n    let responseId = null;\n    const ongoingToolCalls = {};\n    let hasToolCalls = false;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d, _e, _f, _g, _h;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (isResponseOutputItemAddedChunk(value)) {\n              if (value.item.type === \"function_call\") {\n                ongoingToolCalls[value.output_index] = {\n                  toolName: value.item.name,\n                  toolCallId: value.item.call_id\n                };\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: value.item.call_id,\n                  toolName: value.item.name,\n                  argsTextDelta: value.item.arguments\n                });\n              }\n            } else if (isResponseFunctionCallArgumentsDeltaChunk(value)) {\n              const toolCall = ongoingToolCalls[value.output_index];\n              if (toolCall != null) {\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  argsTextDelta: value.delta\n                });\n              }\n            } else if (isResponseCreatedChunk(value)) {\n              responseId = value.response.id;\n              controller.enqueue({\n                type: \"response-metadata\",\n                id: value.response.id,\n                timestamp: new Date(value.response.created_at * 1e3),\n                modelId: value.response.model\n              });\n            } else if (isTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: value.delta\n              });\n            } else if (isResponseReasoningSummaryTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: \"reasoning\",\n                textDelta: value.delta\n              });\n            } else if (isResponseOutputItemDoneChunk(value) && value.item.type === \"function_call\") {\n              ongoingToolCalls[value.output_index] = void 0;\n              hasToolCalls = true;\n              controller.enqueue({\n                type: \"tool-call\",\n                toolCallType: \"function\",\n                toolCallId: value.item.call_id,\n                toolName: value.item.name,\n                args: value.item.arguments\n              });\n            } else if (isResponseFinishedChunk(value)) {\n              finishReason = mapOpenAIResponseFinishReason({\n                finishReason: (_a = value.response.incomplete_details) == null ? void 0 : _a.reason,\n                hasToolCalls\n              });\n              promptTokens = value.response.usage.input_tokens;\n              completionTokens = value.response.usage.output_tokens;\n              cachedPromptTokens = (_c = (_b = value.response.usage.input_tokens_details) == null ? void 0 : _b.cached_tokens) != null ? _c : cachedPromptTokens;\n              reasoningTokens = (_e = (_d = value.response.usage.output_tokens_details) == null ? void 0 : _d.reasoning_tokens) != null ? _e : reasoningTokens;\n            } else if (isResponseAnnotationAddedChunk(value)) {\n              controller.enqueue({\n                type: \"source\",\n                source: {\n                  sourceType: \"url\",\n                  id: (_h = (_g = (_f = self.config).generateId) == null ? void 0 : _g.call(_f)) != null ? _h : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                  url: value.annotation.url,\n                  title: value.annotation.title\n                }\n              });\n            } else if (isErrorChunk(value)) {\n              controller.enqueue({ type: \"error\", error: value });\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage: { promptTokens, completionTokens },\n              ...(cachedPromptTokens != null || reasoningTokens != null) && {\n                providerMetadata: {\n                  openai: {\n                    responseId,\n                    cachedPromptTokens,\n                    reasoningTokens\n                  }\n                }\n              }\n            });\n          }\n        })\n      ),\n      rawCall: {\n        rawPrompt: void 0,\n        rawSettings: {}\n      },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings\n    };\n  }\n};\nvar usageSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  input_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  input_tokens_details: zod__WEBPACK_IMPORTED_MODULE_2__.object({ cached_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish() }).nullish(),\n  output_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  output_tokens_details: zod__WEBPACK_IMPORTED_MODULE_2__.object({ reasoning_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish() }).nullish()\n});\nvar textDeltaChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.output_text.delta\"),\n  delta: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n});\nvar responseFinishedChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\"response.completed\", \"response.incomplete\"]),\n  response: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    incomplete_details: zod__WEBPACK_IMPORTED_MODULE_2__.object({ reason: zod__WEBPACK_IMPORTED_MODULE_2__.string() }).nullish(),\n    usage: usageSchema\n  })\n});\nvar responseCreatedChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.created\"),\n  response: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n  })\n});\nvar responseOutputItemDoneSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.output_item.done\"),\n  output_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  item: zod__WEBPACK_IMPORTED_MODULE_2__.discriminatedUnion(\"type\", [\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"message\")\n    }),\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"function_call\"),\n      id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      call_id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      name: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      status: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"completed\")\n    })\n  ])\n});\nvar responseFunctionCallArgumentsDeltaSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.function_call_arguments.delta\"),\n  item_id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  output_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  delta: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n});\nvar responseOutputItemAddedSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.output_item.added\"),\n  output_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  item: zod__WEBPACK_IMPORTED_MODULE_2__.discriminatedUnion(\"type\", [\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"message\")\n    }),\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"function_call\"),\n      id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      call_id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      name: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n      arguments: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n    })\n  ])\n});\nvar responseAnnotationAddedSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.output_text.annotation.added\"),\n  annotation: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"url_citation\"),\n    url: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n  })\n});\nvar responseReasoningSummaryTextDeltaSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"response.reasoning_summary_text.delta\"),\n  item_id: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  output_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  summary_index: zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n  delta: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n});\nvar errorChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"error\"),\n  code: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  message: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n  param: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  sequence_number: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n});\nvar openaiResponsesChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.union([\n  textDeltaChunkSchema,\n  responseFinishedChunkSchema,\n  responseCreatedChunkSchema,\n  responseOutputItemDoneSchema,\n  responseFunctionCallArgumentsDeltaSchema,\n  responseOutputItemAddedSchema,\n  responseAnnotationAddedSchema,\n  responseReasoningSummaryTextDeltaSchema,\n  errorChunkSchema,\n  zod__WEBPACK_IMPORTED_MODULE_2__.object({ type: zod__WEBPACK_IMPORTED_MODULE_2__.string() }).passthrough()\n  // fallback for unknown chunks\n]);\nfunction isTextDeltaChunk(chunk) {\n  return chunk.type === \"response.output_text.delta\";\n}\nfunction isResponseOutputItemDoneChunk(chunk) {\n  return chunk.type === \"response.output_item.done\";\n}\nfunction isResponseFinishedChunk(chunk) {\n  return chunk.type === \"response.completed\" || chunk.type === \"response.incomplete\";\n}\nfunction isResponseCreatedChunk(chunk) {\n  return chunk.type === \"response.created\";\n}\nfunction isResponseFunctionCallArgumentsDeltaChunk(chunk) {\n  return chunk.type === \"response.function_call_arguments.delta\";\n}\nfunction isResponseOutputItemAddedChunk(chunk) {\n  return chunk.type === \"response.output_item.added\";\n}\nfunction isResponseAnnotationAddedChunk(chunk) {\n  return chunk.type === \"response.output_text.annotation.added\";\n}\nfunction isResponseReasoningSummaryTextDeltaChunk(chunk) {\n  return chunk.type === \"response.reasoning_summary_text.delta\";\n}\nfunction isErrorChunk(chunk) {\n  return chunk.type === \"error\";\n}\nfunction getResponsesModelConfig(modelId) {\n  if (modelId.startsWith(\"o\")) {\n    if (modelId.startsWith(\"o1-mini\") || modelId.startsWith(\"o1-preview\")) {\n      return {\n        isReasoningModel: true,\n        systemMessageMode: \"remove\",\n        requiredAutoTruncation: false\n      };\n    }\n    return {\n      isReasoningModel: true,\n      systemMessageMode: \"developer\",\n      requiredAutoTruncation: false\n    };\n  }\n  return {\n    isReasoningModel: false,\n    systemMessageMode: \"system\",\n    requiredAutoTruncation: false\n  };\n}\nvar openaiResponsesProviderOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  metadata: zod__WEBPACK_IMPORTED_MODULE_2__.any().nullish(),\n  parallelToolCalls: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().nullish(),\n  previousResponseId: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  store: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().nullish(),\n  user: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  reasoningEffort: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  strictSchemas: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().nullish(),\n  instructions: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  reasoningSummary: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n});\n\n// src/openai-tools.ts\n\nvar WebSearchPreviewParameters = zod__WEBPACK_IMPORTED_MODULE_2__.object({});\nfunction webSearchPreviewTool({\n  searchContextSize,\n  userLocation\n} = {}) {\n  return {\n    type: \"provider-defined\",\n    id: \"openai.web_search_preview\",\n    args: {\n      searchContextSize,\n      userLocation\n    },\n    parameters: WebSearchPreviewParameters\n  };\n}\nvar openaiTools = {\n  webSearchPreview: webSearchPreviewTool\n};\n\n// src/openai-speech-model.ts\n\n\nvar OpenAIProviderOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  instructions: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  speed: zod__WEBPACK_IMPORTED_MODULE_2__.number().min(0.25).max(4).default(1).nullish()\n});\nvar OpenAISpeechModel = class {\n  constructor(modelId, config) {\n    this.modelId = modelId;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    text,\n    voice = \"alloy\",\n    outputFormat = \"mp3\",\n    speed,\n    instructions,\n    providerOptions\n  }) {\n    const warnings = [];\n    const openAIOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.parseProviderOptions)({\n      provider: \"openai\",\n      providerOptions,\n      schema: OpenAIProviderOptionsSchema\n    });\n    const requestBody = {\n      model: this.modelId,\n      input: text,\n      voice,\n      response_format: \"mp3\",\n      speed,\n      instructions\n    };\n    if (outputFormat) {\n      if ([\"mp3\", \"opus\", \"aac\", \"flac\", \"wav\", \"pcm\"].includes(outputFormat)) {\n        requestBody.response_format = outputFormat;\n      } else {\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"outputFormat\",\n          details: `Unsupported output format: ${outputFormat}. Using mp3 instead.`\n        });\n      }\n    }\n    if (openAIOptions) {\n      const speechModelOptions = {};\n      for (const key in speechModelOptions) {\n        const value = speechModelOptions[key];\n        if (value !== void 0) {\n          requestBody[key] = value;\n        }\n      }\n    }\n    return {\n      requestBody,\n      warnings\n    };\n  }\n  async doGenerate(options) {\n    var _a, _b, _c;\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { requestBody, warnings } = this.getArgs(options);\n    const {\n      value: audio,\n      responseHeaders,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/audio/speech\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: requestBody,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createBinaryResponseHandler)(),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      audio,\n      warnings,\n      request: {\n        body: JSON.stringify(requestBody)\n      },\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse\n      }\n    };\n  }\n};\n\n// src/openai-provider.ts\nfunction createOpenAI(options = {}) {\n  var _a, _b, _c;\n  const baseURL = (_a = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.withoutTrailingSlash)(options.baseURL)) != null ? _a : \"https://api.openai.com/v1\";\n  const compatibility = (_b = options.compatibility) != null ? _b : \"compatible\";\n  const providerName = (_c = options.name) != null ? _c : \"openai\";\n  const getHeaders = () => ({\n    Authorization: `Bearer ${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"OPENAI_API_KEY\",\n      description: \"OpenAI\"\n    })}`,\n    \"OpenAI-Organization\": options.organization,\n    \"OpenAI-Project\": options.project,\n    ...options.headers\n  });\n  const createChatModel = (modelId, settings = {}) => new OpenAIChatLanguageModel(modelId, settings, {\n    provider: `${providerName}.chat`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch\n  });\n  const createCompletionModel = (modelId, settings = {}) => new OpenAICompletionLanguageModel(modelId, settings, {\n    provider: `${providerName}.completion`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch\n  });\n  const createEmbeddingModel = (modelId, settings = {}) => new OpenAIEmbeddingModel(modelId, settings, {\n    provider: `${providerName}.embedding`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createImageModel = (modelId, settings = {}) => new OpenAIImageModel(modelId, settings, {\n    provider: `${providerName}.image`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createTranscriptionModel = (modelId) => new OpenAITranscriptionModel(modelId, {\n    provider: `${providerName}.transcription`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createSpeechModel = (modelId) => new OpenAISpeechModel(modelId, {\n    provider: `${providerName}.speech`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createLanguageModel = (modelId, settings) => {\n    if (new.target) {\n      throw new Error(\n        \"The OpenAI model function cannot be called with the new keyword.\"\n      );\n    }\n    if (modelId === \"gpt-3.5-turbo-instruct\") {\n      return createCompletionModel(\n        modelId,\n        settings\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  const createResponsesModel = (modelId) => {\n    return new OpenAIResponsesLanguageModel(modelId, {\n      provider: `${providerName}.responses`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch\n    });\n  };\n  const provider = function(modelId, settings) {\n    return createLanguageModel(modelId, settings);\n  };\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  provider.responses = createResponsesModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n  provider.transcription = createTranscriptionModel;\n  provider.transcriptionModel = createTranscriptionModel;\n  provider.speech = createSpeechModel;\n  provider.speechModel = createSpeechModel;\n  provider.tools = openaiTools;\n  return provider;\n}\nvar openai = createOpenAI({\n  compatibility: \"strict\"\n  // strict for OpenAI API\n});\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@ai-sdk+openai@1.3.23_zod@3.25.76/node_modules/@ai-sdk/openai/dist/index.mjs\n");

/***/ })

};
;