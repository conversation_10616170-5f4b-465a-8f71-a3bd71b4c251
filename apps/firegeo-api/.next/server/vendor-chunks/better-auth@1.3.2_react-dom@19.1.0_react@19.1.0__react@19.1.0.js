"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0";
exports.ids = ["vendor-chunks/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/client/react/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/client/react/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   createAuthClient: () => (/* binding */ createAuthClient),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var _shared_better_auth_A_Crzln_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/better-auth.A_Crzln-.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _better_fetch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @better-fetch/fetch */ \"(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js\");\n/* harmony import */ var _shared_better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/better-auth.VTXNLFMT.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\");\n/* harmony import */ var _shared_better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/better-auth.8zoxzg-F.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\");\n/* harmony import */ var _shared_better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/better-auth.DdzSJf-n.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\");\n/* harmony import */ var _shared_better_auth_Buni1mmI_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/better-auth.Buni1mmI.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs\");\n/* harmony import */ var _shared_better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../shared/better-auth.ffWeg50w.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\");\n\n\n\n\n\n\n\n\n\n\nfunction useStore(store, options = {}) {\n  let snapshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(store.get());\n  const { keys, deps = [store, keys] } = options;\n  let subscribe = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((onChange) => {\n    const emitChange = (value) => {\n      if (snapshotRef.current === value) return;\n      snapshotRef.current = value;\n      onChange();\n    };\n    emitChange(store.value);\n    if (keys?.length) {\n      return (0,nanostores__WEBPACK_IMPORTED_MODULE_8__.listenKeys)(store, keys, emitChange);\n    }\n    return store.listen(emitChange);\n  }, deps);\n  let get = () => snapshotRef.current;\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)(subscribe, get, get);\n}\n\nfunction getAtomKey(str) {\n  return `use${capitalizeFirstLetter(str)}`;\n}\nfunction capitalizeFirstLetter(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction createAuthClient(options) {\n  const {\n    pluginPathMethods,\n    pluginsActions,\n    pluginsAtoms,\n    $fetch,\n    $store,\n    atomListeners\n  } = (0,_shared_better_auth_A_Crzln_mjs__WEBPACK_IMPORTED_MODULE_0__.g)(options);\n  let resolvedHooks = {};\n  for (const [key, value] of Object.entries(pluginsAtoms)) {\n    resolvedHooks[getAtomKey(key)] = () => useStore(value);\n  }\n  const routes = {\n    ...pluginsActions,\n    ...resolvedHooks,\n    $fetch,\n    $store\n  };\n  const proxy = (0,_shared_better_auth_A_Crzln_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(\n    routes,\n    $fetch,\n    pluginPathMethods,\n    pluginsAtoms,\n    atomListeners\n  );\n  return proxy;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/client/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ isProduction),\n/* harmony export */   b: () => (/* binding */ isDevelopment),\n/* harmony export */   e: () => (/* binding */ env),\n/* harmony export */   i: () => (/* binding */ isTest)\n/* harmony export */ });\nconst _envShim = /* @__PURE__ */ Object.create(null);\nconst _getEnv = (useShim) => globalThis.process?.env || //@ts-expect-error\nglobalThis.Deno?.env.toObject() || //@ts-expect-error\nglobalThis.__env__ || (useShim ? _envShim : globalThis);\nconst env = new Proxy(_envShim, {\n  get(_, prop) {\n    const env2 = _getEnv();\n    return env2[prop] ?? _envShim[prop];\n  },\n  has(_, prop) {\n    const env2 = _getEnv();\n    return prop in env2 || prop in _envShim;\n  },\n  set(_, prop, value) {\n    const env2 = _getEnv(true);\n    env2[prop] = value;\n    return true;\n  },\n  deleteProperty(_, prop) {\n    if (!prop) {\n      return false;\n    }\n    const env2 = _getEnv(true);\n    delete env2[prop];\n    return true;\n  },\n  ownKeys() {\n    const env2 = _getEnv(true);\n    return Object.keys(env2);\n  }\n});\nfunction toBoolean(val) {\n  return val ? val !== \"false\" : false;\n}\nconst nodeENV = typeof process !== \"undefined\" && process.env && \"development\" || \"\";\nconst isProduction = nodeENV === \"production\";\nconst isDevelopment = nodeENV === \"dev\" || nodeENV === \"development\";\nconst isTest = nodeENV === \"test\" || toBoolean(env.TEST);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   c: () => (/* binding */ createDynamicPathProxy),\n/* harmony export */   g: () => (/* binding */ getClientConfig)\n/* harmony export */ });\n/* harmony import */ var _better_fetch_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @better-fetch/fetch */ \"(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js\");\n/* harmony import */ var _better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./better-auth.VTXNLFMT.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js\");\n/* harmony import */ var _better_auth_Buni1mmI_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./better-auth.Buni1mmI.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs\");\n/* harmony import */ var _better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./better-auth.ffWeg50w.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\");\n\n\n\n\n\n\nconst redirectPlugin = {\n  id: \"redirect\",\n  name: \"Redirect\",\n  hooks: {\n    onSuccess(context) {\n      if (context.data?.url && context.data?.redirect) {\n        if (typeof window !== \"undefined\" && window.location) {\n          if (window.location) {\n            try {\n              window.location.href = context.data.url;\n            } catch {\n            }\n          }\n        }\n      }\n    }\n  }\n};\n\nfunction getSessionAtom($fetch) {\n  const $signal = (0,nanostores__WEBPACK_IMPORTED_MODULE_4__.atom)(false);\n  const session = (0,_better_auth_Buni1mmI_mjs__WEBPACK_IMPORTED_MODULE_2__.u)($signal, \"/get-session\", $fetch, {\n    method: \"GET\"\n  });\n  return {\n    session,\n    $sessionSignal: $signal\n  };\n}\n\nconst getClientConfig = (options) => {\n  const isCredentialsSupported = \"credentials\" in Request.prototype;\n  const baseURL = (0,_better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_1__.a)(options?.baseURL, options?.basePath);\n  const pluginsFetchPlugins = options?.plugins?.flatMap((plugin) => plugin.fetchPlugins).filter((pl) => pl !== void 0) || [];\n  const lifeCyclePlugin = {\n    id: \"lifecycle-hooks\",\n    name: \"lifecycle-hooks\",\n    hooks: {\n      onSuccess: options?.fetchOptions?.onSuccess,\n      onError: options?.fetchOptions?.onError,\n      onRequest: options?.fetchOptions?.onRequest,\n      onResponse: options?.fetchOptions?.onResponse\n    }\n  };\n  const { onSuccess, onError, onRequest, onResponse, ...restOfFetchOptions } = options?.fetchOptions || {};\n  const $fetch = (0,_better_fetch_fetch__WEBPACK_IMPORTED_MODULE_0__.createFetch)({\n    baseURL,\n    ...isCredentialsSupported ? { credentials: \"include\" } : {},\n    method: \"GET\",\n    jsonParser(text) {\n      if (!text) {\n        return null;\n      }\n      return (0,_better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_3__.p)(text, {\n        strict: false\n      });\n    },\n    customFetchImpl: async (input, init) => {\n      try {\n        return await fetch(input, init);\n      } catch (error) {\n        return Response.error();\n      }\n    },\n    ...restOfFetchOptions,\n    plugins: [\n      lifeCyclePlugin,\n      ...restOfFetchOptions.plugins || [],\n      ...options?.disableDefaultFetchPlugins ? [] : [redirectPlugin],\n      ...pluginsFetchPlugins\n    ]\n  });\n  const { $sessionSignal, session } = getSessionAtom($fetch);\n  const plugins = options?.plugins || [];\n  let pluginsActions = {};\n  let pluginsAtoms = {\n    $sessionSignal,\n    session\n  };\n  let pluginPathMethods = {\n    \"/sign-out\": \"POST\",\n    \"/revoke-sessions\": \"POST\",\n    \"/revoke-other-sessions\": \"POST\",\n    \"/delete-user\": \"POST\"\n  };\n  const atomListeners = [\n    {\n      signal: \"$sessionSignal\",\n      matcher(path) {\n        return path === \"/sign-out\" || path === \"/update-user\" || path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path === \"/delete-user\" || path === \"/verify-email\";\n      }\n    }\n  ];\n  for (const plugin of plugins) {\n    if (plugin.getAtoms) {\n      Object.assign(pluginsAtoms, plugin.getAtoms?.($fetch));\n    }\n    if (plugin.pathMethods) {\n      Object.assign(pluginPathMethods, plugin.pathMethods);\n    }\n    if (plugin.atomListeners) {\n      atomListeners.push(...plugin.atomListeners);\n    }\n  }\n  const $store = {\n    notify: (signal) => {\n      pluginsAtoms[signal].set(\n        !pluginsAtoms[signal].get()\n      );\n    },\n    listen: (signal, listener) => {\n      pluginsAtoms[signal].subscribe(listener);\n    },\n    atoms: pluginsAtoms\n  };\n  for (const plugin of plugins) {\n    if (plugin.getActions) {\n      Object.assign(\n        pluginsActions,\n        plugin.getActions?.($fetch, $store, options)\n      );\n    }\n  }\n  return {\n    pluginsActions,\n    pluginsAtoms,\n    pluginPathMethods,\n    atomListeners,\n    $fetch,\n    $store\n  };\n};\n\nfunction getMethod(path, knownPathMethods, args) {\n  const method = knownPathMethods[path];\n  const { fetchOptions, query, ...body } = args || {};\n  if (method) {\n    return method;\n  }\n  if (fetchOptions?.method) {\n    return fetchOptions.method;\n  }\n  if (body && Object.keys(body).length > 0) {\n    return \"POST\";\n  }\n  return \"GET\";\n}\nfunction createDynamicPathProxy(routes, client, knownPathMethods, atoms, atomListeners) {\n  function createProxy(path = []) {\n    return new Proxy(function() {\n    }, {\n      get(target, prop) {\n        const fullPath = [...path, prop];\n        let current = routes;\n        for (const segment of fullPath) {\n          if (current && typeof current === \"object\" && segment in current) {\n            current = current[segment];\n          } else {\n            current = void 0;\n            break;\n          }\n        }\n        if (typeof current === \"function\") {\n          return current;\n        }\n        return createProxy(fullPath);\n      },\n      apply: async (_, __, args) => {\n        const routePath = \"/\" + path.map(\n          (segment) => segment.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`)\n        ).join(\"/\");\n        const arg = args[0] || {};\n        const fetchOptions = args[1] || {};\n        const { query, fetchOptions: argFetchOptions, ...body } = arg;\n        const options = {\n          ...fetchOptions,\n          ...argFetchOptions\n        };\n        const method = getMethod(routePath, knownPathMethods, arg);\n        return await client(routePath, {\n          ...options,\n          body: method === \"GET\" ? void 0 : {\n            ...body,\n            ...options?.body || {}\n          },\n          query: query || options?.query,\n          method,\n          async onSuccess(context) {\n            await options?.onSuccess?.(context);\n            const matches = atomListeners?.find((s) => s.matcher(routePath));\n            if (!matches) return;\n            const signal = atoms[matches.signal];\n            if (!signal) return;\n            const val = signal.get();\n            setTimeout(() => {\n              signal.set(!val);\n            }, 10);\n          }\n        });\n      }\n    });\n  }\n  return createProxy();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   u: () => (/* binding */ useAuthQuery)\n/* harmony export */ });\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js\");\n\n\nconst isServer = typeof window === \"undefined\";\nconst useAuthQuery = (initializedAtom, path, $fetch, options) => {\n  const value = (0,nanostores__WEBPACK_IMPORTED_MODULE_0__.atom)({\n    data: null,\n    error: null,\n    isPending: true,\n    isRefetching: false,\n    refetch: () => {\n      return fn();\n    }\n  });\n  const fn = () => {\n    const opts = typeof options === \"function\" ? options({\n      data: value.get().data,\n      error: value.get().error,\n      isPending: value.get().isPending\n    }) : options;\n    return $fetch(path, {\n      ...opts,\n      async onSuccess(context) {\n        value.set({\n          data: context.data,\n          error: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onSuccess?.(context);\n      },\n      async onError(context) {\n        const { request } = context;\n        const retryAttempts = typeof request.retry === \"number\" ? request.retry : request.retry?.attempts;\n        const retryAttempt = request.retryAttempt || 0;\n        if (retryAttempts && retryAttempt < retryAttempts) return;\n        value.set({\n          error: context.error,\n          data: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onError?.(context);\n      },\n      async onRequest(context) {\n        const currentValue = value.get();\n        value.set({\n          isPending: currentValue.data === null,\n          data: currentValue.data,\n          error: null,\n          isRefetching: true,\n          refetch: value.value.refetch\n        });\n        await opts?.onRequest?.(context);\n      }\n    });\n  };\n  initializedAtom = Array.isArray(initializedAtom) ? initializedAtom : [initializedAtom];\n  let isMounted = false;\n  for (const initAtom of initializedAtom) {\n    initAtom.subscribe(() => {\n      if (isServer) {\n        return;\n      }\n      if (isMounted) {\n        fn();\n      } else {\n        (0,nanostores__WEBPACK_IMPORTED_MODULE_1__.onMount)(value, () => {\n          setTimeout(() => {\n            fn();\n          }, 0);\n          isMounted = true;\n          return () => {\n            value.off();\n            initAtom.off();\n          };\n        });\n      }\n    });\n  }\n  return value;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   B: () => (/* binding */ BetterAuthError),\n/* harmony export */   M: () => (/* binding */ MissingDependencyError)\n/* harmony export */ });\nclass BetterAuthError extends Error {\n  constructor(message, cause) {\n    super(message);\n    this.name = \"BetterAuthError\";\n    this.message = message;\n    this.cause = cause;\n    this.stack = \"\";\n  }\n}\nclass MissingDependencyError extends BetterAuthError {\n  constructor(pkgName) {\n    super(\n      `The package \"${pkgName}\" is required. Make sure it is installed.`,\n      pkgName\n    );\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2JldHRlci1hdXRoQDEuMy4yX3JlYWN0LWRvbUAxOS4xLjBfcmVhY3RAMTkuMS4wX19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2JldHRlci1hdXRoL2Rpc3Qvc2hhcmVkL2JldHRlci1hdXRoLkRkelNKZi1uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixRQUFRO0FBQzlCO0FBQ0E7QUFDQTtBQUNBOztBQUU2RCIsInNvdXJjZXMiOlsiL1VzZXJzL21hZGh1a2Fya3VtYXIvRHJvcGJveC9tYWRodWthci9yb2J5bm52My9jb2RlL3JvYnlubnYzL25vZGVfbW9kdWxlcy8ucG5wbS9iZXR0ZXItYXV0aEAxLjMuMl9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9iZXR0ZXItYXV0aC9kaXN0L3NoYXJlZC9iZXR0ZXItYXV0aC5EZHpTSmYtbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY2xhc3MgQmV0dGVyQXV0aEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBjYXVzZSkge1xuICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgIHRoaXMubmFtZSA9IFwiQmV0dGVyQXV0aEVycm9yXCI7XG4gICAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgICB0aGlzLmNhdXNlID0gY2F1c2U7XG4gICAgdGhpcy5zdGFjayA9IFwiXCI7XG4gIH1cbn1cbmNsYXNzIE1pc3NpbmdEZXBlbmRlbmN5RXJyb3IgZXh0ZW5kcyBCZXR0ZXJBdXRoRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcihwa2dOYW1lKSB7XG4gICAgc3VwZXIoXG4gICAgICBgVGhlIHBhY2thZ2UgXCIke3BrZ05hbWV9XCIgaXMgcmVxdWlyZWQuIE1ha2Ugc3VyZSBpdCBpcyBpbnN0YWxsZWQuYCxcbiAgICAgIHBrZ05hbWVcbiAgICApO1xuICB9XG59XG5cbmV4cG9ydCB7IEJldHRlckF1dGhFcnJvciBhcyBCLCBNaXNzaW5nRGVwZW5kZW5jeUVycm9yIGFzIE0gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ getBaseURL),\n/* harmony export */   b: () => (/* binding */ getHost),\n/* harmony export */   c: () => (/* binding */ getProtocol),\n/* harmony export */   g: () => (/* binding */ getOrigin)\n/* harmony export */ });\n/* harmony import */ var _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./better-auth.8zoxzg-F.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\");\n/* harmony import */ var _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./better-auth.DdzSJf-n.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\");\n\n\n\nfunction checkHasPath(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.pathname !== \"/\";\n  } catch (error) {\n    throw new _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__.B(\n      `Invalid base URL: ${url}. Please provide a valid base URL.`\n    );\n  }\n}\nfunction withPath(url, path = \"/api/auth\") {\n  const hasPath = checkHasPath(url);\n  if (hasPath) {\n    return url;\n  }\n  path = path.startsWith(\"/\") ? path : `/${path}`;\n  return `${url.replace(/\\/+$/, \"\")}${path}`;\n}\nfunction getBaseURL(url, path, request) {\n  if (url) {\n    return withPath(url, path);\n  }\n  const fromEnv = _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NEXT_PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NUXT_PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NUXT_PUBLIC_AUTH_URL || (_better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BASE_URL !== \"/\" ? _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BASE_URL : void 0);\n  if (fromEnv) {\n    return withPath(fromEnv, path);\n  }\n  const fromRequest = request?.headers.get(\"x-forwarded-host\");\n  const fromRequestProto = request?.headers.get(\"x-forwarded-proto\");\n  if (fromRequest && fromRequestProto) {\n    return withPath(`${fromRequestProto}://${fromRequest}`, path);\n  }\n  if (request) {\n    const url2 = getOrigin(request.url);\n    if (!url2) {\n      throw new _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__.B(\n        \"Could not get origin from request. Please provide a valid base URL.\"\n      );\n    }\n    return withPath(url2, path);\n  }\n  if (typeof window !== \"undefined\" && window.location) {\n    return withPath(window.location.origin, path);\n  }\n  return void 0;\n}\nfunction getOrigin(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.origin;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getProtocol(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.protocol;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getHost(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.host;\n  } catch (error) {\n    return url;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: () => (/* binding */ parseJSON)\n/* harmony export */ });\nconst PROTO_POLLUTION_PATTERNS = {\n  proto: /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/,\n  constructor: /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/,\n  protoShort: /\"__proto__\"\\s*:/,\n  constructorShort: /\"constructor\"\\s*:/\n};\nconst JSON_SIGNATURE = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nconst SPECIAL_VALUES = {\n  true: true,\n  false: false,\n  null: null,\n  undefined: void 0,\n  nan: Number.NaN,\n  infinity: Number.POSITIVE_INFINITY,\n  \"-infinity\": Number.NEGATIVE_INFINITY\n};\nconst ISO_DATE_REGEX = /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{1,7}))?(?:Z|([+-])(\\d{2}):(\\d{2}))$/;\nfunction isValidDate(date) {\n  return date instanceof Date && !isNaN(date.getTime());\n}\nfunction parseISODate(value) {\n  const match = ISO_DATE_REGEX.exec(value);\n  if (!match) return null;\n  const [\n    ,\n    year,\n    month,\n    day,\n    hour,\n    minute,\n    second,\n    ms,\n    offsetSign,\n    offsetHour,\n    offsetMinute\n  ] = match;\n  let date = new Date(\n    Date.UTC(\n      parseInt(year, 10),\n      parseInt(month, 10) - 1,\n      parseInt(day, 10),\n      parseInt(hour, 10),\n      parseInt(minute, 10),\n      parseInt(second, 10),\n      ms ? parseInt(ms.padEnd(3, \"0\"), 10) : 0\n    )\n  );\n  if (offsetSign) {\n    const offset = (parseInt(offsetHour, 10) * 60 + parseInt(offsetMinute, 10)) * (offsetSign === \"+\" ? -1 : 1);\n    date.setUTCMinutes(date.getUTCMinutes() + offset);\n  }\n  return isValidDate(date) ? date : null;\n}\nfunction betterJSONParse(value, options = {}) {\n  const {\n    strict = false,\n    warnings = false,\n    reviver,\n    parseDates = true\n  } = options;\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  const trimmed = value.trim();\n  if (trimmed[0] === '\"' && trimmed.endsWith('\"') && !trimmed.slice(1, -1).includes('\"')) {\n    return trimmed.slice(1, -1);\n  }\n  const lowerValue = trimmed.toLowerCase();\n  if (lowerValue.length <= 9 && lowerValue in SPECIAL_VALUES) {\n    return SPECIAL_VALUES[lowerValue];\n  }\n  if (!JSON_SIGNATURE.test(trimmed)) {\n    if (strict) {\n      throw new SyntaxError(\"[better-json] Invalid JSON\");\n    }\n    return value;\n  }\n  const hasProtoPattern = Object.entries(PROTO_POLLUTION_PATTERNS).some(\n    ([key, pattern]) => {\n      const matches = pattern.test(trimmed);\n      if (matches && warnings) {\n        console.warn(\n          `[better-json] Detected potential prototype pollution attempt using ${key} pattern`\n        );\n      }\n      return matches;\n    }\n  );\n  if (hasProtoPattern && strict) {\n    throw new Error(\n      \"[better-json] Potential prototype pollution attempt detected\"\n    );\n  }\n  try {\n    const secureReviver = (key, value2) => {\n      if (key === \"__proto__\" || key === \"constructor\" && value2 && typeof value2 === \"object\" && \"prototype\" in value2) {\n        if (warnings) {\n          console.warn(\n            `[better-json] Dropping \"${key}\" key to prevent prototype pollution`\n          );\n        }\n        return void 0;\n      }\n      if (parseDates && typeof value2 === \"string\") {\n        const date = parseISODate(value2);\n        if (date) {\n          return date;\n        }\n      }\n      return reviver ? reviver(key, value2) : value2;\n    };\n    return JSON.parse(trimmed, secureReviver);\n  } catch (error) {\n    if (strict) {\n      throw error;\n    }\n    return value;\n  }\n}\nfunction parseJSON(value, options = { strict: true }) {\n  return betterJSONParse(value, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\n");

/***/ })

};
;