"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+anthropic@1.2.12_zod@3.25.76";
exports.ids = ["vendor-chunks/@ai-sdk+anthropic@1.2.12_zod@3.25.76"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@ai-sdk+anthropic@1.2.12_zod@3.25.76/node_modules/@ai-sdk/anthropic/dist/index.mjs":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@ai-sdk+anthropic@1.2.12_zod@3.25.76/node_modules/@ai-sdk/anthropic/dist/index.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   anthropic: () => (/* binding */ anthropic),\n/* harmony export */   createAnthropic: () => (/* binding */ createAnthropic)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/../../node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/types.js\");\n// src/anthropic-provider.ts\n\n\n\n// src/anthropic-messages-language-model.ts\n\n\n\n\n// src/anthropic-error.ts\n\n\nvar anthropicErrorDataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"error\"),\n  error: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n  })\n});\nvar anthropicFailedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonErrorResponseHandler)({\n  errorSchema: anthropicErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/anthropic-prepare-tools.ts\n\nfunction prepareTools(mode) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  const betas = /* @__PURE__ */ new Set();\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings, betas };\n  }\n  const anthropicTools2 = [];\n  for (const tool of tools) {\n    switch (tool.type) {\n      case \"function\":\n        anthropicTools2.push({\n          name: tool.name,\n          description: tool.description,\n          input_schema: tool.parameters\n        });\n        break;\n      case \"provider-defined\":\n        switch (tool.id) {\n          case \"anthropic.computer_20250124\":\n            betas.add(\"computer-use-2025-01-24\");\n            anthropicTools2.push({\n              name: tool.name,\n              type: \"computer_20250124\",\n              display_width_px: tool.args.displayWidthPx,\n              display_height_px: tool.args.displayHeightPx,\n              display_number: tool.args.displayNumber\n            });\n            break;\n          case \"anthropic.computer_20241022\":\n            betas.add(\"computer-use-2024-10-22\");\n            anthropicTools2.push({\n              name: tool.name,\n              type: \"computer_20241022\",\n              display_width_px: tool.args.displayWidthPx,\n              display_height_px: tool.args.displayHeightPx,\n              display_number: tool.args.displayNumber\n            });\n            break;\n          case \"anthropic.text_editor_20250124\":\n            betas.add(\"computer-use-2025-01-24\");\n            anthropicTools2.push({\n              name: tool.name,\n              type: \"text_editor_20250124\"\n            });\n            break;\n          case \"anthropic.text_editor_20241022\":\n            betas.add(\"computer-use-2024-10-22\");\n            anthropicTools2.push({\n              name: tool.name,\n              type: \"text_editor_20241022\"\n            });\n            break;\n          case \"anthropic.bash_20250124\":\n            betas.add(\"computer-use-2025-01-24\");\n            anthropicTools2.push({\n              name: tool.name,\n              type: \"bash_20250124\"\n            });\n            break;\n          case \"anthropic.bash_20241022\":\n            betas.add(\"computer-use-2024-10-22\");\n            anthropicTools2.push({\n              name: tool.name,\n              type: \"bash_20241022\"\n            });\n            break;\n          default:\n            toolWarnings.push({ type: \"unsupported-tool\", tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: \"unsupported-tool\", tool });\n        break;\n    }\n  }\n  const toolChoice = mode.toolChoice;\n  if (toolChoice == null) {\n    return {\n      tools: anthropicTools2,\n      tool_choice: void 0,\n      toolWarnings,\n      betas\n    };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n      return {\n        tools: anthropicTools2,\n        tool_choice: { type: \"auto\" },\n        toolWarnings,\n        betas\n      };\n    case \"required\":\n      return {\n        tools: anthropicTools2,\n        tool_choice: { type: \"any\" },\n        toolWarnings,\n        betas\n      };\n    case \"none\":\n      return { tools: void 0, tool_choice: void 0, toolWarnings, betas };\n    case \"tool\":\n      return {\n        tools: anthropicTools2,\n        tool_choice: { type: \"tool\", name: toolChoice.toolName },\n        toolWarnings,\n        betas\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/convert-to-anthropic-messages-prompt.ts\n\n\nfunction convertToAnthropicMessagesPrompt({\n  prompt,\n  sendReasoning,\n  warnings\n}) {\n  var _a, _b, _c, _d;\n  const betas = /* @__PURE__ */ new Set();\n  const blocks = groupIntoBlocks(prompt);\n  let system = void 0;\n  const messages = [];\n  function getCacheControl(providerMetadata) {\n    var _a2;\n    const anthropic2 = providerMetadata == null ? void 0 : providerMetadata.anthropic;\n    const cacheControlValue = (_a2 = anthropic2 == null ? void 0 : anthropic2.cacheControl) != null ? _a2 : anthropic2 == null ? void 0 : anthropic2.cache_control;\n    return cacheControlValue;\n  }\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    const isLastBlock = i === blocks.length - 1;\n    const type = block.type;\n    switch (type) {\n      case \"system\": {\n        if (system != null) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n            functionality: \"Multiple system messages that are separated by user/assistant messages\"\n          });\n        }\n        system = block.messages.map(({ content, providerMetadata }) => ({\n          type: \"text\",\n          text: content,\n          cache_control: getCacheControl(providerMetadata)\n        }));\n        break;\n      }\n      case \"user\": {\n        const anthropicContent = [];\n        for (const message of block.messages) {\n          const { role, content } = message;\n          switch (role) {\n            case \"user\": {\n              for (let j = 0; j < content.length; j++) {\n                const part = content[j];\n                const isLastPart = j === content.length - 1;\n                const cacheControl = (_a = getCacheControl(part.providerMetadata)) != null ? _a : isLastPart ? getCacheControl(message.providerMetadata) : void 0;\n                switch (part.type) {\n                  case \"text\": {\n                    anthropicContent.push({\n                      type: \"text\",\n                      text: part.text,\n                      cache_control: cacheControl\n                    });\n                    break;\n                  }\n                  case \"image\": {\n                    anthropicContent.push({\n                      type: \"image\",\n                      source: part.image instanceof URL ? {\n                        type: \"url\",\n                        url: part.image.toString()\n                      } : {\n                        type: \"base64\",\n                        media_type: (_b = part.mimeType) != null ? _b : \"image/jpeg\",\n                        data: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.convertUint8ArrayToBase64)(part.image)\n                      },\n                      cache_control: cacheControl\n                    });\n                    break;\n                  }\n                  case \"file\": {\n                    if (part.mimeType !== \"application/pdf\") {\n                      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n                        functionality: \"Non-PDF files in user messages\"\n                      });\n                    }\n                    betas.add(\"pdfs-2024-09-25\");\n                    anthropicContent.push({\n                      type: \"document\",\n                      source: part.data instanceof URL ? {\n                        type: \"url\",\n                        url: part.data.toString()\n                      } : {\n                        type: \"base64\",\n                        media_type: \"application/pdf\",\n                        data: part.data\n                      },\n                      cache_control: cacheControl\n                    });\n                    break;\n                  }\n                }\n              }\n              break;\n            }\n            case \"tool\": {\n              for (let i2 = 0; i2 < content.length; i2++) {\n                const part = content[i2];\n                const isLastPart = i2 === content.length - 1;\n                const cacheControl = (_c = getCacheControl(part.providerMetadata)) != null ? _c : isLastPart ? getCacheControl(message.providerMetadata) : void 0;\n                const toolResultContent = part.content != null ? part.content.map((part2) => {\n                  var _a2;\n                  switch (part2.type) {\n                    case \"text\":\n                      return {\n                        type: \"text\",\n                        text: part2.text,\n                        cache_control: void 0\n                      };\n                    case \"image\":\n                      return {\n                        type: \"image\",\n                        source: {\n                          type: \"base64\",\n                          media_type: (_a2 = part2.mimeType) != null ? _a2 : \"image/jpeg\",\n                          data: part2.data\n                        },\n                        cache_control: void 0\n                      };\n                  }\n                }) : JSON.stringify(part.result);\n                anthropicContent.push({\n                  type: \"tool_result\",\n                  tool_use_id: part.toolCallId,\n                  content: toolResultContent,\n                  is_error: part.isError,\n                  cache_control: cacheControl\n                });\n              }\n              break;\n            }\n            default: {\n              const _exhaustiveCheck = role;\n              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n        messages.push({ role: \"user\", content: anthropicContent });\n        break;\n      }\n      case \"assistant\": {\n        const anthropicContent = [];\n        for (let j = 0; j < block.messages.length; j++) {\n          const message = block.messages[j];\n          const isLastMessage = j === block.messages.length - 1;\n          const { content } = message;\n          for (let k = 0; k < content.length; k++) {\n            const part = content[k];\n            const isLastContentPart = k === content.length - 1;\n            const cacheControl = (_d = getCacheControl(part.providerMetadata)) != null ? _d : isLastContentPart ? getCacheControl(message.providerMetadata) : void 0;\n            switch (part.type) {\n              case \"text\": {\n                anthropicContent.push({\n                  type: \"text\",\n                  text: (\n                    // trim the last text part if it's the last message in the block\n                    // because Anthropic does not allow trailing whitespace\n                    // in pre-filled assistant responses\n                    isLastBlock && isLastMessage && isLastContentPart ? part.text.trim() : part.text\n                  ),\n                  cache_control: cacheControl\n                });\n                break;\n              }\n              case \"reasoning\": {\n                if (sendReasoning) {\n                  anthropicContent.push({\n                    type: \"thinking\",\n                    thinking: part.text,\n                    signature: part.signature,\n                    cache_control: cacheControl\n                  });\n                } else {\n                  warnings.push({\n                    type: \"other\",\n                    message: \"sending reasoning content is disabled for this model\"\n                  });\n                }\n                break;\n              }\n              case \"redacted-reasoning\": {\n                anthropicContent.push({\n                  type: \"redacted_thinking\",\n                  data: part.data,\n                  cache_control: cacheControl\n                });\n                break;\n              }\n              case \"tool-call\": {\n                anthropicContent.push({\n                  type: \"tool_use\",\n                  id: part.toolCallId,\n                  name: part.toolName,\n                  input: part.args,\n                  cache_control: cacheControl\n                });\n                break;\n              }\n            }\n          }\n        }\n        messages.push({ role: \"assistant\", content: anthropicContent });\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return {\n    prompt: { system, messages },\n    betas\n  };\n}\nfunction groupIntoBlocks(prompt) {\n  const blocks = [];\n  let currentBlock = void 0;\n  for (const message of prompt) {\n    const { role } = message;\n    switch (role) {\n      case \"system\": {\n        if ((currentBlock == null ? void 0 : currentBlock.type) !== \"system\") {\n          currentBlock = { type: \"system\", messages: [] };\n          blocks.push(currentBlock);\n        }\n        currentBlock.messages.push(message);\n        break;\n      }\n      case \"assistant\": {\n        if ((currentBlock == null ? void 0 : currentBlock.type) !== \"assistant\") {\n          currentBlock = { type: \"assistant\", messages: [] };\n          blocks.push(currentBlock);\n        }\n        currentBlock.messages.push(message);\n        break;\n      }\n      case \"user\": {\n        if ((currentBlock == null ? void 0 : currentBlock.type) !== \"user\") {\n          currentBlock = { type: \"user\", messages: [] };\n          blocks.push(currentBlock);\n        }\n        currentBlock.messages.push(message);\n        break;\n      }\n      case \"tool\": {\n        if ((currentBlock == null ? void 0 : currentBlock.type) !== \"user\") {\n          currentBlock = { type: \"user\", messages: [] };\n          blocks.push(currentBlock);\n        }\n        currentBlock.messages.push(message);\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return blocks;\n}\n\n// src/map-anthropic-stop-reason.ts\nfunction mapAnthropicStopReason(finishReason) {\n  switch (finishReason) {\n    case \"end_turn\":\n    case \"stop_sequence\":\n      return \"stop\";\n    case \"tool_use\":\n      return \"tool-calls\";\n    case \"max_tokens\":\n      return \"length\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/anthropic-messages-language-model.ts\nvar AnthropicMessagesLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = \"tool\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  supportsUrl(url) {\n    return url.protocol === \"https:\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get supportsImageUrls() {\n    return this.config.supportsImageUrls;\n  }\n  async getArgs({\n    mode,\n    prompt,\n    maxTokens = 4096,\n    // 4096: max model output tokens TODO update default in v5\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata: providerOptions\n  }) {\n    var _a, _b, _c;\n    const type = mode.type;\n    const warnings = [];\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"frequencyPenalty\"\n      });\n    }\n    if (presencePenalty != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"presencePenalty\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"seed\"\n      });\n    }\n    if (responseFormat != null && responseFormat.type !== \"text\") {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format is not supported.\"\n      });\n    }\n    const { prompt: messagesPrompt, betas: messagesBetas } = convertToAnthropicMessagesPrompt({\n      prompt,\n      sendReasoning: (_a = this.settings.sendReasoning) != null ? _a : true,\n      warnings\n    });\n    const anthropicOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.parseProviderOptions)({\n      provider: \"anthropic\",\n      providerOptions,\n      schema: anthropicProviderOptionsSchema\n    });\n    const isThinking = ((_b = anthropicOptions == null ? void 0 : anthropicOptions.thinking) == null ? void 0 : _b.type) === \"enabled\";\n    const thinkingBudget = (_c = anthropicOptions == null ? void 0 : anthropicOptions.thinking) == null ? void 0 : _c.budgetTokens;\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_k: topK,\n      top_p: topP,\n      stop_sequences: stopSequences,\n      // provider specific settings:\n      ...isThinking && {\n        thinking: { type: \"enabled\", budget_tokens: thinkingBudget }\n      },\n      // prompt:\n      system: messagesPrompt.system,\n      messages: messagesPrompt.messages\n    };\n    if (isThinking) {\n      if (thinkingBudget == null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n          functionality: \"thinking requires a budget\"\n        });\n      }\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported when thinking is enabled\"\n        });\n      }\n      if (topK != null) {\n        baseArgs.top_k = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topK\",\n          details: \"topK is not supported when thinking is enabled\"\n        });\n      }\n      if (topP != null) {\n        baseArgs.top_p = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topP\",\n          details: \"topP is not supported when thinking is enabled\"\n        });\n      }\n      baseArgs.max_tokens = maxTokens + thinkingBudget;\n    }\n    switch (type) {\n      case \"regular\": {\n        const {\n          tools,\n          tool_choice,\n          toolWarnings,\n          betas: toolsBetas\n        } = prepareTools(mode);\n        return {\n          args: { ...baseArgs, tools, tool_choice },\n          warnings: [...warnings, ...toolWarnings],\n          betas: /* @__PURE__ */ new Set([...messagesBetas, ...toolsBetas])\n        };\n      }\n      case \"object-json\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n          functionality: \"json-mode object generation\"\n        });\n      }\n      case \"object-tool\": {\n        const { name, description, parameters } = mode.tool;\n        return {\n          args: {\n            ...baseArgs,\n            tools: [{ name, description, input_schema: parameters }],\n            tool_choice: { type: \"tool\", name }\n          },\n          warnings,\n          betas: messagesBetas\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async getHeaders({\n    betas,\n    headers\n  }) {\n    return (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(\n      await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.resolve)(this.config.headers),\n      betas.size > 0 ? { \"anthropic-beta\": Array.from(betas).join(\",\") } : {},\n      headers\n    );\n  }\n  buildRequestUrl(isStreaming) {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = this.config).buildRequestUrl) == null ? void 0 : _b.call(_a, this.config.baseURL, isStreaming)) != null ? _c : `${this.config.baseURL}/messages`;\n  }\n  transformRequestBody(args) {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = this.config).transformRequestBody) == null ? void 0 : _b.call(_a, args)) != null ? _c : args;\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d;\n    const { args, warnings, betas } = await this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: this.buildRequestUrl(false),\n      headers: await this.getHeaders({ betas, headers: options.headers }),\n      body: this.transformRequestBody(args),\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonResponseHandler)(\n        anthropicMessagesResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    let text = \"\";\n    for (const content of response.content) {\n      if (content.type === \"text\") {\n        text += content.text;\n      }\n    }\n    let toolCalls = void 0;\n    if (response.content.some((content) => content.type === \"tool_use\")) {\n      toolCalls = [];\n      for (const content of response.content) {\n        if (content.type === \"tool_use\") {\n          toolCalls.push({\n            toolCallType: \"function\",\n            toolCallId: content.id,\n            toolName: content.name,\n            args: JSON.stringify(content.input)\n          });\n        }\n      }\n    }\n    const reasoning = response.content.filter(\n      (content) => content.type === \"redacted_thinking\" || content.type === \"thinking\"\n    ).map(\n      (content) => content.type === \"thinking\" ? {\n        type: \"text\",\n        text: content.thinking,\n        signature: content.signature\n      } : {\n        type: \"redacted\",\n        data: content.data\n      }\n    );\n    return {\n      text,\n      reasoning: reasoning.length > 0 ? reasoning : void 0,\n      toolCalls,\n      finishReason: mapAnthropicStopReason(response.stop_reason),\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse\n      },\n      response: {\n        id: (_a = response.id) != null ? _a : void 0,\n        modelId: (_b = response.model) != null ? _b : void 0\n      },\n      warnings,\n      providerMetadata: {\n        anthropic: {\n          cacheCreationInputTokens: (_c = response.usage.cache_creation_input_tokens) != null ? _c : null,\n          cacheReadInputTokens: (_d = response.usage.cache_read_input_tokens) != null ? _d : null\n        }\n      },\n      request: { body: JSON.stringify(args) }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings, betas } = await this.getArgs(options);\n    const body = { ...args, stream: true };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: this.buildRequestUrl(true),\n      headers: await this.getHeaders({ betas, headers: options.headers }),\n      body: this.transformRequestBody(body),\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createEventSourceResponseHandler)(\n        anthropicMessagesChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    const usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    const toolCallContentBlocks = {};\n    let providerMetadata = void 0;\n    let blockType = void 0;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d;\n            if (!chunk.success) {\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            switch (value.type) {\n              case \"ping\": {\n                return;\n              }\n              case \"content_block_start\": {\n                const contentBlockType = value.content_block.type;\n                blockType = contentBlockType;\n                switch (contentBlockType) {\n                  case \"text\":\n                  case \"thinking\": {\n                    return;\n                  }\n                  case \"redacted_thinking\": {\n                    controller.enqueue({\n                      type: \"redacted-reasoning\",\n                      data: value.content_block.data\n                    });\n                    return;\n                  }\n                  case \"tool_use\": {\n                    toolCallContentBlocks[value.index] = {\n                      toolCallId: value.content_block.id,\n                      toolName: value.content_block.name,\n                      jsonText: \"\"\n                    };\n                    return;\n                  }\n                  default: {\n                    const _exhaustiveCheck = contentBlockType;\n                    throw new Error(\n                      `Unsupported content block type: ${_exhaustiveCheck}`\n                    );\n                  }\n                }\n              }\n              case \"content_block_stop\": {\n                if (toolCallContentBlocks[value.index] != null) {\n                  const contentBlock = toolCallContentBlocks[value.index];\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: contentBlock.toolCallId,\n                    toolName: contentBlock.toolName,\n                    args: contentBlock.jsonText\n                  });\n                  delete toolCallContentBlocks[value.index];\n                }\n                blockType = void 0;\n                return;\n              }\n              case \"content_block_delta\": {\n                const deltaType = value.delta.type;\n                switch (deltaType) {\n                  case \"text_delta\": {\n                    controller.enqueue({\n                      type: \"text-delta\",\n                      textDelta: value.delta.text\n                    });\n                    return;\n                  }\n                  case \"thinking_delta\": {\n                    controller.enqueue({\n                      type: \"reasoning\",\n                      textDelta: value.delta.thinking\n                    });\n                    return;\n                  }\n                  case \"signature_delta\": {\n                    if (blockType === \"thinking\") {\n                      controller.enqueue({\n                        type: \"reasoning-signature\",\n                        signature: value.delta.signature\n                      });\n                    }\n                    return;\n                  }\n                  case \"input_json_delta\": {\n                    const contentBlock = toolCallContentBlocks[value.index];\n                    controller.enqueue({\n                      type: \"tool-call-delta\",\n                      toolCallType: \"function\",\n                      toolCallId: contentBlock.toolCallId,\n                      toolName: contentBlock.toolName,\n                      argsTextDelta: value.delta.partial_json\n                    });\n                    contentBlock.jsonText += value.delta.partial_json;\n                    return;\n                  }\n                  default: {\n                    const _exhaustiveCheck = deltaType;\n                    throw new Error(\n                      `Unsupported delta type: ${_exhaustiveCheck}`\n                    );\n                  }\n                }\n              }\n              case \"message_start\": {\n                usage.promptTokens = value.message.usage.input_tokens;\n                usage.completionTokens = value.message.usage.output_tokens;\n                providerMetadata = {\n                  anthropic: {\n                    cacheCreationInputTokens: (_a = value.message.usage.cache_creation_input_tokens) != null ? _a : null,\n                    cacheReadInputTokens: (_b = value.message.usage.cache_read_input_tokens) != null ? _b : null\n                  }\n                };\n                controller.enqueue({\n                  type: \"response-metadata\",\n                  id: (_c = value.message.id) != null ? _c : void 0,\n                  modelId: (_d = value.message.model) != null ? _d : void 0\n                });\n                return;\n              }\n              case \"message_delta\": {\n                usage.completionTokens = value.usage.output_tokens;\n                finishReason = mapAnthropicStopReason(value.delta.stop_reason);\n                return;\n              }\n              case \"message_stop\": {\n                controller.enqueue({\n                  type: \"finish\",\n                  finishReason,\n                  usage,\n                  providerMetadata\n                });\n                return;\n              }\n              case \"error\": {\n                controller.enqueue({ type: \"error\", error: value.error });\n                return;\n              }\n              default: {\n                const _exhaustiveCheck = value;\n                throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);\n              }\n            }\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) }\n    };\n  }\n};\nvar anthropicMessagesResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"message\"),\n  id: zod__WEBPACK_IMPORTED_MODULE_0__.string().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_0__.string().nullish(),\n  content: zod__WEBPACK_IMPORTED_MODULE_0__.array(\n    zod__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion(\"type\", [\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"text\"),\n        text: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"thinking\"),\n        thinking: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        signature: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"redacted_thinking\"),\n        data: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"tool_use\"),\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        input: zod__WEBPACK_IMPORTED_MODULE_0__.unknown()\n      })\n    ])\n  ),\n  stop_reason: zod__WEBPACK_IMPORTED_MODULE_0__.string().nullish(),\n  usage: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    input_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n    output_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n    cache_creation_input_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().nullish(),\n    cache_read_input_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().nullish()\n  })\n});\nvar anthropicMessagesChunkSchema = zod__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion(\"type\", [\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"message_start\"),\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n      id: zod__WEBPACK_IMPORTED_MODULE_0__.string().nullish(),\n      model: zod__WEBPACK_IMPORTED_MODULE_0__.string().nullish(),\n      usage: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        input_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n        output_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n        cache_creation_input_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().nullish(),\n        cache_read_input_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().nullish()\n      })\n    })\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"content_block_start\"),\n    index: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n    content_block: zod__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion(\"type\", [\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"text\"),\n        text: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"thinking\"),\n        thinking: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"tool_use\"),\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"redacted_thinking\"),\n        data: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      })\n    ])\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"content_block_delta\"),\n    index: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n    delta: zod__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion(\"type\", [\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"input_json_delta\"),\n        partial_json: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"text_delta\"),\n        text: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"thinking_delta\"),\n        thinking: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"signature_delta\"),\n        signature: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n      })\n    ])\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"content_block_stop\"),\n    index: zod__WEBPACK_IMPORTED_MODULE_0__.number()\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"error\"),\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n      type: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n      message: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    })\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"message_delta\"),\n    delta: zod__WEBPACK_IMPORTED_MODULE_0__.object({ stop_reason: zod__WEBPACK_IMPORTED_MODULE_0__.string().nullish() }),\n    usage: zod__WEBPACK_IMPORTED_MODULE_0__.object({ output_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.number() })\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"message_stop\")\n  }),\n  zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"ping\")\n  })\n]);\nvar anthropicProviderOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  thinking: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.union([zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"enabled\"), zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"disabled\")]),\n    budgetTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().optional()\n  }).optional()\n});\n\n// src/anthropic-tools.ts\n\nvar Bash20241022Parameters = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  command: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n  restart: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().optional()\n});\nfunction bashTool_20241022(options = {}) {\n  return {\n    type: \"provider-defined\",\n    id: \"anthropic.bash_20241022\",\n    args: {},\n    parameters: Bash20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent\n  };\n}\nvar Bash20250124Parameters = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  command: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n  restart: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().optional()\n});\nfunction bashTool_20250124(options = {}) {\n  return {\n    type: \"provider-defined\",\n    id: \"anthropic.bash_20250124\",\n    args: {},\n    parameters: Bash20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent\n  };\n}\nvar TextEditor20241022Parameters = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  command: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"view\", \"create\", \"str_replace\", \"insert\", \"undo_edit\"]),\n  path: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n  file_text: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n  insert_line: zod__WEBPACK_IMPORTED_MODULE_0__.number().int().optional(),\n  new_str: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n  old_str: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n  view_range: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int()).optional()\n});\nfunction textEditorTool_20241022(options = {}) {\n  return {\n    type: \"provider-defined\",\n    id: \"anthropic.text_editor_20241022\",\n    args: {},\n    parameters: TextEditor20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent\n  };\n}\nvar TextEditor20250124Parameters = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  command: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"view\", \"create\", \"str_replace\", \"insert\", \"undo_edit\"]),\n  path: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n  file_text: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n  insert_line: zod__WEBPACK_IMPORTED_MODULE_0__.number().int().optional(),\n  new_str: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n  old_str: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n  view_range: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int()).optional()\n});\nfunction textEditorTool_20250124(options = {}) {\n  return {\n    type: \"provider-defined\",\n    id: \"anthropic.text_editor_20250124\",\n    args: {},\n    parameters: TextEditor20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent\n  };\n}\nvar Computer20241022Parameters = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  action: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n    \"key\",\n    \"type\",\n    \"mouse_move\",\n    \"left_click\",\n    \"left_click_drag\",\n    \"right_click\",\n    \"middle_click\",\n    \"double_click\",\n    \"screenshot\",\n    \"cursor_position\"\n  ]),\n  coordinate: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int()).optional(),\n  text: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nfunction computerTool_20241022(options) {\n  return {\n    type: \"provider-defined\",\n    id: \"anthropic.computer_20241022\",\n    args: {\n      displayWidthPx: options.displayWidthPx,\n      displayHeightPx: options.displayHeightPx,\n      displayNumber: options.displayNumber\n    },\n    parameters: Computer20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent\n  };\n}\nvar Computer20250124Parameters = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n  action: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n    \"key\",\n    \"hold_key\",\n    \"type\",\n    \"cursor_position\",\n    \"mouse_move\",\n    \"left_mouse_down\",\n    \"left_mouse_up\",\n    \"left_click\",\n    \"left_click_drag\",\n    \"right_click\",\n    \"middle_click\",\n    \"double_click\",\n    \"triple_click\",\n    \"scroll\",\n    \"wait\",\n    \"screenshot\"\n  ]),\n  coordinate: zod__WEBPACK_IMPORTED_MODULE_0__.tuple([zod__WEBPACK_IMPORTED_MODULE_0__.number().int(), zod__WEBPACK_IMPORTED_MODULE_0__.number().int()]).optional(),\n  duration: zod__WEBPACK_IMPORTED_MODULE_0__.number().optional(),\n  scroll_amount: zod__WEBPACK_IMPORTED_MODULE_0__.number().optional(),\n  scroll_direction: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"up\", \"down\", \"left\", \"right\"]).optional(),\n  start_coordinate: zod__WEBPACK_IMPORTED_MODULE_0__.tuple([zod__WEBPACK_IMPORTED_MODULE_0__.number().int(), zod__WEBPACK_IMPORTED_MODULE_0__.number().int()]).optional(),\n  text: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nfunction computerTool_20250124(options) {\n  return {\n    type: \"provider-defined\",\n    id: \"anthropic.computer_20250124\",\n    args: {\n      displayWidthPx: options.displayWidthPx,\n      displayHeightPx: options.displayHeightPx,\n      displayNumber: options.displayNumber\n    },\n    parameters: Computer20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent\n  };\n}\nvar anthropicTools = {\n  bash_20241022: bashTool_20241022,\n  bash_20250124: bashTool_20250124,\n  textEditor_20241022: textEditorTool_20241022,\n  textEditor_20250124: textEditorTool_20250124,\n  computer_20241022: computerTool_20241022,\n  computer_20250124: computerTool_20250124\n};\n\n// src/anthropic-provider.ts\nfunction createAnthropic(options = {}) {\n  var _a;\n  const baseURL = (_a = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.withoutTrailingSlash)(options.baseURL)) != null ? _a : \"https://api.anthropic.com/v1\";\n  const getHeaders = () => ({\n    \"anthropic-version\": \"2023-06-01\",\n    \"x-api-key\": (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"ANTHROPIC_API_KEY\",\n      description: \"Anthropic\"\n    }),\n    ...options.headers\n  });\n  const createChatModel = (modelId, settings = {}) => new AnthropicMessagesLanguageModel(modelId, settings, {\n    provider: \"anthropic.messages\",\n    baseURL,\n    headers: getHeaders,\n    fetch: options.fetch,\n    supportsImageUrls: true\n  });\n  const provider = function(modelId, settings) {\n    if (new.target) {\n      throw new Error(\n        \"The Anthropic model function cannot be called with the new keyword.\"\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.messages = createChatModel;\n  provider.textEmbeddingModel = (modelId) => {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.NoSuchModelError({ modelId, modelType: \"textEmbeddingModel\" });\n  };\n  provider.tools = anthropicTools;\n  return provider;\n}\nvar anthropic = createAnthropic();\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@ai-sdk+anthropic@1.2.12_zod@3.25.76/node_modules/@ai-sdk/anthropic/dist/index.mjs\n");

/***/ })

};
;