"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nanostores@0.11.4";
exports.ids = ["vendor-chunks/nanostores@0.11.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   atom: () => (/* binding */ atom),\n/* harmony export */   epoch: () => (/* binding */ epoch)\n/* harmony export */ });\n/* harmony import */ var _clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../clean-stores/index.js */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js\");\n\n\nlet listenerQueue = []\nlet lqIndex = 0\nconst QUEUE_ITEMS_PER_LISTENER = 4\nlet epoch = 0\n\nlet atom = (initialValue) => {\n  let listeners = []\n  let $atom = {\n    get() {\n      if (!$atom.lc) {\n        $atom.listen(() => {})()\n      }\n      return $atom.value\n    },\n    lc: 0,\n    listen(listener) {\n      $atom.lc = listeners.push(listener)\n\n      return () => {\n        for (let i = lqIndex + QUEUE_ITEMS_PER_LISTENER; i < listenerQueue.length;) {\n          if (listenerQueue[i] === listener) {\n            listenerQueue.splice(i, QUEUE_ITEMS_PER_LISTENER)\n          } else {\n            i += QUEUE_ITEMS_PER_LISTENER\n          }\n        }\n\n        let index = listeners.indexOf(listener)\n        if (~index) {\n          listeners.splice(index, 1)\n          if (!--$atom.lc) $atom.off()\n        }\n      }\n    },\n    notify(oldValue, changedKey) {\n      epoch++\n      let runListenerQueue = !listenerQueue.length\n      for (let listener of listeners) {\n        listenerQueue.push(\n          listener,\n          $atom.value,\n          oldValue,\n          changedKey\n        )\n      }\n\n      if (runListenerQueue) {\n        for (lqIndex = 0; lqIndex < listenerQueue.length; lqIndex += QUEUE_ITEMS_PER_LISTENER) {\n            listenerQueue[lqIndex](\n              listenerQueue[lqIndex + 1],\n              listenerQueue[lqIndex + 2],\n              listenerQueue[lqIndex + 3]\n            )\n        }\n        listenerQueue.length = 0\n      }\n    },\n    /* It will be called on last listener unsubscribing.\n       We will redefine it in onMount and onStop. */\n    off() {},\n    set(newValue) {\n      let oldValue = $atom.value\n      if (oldValue !== newValue) {\n        $atom.value = newValue\n        $atom.notify(oldValue)\n      }\n    },\n    subscribe(listener) {\n      let unbind = $atom.listen(listener)\n      listener($atom.value)\n      return unbind\n    },\n    value: initialValue\n  }\n\n  if (true) {\n    $atom[_clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__.clean] = () => {\n      listeners = []\n      $atom.lc = 0\n      $atom.off()\n    }\n  }\n\n  return $atom\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   cleanStores: () => (/* binding */ cleanStores)\n/* harmony export */ });\n/* harmony import */ var _task_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../task/index.js */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js\");\n\n\nlet clean = Symbol('clean')\n\nlet cleanStores = (...stores) => {\n  if (false) {}\n  (0,_task_index_js__WEBPACK_IMPORTED_MODULE_0__.cleanTasks)()\n  for (let $store of stores) {\n    if ($store) {\n      if ($store.mocked) delete $store.mocked\n      if ($store[clean]) $store[clean]()\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL2NsZWFuLXN0b3Jlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7O0FBRXRDOztBQUVBO0FBQ1AsTUFBTSxLQUFxQyxFQUFFLEVBSTFDO0FBQ0gsRUFBRSwwREFBVTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbWFkaHVrYXJrdW1hci9Ecm9wYm94L21hZGh1a2FyL3JvYnlubnYzL2NvZGUvcm9ieW5udjMvbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL2NsZWFuLXN0b3Jlcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbGVhblRhc2tzIH0gZnJvbSAnLi4vdGFzay9pbmRleC5qcydcblxuZXhwb3J0IGxldCBjbGVhbiA9IFN5bWJvbCgnY2xlYW4nKVxuXG5leHBvcnQgbGV0IGNsZWFuU3RvcmVzID0gKC4uLnN0b3JlcykgPT4ge1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdjbGVhblN0b3JlcygpIGNhbiBiZSB1c2VkIG9ubHkgZHVyaW5nIGRldmVsb3BtZW50IG9yIHRlc3RzJ1xuICAgIClcbiAgfVxuICBjbGVhblRhc2tzKClcbiAgZm9yIChsZXQgJHN0b3JlIG9mIHN0b3Jlcykge1xuICAgIGlmICgkc3RvcmUpIHtcbiAgICAgIGlmICgkc3RvcmUubW9ja2VkKSBkZWxldGUgJHN0b3JlLm1vY2tlZFxuICAgICAgaWYgKCRzdG9yZVtjbGVhbl0pICRzdG9yZVtjbGVhbl0oKVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORE_UNMOUNT_DELAY: () => (/* binding */ STORE_UNMOUNT_DELAY),\n/* harmony export */   on: () => (/* binding */ on),\n/* harmony export */   onMount: () => (/* binding */ onMount),\n/* harmony export */   onNotify: () => (/* binding */ onNotify),\n/* harmony export */   onSet: () => (/* binding */ onSet),\n/* harmony export */   onStart: () => (/* binding */ onStart),\n/* harmony export */   onStop: () => (/* binding */ onStop)\n/* harmony export */ });\n/* harmony import */ var _clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../clean-stores/index.js */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js\");\n\n\nconst START = 0\nconst STOP = 1\nconst SET = 2\nconst NOTIFY = 3\nconst MOUNT = 5\nconst UNMOUNT = 6\nconst REVERT_MUTATION = 10\n\nlet on = (object, listener, eventKey, mutateStore) => {\n  object.events = object.events || {}\n  if (!object.events[eventKey + REVERT_MUTATION]) {\n    object.events[eventKey + REVERT_MUTATION] = mutateStore(eventProps => {\n      // eslint-disable-next-line no-sequences\n      object.events[eventKey].reduceRight((event, l) => (l(event), event), {\n        shared: {},\n        ...eventProps\n      })\n    })\n  }\n  object.events[eventKey] = object.events[eventKey] || []\n  object.events[eventKey].push(listener)\n  return () => {\n    let currentListeners = object.events[eventKey]\n    let index = currentListeners.indexOf(listener)\n    currentListeners.splice(index, 1)\n    if (!currentListeners.length) {\n      delete object.events[eventKey]\n      object.events[eventKey + REVERT_MUTATION]()\n      delete object.events[eventKey + REVERT_MUTATION]\n    }\n  }\n}\n\nlet onStart = ($store, listener) =>\n  on($store, listener, START, runListeners => {\n    let originListen = $store.listen\n    $store.listen = arg => {\n      if (!$store.lc && !$store.starting) {\n        $store.starting = true\n        runListeners()\n        delete $store.starting\n      }\n      return originListen(arg)\n    }\n    return () => {\n      $store.listen = originListen\n    }\n  })\n\nlet onStop = ($store, listener) =>\n  on($store, listener, STOP, runListeners => {\n    let originOff = $store.off\n    $store.off = () => {\n      runListeners()\n      originOff()\n    }\n    return () => {\n      $store.off = originOff\n    }\n  })\n\nlet onSet = ($store, listener) =>\n  on($store, listener, SET, runListeners => {\n    let originSet = $store.set\n    let originSetKey = $store.setKey\n    if ($store.setKey) {\n      $store.setKey = (changed, changedValue) => {\n        let isAborted\n        let abort = () => {\n          isAborted = true\n        }\n\n        runListeners({\n          abort,\n          changed,\n          newValue: { ...$store.value, [changed]: changedValue }\n        })\n        if (!isAborted) return originSetKey(changed, changedValue)\n      }\n    }\n    $store.set = newValue => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, newValue })\n      if (!isAborted) return originSet(newValue)\n    }\n    return () => {\n      $store.set = originSet\n      $store.setKey = originSetKey\n    }\n  })\n\nlet onNotify = ($store, listener) =>\n  on($store, listener, NOTIFY, runListeners => {\n    let originNotify = $store.notify\n    $store.notify = (oldValue, changed) => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, changed, oldValue })\n      if (!isAborted) return originNotify(oldValue, changed)\n    }\n    return () => {\n      $store.notify = originNotify\n    }\n  })\n\nlet STORE_UNMOUNT_DELAY = 1000\n\nlet onMount = ($store, initialize) => {\n  let listener = payload => {\n    let destroy = initialize(payload)\n    if (destroy) $store.events[UNMOUNT].push(destroy)\n  }\n  return on($store, listener, MOUNT, runListeners => {\n    let originListen = $store.listen\n    $store.listen = (...args) => {\n      if (!$store.lc && !$store.active) {\n        $store.active = true\n        runListeners()\n      }\n      return originListen(...args)\n    }\n\n    let originOff = $store.off\n    $store.events[UNMOUNT] = []\n    $store.off = () => {\n      originOff()\n      setTimeout(() => {\n        if ($store.active && !$store.lc) {\n          $store.active = false\n          for (let destroy of $store.events[UNMOUNT]) destroy()\n          $store.events[UNMOUNT] = []\n        }\n      }, STORE_UNMOUNT_DELAY)\n    }\n\n    if (true) {\n      let originClean = $store[_clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__.clean]\n      $store[_clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__.clean] = () => {\n        for (let destroy of $store.events[UNMOUNT]) destroy()\n        $store.events[UNMOUNT] = []\n        $store.active = false\n        originClean()\n      }\n    }\n\n    return () => {\n      $store.listen = originListen\n      $store.off = originOff\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listenKeys: () => (/* binding */ listenKeys),\n/* harmony export */   subscribeKeys: () => (/* binding */ subscribeKeys)\n/* harmony export */ });\nfunction listenKeys($store, keys, listener) {\n  let keysSet = new Set(keys).add(undefined)\n  return $store.listen((value, oldValue, changed) => {\n    if (keysSet.has(changed)) {\n      listener(value, oldValue, changed)\n    }\n  })\n}\n\nfunction subscribeKeys($store, keys, listener) {\n  let unbind = listenKeys($store, keys, listener)\n  listener($store.value)\n  return unbind\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL2xpc3Rlbi1rZXlzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWRodWthcmt1bWFyL0Ryb3Bib3gvbWFkaHVrYXIvcm9ieW5udjMvY29kZS9yb2J5bm52My9ub2RlX21vZHVsZXMvLnBucG0vbmFub3N0b3Jlc0AwLjExLjQvbm9kZV9tb2R1bGVzL25hbm9zdG9yZXMvbGlzdGVuLWtleXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGxpc3RlbktleXMoJHN0b3JlLCBrZXlzLCBsaXN0ZW5lcikge1xuICBsZXQga2V5c1NldCA9IG5ldyBTZXQoa2V5cykuYWRkKHVuZGVmaW5lZClcbiAgcmV0dXJuICRzdG9yZS5saXN0ZW4oKHZhbHVlLCBvbGRWYWx1ZSwgY2hhbmdlZCkgPT4ge1xuICAgIGlmIChrZXlzU2V0LmhhcyhjaGFuZ2VkKSkge1xuICAgICAgbGlzdGVuZXIodmFsdWUsIG9sZFZhbHVlLCBjaGFuZ2VkKVxuICAgIH1cbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHN1YnNjcmliZUtleXMoJHN0b3JlLCBrZXlzLCBsaXN0ZW5lcikge1xuICBsZXQgdW5iaW5kID0gbGlzdGVuS2V5cygkc3RvcmUsIGtleXMsIGxpc3RlbmVyKVxuICBsaXN0ZW5lcigkc3RvcmUudmFsdWUpXG4gIHJldHVybiB1bmJpbmRcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allTasks: () => (/* binding */ allTasks),\n/* harmony export */   cleanTasks: () => (/* binding */ cleanTasks),\n/* harmony export */   startTask: () => (/* binding */ startTask),\n/* harmony export */   task: () => (/* binding */ task)\n/* harmony export */ });\nlet tasks = 0\nlet resolves = []\n\nfunction startTask() {\n  tasks += 1\n  return () => {\n    tasks -= 1\n    if (tasks === 0) {\n      let prevResolves = resolves\n      resolves = []\n      for (let i of prevResolves) i()\n    }\n  }\n}\n\nfunction task(cb) {\n  let endTask = startTask()\n  let promise = cb().finally(endTask)\n  promise.t = true\n  return promise\n}\n\nfunction allTasks() {\n  if (tasks === 0) {\n    return Promise.resolve()\n  } else {\n    return new Promise(resolve => {\n      resolves.push(resolve)\n    })\n  }\n}\n\nfunction cleanTasks() {\n  tasks = 0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL3Rhc2svaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWRodWthcmt1bWFyL0Ryb3Bib3gvbWFkaHVrYXIvcm9ieW5udjMvY29kZS9yb2J5bm52My9ub2RlX21vZHVsZXMvLnBucG0vbmFub3N0b3Jlc0AwLjExLjQvbm9kZV9tb2R1bGVzL25hbm9zdG9yZXMvdGFzay9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgdGFza3MgPSAwXG5sZXQgcmVzb2x2ZXMgPSBbXVxuXG5leHBvcnQgZnVuY3Rpb24gc3RhcnRUYXNrKCkge1xuICB0YXNrcyArPSAxXG4gIHJldHVybiAoKSA9PiB7XG4gICAgdGFza3MgLT0gMVxuICAgIGlmICh0YXNrcyA9PT0gMCkge1xuICAgICAgbGV0IHByZXZSZXNvbHZlcyA9IHJlc29sdmVzXG4gICAgICByZXNvbHZlcyA9IFtdXG4gICAgICBmb3IgKGxldCBpIG9mIHByZXZSZXNvbHZlcykgaSgpXG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB0YXNrKGNiKSB7XG4gIGxldCBlbmRUYXNrID0gc3RhcnRUYXNrKClcbiAgbGV0IHByb21pc2UgPSBjYigpLmZpbmFsbHkoZW5kVGFzaylcbiAgcHJvbWlzZS50ID0gdHJ1ZVxuICByZXR1cm4gcHJvbWlzZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gYWxsVGFza3MoKSB7XG4gIGlmICh0YXNrcyA9PT0gMCkge1xuICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKVxuICB9IGVsc2Uge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgIHJlc29sdmVzLnB1c2gocmVzb2x2ZSlcbiAgICB9KVxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjbGVhblRhc2tzKCkge1xuICB0YXNrcyA9IDBcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js\n");

/***/ })

};
;