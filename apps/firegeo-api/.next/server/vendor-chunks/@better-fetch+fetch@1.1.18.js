"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@better-fetch+fetch@1.1.18";
exports.ids = ["vendor-chunks/@better-fetch+fetch@1.1.18"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BetterFetchError: () => (/* binding */ BetterFetchError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   applySchemaPlugin: () => (/* binding */ applySchemaPlugin),\n/* harmony export */   betterFetch: () => (/* binding */ betterFetch),\n/* harmony export */   bodyParser: () => (/* binding */ bodyParser),\n/* harmony export */   createFetch: () => (/* binding */ createFetch),\n/* harmony export */   createRetryStrategy: () => (/* binding */ createRetryStrategy),\n/* harmony export */   createSchema: () => (/* binding */ createSchema),\n/* harmony export */   detectContentType: () => (/* binding */ detectContentType),\n/* harmony export */   detectResponseType: () => (/* binding */ detectResponseType),\n/* harmony export */   getBody: () => (/* binding */ getBody),\n/* harmony export */   getFetch: () => (/* binding */ getFetch),\n/* harmony export */   getHeaders: () => (/* binding */ getHeaders),\n/* harmony export */   getMethod: () => (/* binding */ getMethod),\n/* harmony export */   getTimeout: () => (/* binding */ getTimeout),\n/* harmony export */   getURL: () => (/* binding */ getURL),\n/* harmony export */   initializePlugins: () => (/* binding */ initializePlugins),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isJSONParsable: () => (/* binding */ isJSONParsable),\n/* harmony export */   isJSONSerializable: () => (/* binding */ isJSONSerializable),\n/* harmony export */   isPayloadMethod: () => (/* binding */ isPayloadMethod),\n/* harmony export */   isRouteMethod: () => (/* binding */ isRouteMethod),\n/* harmony export */   jsonParse: () => (/* binding */ jsonParse),\n/* harmony export */   methods: () => (/* binding */ methods),\n/* harmony export */   parseStandardSchema: () => (/* binding */ parseStandardSchema)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\n\n// src/error.ts\nvar BetterFetchError = class extends Error {\n  constructor(status, statusText, error) {\n    super(statusText || status.toString(), {\n      cause: error\n    });\n    this.status = status;\n    this.statusText = statusText;\n    this.error = error;\n  }\n};\n\n// src/plugins.ts\nvar initializePlugins = async (url, options) => {\n  var _a, _b, _c, _d, _e, _f;\n  let opts = options || {};\n  const hooks = {\n    onRequest: [options == null ? void 0 : options.onRequest],\n    onResponse: [options == null ? void 0 : options.onResponse],\n    onSuccess: [options == null ? void 0 : options.onSuccess],\n    onError: [options == null ? void 0 : options.onError],\n    onRetry: [options == null ? void 0 : options.onRetry]\n  };\n  if (!options || !(options == null ? void 0 : options.plugins)) {\n    return {\n      url,\n      options: opts,\n      hooks\n    };\n  }\n  for (const plugin of (options == null ? void 0 : options.plugins) || []) {\n    if (plugin.init) {\n      const pluginRes = await ((_a = plugin.init) == null ? void 0 : _a.call(plugin, url.toString(), options));\n      opts = pluginRes.options || opts;\n      url = pluginRes.url;\n    }\n    hooks.onRequest.push((_b = plugin.hooks) == null ? void 0 : _b.onRequest);\n    hooks.onResponse.push((_c = plugin.hooks) == null ? void 0 : _c.onResponse);\n    hooks.onSuccess.push((_d = plugin.hooks) == null ? void 0 : _d.onSuccess);\n    hooks.onError.push((_e = plugin.hooks) == null ? void 0 : _e.onError);\n    hooks.onRetry.push((_f = plugin.hooks) == null ? void 0 : _f.onRetry);\n  }\n  return {\n    url,\n    options: opts,\n    hooks\n  };\n};\n\n// src/retry.ts\nvar LinearRetryStrategy = class {\n  constructor(options) {\n    this.options = options;\n  }\n  shouldAttemptRetry(attempt, response) {\n    if (this.options.shouldRetry) {\n      return Promise.resolve(\n        attempt < this.options.attempts && this.options.shouldRetry(response)\n      );\n    }\n    return Promise.resolve(attempt < this.options.attempts);\n  }\n  getDelay() {\n    return this.options.delay;\n  }\n};\nvar ExponentialRetryStrategy = class {\n  constructor(options) {\n    this.options = options;\n  }\n  shouldAttemptRetry(attempt, response) {\n    if (this.options.shouldRetry) {\n      return Promise.resolve(\n        attempt < this.options.attempts && this.options.shouldRetry(response)\n      );\n    }\n    return Promise.resolve(attempt < this.options.attempts);\n  }\n  getDelay(attempt) {\n    const delay = Math.min(\n      this.options.maxDelay,\n      this.options.baseDelay * 2 ** attempt\n    );\n    return delay;\n  }\n};\nfunction createRetryStrategy(options) {\n  if (typeof options === \"number\") {\n    return new LinearRetryStrategy({\n      type: \"linear\",\n      attempts: options,\n      delay: 1e3\n    });\n  }\n  switch (options.type) {\n    case \"linear\":\n      return new LinearRetryStrategy(options);\n    case \"exponential\":\n      return new ExponentialRetryStrategy(options);\n    default:\n      throw new Error(\"Invalid retry strategy\");\n  }\n}\n\n// src/auth.ts\nvar getAuthHeader = async (options) => {\n  const headers = {};\n  const getValue = async (value) => typeof value === \"function\" ? await value() : value;\n  if (options == null ? void 0 : options.auth) {\n    if (options.auth.type === \"Bearer\") {\n      const token = await getValue(options.auth.token);\n      if (!token) {\n        return headers;\n      }\n      headers[\"authorization\"] = `Bearer ${token}`;\n    } else if (options.auth.type === \"Basic\") {\n      const username = getValue(options.auth.username);\n      const password = getValue(options.auth.password);\n      if (!username || !password) {\n        return headers;\n      }\n      headers[\"authorization\"] = `Basic ${btoa(`${username}:${password}`)}`;\n    } else if (options.auth.type === \"Custom\") {\n      const value = getValue(options.auth.value);\n      if (!value) {\n        return headers;\n      }\n      headers[\"authorization\"] = `${getValue(options.auth.prefix)} ${value}`;\n    }\n  }\n  return headers;\n};\n\n// src/utils.ts\nvar JSON_RE = /^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;\nfunction detectResponseType(request) {\n  const _contentType = request.headers.get(\"content-type\");\n  const textTypes = /* @__PURE__ */ new Set([\n    \"image/svg\",\n    \"application/xml\",\n    \"application/xhtml\",\n    \"application/html\"\n  ]);\n  if (!_contentType) {\n    return \"json\";\n  }\n  const contentType = _contentType.split(\";\").shift() || \"\";\n  if (JSON_RE.test(contentType)) {\n    return \"json\";\n  }\n  if (textTypes.has(contentType) || contentType.startsWith(\"text/\")) {\n    return \"text\";\n  }\n  return \"blob\";\n}\nfunction isJSONParsable(value) {\n  try {\n    JSON.parse(value);\n    return true;\n  } catch (error) {\n    return false;\n  }\n}\nfunction isJSONSerializable(value) {\n  if (value === void 0) {\n    return false;\n  }\n  const t = typeof value;\n  if (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n    return true;\n  }\n  if (t !== \"object\") {\n    return false;\n  }\n  if (Array.isArray(value)) {\n    return true;\n  }\n  if (value.buffer) {\n    return false;\n  }\n  return value.constructor && value.constructor.name === \"Object\" || typeof value.toJSON === \"function\";\n}\nfunction jsonParse(text) {\n  try {\n    return JSON.parse(text);\n  } catch (error) {\n    return text;\n  }\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction getFetch(options) {\n  if (options == null ? void 0 : options.customFetchImpl) {\n    return options.customFetchImpl;\n  }\n  if (typeof globalThis !== \"undefined\" && isFunction(globalThis.fetch)) {\n    return globalThis.fetch;\n  }\n  if (typeof window !== \"undefined\" && isFunction(window.fetch)) {\n    return window.fetch;\n  }\n  throw new Error(\"No fetch implementation found\");\n}\nfunction isPayloadMethod(method) {\n  if (!method) {\n    return false;\n  }\n  const payloadMethod = [\"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n  return payloadMethod.includes(method.toUpperCase());\n}\nfunction isRouteMethod(method) {\n  const routeMethod = [\"GET\", \"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n  if (!method) {\n    return false;\n  }\n  return routeMethod.includes(method.toUpperCase());\n}\nasync function getHeaders(opts) {\n  const headers = new Headers(opts == null ? void 0 : opts.headers);\n  const authHeader = await getAuthHeader(opts);\n  for (const [key, value] of Object.entries(authHeader || {})) {\n    headers.set(key, value);\n  }\n  if (!headers.has(\"content-type\")) {\n    const t = detectContentType(opts == null ? void 0 : opts.body);\n    if (t) {\n      headers.set(\"content-type\", t);\n    }\n  }\n  return headers;\n}\nfunction getURL(url, options) {\n  if (url.startsWith(\"@\")) {\n    const m = url.toString().split(\"@\")[1].split(\"/\")[0];\n    if (methods.includes(m)) {\n      url = url.replace(`@${m}/`, \"/\");\n    }\n  }\n  let _url;\n  try {\n    if (url.startsWith(\"http\")) {\n      _url = url;\n    } else {\n      let baseURL = options == null ? void 0 : options.baseURL;\n      if (baseURL && !(baseURL == null ? void 0 : baseURL.endsWith(\"/\"))) {\n        baseURL = baseURL + \"/\";\n      }\n      if (url.startsWith(\"/\")) {\n        _url = new URL(url.substring(1), baseURL);\n      } else {\n        _url = new URL(url, options == null ? void 0 : options.baseURL);\n      }\n    }\n  } catch (e) {\n    if (e instanceof TypeError) {\n      if (!(options == null ? void 0 : options.baseURL)) {\n        throw TypeError(\n          `Invalid URL ${url}. Are you passing in a relative url but not setting the baseURL?`\n        );\n      }\n      throw TypeError(\n        `Invalid URL ${url}. Please validate that you are passing the correct input.`\n      );\n    }\n    throw e;\n  }\n  if (options == null ? void 0 : options.params) {\n    if (Array.isArray(options == null ? void 0 : options.params)) {\n      const params = (options == null ? void 0 : options.params) ? Array.isArray(options.params) ? `/${options.params.join(\"/\")}` : `/${Object.values(options.params).join(\"/\")}` : \"\";\n      _url = _url.toString().split(\"/:\")[0];\n      _url = `${_url.toString()}${params}`;\n    } else {\n      for (const [key, value] of Object.entries(options == null ? void 0 : options.params)) {\n        _url = _url.toString().replace(`:${key}`, String(value));\n      }\n    }\n  }\n  const __url = new URL(_url);\n  const queryParams = options == null ? void 0 : options.query;\n  if (queryParams) {\n    for (const [key, value] of Object.entries(queryParams)) {\n      __url.searchParams.append(key, String(value));\n    }\n  }\n  return __url;\n}\nfunction detectContentType(body) {\n  if (isJSONSerializable(body)) {\n    return \"application/json\";\n  }\n  return null;\n}\nfunction getBody(options) {\n  if (!(options == null ? void 0 : options.body)) {\n    return null;\n  }\n  const headers = new Headers(options == null ? void 0 : options.headers);\n  if (isJSONSerializable(options.body) && !headers.has(\"content-type\")) {\n    for (const [key, value] of Object.entries(options == null ? void 0 : options.body)) {\n      if (value instanceof Date) {\n        options.body[key] = value.toISOString();\n      }\n    }\n    return JSON.stringify(options.body);\n  }\n  return options.body;\n}\nfunction getMethod(url, options) {\n  var _a;\n  if (options == null ? void 0 : options.method) {\n    return options.method.toUpperCase();\n  }\n  if (url.startsWith(\"@\")) {\n    const pMethod = (_a = url.split(\"@\")[1]) == null ? void 0 : _a.split(\"/\")[0];\n    if (!methods.includes(pMethod)) {\n      return (options == null ? void 0 : options.body) ? \"POST\" : \"GET\";\n    }\n    return pMethod.toUpperCase();\n  }\n  return (options == null ? void 0 : options.body) ? \"POST\" : \"GET\";\n}\nfunction getTimeout(options, controller) {\n  let abortTimeout;\n  if (!(options == null ? void 0 : options.signal) && (options == null ? void 0 : options.timeout)) {\n    abortTimeout = setTimeout(() => controller == null ? void 0 : controller.abort(), options == null ? void 0 : options.timeout);\n  }\n  return {\n    abortTimeout,\n    clearTimeout: () => {\n      if (abortTimeout) {\n        clearTimeout(abortTimeout);\n      }\n    }\n  };\n}\nfunction bodyParser(data, responseType) {\n  if (responseType === \"json\") {\n    return JSON.parse(data);\n  }\n  return data;\n}\nvar ValidationError = class _ValidationError extends Error {\n  constructor(issues, message) {\n    super(message || JSON.stringify(issues, null, 2));\n    this.issues = issues;\n    Object.setPrototypeOf(this, _ValidationError.prototype);\n  }\n};\nasync function parseStandardSchema(schema, input) {\n  let result = await schema[\"~standard\"].validate(input);\n  if (result.issues) {\n    throw new ValidationError(result.issues);\n  }\n  return result.value;\n}\n\n// src/create-fetch/schema.ts\nvar methods = [\"get\", \"post\", \"put\", \"patch\", \"delete\"];\nvar createSchema = (schema, config) => {\n  return {\n    schema,\n    config\n  };\n};\n\n// src/create-fetch/index.ts\nvar applySchemaPlugin = (config) => ({\n  id: \"apply-schema\",\n  name: \"Apply Schema\",\n  version: \"1.0.0\",\n  async init(url, options) {\n    var _a, _b, _c, _d;\n    const schema = ((_b = (_a = config.plugins) == null ? void 0 : _a.find(\n      (plugin) => {\n        var _a2;\n        return ((_a2 = plugin.schema) == null ? void 0 : _a2.config) ? url.startsWith(plugin.schema.config.baseURL || \"\") || url.startsWith(plugin.schema.config.prefix || \"\") : false;\n      }\n    )) == null ? void 0 : _b.schema) || config.schema;\n    if (schema) {\n      let urlKey = url;\n      if ((_c = schema.config) == null ? void 0 : _c.prefix) {\n        if (urlKey.startsWith(schema.config.prefix)) {\n          urlKey = urlKey.replace(schema.config.prefix, \"\");\n          if (schema.config.baseURL) {\n            url = url.replace(schema.config.prefix, schema.config.baseURL);\n          }\n        }\n      }\n      if ((_d = schema.config) == null ? void 0 : _d.baseURL) {\n        if (urlKey.startsWith(schema.config.baseURL)) {\n          urlKey = urlKey.replace(schema.config.baseURL, \"\");\n        }\n      }\n      const keySchema = schema.schema[urlKey];\n      if (keySchema) {\n        let opts = __spreadProps(__spreadValues({}, options), {\n          method: keySchema.method,\n          output: keySchema.output\n        });\n        if (!(options == null ? void 0 : options.disableValidation)) {\n          opts = __spreadProps(__spreadValues({}, opts), {\n            body: keySchema.input ? await parseStandardSchema(keySchema.input, options == null ? void 0 : options.body) : options == null ? void 0 : options.body,\n            params: keySchema.params ? await parseStandardSchema(keySchema.params, options == null ? void 0 : options.params) : options == null ? void 0 : options.params,\n            query: keySchema.query ? await parseStandardSchema(keySchema.query, options == null ? void 0 : options.query) : options == null ? void 0 : options.query\n          });\n        }\n        return {\n          url,\n          options: opts\n        };\n      }\n    }\n    return {\n      url,\n      options\n    };\n  }\n});\nvar createFetch = (config) => {\n  async function $fetch(url, options) {\n    const opts = __spreadProps(__spreadValues(__spreadValues({}, config), options), {\n      plugins: [...(config == null ? void 0 : config.plugins) || [], applySchemaPlugin(config || {})]\n    });\n    if (config == null ? void 0 : config.catchAllError) {\n      try {\n        return await betterFetch(url, opts);\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            status: 500,\n            statusText: \"Fetch Error\",\n            message: \"Fetch related error. Captured by catchAllError option. See error property for more details.\",\n            error\n          }\n        };\n      }\n    }\n    return await betterFetch(url, opts);\n  }\n  return $fetch;\n};\n\n// src/url.ts\nfunction getURL2(url, option) {\n  let { baseURL, params, query } = option || {\n    query: {},\n    params: {},\n    baseURL: \"\"\n  };\n  let basePath = url.startsWith(\"http\") ? url.split(\"/\").slice(0, 3).join(\"/\") : baseURL || \"\";\n  if (url.startsWith(\"@\")) {\n    const m = url.toString().split(\"@\")[1].split(\"/\")[0];\n    if (methods.includes(m)) {\n      url = url.replace(`@${m}/`, \"/\");\n    }\n  }\n  if (!basePath.endsWith(\"/\")) basePath += \"/\";\n  let [path, urlQuery] = url.replace(basePath, \"\").split(\"?\");\n  const queryParams = new URLSearchParams(urlQuery);\n  for (const [key, value] of Object.entries(query || {})) {\n    if (value == null) continue;\n    queryParams.set(key, String(value));\n  }\n  if (params) {\n    if (Array.isArray(params)) {\n      const paramPaths = path.split(\"/\").filter((p) => p.startsWith(\":\"));\n      for (const [index, key] of paramPaths.entries()) {\n        const value = params[index];\n        path = path.replace(key, value);\n      }\n    } else {\n      for (const [key, value] of Object.entries(params)) {\n        path = path.replace(`:${key}`, String(value));\n      }\n    }\n  }\n  path = path.split(\"/\").map(encodeURIComponent).join(\"/\");\n  if (path.startsWith(\"/\")) path = path.slice(1);\n  let queryParamString = queryParams.toString();\n  queryParamString = queryParamString.length > 0 ? `?${queryParamString}`.replace(/\\+/g, \"%20\") : \"\";\n  if (!basePath.startsWith(\"http\")) {\n    return `${basePath}${path}${queryParamString}`;\n  }\n  const _url = new URL(`${path}${queryParamString}`, basePath);\n  return _url;\n}\n\n// src/fetch.ts\nvar betterFetch = async (url, options) => {\n  var _a, _b, _c, _d, _e, _f, _g, _h;\n  const {\n    hooks,\n    url: __url,\n    options: opts\n  } = await initializePlugins(url, options);\n  const fetch = getFetch(opts);\n  const controller = new AbortController();\n  const signal = (_a = opts.signal) != null ? _a : controller.signal;\n  const _url = getURL2(__url, opts);\n  const body = getBody(opts);\n  const headers = await getHeaders(opts);\n  const method = getMethod(__url, opts);\n  let context = __spreadProps(__spreadValues({}, opts), {\n    url: _url,\n    headers,\n    body,\n    method,\n    signal\n  });\n  for (const onRequest of hooks.onRequest) {\n    if (onRequest) {\n      const res = await onRequest(context);\n      if (res instanceof Object) {\n        context = res;\n      }\n    }\n  }\n  if (\"pipeTo\" in context && typeof context.pipeTo === \"function\" || typeof ((_b = options == null ? void 0 : options.body) == null ? void 0 : _b.pipe) === \"function\") {\n    if (!(\"duplex\" in context)) {\n      context.duplex = \"half\";\n    }\n  }\n  const { clearTimeout: clearTimeout2 } = getTimeout(opts, controller);\n  let response = await fetch(context.url, context);\n  clearTimeout2();\n  const responseContext = {\n    response,\n    request: context\n  };\n  for (const onResponse of hooks.onResponse) {\n    if (onResponse) {\n      const r = await onResponse(__spreadProps(__spreadValues({}, responseContext), {\n        response: ((_c = options == null ? void 0 : options.hookOptions) == null ? void 0 : _c.cloneResponse) ? response.clone() : response\n      }));\n      if (r instanceof Response) {\n        response = r;\n      } else if (r instanceof Object) {\n        response = r.response;\n      }\n    }\n  }\n  if (response.ok) {\n    const hasBody = context.method !== \"HEAD\";\n    if (!hasBody) {\n      return {\n        data: \"\",\n        error: null\n      };\n    }\n    const responseType = detectResponseType(response);\n    const successContext = {\n      data: \"\",\n      response,\n      request: context\n    };\n    if (responseType === \"json\" || responseType === \"text\") {\n      const text = await response.text();\n      const parser2 = (_d = context.jsonParser) != null ? _d : jsonParse;\n      const data = await parser2(text);\n      successContext.data = data;\n    } else {\n      successContext.data = await response[responseType]();\n    }\n    if (context == null ? void 0 : context.output) {\n      if (context.output && !context.disableValidation) {\n        successContext.data = await parseStandardSchema(\n          context.output,\n          successContext.data\n        );\n      }\n    }\n    for (const onSuccess of hooks.onSuccess) {\n      if (onSuccess) {\n        await onSuccess(__spreadProps(__spreadValues({}, successContext), {\n          response: ((_e = options == null ? void 0 : options.hookOptions) == null ? void 0 : _e.cloneResponse) ? response.clone() : response\n        }));\n      }\n    }\n    if (options == null ? void 0 : options.throw) {\n      return successContext.data;\n    }\n    return {\n      data: successContext.data,\n      error: null\n    };\n  }\n  const parser = (_f = options == null ? void 0 : options.jsonParser) != null ? _f : jsonParse;\n  const responseText = await response.text();\n  const isJSONResponse = isJSONParsable(responseText);\n  const errorObject = isJSONResponse ? await parser(responseText) : null;\n  const errorContext = {\n    response,\n    responseText,\n    request: context,\n    error: __spreadProps(__spreadValues({}, errorObject), {\n      status: response.status,\n      statusText: response.statusText\n    })\n  };\n  for (const onError of hooks.onError) {\n    if (onError) {\n      await onError(__spreadProps(__spreadValues({}, errorContext), {\n        response: ((_g = options == null ? void 0 : options.hookOptions) == null ? void 0 : _g.cloneResponse) ? response.clone() : response\n      }));\n    }\n  }\n  if (options == null ? void 0 : options.retry) {\n    const retryStrategy = createRetryStrategy(options.retry);\n    const _retryAttempt = (_h = options.retryAttempt) != null ? _h : 0;\n    if (await retryStrategy.shouldAttemptRetry(_retryAttempt, response)) {\n      for (const onRetry of hooks.onRetry) {\n        if (onRetry) {\n          await onRetry(responseContext);\n        }\n      }\n      const delay = retryStrategy.getDelay(_retryAttempt);\n      await new Promise((resolve) => setTimeout(resolve, delay));\n      return await betterFetch(url, __spreadProps(__spreadValues({}, options), {\n        retryAttempt: _retryAttempt + 1\n      }));\n    }\n  }\n  if (options == null ? void 0 : options.throw) {\n    throw new BetterFetchError(\n      response.status,\n      response.statusText,\n      isJSONResponse ? errorObject : responseText\n    );\n  }\n  return {\n    data: null,\n    error: __spreadProps(__spreadValues({}, errorObject), {\n      status: response.status,\n      statusText: response.statusText\n    })\n  };\n};\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js\n");

/***/ })

};
;