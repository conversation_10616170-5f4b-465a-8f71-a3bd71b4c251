"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@19.1.6_@types+react@19.1.8__@types_7b46adce8be1bcd7dba6d0dca748f267";
exports.ids = ["vendor-chunks/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@19.1.6_@types+react@19.1.8__@types_7b46adce8be1bcd7dba6d0dca748f267"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@19.1.6_@types+react@19.1.8__@types_7b46adce8be1bcd7dba6d0dca748f267/node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@19.1.6_@types+react@19.1.8__@types_7b46adce8be1bcd7dba6d0dca748f267/node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@19.1.6_@types+react@19.1.8__@types+re_b26c6d948d533107753195e05bbf9d47/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@19.1.6_@types+react@19.1.8__@types+rea_6e0f845fa0b5165e723599b67dc13bbf/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Item,Root,RovingFocusGroup,RovingFocusGroupItem,createRovingFocusGroupScope auto */ // src/roving-focus-group.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(GROUP_NAME, [\n    createCollectionScope\n]);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeRovingFocusGroup,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: props.__scopeRovingFocusGroup,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, {\n                ...props,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n    const [currentTabStopId, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n        prop: currentTabStopIdProp,\n        defaultProp: defaultCurrentTabStopId ?? null,\n        onChange: onCurrentTabStopIdChange,\n        caller: GROUP_NAME\n    });\n    const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupImpl.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n                return ({\n                    \"RovingFocusGroupImpl.useEffect\": ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus)\n                })[\"RovingFocusGroupImpl.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupImpl.useEffect\"], [\n        handleEntryFocus\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusProvider, {\n        scope: __scopeRovingFocusGroup,\n        orientation,\n        dir: direction,\n        loop,\n        currentTabStopId,\n        onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": (tabStopId)=>setCurrentTabStopId(tabStopId)\n        }[\"RovingFocusGroupImpl.useCallback\"], [\n            setCurrentTabStopId\n        ]),\n        onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setIsTabbingBackOut(true)\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount + 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount - 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n            \"data-orientation\": orientation,\n            ...groupProps,\n            ref: composedRefs,\n            style: {\n                outline: \"none\",\n                ...props.style\n            },\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, ()=>{\n                isClickFocusRef.current = true;\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event)=>{\n                const isKeyboardFocus = !isClickFocusRef.current;\n                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n                    event.currentTarget.dispatchEvent(entryFocusEvent);\n                    if (!entryFocusEvent.defaultPrevented) {\n                        const items = getItems().filter((item)=>item.focusable);\n                        const activeItem = items.find((item)=>item.active);\n                        const currentItem = items.find((item)=>item.id === currentTabStopId);\n                        const candidateItems = [\n                            activeItem,\n                            currentItem,\n                            ...items\n                        ].filter(Boolean);\n                        const candidateNodes = candidateItems.map((item)=>item.ref.current);\n                        focusFirst(candidateNodes, preventScrollOnEntryFocus);\n                    }\n                }\n                isClickFocusRef.current = false;\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, ()=>setIsTabbingBackOut(false))\n        })\n    });\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, children, ...itemProps } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupItem.useEffect\": ()=>{\n            if (focusable) {\n                onFocusableItemAdd();\n                return ({\n                    \"RovingFocusGroupItem.useEffect\": ()=>onFocusableItemRemove()\n                })[\"RovingFocusGroupItem.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupItem.useEffect\"], [\n        focusable,\n        onFocusableItemAdd,\n        onFocusableItemRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span, {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!focusable) event.preventDefault();\n                else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, ()=>context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (event.key === \"Tab\" && event.shiftKey) {\n                    context.onItemShiftTab();\n                    return;\n                }\n                if (event.target !== event.currentTarget) return;\n                const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n                if (focusIntent !== void 0) {\n                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item)=>item.focusable);\n                    let candidateNodes = items.map((item)=>item.ref.current);\n                    if (focusIntent === \"last\") candidateNodes.reverse();\n                    else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                        if (focusIntent === \"prev\") candidateNodes.reverse();\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                }\n            }),\n            children: typeof children === \"function\" ? children({\n                isCurrentTabStop,\n                hasTabStop: currentTabStopId != null\n            }) : children\n        })\n    });\n});\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n    ArrowLeft: \"prev\",\n    ArrowUp: \"prev\",\n    ArrowRight: \"next\",\n    ArrowDown: \"next\",\n    PageUp: \"first\",\n    Home: \"first\",\n    PageDown: \"last\",\n    End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n    if (dir !== \"rtl\") return key;\n    return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n    const key = getDirectionAwareKey(event.key, dir);\n    if (orientation === \"vertical\" && [\n        \"ArrowLeft\",\n        \"ArrowRight\"\n    ].includes(key)) return void 0;\n    if (orientation === \"horizontal\" && [\n        \"ArrowUp\",\n        \"ArrowDown\"\n    ].includes(key)) return void 0;\n    return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus({\n            preventScroll\n        });\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByYWRpeC11aStyZWFjdC1yb3ZpbmctZm9jdXNAMS4xLjEwX0B0eXBlcytyZWFjdC1kb21AMTkuMS42X0B0eXBlcytyZWFjdEAxOS4xLjhfX0B0eXBlc183YjQ2YWRjZThiZTFiY2Q3ZGJhNmQwZGNhNzQ4ZjI2Ny9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXJvdmluZy1mb2N1cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNjO0FBQ0o7QUFDRDtBQUNHO0FBQ2I7QUFDSTtBQUNLO0FBQ007QUFDUjtBQWdFbkI7QUE1RFYsSUFBTSxjQUFjO0FBQ3BCLElBQU0sZ0JBQWdCO0lBQUUsU0FBUztJQUFPLFlBQVk7QUFBSztBQU16RCxJQUFNLGFBQWE7QUFHbkIsSUFBTSxDQUFDLFlBQVksZUFBZSxxQkFBcUIsSUFBSSw0RUFBZ0IsQ0FHekUsVUFBVTtBQUdaLElBQU0sQ0FBQywrQkFBK0IsMkJBQTJCLElBQUksMkVBQWtCLENBQ3JGLFlBQ0E7SUFBQyxxQkFBcUI7Q0FBQTtBQStCeEIsSUFBTSxDQUFDLHFCQUFxQixxQkFBcUIsSUFDL0MsOEJBQWtELFVBQVU7QUFLOUQsSUFBTSxpQ0FBeUIsOENBQzdCLENBQUMsT0FBMkM7SUFDMUMsT0FDRSx1RUFBQyxXQUFXLFVBQVg7UUFBb0IsT0FBTyxNQUFNO1FBQ2hDLGlGQUFDLFdBQVcsTUFBWDtZQUFnQixPQUFPLE1BQU07WUFDNUIsaUZBQUM7Z0JBQXNCLEdBQUc7Z0JBQU8sS0FBSztZQUFBLENBQWM7UUFBQSxDQUN0RDtJQUFBLENBQ0Y7QUFFSjtBQUdGLGlCQUFpQixjQUFjO0FBZ0IvQixJQUFNLHFDQUE2Qiw4Q0FHakMsQ0FBQyxPQUErQztJQUNoRCxNQUFNLEVBQ0oseUJBQ0EsYUFDQSxPQUFPLE9BQ1AsS0FDQSxrQkFBa0Isc0JBQ2xCLHlCQUNBLDBCQUNBLGNBQ0EsNEJBQTRCLE9BQzVCLEdBQUcsWUFDTCxHQUFJO0lBQ0osTUFBTSxNQUFZLDBDQUFvQyxJQUFJO0lBQzFELE1BQU0sZUFBZSw2RUFBZSxDQUFDLGNBQWMsR0FBRztJQUN0RCxNQUFNLFlBQVksdUVBQVksQ0FBQyxHQUFHO0lBQ2xDLE1BQU0sQ0FBQyxrQkFBa0IsbUJBQW1CLElBQUksNEZBQW9CLENBQUM7UUFDbkUsTUFBTTtRQUNOLGFBQWEsMkJBQTJCO1FBQ3hDLFVBQVU7UUFDVixRQUFRO0lBQ1YsQ0FBQztJQUNELE1BQU0sQ0FBQyxrQkFBa0IsbUJBQW1CLElBQVUsNENBQVMsS0FBSztJQUNwRSxNQUFNLG1CQUFtQixnRkFBYyxDQUFDLFlBQVk7SUFDcEQsTUFBTSxXQUFXLGNBQWMsdUJBQXVCO0lBQ3RELE1BQU0sa0JBQXdCLDBDQUFPLEtBQUs7SUFDMUMsTUFBTSxDQUFDLHFCQUFxQixzQkFBc0IsSUFBVSw0Q0FBUyxDQUFDO0lBRWhFOzBDQUFVO1lBQ2QsTUFBTSxPQUFPLElBQUk7WUFDakIsSUFBSSxNQUFNO2dCQUNSLEtBQUssaUJBQWlCLGFBQWEsZ0JBQWdCO2dCQUNuRDtzREFBTyxJQUFNLEtBQUssb0JBQW9CLGFBQWEsZ0JBQWdCOztZQUNyRTtRQUNGO3lDQUFHO1FBQUMsZ0JBQWdCO0tBQUM7SUFFckIsT0FDRSx1RUFBQztRQUNDLE9BQU87UUFDUDtRQUNBLEtBQUs7UUFDTDtRQUNBO1FBQ0EsYUFBbUI7Z0RBQ2pCLENBQUMsWUFBYyxvQkFBb0IsU0FBUzsrQ0FDNUM7WUFBQyxtQkFBbUI7U0FBQTtRQUV0QixnQkFBc0I7Z0RBQVksSUFBTSxvQkFBb0IsSUFBSTsrQ0FBRyxDQUFDLENBQUM7UUFDckUsb0JBQTBCO2dEQUN4QixJQUFNO3dEQUF1QixDQUFDLFlBQWMsWUFBWSxDQUFDOzsrQ0FDekQsQ0FBQztRQUVILHVCQUE2QjtnREFDM0IsSUFBTTt3REFBdUIsQ0FBQyxZQUFjLFlBQVksQ0FBQzs7K0NBQ3pELENBQUM7UUFHSCxpRkFBQyxnRUFBUyxDQUFDLEtBQVY7WUFDQyxVQUFVLG9CQUFvQix3QkFBd0IsSUFBSSxLQUFLO1lBQy9ELG9CQUFrQjtZQUNqQixHQUFHO1lBQ0osS0FBSztZQUNMLE9BQU87Z0JBQUUsU0FBUztnQkFBUSxHQUFHLE1BQU07WUFBTTtZQUN6QyxhQUFhLHlFQUFvQixDQUFDLE1BQU0sYUFBYTtnQkFDbkQsZ0JBQWdCLFVBQVU7WUFDNUIsQ0FBQztZQUNELFNBQVMseUVBQW9CLENBQUMsTUFBTSxTQUFTLENBQUM7Z0JBSzVDLE1BQU0sa0JBQWtCLENBQUMsZ0JBQWdCO2dCQUV6QyxJQUFJLE1BQU0sV0FBVyxNQUFNLGlCQUFpQixtQkFBbUIsQ0FBQyxrQkFBa0I7b0JBQ2hGLE1BQU0sa0JBQWtCLElBQUksWUFBWSxhQUFhLGFBQWE7b0JBQ2xFLE1BQU0sY0FBYyxjQUFjLGVBQWU7b0JBRWpELElBQUksQ0FBQyxnQkFBZ0Isa0JBQWtCO3dCQUNyQyxNQUFNLFFBQVEsU0FBUyxFQUFFLE9BQU8sQ0FBQyxPQUFTLEtBQUssU0FBUzt3QkFDeEQsTUFBTSxhQUFhLE1BQU0sS0FBSyxDQUFDLE9BQVMsS0FBSyxNQUFNO3dCQUNuRCxNQUFNLGNBQWMsTUFBTSxLQUFLLENBQUMsT0FBUyxLQUFLLE9BQU8sZ0JBQWdCO3dCQUNyRSxNQUFNLGlCQUFpQjs0QkFBQzs0QkFBWTsrQkFBZ0IsS0FBSzt5QkFBQSxDQUFFLE9BQ3pEO3dCQUVGLE1BQU0saUJBQWlCLGVBQWUsSUFBSSxDQUFDLE9BQVMsS0FBSyxJQUFJLE9BQVE7d0JBQ3JFLFdBQVcsZ0JBQWdCLHlCQUF5QjtvQkFDdEQ7Z0JBQ0Y7Z0JBRUEsZ0JBQWdCLFVBQVU7WUFDNUIsQ0FBQztZQUNELFFBQVEseUVBQW9CLENBQUMsTUFBTSxRQUFRLElBQU0sb0JBQW9CLEtBQUssQ0FBQztRQUFBO0lBQzdFO0FBR04sQ0FBQztBQU1ELElBQU0sWUFBWTtBQWFsQixJQUFNLHFDQUE2Qiw4Q0FDakMsQ0FBQyxPQUEwQztJQUN6QyxNQUFNLEVBQ0oseUJBQ0EsWUFBWSxNQUNaLFNBQVMsT0FDVCxXQUNBLFVBQ0EsR0FBRyxXQUNMLEdBQUk7SUFDSixNQUFNLFNBQVMsMERBQUssQ0FBQztJQUNyQixNQUFNLEtBQUssYUFBYTtJQUN4QixNQUFNLFVBQVUsc0JBQXNCLFdBQVcsdUJBQXVCO0lBQ3hFLE1BQU0sbUJBQW1CLFFBQVEscUJBQXFCO0lBQ3RELE1BQU0sV0FBVyxjQUFjLHVCQUF1QjtJQUV0RCxNQUFNLEVBQUUsb0JBQW9CLHVCQUF1QixpQkFBaUIsSUFBSTtJQUVsRTswQ0FBVTtZQUNkLElBQUksV0FBVztnQkFDYixtQkFBbUI7Z0JBQ25CO3NEQUFPLElBQU0sc0JBQXNCOztZQUNyQztRQUNGO3lDQUFHO1FBQUM7UUFBVztRQUFvQixxQkFBcUI7S0FBQztJQUV6RCxPQUNFLHVFQUFDLFdBQVcsVUFBWDtRQUNDLE9BQU87UUFDUDtRQUNBO1FBQ0E7UUFFQSxpRkFBQyxnRUFBUyxDQUFDLE1BQVY7WUFDQyxVQUFVLG1CQUFtQixJQUFJO1lBQ2pDLG9CQUFrQixRQUFRO1lBQ3pCLEdBQUc7WUFDSixLQUFLO1lBQ0wsYUFBYSx5RUFBb0IsQ0FBQyxNQUFNLGFBQWEsQ0FBQztnQkFHcEQsSUFBSSxDQUFDLFVBQVcsT0FBTSxlQUFlO3FCQUVoQyxRQUFRLFlBQVksRUFBRTtZQUM3QixDQUFDO1lBQ0QsU0FBUyx5RUFBb0IsQ0FBQyxNQUFNLFNBQVMsSUFBTSxRQUFRLFlBQVksRUFBRSxDQUFDO1lBQzFFLFdBQVcseUVBQW9CLENBQUMsTUFBTSxXQUFXLENBQUM7Z0JBQ2hELElBQUksTUFBTSxRQUFRLFNBQVMsTUFBTSxVQUFVO29CQUN6QyxRQUFRLGVBQWU7b0JBQ3ZCO2dCQUNGO2dCQUVBLElBQUksTUFBTSxXQUFXLE1BQU0sY0FBZTtnQkFFMUMsTUFBTSxjQUFjLGVBQWUsT0FBTyxRQUFRLGFBQWEsUUFBUSxHQUFHO2dCQUUxRSxJQUFJLGdCQUFnQixRQUFXO29CQUM3QixJQUFJLE1BQU0sV0FBVyxNQUFNLFdBQVcsTUFBTSxVQUFVLE1BQU0sU0FBVTtvQkFDdEUsTUFBTSxlQUFlO29CQUNyQixNQUFNLFFBQVEsU0FBUyxFQUFFLE9BQU8sQ0FBQyxPQUFTLEtBQUssU0FBUztvQkFDeEQsSUFBSSxpQkFBaUIsTUFBTSxJQUFJLENBQUMsT0FBUyxLQUFLLElBQUksT0FBUTtvQkFFMUQsSUFBSSxnQkFBZ0IsT0FBUSxnQkFBZSxRQUFRO3lCQUFBLElBQzFDLGdCQUFnQixVQUFVLGdCQUFnQixRQUFRO3dCQUN6RCxJQUFJLGdCQUFnQixPQUFRLGdCQUFlLFFBQVE7d0JBQ25ELE1BQU0sZUFBZSxlQUFlLFFBQVEsTUFBTSxhQUFhO3dCQUMvRCxpQkFBaUIsUUFBUSxPQUNyQixVQUFVLGdCQUFnQixlQUFlLENBQUMsSUFDMUMsZUFBZSxNQUFNLGVBQWUsQ0FBQztvQkFDM0M7b0JBTUEsV0FBVyxJQUFNLFdBQVcsY0FBYyxDQUFDO2dCQUM3QztZQUNGLENBQUM7WUFFQSxpQkFBTyxhQUFhLGFBQ2pCLFNBQVM7Z0JBQUU7Z0JBQWtCLFlBQVksb0JBQW9CO1lBQUssQ0FBQyxJQUNuRTtRQUFBO0lBQ047QUFHTjtBQUdGLHFCQUFxQixjQUFjO0FBS25DLElBQU0sMEJBQXVEO0lBQzNELFdBQVc7SUFBUSxTQUFTO0lBQzVCLFlBQVk7SUFBUSxXQUFXO0lBQy9CLFFBQVE7SUFBUyxNQUFNO0lBQ3ZCLFVBQVU7SUFBUSxLQUFLO0FBQ3pCO0FBRUEsU0FBUyxxQkFBcUIsS0FBYSxLQUFpQjtJQUMxRCxJQUFJLFFBQVEsTUFBTyxRQUFPO0lBQzFCLE9BQU8sUUFBUSxjQUFjLGVBQWUsUUFBUSxlQUFlLGNBQWM7QUFDbkY7QUFJQSxTQUFTLGVBQWUsT0FBNEIsYUFBMkIsS0FBaUI7SUFDOUYsTUFBTSxNQUFNLHFCQUFxQixNQUFNLEtBQUssR0FBRztJQUMvQyxJQUFJLGdCQUFnQixjQUFjO1FBQUM7UUFBYSxZQUFZO0tBQUEsQ0FBRSxTQUFTLEdBQUcsRUFBRyxRQUFPO0lBQ3BGLElBQUksZ0JBQWdCLGdCQUFnQjtRQUFDO1FBQVcsV0FBVztLQUFBLENBQUUsU0FBUyxHQUFHLEVBQUcsUUFBTztJQUNuRixPQUFPLHdCQUF3QixHQUFHO0FBQ3BDO0FBRUEsU0FBUyxXQUFXLFlBQTJCLGdCQUFnQixPQUFPO0lBQ3BFLE1BQU0sNkJBQTZCLFNBQVM7SUFDNUMsV0FBVyxhQUFhLFdBQVk7UUFFbEMsSUFBSSxjQUFjLDJCQUE0QjtRQUM5QyxVQUFVLE1BQU07WUFBRTtRQUFjLENBQUM7UUFDakMsSUFBSSxTQUFTLGtCQUFrQiwyQkFBNEI7SUFDN0Q7QUFDRjtBQU1BLFNBQVMsVUFBYSxPQUFZLFlBQW9CO0lBQ3BELE9BQU8sTUFBTSxJQUFPLENBQUMsR0FBRyxRQUFVLE9BQU8sYUFBYSxTQUFTLE1BQU0sTUFBTSxDQUFFO0FBQy9FO0FBRUEsSUFBTSxPQUFPO0FBQ2IsSUFBTSxPQUFPIiwic291cmNlcyI6WyIvVXNlcnMvbWFkaHVrYXJrdW1hci9Ecm9wYm94L21hZGh1a2FyL3JvYnlubnYzL2NvZGUvcm9ieW5udjMvYXBwcy9zcmMvcm92aW5nLWZvY3VzLWdyb3VwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gJ0ByYWRpeC11aS9wcmltaXRpdmUnO1xuaW1wb3J0IHsgY3JlYXRlQ29sbGVjdGlvbiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb2xsZWN0aW9uJztcbmltcG9ydCB7IHVzZUNvbXBvc2VkUmVmcyB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuaW1wb3J0IHsgdXNlSWQgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtaWQnO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZSc7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmJztcbmltcG9ydCB7IHVzZUNvbnRyb2xsYWJsZVN0YXRlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXVzZS1jb250cm9sbGFibGUtc3RhdGUnO1xuaW1wb3J0IHsgdXNlRGlyZWN0aW9uIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbic7XG5cbmltcG9ydCB0eXBlIHsgU2NvcGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29udGV4dCc7XG5cbmNvbnN0IEVOVFJZX0ZPQ1VTID0gJ3JvdmluZ0ZvY3VzR3JvdXAub25FbnRyeUZvY3VzJztcbmNvbnN0IEVWRU5UX09QVElPTlMgPSB7IGJ1YmJsZXM6IGZhbHNlLCBjYW5jZWxhYmxlOiB0cnVlIH07XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFJvdmluZ0ZvY3VzR3JvdXBcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgR1JPVVBfTkFNRSA9ICdSb3ZpbmdGb2N1c0dyb3VwJztcblxudHlwZSBJdGVtRGF0YSA9IHsgaWQ6IHN0cmluZzsgZm9jdXNhYmxlOiBib29sZWFuOyBhY3RpdmU6IGJvb2xlYW4gfTtcbmNvbnN0IFtDb2xsZWN0aW9uLCB1c2VDb2xsZWN0aW9uLCBjcmVhdGVDb2xsZWN0aW9uU2NvcGVdID0gY3JlYXRlQ29sbGVjdGlvbjxcbiAgSFRNTFNwYW5FbGVtZW50LFxuICBJdGVtRGF0YVxuPihHUk9VUF9OQU1FKTtcblxudHlwZSBTY29wZWRQcm9wczxQPiA9IFAgJiB7IF9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwPzogU2NvcGUgfTtcbmNvbnN0IFtjcmVhdGVSb3ZpbmdGb2N1c0dyb3VwQ29udGV4dCwgY3JlYXRlUm92aW5nRm9jdXNHcm91cFNjb3BlXSA9IGNyZWF0ZUNvbnRleHRTY29wZShcbiAgR1JPVVBfTkFNRSxcbiAgW2NyZWF0ZUNvbGxlY3Rpb25TY29wZV1cbik7XG5cbnR5cGUgT3JpZW50YXRpb24gPSBSZWFjdC5BcmlhQXR0cmlidXRlc1snYXJpYS1vcmllbnRhdGlvbiddO1xudHlwZSBEaXJlY3Rpb24gPSAnbHRyJyB8ICdydGwnO1xuXG5pbnRlcmZhY2UgUm92aW5nRm9jdXNHcm91cE9wdGlvbnMge1xuICAvKipcbiAgICogVGhlIG9yaWVudGF0aW9uIG9mIHRoZSBncm91cC5cbiAgICogTWFpbmx5IHNvIGFycm93IG5hdmlnYXRpb24gaXMgZG9uZSBhY2NvcmRpbmdseSAobGVmdCAmIHJpZ2h0IHZzLiB1cCAmIGRvd24pXG4gICAqL1xuICBvcmllbnRhdGlvbj86IE9yaWVudGF0aW9uO1xuICAvKipcbiAgICogVGhlIGRpcmVjdGlvbiBvZiBuYXZpZ2F0aW9uIGJldHdlZW4gaXRlbXMuXG4gICAqL1xuICBkaXI/OiBEaXJlY3Rpb247XG4gIC8qKlxuICAgKiBXaGV0aGVyIGtleWJvYXJkIG5hdmlnYXRpb24gc2hvdWxkIGxvb3AgYXJvdW5kXG4gICAqIEBkZWZhdWx0VmFsdWUgZmFsc2VcbiAgICovXG4gIGxvb3A/OiBib29sZWFuO1xufVxuXG50eXBlIFJvdmluZ0NvbnRleHRWYWx1ZSA9IFJvdmluZ0ZvY3VzR3JvdXBPcHRpb25zICYge1xuICBjdXJyZW50VGFiU3RvcElkOiBzdHJpbmcgfCBudWxsO1xuICBvbkl0ZW1Gb2N1cyh0YWJTdG9wSWQ6IHN0cmluZyk6IHZvaWQ7XG4gIG9uSXRlbVNoaWZ0VGFiKCk6IHZvaWQ7XG4gIG9uRm9jdXNhYmxlSXRlbUFkZCgpOiB2b2lkO1xuICBvbkZvY3VzYWJsZUl0ZW1SZW1vdmUoKTogdm9pZDtcbn07XG5cbmNvbnN0IFtSb3ZpbmdGb2N1c1Byb3ZpZGVyLCB1c2VSb3ZpbmdGb2N1c0NvbnRleHRdID1cbiAgY3JlYXRlUm92aW5nRm9jdXNHcm91cENvbnRleHQ8Um92aW5nQ29udGV4dFZhbHVlPihHUk9VUF9OQU1FKTtcblxudHlwZSBSb3ZpbmdGb2N1c0dyb3VwRWxlbWVudCA9IFJvdmluZ0ZvY3VzR3JvdXBJbXBsRWxlbWVudDtcbmludGVyZmFjZSBSb3ZpbmdGb2N1c0dyb3VwUHJvcHMgZXh0ZW5kcyBSb3ZpbmdGb2N1c0dyb3VwSW1wbFByb3BzIHt9XG5cbmNvbnN0IFJvdmluZ0ZvY3VzR3JvdXAgPSBSZWFjdC5mb3J3YXJkUmVmPFJvdmluZ0ZvY3VzR3JvdXBFbGVtZW50LCBSb3ZpbmdGb2N1c0dyb3VwUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFJvdmluZ0ZvY3VzR3JvdXBQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8Q29sbGVjdGlvbi5Qcm92aWRlciBzY29wZT17cHJvcHMuX19zY29wZVJvdmluZ0ZvY3VzR3JvdXB9PlxuICAgICAgICA8Q29sbGVjdGlvbi5TbG90IHNjb3BlPXtwcm9wcy5fX3Njb3BlUm92aW5nRm9jdXNHcm91cH0+XG4gICAgICAgICAgPFJvdmluZ0ZvY3VzR3JvdXBJbXBsIHsuLi5wcm9wc30gcmVmPXtmb3J3YXJkZWRSZWZ9IC8+XG4gICAgICAgIDwvQ29sbGVjdGlvbi5TbG90PlxuICAgICAgPC9Db2xsZWN0aW9uLlByb3ZpZGVyPlxuICAgICk7XG4gIH1cbik7XG5cblJvdmluZ0ZvY3VzR3JvdXAuZGlzcGxheU5hbWUgPSBHUk9VUF9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgUm92aW5nRm9jdXNHcm91cEltcGxFbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFJvdmluZ0ZvY3VzR3JvdXBJbXBsUHJvcHNcbiAgZXh0ZW5kcyBPbWl0PFByaW1pdGl2ZURpdlByb3BzLCAnZGlyJz4sXG4gICAgUm92aW5nRm9jdXNHcm91cE9wdGlvbnMge1xuICBjdXJyZW50VGFiU3RvcElkPzogc3RyaW5nIHwgbnVsbDtcbiAgZGVmYXVsdEN1cnJlbnRUYWJTdG9wSWQ/OiBzdHJpbmc7XG4gIG9uQ3VycmVudFRhYlN0b3BJZENoYW5nZT86ICh0YWJTdG9wSWQ6IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XG4gIG9uRW50cnlGb2N1cz86IChldmVudDogRXZlbnQpID0+IHZvaWQ7XG4gIHByZXZlbnRTY3JvbGxPbkVudHJ5Rm9jdXM/OiBib29sZWFuO1xufVxuXG5jb25zdCBSb3ZpbmdGb2N1c0dyb3VwSW1wbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJvdmluZ0ZvY3VzR3JvdXBJbXBsRWxlbWVudCxcbiAgUm92aW5nRm9jdXNHcm91cEltcGxQcm9wc1xuPigocHJvcHM6IFNjb3BlZFByb3BzPFJvdmluZ0ZvY3VzR3JvdXBJbXBsUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgY29uc3Qge1xuICAgIF9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwLFxuICAgIG9yaWVudGF0aW9uLFxuICAgIGxvb3AgPSBmYWxzZSxcbiAgICBkaXIsXG4gICAgY3VycmVudFRhYlN0b3BJZDogY3VycmVudFRhYlN0b3BJZFByb3AsXG4gICAgZGVmYXVsdEN1cnJlbnRUYWJTdG9wSWQsXG4gICAgb25DdXJyZW50VGFiU3RvcElkQ2hhbmdlLFxuICAgIG9uRW50cnlGb2N1cyxcbiAgICBwcmV2ZW50U2Nyb2xsT25FbnRyeUZvY3VzID0gZmFsc2UsXG4gICAgLi4uZ3JvdXBQcm9wc1xuICB9ID0gcHJvcHM7XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZjxSb3ZpbmdGb2N1c0dyb3VwSW1wbEVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBjb21wb3NlZFJlZnMgPSB1c2VDb21wb3NlZFJlZnMoZm9yd2FyZGVkUmVmLCByZWYpO1xuICBjb25zdCBkaXJlY3Rpb24gPSB1c2VEaXJlY3Rpb24oZGlyKTtcbiAgY29uc3QgW2N1cnJlbnRUYWJTdG9wSWQsIHNldEN1cnJlbnRUYWJTdG9wSWRdID0gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICAgIHByb3A6IGN1cnJlbnRUYWJTdG9wSWRQcm9wLFxuICAgIGRlZmF1bHRQcm9wOiBkZWZhdWx0Q3VycmVudFRhYlN0b3BJZCA/PyBudWxsLFxuICAgIG9uQ2hhbmdlOiBvbkN1cnJlbnRUYWJTdG9wSWRDaGFuZ2UsXG4gICAgY2FsbGVyOiBHUk9VUF9OQU1FLFxuICB9KTtcbiAgY29uc3QgW2lzVGFiYmluZ0JhY2tPdXQsIHNldElzVGFiYmluZ0JhY2tPdXRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBoYW5kbGVFbnRyeUZvY3VzID0gdXNlQ2FsbGJhY2tSZWYob25FbnRyeUZvY3VzKTtcbiAgY29uc3QgZ2V0SXRlbXMgPSB1c2VDb2xsZWN0aW9uKF9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwKTtcbiAgY29uc3QgaXNDbGlja0ZvY3VzUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgY29uc3QgW2ZvY3VzYWJsZUl0ZW1zQ291bnQsIHNldEZvY3VzYWJsZUl0ZW1zQ291bnRdID0gUmVhY3QudXNlU3RhdGUoMCk7XG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBub2RlID0gcmVmLmN1cnJlbnQ7XG4gICAgaWYgKG5vZGUpIHtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcihFTlRSWV9GT0NVUywgaGFuZGxlRW50cnlGb2N1cyk7XG4gICAgICByZXR1cm4gKCkgPT4gbm9kZS5yZW1vdmVFdmVudExpc3RlbmVyKEVOVFJZX0ZPQ1VTLCBoYW5kbGVFbnRyeUZvY3VzKTtcbiAgICB9XG4gIH0sIFtoYW5kbGVFbnRyeUZvY3VzXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8Um92aW5nRm9jdXNQcm92aWRlclxuICAgICAgc2NvcGU9e19fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwfVxuICAgICAgb3JpZW50YXRpb249e29yaWVudGF0aW9ufVxuICAgICAgZGlyPXtkaXJlY3Rpb259XG4gICAgICBsb29wPXtsb29wfVxuICAgICAgY3VycmVudFRhYlN0b3BJZD17Y3VycmVudFRhYlN0b3BJZH1cbiAgICAgIG9uSXRlbUZvY3VzPXtSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgICAgKHRhYlN0b3BJZCkgPT4gc2V0Q3VycmVudFRhYlN0b3BJZCh0YWJTdG9wSWQpLFxuICAgICAgICBbc2V0Q3VycmVudFRhYlN0b3BJZF1cbiAgICAgICl9XG4gICAgICBvbkl0ZW1TaGlmdFRhYj17UmVhY3QudXNlQ2FsbGJhY2soKCkgPT4gc2V0SXNUYWJiaW5nQmFja091dCh0cnVlKSwgW10pfVxuICAgICAgb25Gb2N1c2FibGVJdGVtQWRkPXtSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgICAgKCkgPT4gc2V0Rm9jdXNhYmxlSXRlbXNDb3VudCgocHJldkNvdW50KSA9PiBwcmV2Q291bnQgKyAxKSxcbiAgICAgICAgW11cbiAgICAgICl9XG4gICAgICBvbkZvY3VzYWJsZUl0ZW1SZW1vdmU9e1JlYWN0LnVzZUNhbGxiYWNrKFxuICAgICAgICAoKSA9PiBzZXRGb2N1c2FibGVJdGVtc0NvdW50KChwcmV2Q291bnQpID0+IHByZXZDb3VudCAtIDEpLFxuICAgICAgICBbXVxuICAgICAgKX1cbiAgICA+XG4gICAgICA8UHJpbWl0aXZlLmRpdlxuICAgICAgICB0YWJJbmRleD17aXNUYWJiaW5nQmFja091dCB8fCBmb2N1c2FibGVJdGVtc0NvdW50ID09PSAwID8gLTEgOiAwfVxuICAgICAgICBkYXRhLW9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICAgICAgey4uLmdyb3VwUHJvcHN9XG4gICAgICAgIHJlZj17Y29tcG9zZWRSZWZzfVxuICAgICAgICBzdHlsZT17eyBvdXRsaW5lOiAnbm9uZScsIC4uLnByb3BzLnN0eWxlIH19XG4gICAgICAgIG9uTW91c2VEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbk1vdXNlRG93biwgKCkgPT4ge1xuICAgICAgICAgIGlzQ2xpY2tGb2N1c1JlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgfSl9XG4gICAgICAgIG9uRm9jdXM9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uRm9jdXMsIChldmVudCkgPT4ge1xuICAgICAgICAgIC8vIFdlIG5vcm1hbGx5IHdvdWxkbid0IG5lZWQgdGhpcyBjaGVjaywgYmVjYXVzZSB3ZSBhbHJlYWR5IGNoZWNrXG4gICAgICAgICAgLy8gdGhhdCB0aGUgZm9jdXMgaXMgb24gdGhlIGN1cnJlbnQgdGFyZ2V0IGFuZCBub3QgYnViYmxpbmcgdG8gaXQuXG4gICAgICAgICAgLy8gV2UgZG8gdGhpcyBiZWNhdXNlIFNhZmFyaSBkb2Vzbid0IGZvY3VzIGJ1dHRvbnMgd2hlbiBjbGlja2VkLCBhbmRcbiAgICAgICAgICAvLyBpbnN0ZWFkLCB0aGUgd3JhcHBlciB3aWxsIGdldCBmb2N1c2VkIGFuZCBub3QgdGhyb3VnaCBhIGJ1YmJsaW5nIGV2ZW50LlxuICAgICAgICAgIGNvbnN0IGlzS2V5Ym9hcmRGb2N1cyA9ICFpc0NsaWNrRm9jdXNSZWYuY3VycmVudDtcblxuICAgICAgICAgIGlmIChldmVudC50YXJnZXQgPT09IGV2ZW50LmN1cnJlbnRUYXJnZXQgJiYgaXNLZXlib2FyZEZvY3VzICYmICFpc1RhYmJpbmdCYWNrT3V0KSB7XG4gICAgICAgICAgICBjb25zdCBlbnRyeUZvY3VzRXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoRU5UUllfRk9DVVMsIEVWRU5UX09QVElPTlMpO1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5kaXNwYXRjaEV2ZW50KGVudHJ5Rm9jdXNFdmVudCk7XG5cbiAgICAgICAgICAgIGlmICghZW50cnlGb2N1c0V2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgICAgICAgICAgY29uc3QgaXRlbXMgPSBnZXRJdGVtcygpLmZpbHRlcigoaXRlbSkgPT4gaXRlbS5mb2N1c2FibGUpO1xuICAgICAgICAgICAgICBjb25zdCBhY3RpdmVJdGVtID0gaXRlbXMuZmluZCgoaXRlbSkgPT4gaXRlbS5hY3RpdmUpO1xuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50SXRlbSA9IGl0ZW1zLmZpbmQoKGl0ZW0pID0+IGl0ZW0uaWQgPT09IGN1cnJlbnRUYWJTdG9wSWQpO1xuICAgICAgICAgICAgICBjb25zdCBjYW5kaWRhdGVJdGVtcyA9IFthY3RpdmVJdGVtLCBjdXJyZW50SXRlbSwgLi4uaXRlbXNdLmZpbHRlcihcbiAgICAgICAgICAgICAgICBCb29sZWFuXG4gICAgICAgICAgICAgICkgYXMgdHlwZW9mIGl0ZW1zO1xuICAgICAgICAgICAgICBjb25zdCBjYW5kaWRhdGVOb2RlcyA9IGNhbmRpZGF0ZUl0ZW1zLm1hcCgoaXRlbSkgPT4gaXRlbS5yZWYuY3VycmVudCEpO1xuICAgICAgICAgICAgICBmb2N1c0ZpcnN0KGNhbmRpZGF0ZU5vZGVzLCBwcmV2ZW50U2Nyb2xsT25FbnRyeUZvY3VzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpc0NsaWNrRm9jdXNSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgICB9KX1cbiAgICAgICAgb25CbHVyPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkJsdXIsICgpID0+IHNldElzVGFiYmluZ0JhY2tPdXQoZmFsc2UpKX1cbiAgICAgIC8+XG4gICAgPC9Sb3ZpbmdGb2N1c1Byb3ZpZGVyPlxuICApO1xufSk7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFJvdmluZ0ZvY3VzR3JvdXBJdGVtXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IElURU1fTkFNRSA9ICdSb3ZpbmdGb2N1c0dyb3VwSXRlbSc7XG5cbnR5cGUgUm92aW5nRm9jdXNJdGVtRWxlbWVudCA9IFJlYWN0LkNvbXBvbmVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLnNwYW4+O1xudHlwZSBQcmltaXRpdmVTcGFuUHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5zcGFuPjtcbmludGVyZmFjZSBSb3ZpbmdGb2N1c0l0ZW1Qcm9wcyBleHRlbmRzIE9taXQ8UHJpbWl0aXZlU3BhblByb3BzLCAnY2hpbGRyZW4nPiB7XG4gIHRhYlN0b3BJZD86IHN0cmluZztcbiAgZm9jdXNhYmxlPzogYm9vbGVhbjtcbiAgYWN0aXZlPzogYm9vbGVhbjtcbiAgY2hpbGRyZW4/OlxuICAgIHwgUmVhY3QuUmVhY3ROb2RlXG4gICAgfCAoKHByb3BzOiB7IGhhc1RhYlN0b3A6IGJvb2xlYW47IGlzQ3VycmVudFRhYlN0b3A6IGJvb2xlYW4gfSkgPT4gUmVhY3QuUmVhY3ROb2RlKTtcbn1cblxuY29uc3QgUm92aW5nRm9jdXNHcm91cEl0ZW0gPSBSZWFjdC5mb3J3YXJkUmVmPFJvdmluZ0ZvY3VzSXRlbUVsZW1lbnQsIFJvdmluZ0ZvY3VzSXRlbVByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxSb3ZpbmdGb2N1c0l0ZW1Qcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIF9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwLFxuICAgICAgZm9jdXNhYmxlID0gdHJ1ZSxcbiAgICAgIGFjdGl2ZSA9IGZhbHNlLFxuICAgICAgdGFiU3RvcElkLFxuICAgICAgY2hpbGRyZW4sXG4gICAgICAuLi5pdGVtUHJvcHNcbiAgICB9ID0gcHJvcHM7XG4gICAgY29uc3QgYXV0b0lkID0gdXNlSWQoKTtcbiAgICBjb25zdCBpZCA9IHRhYlN0b3BJZCB8fCBhdXRvSWQ7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVJvdmluZ0ZvY3VzQ29udGV4dChJVEVNX05BTUUsIF9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwKTtcbiAgICBjb25zdCBpc0N1cnJlbnRUYWJTdG9wID0gY29udGV4dC5jdXJyZW50VGFiU3RvcElkID09PSBpZDtcbiAgICBjb25zdCBnZXRJdGVtcyA9IHVzZUNvbGxlY3Rpb24oX19zY29wZVJvdmluZ0ZvY3VzR3JvdXApO1xuXG4gICAgY29uc3QgeyBvbkZvY3VzYWJsZUl0ZW1BZGQsIG9uRm9jdXNhYmxlSXRlbVJlbW92ZSwgY3VycmVudFRhYlN0b3BJZCB9ID0gY29udGV4dDtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBpZiAoZm9jdXNhYmxlKSB7XG4gICAgICAgIG9uRm9jdXNhYmxlSXRlbUFkZCgpO1xuICAgICAgICByZXR1cm4gKCkgPT4gb25Gb2N1c2FibGVJdGVtUmVtb3ZlKCk7XG4gICAgICB9XG4gICAgfSwgW2ZvY3VzYWJsZSwgb25Gb2N1c2FibGVJdGVtQWRkLCBvbkZvY3VzYWJsZUl0ZW1SZW1vdmVdKTtcblxuICAgIHJldHVybiAoXG4gICAgICA8Q29sbGVjdGlvbi5JdGVtU2xvdFxuICAgICAgICBzY29wZT17X19zY29wZVJvdmluZ0ZvY3VzR3JvdXB9XG4gICAgICAgIGlkPXtpZH1cbiAgICAgICAgZm9jdXNhYmxlPXtmb2N1c2FibGV9XG4gICAgICAgIGFjdGl2ZT17YWN0aXZlfVxuICAgICAgPlxuICAgICAgICA8UHJpbWl0aXZlLnNwYW5cbiAgICAgICAgICB0YWJJbmRleD17aXNDdXJyZW50VGFiU3RvcCA/IDAgOiAtMX1cbiAgICAgICAgICBkYXRhLW9yaWVudGF0aW9uPXtjb250ZXh0Lm9yaWVudGF0aW9ufVxuICAgICAgICAgIHsuLi5pdGVtUHJvcHN9XG4gICAgICAgICAgcmVmPXtmb3J3YXJkZWRSZWZ9XG4gICAgICAgICAgb25Nb3VzZURvd249e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uTW91c2VEb3duLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIC8vIFdlIHByZXZlbnQgZm9jdXNpbmcgbm9uLWZvY3VzYWJsZSBpdGVtcyBvbiBgbW91c2Vkb3duYC5cbiAgICAgICAgICAgIC8vIEV2ZW4gdGhvdWdoIHRoZSBpdGVtIGhhcyB0YWJJbmRleD17LTF9LCB0aGF0IG9ubHkgbWVhbnMgdGFrZSBpdCBvdXQgb2YgdGhlIHRhYiBvcmRlci5cbiAgICAgICAgICAgIGlmICghZm9jdXNhYmxlKSBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgLy8gU2FmYXJpIGRvZXNuJ3QgZm9jdXMgYSBidXR0b24gd2hlbiBjbGlja2VkIHNvIHdlIHJ1biBvdXIgbG9naWMgb24gbW91c2Vkb3duIGFsc29cbiAgICAgICAgICAgIGVsc2UgY29udGV4dC5vbkl0ZW1Gb2N1cyhpZCk7XG4gICAgICAgICAgfSl9XG4gICAgICAgICAgb25Gb2N1cz17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Gb2N1cywgKCkgPT4gY29udGV4dC5vbkl0ZW1Gb2N1cyhpZCkpfVxuICAgICAgICAgIG9uS2V5RG93bj17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25LZXlEb3duLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIGlmIChldmVudC5rZXkgPT09ICdUYWInICYmIGV2ZW50LnNoaWZ0S2V5KSB7XG4gICAgICAgICAgICAgIGNvbnRleHQub25JdGVtU2hpZnRUYWIoKTtcbiAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoZXZlbnQudGFyZ2V0ICE9PSBldmVudC5jdXJyZW50VGFyZ2V0KSByZXR1cm47XG5cbiAgICAgICAgICAgIGNvbnN0IGZvY3VzSW50ZW50ID0gZ2V0Rm9jdXNJbnRlbnQoZXZlbnQsIGNvbnRleHQub3JpZW50YXRpb24sIGNvbnRleHQuZGlyKTtcblxuICAgICAgICAgICAgaWYgKGZvY3VzSW50ZW50ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgaWYgKGV2ZW50Lm1ldGFLZXkgfHwgZXZlbnQuY3RybEtleSB8fCBldmVudC5hbHRLZXkgfHwgZXZlbnQuc2hpZnRLZXkpIHJldHVybjtcbiAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgY29uc3QgaXRlbXMgPSBnZXRJdGVtcygpLmZpbHRlcigoaXRlbSkgPT4gaXRlbS5mb2N1c2FibGUpO1xuICAgICAgICAgICAgICBsZXQgY2FuZGlkYXRlTm9kZXMgPSBpdGVtcy5tYXAoKGl0ZW0pID0+IGl0ZW0ucmVmLmN1cnJlbnQhKTtcblxuICAgICAgICAgICAgICBpZiAoZm9jdXNJbnRlbnQgPT09ICdsYXN0JykgY2FuZGlkYXRlTm9kZXMucmV2ZXJzZSgpO1xuICAgICAgICAgICAgICBlbHNlIGlmIChmb2N1c0ludGVudCA9PT0gJ3ByZXYnIHx8IGZvY3VzSW50ZW50ID09PSAnbmV4dCcpIHtcbiAgICAgICAgICAgICAgICBpZiAoZm9jdXNJbnRlbnQgPT09ICdwcmV2JykgY2FuZGlkYXRlTm9kZXMucmV2ZXJzZSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRJbmRleCA9IGNhbmRpZGF0ZU5vZGVzLmluZGV4T2YoZXZlbnQuY3VycmVudFRhcmdldCk7XG4gICAgICAgICAgICAgICAgY2FuZGlkYXRlTm9kZXMgPSBjb250ZXh0Lmxvb3BcbiAgICAgICAgICAgICAgICAgID8gd3JhcEFycmF5KGNhbmRpZGF0ZU5vZGVzLCBjdXJyZW50SW5kZXggKyAxKVxuICAgICAgICAgICAgICAgICAgOiBjYW5kaWRhdGVOb2Rlcy5zbGljZShjdXJyZW50SW5kZXggKyAxKTtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgKiBJbXBlcmF0aXZlIGZvY3VzIGR1cmluZyBrZXlkb3duIGlzIHJpc2t5IHNvIHdlIHByZXZlbnQgUmVhY3QncyBiYXRjaGluZyB1cGRhdGVzXG4gICAgICAgICAgICAgICAqIHRvIGF2b2lkIHBvdGVudGlhbCBidWdzLiBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9pc3N1ZXMvMjAzMzJcbiAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gZm9jdXNGaXJzdChjYW5kaWRhdGVOb2RlcykpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pfVxuICAgICAgICA+XG4gICAgICAgICAge3R5cGVvZiBjaGlsZHJlbiA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICAgICAgPyBjaGlsZHJlbih7IGlzQ3VycmVudFRhYlN0b3AsIGhhc1RhYlN0b3A6IGN1cnJlbnRUYWJTdG9wSWQgIT0gbnVsbCB9KVxuICAgICAgICAgICAgOiBjaGlsZHJlbn1cbiAgICAgICAgPC9QcmltaXRpdmUuc3Bhbj5cbiAgICAgIDwvQ29sbGVjdGlvbi5JdGVtU2xvdD5cbiAgICApO1xuICB9XG4pO1xuXG5Sb3ZpbmdGb2N1c0dyb3VwSXRlbS5kaXNwbGF5TmFtZSA9IElURU1fTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG4vLyBwcmV0dGllci1pZ25vcmVcbmNvbnN0IE1BUF9LRVlfVE9fRk9DVVNfSU5URU5UOiBSZWNvcmQ8c3RyaW5nLCBGb2N1c0ludGVudD4gPSB7XG4gIEFycm93TGVmdDogJ3ByZXYnLCBBcnJvd1VwOiAncHJldicsXG4gIEFycm93UmlnaHQ6ICduZXh0JywgQXJyb3dEb3duOiAnbmV4dCcsXG4gIFBhZ2VVcDogJ2ZpcnN0JywgSG9tZTogJ2ZpcnN0JyxcbiAgUGFnZURvd246ICdsYXN0JywgRW5kOiAnbGFzdCcsXG59O1xuXG5mdW5jdGlvbiBnZXREaXJlY3Rpb25Bd2FyZUtleShrZXk6IHN0cmluZywgZGlyPzogRGlyZWN0aW9uKSB7XG4gIGlmIChkaXIgIT09ICdydGwnKSByZXR1cm4ga2V5O1xuICByZXR1cm4ga2V5ID09PSAnQXJyb3dMZWZ0JyA/ICdBcnJvd1JpZ2h0JyA6IGtleSA9PT0gJ0Fycm93UmlnaHQnID8gJ0Fycm93TGVmdCcgOiBrZXk7XG59XG5cbnR5cGUgRm9jdXNJbnRlbnQgPSAnZmlyc3QnIHwgJ2xhc3QnIHwgJ3ByZXYnIHwgJ25leHQnO1xuXG5mdW5jdGlvbiBnZXRGb2N1c0ludGVudChldmVudDogUmVhY3QuS2V5Ym9hcmRFdmVudCwgb3JpZW50YXRpb24/OiBPcmllbnRhdGlvbiwgZGlyPzogRGlyZWN0aW9uKSB7XG4gIGNvbnN0IGtleSA9IGdldERpcmVjdGlvbkF3YXJlS2V5KGV2ZW50LmtleSwgZGlyKTtcbiAgaWYgKG9yaWVudGF0aW9uID09PSAndmVydGljYWwnICYmIFsnQXJyb3dMZWZ0JywgJ0Fycm93UmlnaHQnXS5pbmNsdWRlcyhrZXkpKSByZXR1cm4gdW5kZWZpbmVkO1xuICBpZiAob3JpZW50YXRpb24gPT09ICdob3Jpem9udGFsJyAmJiBbJ0Fycm93VXAnLCAnQXJyb3dEb3duJ10uaW5jbHVkZXMoa2V5KSkgcmV0dXJuIHVuZGVmaW5lZDtcbiAgcmV0dXJuIE1BUF9LRVlfVE9fRk9DVVNfSU5URU5UW2tleV07XG59XG5cbmZ1bmN0aW9uIGZvY3VzRmlyc3QoY2FuZGlkYXRlczogSFRNTEVsZW1lbnRbXSwgcHJldmVudFNjcm9sbCA9IGZhbHNlKSB7XG4gIGNvbnN0IFBSRVZJT1VTTFlfRk9DVVNFRF9FTEVNRU5UID0gZG9jdW1lbnQuYWN0aXZlRWxlbWVudDtcbiAgZm9yIChjb25zdCBjYW5kaWRhdGUgb2YgY2FuZGlkYXRlcykge1xuICAgIC8vIGlmIGZvY3VzIGlzIGFscmVhZHkgd2hlcmUgd2Ugd2FudCB0byBnbywgd2UgZG9uJ3Qgd2FudCB0byBrZWVwIGdvaW5nIHRocm91Z2ggdGhlIGNhbmRpZGF0ZXNcbiAgICBpZiAoY2FuZGlkYXRlID09PSBQUkVWSU9VU0xZX0ZPQ1VTRURfRUxFTUVOVCkgcmV0dXJuO1xuICAgIGNhbmRpZGF0ZS5mb2N1cyh7IHByZXZlbnRTY3JvbGwgfSk7XG4gICAgaWYgKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgIT09IFBSRVZJT1VTTFlfRk9DVVNFRF9FTEVNRU5UKSByZXR1cm47XG4gIH1cbn1cblxuLyoqXG4gKiBXcmFwcyBhbiBhcnJheSBhcm91bmQgaXRzZWxmIGF0IGEgZ2l2ZW4gc3RhcnQgaW5kZXhcbiAqIEV4YW1wbGU6IGB3cmFwQXJyYXkoWydhJywgJ2InLCAnYycsICdkJ10sIDIpID09PSBbJ2MnLCAnZCcsICdhJywgJ2InXWBcbiAqL1xuZnVuY3Rpb24gd3JhcEFycmF5PFQ+KGFycmF5OiBUW10sIHN0YXJ0SW5kZXg6IG51bWJlcikge1xuICByZXR1cm4gYXJyYXkubWFwPFQ+KChfLCBpbmRleCkgPT4gYXJyYXlbKHN0YXJ0SW5kZXggKyBpbmRleCkgJSBhcnJheS5sZW5ndGhdISk7XG59XG5cbmNvbnN0IFJvb3QgPSBSb3ZpbmdGb2N1c0dyb3VwO1xuY29uc3QgSXRlbSA9IFJvdmluZ0ZvY3VzR3JvdXBJdGVtO1xuXG5leHBvcnQge1xuICBjcmVhdGVSb3ZpbmdGb2N1c0dyb3VwU2NvcGUsXG4gIC8vXG4gIFJvdmluZ0ZvY3VzR3JvdXAsXG4gIFJvdmluZ0ZvY3VzR3JvdXBJdGVtLFxuICAvL1xuICBSb290LFxuICBJdGVtLFxufTtcbmV4cG9ydCB0eXBlIHsgUm92aW5nRm9jdXNHcm91cFByb3BzLCBSb3ZpbmdGb2N1c0l0ZW1Qcm9wcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@19.1.6_@types+react@19.1.8__@types_7b46adce8be1bcd7dba6d0dca748f267/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ })

};
;