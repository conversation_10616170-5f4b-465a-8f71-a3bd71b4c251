import type { AuthBridgeResponse, BrandAnalysisRequest, BrandAnalysisResponse, BrandAnalysis } from '../types';
export declare class FireGeoApiClient {
    private baseUrl;
    private apiSecret;
    private session?;
    constructor(baseUrl: string, apiSecret: string);
    /**
     * Make a request to the FireGeo API
     */
    private makeRequest;
    /**
     * Make an authenticated request with Supabase token
     */
    private makeAuthenticatedRequest;
    /**
     * Authenticate user with Supabase token
     */
    authenticateUser(supabaseToken: string): Promise<AuthBridgeResponse>;
    /**
     * Start a new brand analysis
     */
    startBrandAnalysis(data: BrandAnalysisRequest, supabaseToken: string): Promise<BrandAnalysisResponse>;
    /**
     * Get all brand analyses for the authenticated user
     */
    getBrandAnalyses(supabaseToken: string): Promise<BrandAnalysis[]>;
    /**
     * Get a specific brand analysis by ID
     */
    getBrandAnalysis(analysisId: string, supabaseToken: string): Promise<BrandAnalysis | null>;
    /**
     * Check AI provider availability
     */
    checkProviders(supabaseToken: string): Promise<any>;
    /**
     * Scrape a website URL
     */
    scrapeWebsite(url: string, supabaseToken: string): Promise<any>;
    /**
     * Health check endpoint
     */
    healthCheck(): Promise<any>;
    /**
     * Create SSE connection for real-time updates
     */
    createEventSource(endpoint: string, supabaseToken: string, onMessage?: (data: any) => void, onError?: (error: Event) => void): EventSource;
}
