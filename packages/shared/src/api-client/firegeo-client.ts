import type {
  ApiResponse,
  AuthBridgeRequest,
  AuthBridgeResponse,
  BrandAnalysisRequest,
  BrandAnalysisResponse,
  BrandAnalysis,
  FireGeoSession,
  HttpMethod,
  RequestConfig
} from '../types'

export class FireGeoApiClient {
  private baseUrl: string
  private apiSecret: string
  private session?: FireGeoSession

  constructor(baseUrl: string, apiSecret: string) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // Remove trailing slash
    this.apiSecret = apiSecret
  }
  
  /**
   * Make a request to the FireGeo API
   */
  private async makeRequest<T = any>(
    endpoint: string, 
    options: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': this.apiSecret,
        ...options.headers,
      },
    }
    
    if (options.body && (config.method === 'POST' || config.method === 'PUT' || config.method === 'PATCH')) {
      config.body = JSON.stringify(options.body)
    }
    
    try {
      const response = await fetch(url, config)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      return data
    } catch (error) {
      console.error('FireGeo API request failed:', error)
      throw error
    }
  }
  
  /**
   * Make an authenticated request with Supabase token
   */
  private async makeAuthenticatedRequest<T = any>(
    endpoint: string,
    supabaseToken: string,
    options: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      headers: {
        'Authorization': `Bearer ${supabaseToken}`,
        ...options.headers,
      },
    })
  }
  
  /**
   * Authenticate user with Supabase token
   */
  async authenticateUser(supabaseToken: string): Promise<AuthBridgeResponse> {
    const request: AuthBridgeRequest = {
      supabaseToken,
      user: {} as any // Will be validated by the API
    }

    const response = await this.makeRequest<AuthBridgeResponse>('/api/auth/bridge', {
      method: 'POST',
      body: request,
    })

    // Store the session for future requests
    if (response.data?.success && response.data.session) {
      this.session = response.data.session
    }

    return response.data!
  }
  
  /**
   * Start a new brand analysis
   */
  async startBrandAnalysis(
    data: BrandAnalysisRequest,
    supabaseToken: string
  ): Promise<BrandAnalysisResponse> {
    const response = await this.makeAuthenticatedRequest<BrandAnalysisResponse>(
      '/api/brand-monitor/analyze',
      supabaseToken,
      {
        method: 'POST',
        body: data,
      }
    )
    return response.data!
  }
  
  /**
   * Get all brand analyses for the authenticated user
   */
  async getBrandAnalyses(supabaseToken: string): Promise<BrandAnalysis[]> {
    const response = await this.makeAuthenticatedRequest<BrandAnalysis[]>(
      '/api/brand-monitor/analyses',
      supabaseToken
    )
    return response.data || []
  }
  
  /**
   * Get a specific brand analysis by ID
   */
  async getBrandAnalysis(
    analysisId: string, 
    supabaseToken: string
  ): Promise<BrandAnalysis | null> {
    try {
      const response = await this.makeAuthenticatedRequest<BrandAnalysis>(
        `/api/brand-monitor/analyses/${analysisId}`,
        supabaseToken
      )
      return response.data || null
    } catch (error) {
      console.error('Failed to get brand analysis:', error)
      return null
    }
  }
  
  /**
   * Check AI provider availability
   */
  async checkProviders(supabaseToken: string): Promise<any> {
    return this.makeAuthenticatedRequest(
      '/api/brand-monitor/check-providers',
      supabaseToken
    )
  }
  
  /**
   * Scrape a website URL
   */
  async scrapeWebsite(
    url: string, 
    supabaseToken: string
  ): Promise<any> {
    return this.makeAuthenticatedRequest(
      '/api/brand-monitor/scrape',
      supabaseToken,
      {
        method: 'POST',
        body: { url },
      }
    )
  }
  
  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<any> {
    return this.makeRequest('/api/auth/bridge')
  }
  
  /**
   * Create SSE connection for real-time updates
   */
  createEventSource(
    endpoint: string,
    supabaseToken: string,
    onMessage?: (data: any) => void,
    onError?: (error: Event) => void
  ): EventSource {
    const url = new URL(`${this.baseUrl}${endpoint}`)
    url.searchParams.set('token', supabaseToken)
    
    const eventSource = new EventSource(url.toString())
    
    if (onMessage) {
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onMessage(data)
        } catch (error) {
          console.error('Failed to parse SSE data:', error)
        }
      }
    }
    
    if (onError) {
      eventSource.onerror = onError
    }
    
    return eventSource
  }
}
